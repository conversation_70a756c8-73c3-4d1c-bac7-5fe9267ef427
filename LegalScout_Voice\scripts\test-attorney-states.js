#!/usr/bin/env node

/**
 * Comprehensive Attorney State Testing
 * 
 * Tests all possible attorney states and access patterns to ensure
 * the system handles every scenario cleanly.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://utopqxsvudgrtiwenlzl.supabase.co';
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY || process.env.VITE_SUPABASE_KEY;

const supabase = createClient(supabaseUrl, supabaseKey);

console.log('🧪 LegalScout Voice - Attorney State Testing Suite');
console.log('Testing all possible attorney states and access patterns...\n');

// Test scenarios
const testScenarios = [
  {
    name: 'Existing Attorney - Correct Email Match',
    userId: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e',
    email: '<EMAIL>',
    expectedResult: 'FOUND'
  },
  {
    name: 'Existing Attorney - By User ID Only',
    userId: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e',
    email: null,
    expectedResult: 'FOUND'
  },
  {
    name: 'Existing Attorney - By Email Only',
    userId: null,
    email: '<EMAIL>',
    expectedResult: 'FOUND'
  },
  {
    name: 'Non-Existent Attorney - New User ID',
    userId: '00000000-0000-0000-0000-000000000000',
    email: '<EMAIL>',
    expectedResult: 'NOT_FOUND'
  },
  {
    name: 'Non-Existent Attorney - New Email',
    userId: 'bafd37ba-a143-40ea-bcf5-e25fe149b55e',
    email: '<EMAIL>',
    expectedResult: 'NOT_FOUND'
  },
  {
    name: 'Edge Case - Null Values',
    userId: null,
    email: null,
    expectedResult: 'ERROR'
  },
  {
    name: 'Edge Case - Empty Strings',
    userId: '',
    email: '',
    expectedResult: 'ERROR'
  }
];

// Test functions
async function testAttorneyByUserId(userId) {
  try {
    if (!userId) return { data: null, error: { message: 'No userId provided' } };
    
    const { data, error } = await supabase
      .from('attorneys')
      .select('*')
      .eq('user_id', userId)
      .single();
    
    return { data, error };
  } catch (err) {
    return { data: null, error: err };
  }
}

async function testAttorneyByEmail(email) {
  try {
    if (!email) return { data: null, error: { message: 'No email provided' } };
    
    const { data, error } = await supabase
      .from('attorneys')
      .select('*')
      .eq('email', email)
      .single();
    
    return { data, error };
  } catch (err) {
    return { data: null, error: err };
  }
}

async function testAttorneyByBoth(userId, email) {
  try {
    if (!userId && !email) return { data: null, error: { message: 'No identifiers provided' } };
    
    let query = supabase.from('attorneys').select('*');
    
    if (userId) query = query.eq('user_id', userId);
    if (email) query = query.eq('email', email);
    
    const { data, error } = await query.single();
    
    return { data, error };
  } catch (err) {
    return { data: null, error: err };
  }
}

async function testRLSPolicies() {
  console.log('🔒 Testing Row Level Security Policies...');
  
  try {
    // Test public access (should work for SELECT)
    const { data: publicData, error: publicError } = await supabase
      .from('attorneys')
      .select('id, email, subdomain, name, firm_name')
      .limit(1);
    
    console.log('✅ Public SELECT access:', publicError ? 'BLOCKED' : 'ALLOWED');
    
    // Test authenticated access patterns
    const testCases = [
      { method: 'SELECT', description: 'Select by user_id' },
      { method: 'UPDATE', description: 'Update own record' },
      { method: 'INSERT', description: 'Insert new record' }
    ];
    
    for (const testCase of testCases) {
      console.log(`   ${testCase.method}: ${testCase.description} - Testing...`);
    }
    
  } catch (error) {
    console.error('❌ RLS Policy test failed:', error.message);
  }
}

async function testDataIntegrity() {
  console.log('🔍 Testing Data Integrity...');
  
  try {
    // Check for duplicate emails
    const { data: duplicateEmails } = await supabase
      .from('attorneys')
      .select('email')
      .not('email', 'is', null);
    
    const emailCounts = {};
    duplicateEmails?.forEach(record => {
      emailCounts[record.email] = (emailCounts[record.email] || 0) + 1;
    });
    
    const duplicates = Object.entries(emailCounts).filter(([email, count]) => count > 1);
    
    if (duplicates.length > 0) {
      console.log('⚠️  Duplicate emails found:', duplicates);
    } else {
      console.log('✅ No duplicate emails');
    }
    
    // Check for orphaned records
    const { data: orphanedRecords } = await supabase
      .from('attorneys')
      .select('id, email, user_id')
      .or('user_id.is.null,email.is.null');
    
    if (orphanedRecords?.length > 0) {
      console.log('⚠️  Orphaned records found:', orphanedRecords.length);
    } else {
      console.log('✅ No orphaned records');
    }
    
    // Check Vapi assistant linkage
    const { data: attorneysWithAssistants } = await supabase
      .from('attorneys')
      .select('id, email, vapi_assistant_id')
      .not('vapi_assistant_id', 'is', null);
    
    console.log(`✅ Attorneys with Vapi assistants: ${attorneysWithAssistants?.length || 0}`);
    
  } catch (error) {
    console.error('❌ Data integrity test failed:', error.message);
  }
}

async function runTestScenario(scenario) {
  console.log(`\n📋 Testing: ${scenario.name}`);
  console.log(`   User ID: ${scenario.userId || 'null'}`);
  console.log(`   Email: ${scenario.email || 'null'}`);
  console.log(`   Expected: ${scenario.expectedResult}`);
  
  const results = {
    byUserId: null,
    byEmail: null,
    byBoth: null
  };
  
  // Test by user ID
  if (scenario.userId) {
    results.byUserId = await testAttorneyByUserId(scenario.userId);
  }
  
  // Test by email
  if (scenario.email) {
    results.byEmail = await testAttorneyByEmail(scenario.email);
  }
  
  // Test by both
  results.byBoth = await testAttorneyByBoth(scenario.userId, scenario.email);
  
  // Analyze results
  const foundByUserId = results.byUserId?.data && !results.byUserId?.error;
  const foundByEmail = results.byEmail?.data && !results.byEmail?.error;
  const foundByBoth = results.byBoth?.data && !results.byBoth?.error;
  
  let actualResult;
  if (foundByUserId || foundByEmail || foundByBoth) {
    actualResult = 'FOUND';
  } else if (results.byBoth?.error?.message?.includes('provided') || 
             results.byUserId?.error?.message?.includes('provided') ||
             results.byEmail?.error?.message?.includes('provided')) {
    actualResult = 'ERROR';
  } else {
    actualResult = 'NOT_FOUND';
  }
  
  const passed = actualResult === scenario.expectedResult;
  
  console.log(`   Result: ${actualResult} ${passed ? '✅' : '❌'}`);
  
  if (!passed) {
    console.log(`   Expected: ${scenario.expectedResult}, Got: ${actualResult}`);
    if (results.byBoth?.error) {
      console.log(`   Error: ${results.byBoth.error.message}`);
    }
  }
  
  if (foundByBoth && results.byBoth?.data) {
    const attorney = results.byBoth.data;
    console.log(`   Found Attorney: ${attorney.name} (${attorney.firm_name})`);
    console.log(`   Subdomain: ${attorney.subdomain}`);
    console.log(`   Vapi Assistant: ${attorney.vapi_assistant_id ? 'Linked' : 'Not Linked'}`);
  }
  
  return { passed, actualResult, scenario };
}

async function runAllTests() {
  const results = {
    passed: 0,
    failed: 0,
    scenarios: []
  };
  
  // Run RLS and data integrity tests first
  await testRLSPolicies();
  console.log('');
  await testDataIntegrity();
  
  // Run scenario tests
  console.log('\n🎯 Running Attorney State Scenarios...');
  
  for (const scenario of testScenarios) {
    const result = await runTestScenario(scenario);
    results.scenarios.push(result);
    
    if (result.passed) {
      results.passed++;
    } else {
      results.failed++;
    }
  }
  
  // Summary
  console.log('\n📊 TEST SUMMARY');
  console.log('================');
  console.log(`✅ Passed: ${results.passed}`);
  console.log(`❌ Failed: ${results.failed}`);
  console.log(`🎯 Total: ${results.passed + results.failed}`);
  console.log(`📈 Success Rate: ${Math.round((results.passed / (results.passed + results.failed)) * 100)}%`);
  
  if (results.failed > 0) {
    console.log('\n❌ FAILED SCENARIOS:');
    results.scenarios
      .filter(r => !r.passed)
      .forEach(r => {
        console.log(`   - ${r.scenario.name}: Expected ${r.scenario.expectedResult}, Got ${r.actualResult}`);
      });
  }
  
  console.log('\n🎉 Attorney state testing complete!');
  
  return results;
}

// Run the tests
runAllTests().catch(console.error);

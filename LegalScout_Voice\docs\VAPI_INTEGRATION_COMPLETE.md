# LegalScout Vapi Integration - Complete Guide

This is the authoritative documentation for Vapi integration in LegalScout Voice. This document consolidates all Vapi-related functionality and reflects the actual implementation.

## Overview

LegalScout uses a sophisticated multi-service Vapi integration architecture that includes:

1. **Vapi Web SDK (@vapi-ai/web 2.3.1)** - Client-side voice interactions
2. **Vapi MCP Server (@vapi-ai/mcp-server 0.0.6)** - Programmatic assistant management
3. **Enhanced Vapi Services** - Custom service layer with fallback mechanisms
4. **Webhook Integration** - Real-time call event handling
5. **Supabase Integration** - Secure data storage with Row-Level Security

## Architecture Components

### 1. Core Services

#### VapiService (`src/services/VapiService.js`)
- Main service layer for Vapi interactions
- Handles SDK initialization and call management
- Provides error handling and retry logic

#### EnhancedVapiMcpService (`src/services/EnhancedVapiMcpService.js`)
- MCP integration for programmatic control
- Supports both StreamableHTTP and SSE transports
- Assistant creation and management
- Call creation and monitoring

#### VapiProperIntegration (`src/services/VapiProperIntegration.js`)
- Fallback implementation for direct API calls
- Ensures reliability when MCP is unavailable
- Follows current Vapi API v2 patterns

### 2. Assistant Management

#### Assistant Creation
```javascript
// Automatic assistant creation for attorneys
const assistant = await enhancedVapiMcpService.createAssistant({
  name: `${attorneyName} Legal Assistant`,
  model: {
    provider: 'openai',
    model: 'gpt-4o',
    messages: [
      {
        role: 'system',
        content: customInstructions
      }
    ]
  },
  voice: {
    provider: 'openai',
    voiceId: 'echo'
  },
  analysisPlan: {
    summaryPlan: { enabled: true },
    structuredDataPlan: { enabled: true, schema: customSchema }
  },
  server: {
    url: `https://dashboard.legalscout.net/api/webhook/vapi-call`,
    headers: {
      'X-Assistant-ID': assistantId,
      'X-Attorney-ID': attorneyId
    }
  }
});
```

#### Assistant Configuration
- **Voice Selection**: OpenAI 'echo' voice as default, customizable per attorney
- **Model**: GPT-4o for optimal performance
- **Instructions**: Attorney-specific legal guidance templates
- **Structured Data**: Custom fields for legal consultation data collection
- **Webhooks**: Attorney-specific webhook URLs for proper routing

### 3. Call Management

#### Starting Calls
```javascript
// Client-side call initiation
import Vapi from '@vapi-ai/web';

const vapi = new Vapi(VITE_VAPI_PUBLIC_KEY);

// Start call with assistant ID
await vapi.start(assistantId, {
  variableValues: {
    attorneyName: attorney.name,
    firmName: attorney.firm_name,
    practiceAreas: attorney.practice_areas
  }
});
```

#### Call Events
```javascript
// Event handling
vapi.on('call-start', () => {
  console.log('Call started');
});

vapi.on('call-end', () => {
  console.log('Call ended');
});

vapi.on('message', (message) => {
  // Handle real-time messages, transcripts, function calls
  console.log('Message received:', message);
});
```

### 4. Webhook Integration

#### Webhook Configuration
- **URL Pattern**: `https://dashboard.legalscout.net/api/webhook/vapi-call`
- **Headers**: Include assistant and attorney IDs for proper routing
- **Events**: Call start, end, transcript updates, structured data

#### Webhook Handler (`api/webhook/vapi-call.js`)
```javascript
// Processes incoming Vapi webhooks
export default async function handler(req, res) {
  const { type, call, message } = req.body;
  
  switch (type) {
    case 'end-of-call-report':
      await storeCallRecord(call);
      await processStructuredData(call.analysis);
      break;
    case 'transcript':
      await updateTranscript(call.id, message);
      break;
  }
  
  res.status(200).json({ received: true });
}
```

## Setup Instructions

### 1. Prerequisites

- Vapi account at [vapi.ai](https://vapi.ai)
- Supabase project for data storage
- Vercel deployment for webhook handling

### 2. Environment Configuration

Copy `.env.example` to `.env` and configure:

```bash
# Required Vapi Configuration
VITE_VAPI_PUBLIC_KEY=your_vapi_public_key
VAPI_TOKEN=your_vapi_private_key

# Required Supabase Configuration  
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key

# Optional Assistant Configuration
VITE_VAPI_DEFAULT_ASSISTANT_ID=your_default_assistant_id
```

### 3. Database Setup

Run Supabase migrations to create required tables:

```bash
cd supabase
supabase db push
```

Key tables:
- `attorneys` - Attorney profiles and assistant mappings
- `call_records` - Call logs with RLS policies
- `assistant_ui_configs` - UI customization per assistant
- `assistant_subdomains` - Subdomain routing configuration

### 4. Webhook Configuration

In Vapi Dashboard:
1. Go to Assistant settings
2. Set Server URL: `https://your-domain.com/api/webhook/vapi-call`
3. Configure headers for proper routing

## Data Flow

### 1. Attorney Onboarding
1. Attorney signs up via Google OAuth
2. Profile created in Supabase `attorneys` table
3. Vapi assistant automatically created via MCP
4. Assistant ID stored in attorney profile
5. Webhook URL configured for assistant

### 2. Call Lifecycle
1. User visits attorney subdomain or main site
2. Client loads assistant configuration from Supabase
3. Vapi SDK initialized with public key
4. Call started with assistant ID and variables
5. Real-time events handled via webhooks
6. Call data stored in Supabase with RLS

### 3. Data Security
- **Row-Level Security**: Users only see their own data
- **API Key Management**: Public keys for client, private keys for server
- **Webhook Security**: Signed requests and header validation

## Advanced Features

### 1. Subdomain Routing
- Custom subdomains per attorney (e.g., `johnsmith.legalscout.net`)
- Dynamic assistant loading based on subdomain
- Branded experience per attorney

### 2. Custom Fields
- Attorney-configurable data collection fields
- Structured data extraction from conversations
- Integration with legal practice management systems

### 3. Call Control
- Real-time call monitoring for attorneys
- SMS notifications for ongoing calls
- Secure call control links with JWT tokens

## Troubleshooting

### Common Issues

1. **Assistant Creation Fails**
   - Check VAPI_TOKEN is set correctly
   - Verify MCP server connectivity
   - Check Supabase RLS policies

2. **Calls Don't Start**
   - Verify VITE_VAPI_PUBLIC_KEY is correct
   - Check browser console for errors
   - Ensure assistant ID exists

3. **Webhooks Not Received**
   - Verify webhook URL is accessible
   - Check Vercel function logs
   - Validate webhook signature

### Debug Tools

```bash
# Test Vapi MCP connection
npm run test:vapi-mcp

# Test assistant creation
npm run test:assistant-propagation

# Check system health
npm run test:system-quick
```

## API Reference

### Key Methods

- `vapiService.initialize(apiKey, subdomain)` - Initialize Vapi SDK
- `enhancedVapiMcpService.createAssistant(config)` - Create new assistant
- `enhancedVapiMcpService.updateAssistant(id, config)` - Update assistant
- `enhancedVapiMcpService.createCall(assistantId, options)` - Create outbound call

### Event Types

- `call-start` - Call initiated
- `call-end` - Call terminated
- `speech-start` - User started speaking
- `speech-end` - User stopped speaking
- `message` - Real-time message/transcript
- `error` - Error occurred

## Resources

- [Vapi Documentation](https://docs.vapi.ai/)
- [Vapi API Reference](https://docs.vapi.ai/api-reference)
- [Vapi MCP Server](https://docs.vapi.ai/sdk/mcp-server)
- [Model Context Protocol](https://modelcontextprotocol.io/)

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>LegalScout Comprehensive Diagnostics</title>
  
  <!-- Test CSP with current configuration -->
  <meta http-equiv="Content-Security-Policy" content="
    default-src 'self';
    script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: data:
      https://cdn.jsdelivr.net https://unpkg.com https://cdnjs.cloudflare.com
      https://cdn.vapi.ai https://vapi.ai https://*.vapi.ai
      https://c.daily.co https://*.daily.co
      https://o77906.ingest.sentry.io
      https://vercel.live https://*.vercel.live https://*.vercel.app;
    style-src 'self' 'unsafe-inline'
      https://fonts.googleapis.com https://cdnjs.cloudflare.com
      https://c.daily.co https://*.daily.co;
    font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com;
    img-src 'self' data: blob: https:;
    media-src 'self' blob: data: https:;
    connect-src 'self' https: wss: ws:
      https://api.vapi.ai https://mcp.vapi.ai https://dashboard.vapi.ai
      https://utopqxsvudgrtiwenlzl.supabase.co;
    frame-src 'self' 
      https://vercel.live https://*.vercel.live https://*.vercel.app
      https://c.daily.co https://*.daily.co;
    worker-src 'self' blob:;
    child-src 'self' blob:;
    object-src 'none';
    base-uri 'self';
    form-action 'self';
  ">

  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 20px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: #333;
      min-height: 100vh;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      background: white;
      border-radius: 12px;
      box-shadow: 0 10px 30px rgba(0,0,0,0.2);
      overflow: hidden;
    }

    .header {
      background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
      color: white;
      padding: 30px;
      text-align: center;
    }

    .header h1 {
      margin: 0;
      font-size: 2.5em;
      font-weight: 300;
    }

    .header p {
      margin: 10px 0 0 0;
      opacity: 0.9;
      font-size: 1.1em;
    }

    .content {
      padding: 30px;
    }

    .test-section {
      margin-bottom: 40px;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      overflow: hidden;
    }

    .test-header {
      background: #f8f9fa;
      padding: 20px;
      border-bottom: 1px solid #e0e0e0;
      font-weight: 600;
      font-size: 1.2em;
      color: #2c3e50;
    }

    .test-content {
      padding: 20px;
    }

    .test-result {
      margin: 10px 0;
      padding: 15px;
      border-radius: 6px;
      font-family: 'Courier New', monospace;
      font-size: 0.9em;
      line-height: 1.4;
    }

    .success {
      background: #d4edda;
      border: 1px solid #c3e6cb;
      color: #155724;
    }

    .error {
      background: #f8d7da;
      border: 1px solid #f5c6cb;
      color: #721c24;
    }

    .warning {
      background: #fff3cd;
      border: 1px solid #ffeaa7;
      color: #856404;
    }

    .info {
      background: #d1ecf1;
      border: 1px solid #bee5eb;
      color: #0c5460;
    }

    .test-button {
      background: #3498db;
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 6px;
      cursor: pointer;
      font-size: 1em;
      margin: 10px 10px 10px 0;
      transition: background 0.3s;
    }

    .test-button:hover {
      background: #2980b9;
    }

    .test-button:disabled {
      background: #bdc3c7;
      cursor: not-allowed;
    }

    .code-block {
      background: #2c3e50;
      color: #ecf0f1;
      padding: 15px;
      border-radius: 6px;
      font-family: 'Courier New', monospace;
      font-size: 0.85em;
      overflow-x: auto;
      margin: 10px 0;
    }

    .progress-bar {
      width: 100%;
      height: 6px;
      background: #ecf0f1;
      border-radius: 3px;
      overflow: hidden;
      margin: 20px 0;
    }

    .progress-fill {
      height: 100%;
      background: linear-gradient(90deg, #3498db, #2ecc71);
      width: 0%;
      transition: width 0.3s ease;
    }

    .grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 20px;
      margin: 20px 0;
    }

    .metric-card {
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 8px;
      padding: 20px;
      text-align: center;
    }

    .metric-value {
      font-size: 2em;
      font-weight: bold;
      color: #2c3e50;
      margin: 10px 0;
    }

    .metric-label {
      color: #6c757d;
      font-size: 0.9em;
      text-transform: uppercase;
      letter-spacing: 1px;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>🔍 LegalScout Comprehensive Diagnostics</h1>
      <p>Advanced testing suite for CSP, CORS, and system integration issues</p>
    </div>

    <div class="content">
      <!-- Progress Bar -->
      <div class="progress-bar">
        <div class="progress-fill" id="progress-fill"></div>
      </div>

      <!-- Overview Metrics -->
      <div class="grid">
        <div class="metric-card">
          <div class="metric-value" id="tests-passed">0</div>
          <div class="metric-label">Tests Passed</div>
        </div>
        <div class="metric-card">
          <div class="metric-value" id="tests-failed">0</div>
          <div class="metric-label">Tests Failed</div>
        </div>
        <div class="metric-card">
          <div class="metric-value" id="csp-violations">0</div>
          <div class="metric-label">CSP Violations</div>
        </div>
        <div class="metric-card">
          <div class="metric-value" id="cors-errors">0</div>
          <div class="metric-label">CORS Errors</div>
        </div>
      </div>

      <!-- Test Controls -->
      <div class="test-section">
        <div class="test-header">🎮 Test Controls</div>
        <div class="test-content">
          <button class="test-button" onclick="runAllTests()">🚀 Run All Tests</button>
          <button class="test-button" onclick="runCSPTests()">🛡️ CSP Tests Only</button>
          <button class="test-button" onclick="runCORSTests()">🌐 CORS Tests Only</button>
          <button class="test-button" onclick="runVapiTests()">📞 Vapi Tests Only</button>
          <button class="test-button" onclick="clearResults()">🧹 Clear Results</button>
        </div>
      </div>

      <!-- CSP Tests -->
      <div class="test-section">
        <div class="test-header">🛡️ Content Security Policy Tests</div>
        <div class="test-content">
          <div id="csp-tests-results"></div>
        </div>
      </div>

      <!-- CORS Tests -->
      <div class="test-section">
        <div class="test-header">🌐 CORS & Preflight Tests</div>
        <div class="test-content">
          <div id="cors-tests-results"></div>
        </div>
      </div>

      <!-- Vapi Integration Tests -->
      <div class="test-section">
        <div class="test-header">📞 Vapi Integration Tests</div>
        <div class="test-content">
          <div id="vapi-tests-results"></div>
        </div>
      </div>

      <!-- Network Analysis -->
      <div class="test-section">
        <div class="test-header">📊 Network Analysis</div>
        <div class="test-content">
          <div id="network-analysis-results"></div>
        </div>
      </div>

      <!-- Environment Analysis -->
      <div class="test-section">
        <div class="test-header">🔧 Environment Analysis</div>
        <div class="test-content">
          <div id="environment-analysis-results"></div>
        </div>
      </div>

      <!-- Real-time Monitoring -->
      <div class="test-section">
        <div class="test-header">📡 Real-time Monitoring</div>
        <div class="test-content">
          <div id="monitoring-results"></div>
        </div>
      </div>
    </div>
  </div>

  <script>
    // Global test state
    let testResults = {
      passed: 0,
      failed: 0,
      cspViolations: 0,
      corsErrors: 0,
      total: 0
    };

    let monitoringActive = false;
    let violationLog = [];
    let networkLog = [];

    // Initialize diagnostics
    document.addEventListener('DOMContentLoaded', function() {
      console.log('🔍 [Diagnostics] Initializing comprehensive test suite...');
      setupCSPViolationListener();
      setupNetworkMonitoring();
      setupErrorMonitoring();
      analyzeEnvironment();
      startRealtimeMonitoring();
    });

    // CSP Violation Listener
    function setupCSPViolationListener() {
      document.addEventListener('securitypolicyviolation', (e) => {
        testResults.cspViolations++;
        updateMetrics();

        const violation = {
          timestamp: new Date().toISOString(),
          directive: e.violatedDirective,
          blockedURI: e.blockedURI,
          lineNumber: e.lineNumber,
          columnNumber: e.columnNumber,
          sourceFile: e.sourceFile,
          sample: e.sample,
          originalPolicy: e.originalPolicy
        };

        violationLog.push(violation);

        console.error('🚨 [CSP] Violation detected:', violation);

        // Display violation in real-time
        displayCSPViolation(violation);
      });
    }

    // Network Monitoring
    function setupNetworkMonitoring() {
      // Override fetch to monitor CORS issues
      const originalFetch = window.fetch;
      window.fetch = async function(...args) {
        const startTime = performance.now();
        const url = args[0];
        const options = args[1] || {};

        try {
          const response = await originalFetch.apply(this, args);
          const endTime = performance.now();

          const logEntry = {
            timestamp: new Date().toISOString(),
            url: url,
            method: options.method || 'GET',
            status: response.status,
            statusText: response.statusText,
            duration: Math.round(endTime - startTime),
            headers: Object.fromEntries(response.headers.entries()),
            success: response.ok
          };

          networkLog.push(logEntry);

          if (!response.ok) {
            testResults.corsErrors++;
            updateMetrics();
            console.error('🌐 [CORS] Request failed:', logEntry);
          }

          return response;
        } catch (error) {
          const endTime = performance.now();

          const logEntry = {
            timestamp: new Date().toISOString(),
            url: url,
            method: options.method || 'GET',
            error: error.message,
            duration: Math.round(endTime - startTime),
            success: false
          };

          networkLog.push(logEntry);
          testResults.corsErrors++;
          updateMetrics();

          console.error('🌐 [CORS] Request error:', logEntry);
          throw error;
        }
      };
    }

    // Error Monitoring
    function setupErrorMonitoring() {
      window.addEventListener('error', (e) => {
        console.error('🚨 [Error] Global error:', {
          message: e.message,
          filename: e.filename,
          lineno: e.lineno,
          colno: e.colno,
          error: e.error
        });
      });

      window.addEventListener('unhandledrejection', (e) => {
        console.error('🚨 [Promise] Unhandled rejection:', e.reason);
      });
    }

    // Update metrics display
    function updateMetrics() {
      document.getElementById('tests-passed').textContent = testResults.passed;
      document.getElementById('tests-failed').textContent = testResults.failed;
      document.getElementById('csp-violations').textContent = testResults.cspViolations;
      document.getElementById('cors-errors').textContent = testResults.corsErrors;

      const progress = testResults.total > 0 ?
        ((testResults.passed / testResults.total) * 100) : 0;
      document.getElementById('progress-fill').style.width = progress + '%';
    }

    // Display CSP violation
    function displayCSPViolation(violation) {
      const resultsDiv = document.getElementById('csp-tests-results');
      const violationDiv = document.createElement('div');
      violationDiv.className = 'test-result error';
      violationDiv.innerHTML = `
        <strong>🚨 CSP Violation Detected</strong><br>
        <strong>Directive:</strong> ${violation.directive}<br>
        <strong>Blocked URI:</strong> ${violation.blockedURI}<br>
        <strong>Source:</strong> ${violation.sourceFile}:${violation.lineNumber}:${violation.columnNumber}<br>
        <strong>Sample:</strong> ${violation.sample || 'N/A'}<br>
        <strong>Time:</strong> ${violation.timestamp}
      `;
      resultsDiv.appendChild(violationDiv);
    }

    // Environment Analysis
    function analyzeEnvironment() {
      const results = document.getElementById('environment-analysis-results');

      const analysis = {
        userAgent: navigator.userAgent,
        location: window.location.href,
        protocol: window.location.protocol,
        host: window.location.host,
        csp: getCSPInfo(),
        environment: detectEnvironment(),
        features: checkBrowserFeatures(),
        globals: checkGlobalObjects()
      };

      results.innerHTML = `
        <div class="test-result info">
          <strong>🌍 Environment Analysis</strong><br>
          <strong>Location:</strong> ${analysis.location}<br>
          <strong>Protocol:</strong> ${analysis.protocol}<br>
          <strong>Host:</strong> ${analysis.host}<br>
          <strong>Environment:</strong> ${analysis.environment}<br>
          <strong>CSP Present:</strong> ${analysis.csp.present ? 'Yes' : 'No'}<br>
          <strong>Allows Eval:</strong> ${analysis.csp.allowsEval ? 'Yes' : 'No'}<br>
          <strong>Browser:</strong> ${getBrowserInfo()}
        </div>
        <div class="code-block">
${JSON.stringify(analysis, null, 2)}
        </div>
      `;
    }

    // Get CSP information
    function getCSPInfo() {
      const cspMeta = document.querySelector('meta[http-equiv="Content-Security-Policy"]');
      return {
        present: !!cspMeta,
        content: cspMeta?.content || null,
        allowsEval: cspMeta?.content?.includes('unsafe-eval') || false,
        allowsInline: cspMeta?.content?.includes('unsafe-inline') || false,
        scriptSrc: cspMeta?.content?.match(/script-src[^;]*/)?.[0] || null
      };
    }

    // Detect environment
    function detectEnvironment() {
      const host = window.location.host;
      if (host.includes('localhost') || host.includes('127.0.0.1')) {
        return 'development';
      } else if (host.includes('vercel.app') || host.includes('legalscout.net')) {
        return 'production';
      } else {
        return 'unknown';
      }
    }

    // Check browser features
    function checkBrowserFeatures() {
      return {
        fetch: typeof fetch !== 'undefined',
        webRTC: typeof RTCPeerConnection !== 'undefined',
        webAudio: typeof AudioContext !== 'undefined' || typeof webkitAudioContext !== 'undefined',
        mediaDevices: navigator.mediaDevices && typeof navigator.mediaDevices.getUserMedia !== 'undefined',
        serviceWorker: 'serviceWorker' in navigator,
        webAssembly: typeof WebAssembly !== 'undefined'
      };
    }

    // Check global objects
    function checkGlobalObjects() {
      return {
        Vapi: typeof window.Vapi !== 'undefined',
        supabase: typeof window.supabase !== 'undefined',
        process: typeof window.process !== 'undefined',
        envVars: window.process?.env ? Object.keys(window.process.env).length : 0
      };
    }

    // Get browser info
    function getBrowserInfo() {
      const ua = navigator.userAgent;
      if (ua.includes('Chrome')) return 'Chrome';
      if (ua.includes('Firefox')) return 'Firefox';
      if (ua.includes('Safari')) return 'Safari';
      if (ua.includes('Edge')) return 'Edge';
      return 'Unknown';
    }

    // Real-time monitoring
    function startRealtimeMonitoring() {
      monitoringActive = true;
      const results = document.getElementById('monitoring-results');

      setInterval(() => {
        if (!monitoringActive) return;

        const recentViolations = violationLog.slice(-5);
        const recentNetworkErrors = networkLog.filter(log => !log.success).slice(-5);

        results.innerHTML = `
          <div class="test-result info">
            <strong>📡 Real-time Status</strong><br>
            <strong>Monitoring:</strong> Active<br>
            <strong>Total Violations:</strong> ${violationLog.length}<br>
            <strong>Total Network Errors:</strong> ${networkLog.filter(log => !log.success).length}<br>
            <strong>Last Update:</strong> ${new Date().toLocaleTimeString()}
          </div>
          ${recentViolations.length > 0 ? `
            <div class="test-result warning">
              <strong>Recent CSP Violations:</strong><br>
              ${recentViolations.map(v => `• ${v.directive}: ${v.blockedURI}`).join('<br>')}
            </div>
          ` : ''}
          ${recentNetworkErrors.length > 0 ? `
            <div class="test-result error">
              <strong>Recent Network Errors:</strong><br>
              ${recentNetworkErrors.map(e => `• ${e.method} ${e.url}: ${e.error || e.status}`).join('<br>')}
            </div>
          ` : ''}
        `;
      }, 2000);
    }

    // Test functions
    async function runAllTests() {
      console.log('🚀 [Diagnostics] Running all tests...');
      clearResults();

      await runCSPTests();
      await runCORSTests();
      await runVapiTests();

      console.log('✅ [Diagnostics] All tests completed');
    }

    async function runCSPTests() {
      console.log('🛡️ [CSP] Running CSP tests...');
      const results = document.getElementById('csp-tests-results');

      // Test 1: Basic eval test
      await testEval(results);

      // Test 2: Dynamic script creation
      await testDynamicScript(results);

      // Test 3: setTimeout with string
      await testSetTimeoutString(results);

      // Test 4: Function constructor
      await testFunctionConstructor(results);

      // Test 5: Inline event handlers
      await testInlineEvents(results);
    }

    async function testEval(results) {
      try {
        eval('1 + 1');
        addTestResult(results, 'success', '✅ Eval Test', 'eval() is allowed (CSP permits unsafe-eval)');
        testResults.passed++;
      } catch (error) {
        addTestResult(results, 'error', '❌ Eval Test', `eval() blocked: ${error.message}`);
        testResults.failed++;
      }
      testResults.total++;
      updateMetrics();
    }

    async function testDynamicScript(results) {
      try {
        const script = document.createElement('script');
        script.textContent = 'window.testDynamicScript = true;';
        document.head.appendChild(script);
        document.head.removeChild(script);

        if (window.testDynamicScript) {
          addTestResult(results, 'success', '✅ Dynamic Script Test', 'Dynamic script execution allowed');
          testResults.passed++;
        } else {
          addTestResult(results, 'warning', '⚠️ Dynamic Script Test', 'Dynamic script created but may not have executed');
          testResults.failed++;
        }
      } catch (error) {
        addTestResult(results, 'error', '❌ Dynamic Script Test', `Dynamic script blocked: ${error.message}`);
        testResults.failed++;
      }
      testResults.total++;
      updateMetrics();
    }

    async function testSetTimeoutString(results) {
      try {
        const timeoutId = setTimeout('window.testSetTimeout = true;', 100);

        setTimeout(() => {
          if (window.testSetTimeout) {
            addTestResult(results, 'warning', '⚠️ setTimeout String Test', 'setTimeout with string is allowed (potential security risk)');
          } else {
            addTestResult(results, 'success', '✅ setTimeout String Test', 'setTimeout with string blocked by CSP');
          }
          clearTimeout(timeoutId);
        }, 200);

        testResults.passed++;
      } catch (error) {
        addTestResult(results, 'success', '✅ setTimeout String Test', `setTimeout with string blocked: ${error.message}`);
        testResults.passed++;
      }
      testResults.total++;
      updateMetrics();
    }

    async function testFunctionConstructor(results) {
      try {
        const func = new Function('return 1 + 1;');
        const result = func();
        addTestResult(results, 'warning', '⚠️ Function Constructor Test', `Function constructor allowed, result: ${result}`);
        testResults.failed++;
      } catch (error) {
        addTestResult(results, 'success', '✅ Function Constructor Test', `Function constructor blocked: ${error.message}`);
        testResults.passed++;
      }
      testResults.total++;
      updateMetrics();
    }

    async function testInlineEvents(results) {
      try {
        const button = document.createElement('button');
        button.setAttribute('onclick', 'window.testInlineEvent = true;');
        button.textContent = 'Test';
        document.body.appendChild(button);
        button.click();
        document.body.removeChild(button);

        if (window.testInlineEvent) {
          addTestResult(results, 'warning', '⚠️ Inline Event Test', 'Inline event handlers are allowed');
          testResults.failed++;
        } else {
          addTestResult(results, 'success', '✅ Inline Event Test', 'Inline event handlers blocked by CSP');
          testResults.passed++;
        }
      } catch (error) {
        addTestResult(results, 'success', '✅ Inline Event Test', `Inline events blocked: ${error.message}`);
        testResults.passed++;
      }
      testResults.total++;
      updateMetrics();
    }

    async function runCORSTests() {
      console.log('🌐 [CORS] Running CORS tests...');
      const results = document.getElementById('cors-tests-results');

      // Test 1: Supabase API
      await testSupabaseAPI(results);

      // Test 2: Vapi API
      await testVapiAPI(results);

      // Test 3: Preflight OPTIONS
      await testPreflightOptions(results);

      // Test 4: Local API endpoints
      await testLocalAPI(results);
    }

    async function testSupabaseAPI(results) {
      try {
        const supabaseUrl = window.VITE_SUPABASE_URL || 'https://utopqxsvudgrtiwenlzl.supabase.co';
        const response = await fetch(`${supabaseUrl}/rest/v1/`, {
          method: 'GET',
          headers: {
            'apikey': window.VITE_SUPABASE_KEY || '',
            'Content-Type': 'application/json'
          }
        });

        if (response.ok) {
          addTestResult(results, 'success', '✅ Supabase API Test', `Supabase API accessible: ${response.status}`);
          testResults.passed++;
        } else {
          addTestResult(results, 'error', '❌ Supabase API Test', `Supabase API failed: ${response.status} ${response.statusText}`);
          testResults.failed++;
        }
      } catch (error) {
        addTestResult(results, 'error', '❌ Supabase API Test', `Supabase API error: ${error.message}`);
        testResults.failed++;
      }
      testResults.total++;
      updateMetrics();
    }

    async function testVapiAPI(results) {
      try {
        const response = await fetch('https://api.vapi.ai/assistant', {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${window.VITE_VAPI_SECRET_KEY || ''}`,
            'Content-Type': 'application/json'
          }
        });

        if (response.ok) {
          addTestResult(results, 'success', '✅ Vapi API Test', `Vapi API accessible: ${response.status}`);
          testResults.passed++;
        } else {
          addTestResult(results, 'error', '❌ Vapi API Test', `Vapi API failed: ${response.status} ${response.statusText}`);
          testResults.failed++;
        }
      } catch (error) {
        addTestResult(results, 'error', '❌ Vapi API Test', `Vapi API error: ${error.message}`);
        testResults.failed++;
      }
      testResults.total++;
      updateMetrics();
    }

    async function testPreflightOptions(results) {
      try {
        const response = await fetch('/api/health', {
          method: 'OPTIONS',
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Request-Method': 'POST',
            'Access-Control-Request-Headers': 'Content-Type, Authorization'
          }
        });

        if (response.ok) {
          addTestResult(results, 'success', '✅ Preflight OPTIONS Test', `OPTIONS request successful: ${response.status}`);
          testResults.passed++;
        } else {
          addTestResult(results, 'error', '❌ Preflight OPTIONS Test', `OPTIONS request failed: ${response.status} ${response.statusText}`);
          testResults.failed++;
        }
      } catch (error) {
        addTestResult(results, 'error', '❌ Preflight OPTIONS Test', `OPTIONS request error: ${error.message}`);
        testResults.failed++;
      }
      testResults.total++;
      updateMetrics();
    }

    async function testLocalAPI(results) {
      try {
        const response = await fetch('/api/health', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json'
          }
        });

        if (response.ok) {
          const data = await response.json();
          addTestResult(results, 'success', '✅ Local API Test', `Local API accessible: ${JSON.stringify(data)}`);
          testResults.passed++;
        } else {
          addTestResult(results, 'error', '❌ Local API Test', `Local API failed: ${response.status} ${response.statusText}`);
          testResults.failed++;
        }
      } catch (error) {
        addTestResult(results, 'error', '❌ Local API Test', `Local API error: ${error.message}`);
        testResults.failed++;
      }
      testResults.total++;
      updateMetrics();
    }

    async function runVapiTests() {
      console.log('📞 [Vapi] Running Vapi integration tests...');
      const results = document.getElementById('vapi-tests-results');

      // Test 1: Vapi SDK loading
      await testVapiSDKLoading(results);

      // Test 2: Vapi configuration
      await testVapiConfiguration(results);

      // Test 3: Vapi MCP server
      await testVapiMCPServer(results);
    }

    async function testVapiSDKLoading(results) {
      try {
        // Try to load Vapi SDK
        if (typeof window.Vapi !== 'undefined') {
          addTestResult(results, 'success', '✅ Vapi SDK Test', 'Vapi SDK already loaded');
          testResults.passed++;
        } else {
          // Try to load from CDN
          const script = document.createElement('script');
          script.src = 'https://cdn.vapi.ai/web-sdk@2.3.1/dist/web-sdk.js';
          script.async = true;

          const loadPromise = new Promise((resolve, reject) => {
            script.onload = resolve;
            script.onerror = reject;
            setTimeout(reject, 10000); // 10 second timeout
          });

          document.head.appendChild(script);
          await loadPromise;

          if (typeof window.Vapi !== 'undefined') {
            addTestResult(results, 'success', '✅ Vapi SDK Test', 'Vapi SDK loaded from CDN');
            testResults.passed++;
          } else {
            addTestResult(results, 'error', '❌ Vapi SDK Test', 'Vapi SDK failed to load');
            testResults.failed++;
          }
        }
      } catch (error) {
        addTestResult(results, 'error', '❌ Vapi SDK Test', `Vapi SDK loading error: ${error.message}`);
        testResults.failed++;
      }
      testResults.total++;
      updateMetrics();
    }

    async function testVapiConfiguration(results) {
      try {
        const publicKey = window.VITE_VAPI_PUBLIC_KEY;
        const secretKey = window.VITE_VAPI_SECRET_KEY;

        if (publicKey && secretKey) {
          addTestResult(results, 'success', '✅ Vapi Config Test', 'Vapi keys are configured');
          testResults.passed++;
        } else {
          addTestResult(results, 'error', '❌ Vapi Config Test', 'Vapi keys missing');
          testResults.failed++;
        }
      } catch (error) {
        addTestResult(results, 'error', '❌ Vapi Config Test', `Vapi config error: ${error.message}`);
        testResults.failed++;
      }
      testResults.total++;
      updateMetrics();
    }

    async function testVapiMCPServer(results) {
      try {
        const response = await fetch('/api/vapi-mcp-server', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            jsonrpc: '2.0',
            id: 1,
            method: 'ping'
          })
        });

        if (response.ok) {
          const data = await response.json();
          addTestResult(results, 'success', '✅ Vapi MCP Test', `MCP server accessible: ${JSON.stringify(data)}`);
          testResults.passed++;
        } else {
          addTestResult(results, 'error', '❌ Vapi MCP Test', `MCP server failed: ${response.status} ${response.statusText}`);
          testResults.failed++;
        }
      } catch (error) {
        addTestResult(results, 'error', '❌ Vapi MCP Test', `MCP server error: ${error.message}`);
        testResults.failed++;
      }
      testResults.total++;
      updateMetrics();
    }

    // Helper function to add test results
    function addTestResult(container, type, title, message) {
      const resultDiv = document.createElement('div');
      resultDiv.className = `test-result ${type}`;
      resultDiv.innerHTML = `<strong>${title}</strong><br>${message}`;
      container.appendChild(resultDiv);
    }

    // Clear all results
    function clearResults() {
      testResults = { passed: 0, failed: 0, cspViolations: 0, corsErrors: 0, total: 0 };
      updateMetrics();

      document.getElementById('csp-tests-results').innerHTML = '';
      document.getElementById('cors-tests-results').innerHTML = '';
      document.getElementById('vapi-tests-results').innerHTML = '';
      document.getElementById('network-analysis-results').innerHTML = '';
    }
  </script>
</body>
</html>

#!/usr/bin/env node
/**
 * Vapi MCP Server Example Script
 * 
 * This script demonstrates how to use the Vapi MCP Server from a Node.js environment.
 * It connects to the Vapi MCP Server, lists available tools, assistants, and phone numbers,
 * and optionally creates a call.
 * 
 * Usage:
 *   node vapi-mcp-example.js
 * 
 * Environment variables:
 *   VAPI_TOKEN - Your Vapi API key
 */

import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { SSEClientTransport } from '@modelcontextprotocol/sdk/client/sse.js';
import dotenv from 'dotenv';

// Load environment variables from .env file
dotenv.config();

// Ensure API key is available
if (!process.env.VAPI_TOKEN) {
  console.error('Error: VAPI_TOKEN environment variable is required');
  console.error('Create a .env file with VAPI_TOKEN=your_api_key or set it in your environment');
  process.exit(1);
}

async function main() {
  try {
    // Initialize MCP client
    const mcpClient = new Client({
      name: 'legalscout-cli',
      version: '1.0.0',
    });
    
    // Create SSE transport for connection to remote Vapi MCP server
    const transport = new SSEClientTransport({
      url: 'https://mcp.vapi.ai/sse',
      headers: {
        'Authorization': `Bearer ${process.env.VAPI_TOKEN}`
      }
    });
    
    console.log('Connecting to Vapi MCP server via SSE...');
    await mcpClient.connect(transport);
    console.log('Connected successfully');
    
    try {
      // List available tools
      const toolsResult = await mcpClient.listTools();
      console.log('\nAvailable tools:');
      toolsResult.tools.forEach((tool) => {
        console.log(`- ${tool.name}: ${tool.description}`);
      });
      
      // List assistants
      console.log('\nListing assistants...');
      const assistantsResponse = await mcpClient.callTool({
        name: 'list_assistants',
        arguments: {},
      });
      
      const assistants = assistantsResponse.content;
      if (!(Array.isArray(assistants) && assistants.length > 0)) {
        console.log('No assistants found. Please create an assistant in the Vapi dashboard first.');
        return;
      }
      
      console.log('Your assistants:');
      assistants.forEach((assistant) => {
        console.log(`- ${assistant.name} (${assistant.id})`);
      });
      
      // List phone numbers
      console.log('\nListing phone numbers...');
      const phoneNumbersResponse = await mcpClient.callTool({
        name: 'list_phone_numbers',
        arguments: {},
      });
      
      const phoneNumbers = phoneNumbersResponse.content;
      if (!(Array.isArray(phoneNumbers) && phoneNumbers.length > 0)) {
        console.log('No phone numbers found. Please add a phone number in the Vapi dashboard first.');
        return;
      }
      
      console.log('Your phone numbers:');
      phoneNumbers.forEach((phoneNumber) => {
        console.log(`- ${phoneNumber.phoneNumber} (${phoneNumber.id})`);
      });
      
      // Uncomment the following code to create a test call
      /*
      // Create a call using the first assistant and first phone number
      const phoneNumberId = phoneNumbers[0].id;
      const assistantId = assistants[0].id;
      
      console.log(`\nCreating a call using assistant (${assistantId}) and phone number (${phoneNumberId})...`);
      const createCallResponse = await mcpClient.callTool({
        name: 'create_call',
        arguments: {
          assistantId: assistantId,
          phoneNumberId: phoneNumberId,
          customer: {
            phoneNumber: "+1234567890"  // Replace with actual customer phone number
          }
          // Optional: schedule a call for the future
          // scheduledAt: "2025-04-15T15:30:00Z"
        },
      });
      
      console.log('Call created:', JSON.stringify(createCallResponse.content, null, 2));
      */
      
    } finally {
      console.log('\nDisconnecting from server...');
      await mcpClient.close();
      console.log('Disconnected');
    }
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

main();

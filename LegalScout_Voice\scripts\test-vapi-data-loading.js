/**
 * Test Vapi Data Loading
 * 
 * This script tests whether the VapiDirectApiService can properly load
 * complete assistant data including firstMessage and system instructions.
 */

import { vapiDirectApiService } from '../src/services/VapiDirectApiService.js';

const ASSISTANT_ID = 'f9b97d13-f9c4-40af-a660-62ba5925ff2a';

async function testVapiDataLoading() {
  try {
    console.log('🧪 Testing Vapi Data Loading...\n');
    
    // Test 1: Direct API Service
    console.log('📡 Test 1: Direct API Service');
    const directData = await vapiDirectApiService.getAssistant(ASSISTANT_ID);
    
    console.log('✅ Direct API Results:');
    console.log('- Name:', directData.name);
    console.log('- First Message:', directData.firstMessage || 'MISSING');
    console.log('- System Instructions:', directData.model?.messages?.[0]?.content ? 'PRESENT' : 'MISSING');
    console.log('- Voice:', `${directData.voice?.provider}/${directData.voice?.voiceId}`);
    console.log('- Model:', `${directData.model?.provider}/${directData.model?.model}`);
    
    // Test 2: Complete Assistant Data (with fallback)
    console.log('\n🔄 Test 2: Complete Assistant Data (with fallback)');
    const completeData = await vapiDirectApiService.getCompleteAssistantData(ASSISTANT_ID);
    
    console.log('✅ Complete Data Results:');
    console.log('- Name:', completeData.name);
    console.log('- First Message:', completeData.firstMessage || 'MISSING');
    console.log('- System Instructions:', completeData.model?.messages?.[0]?.content ? 'PRESENT' : 'MISSING');
    console.log('- Voice:', `${completeData.voice?.provider}/${completeData.voice?.voiceId}`);
    console.log('- Model:', `${completeData.model?.provider}/${completeData.model?.model}`);
    
    // Test 3: Check if data is complete for UI loading
    console.log('\n🎯 Test 3: UI Data Completeness Check');
    const isComplete = !!(
      completeData.firstMessage &&
      completeData.model?.messages?.[0]?.content &&
      completeData.voice?.voiceId &&
      completeData.model?.model
    );
    
    console.log('✅ UI Data Completeness:', isComplete ? 'COMPLETE' : 'INCOMPLETE');
    
    if (isComplete) {
      console.log('\n🎉 SUCCESS: All required data is present for UI loading!');
      console.log('The orphaned assistant problem should now be resolved.');
    } else {
      console.log('\n❌ ISSUE: Some required data is still missing.');
      
      const missing = [];
      if (!completeData.firstMessage) missing.push('firstMessage');
      if (!completeData.model?.messages?.[0]?.content) missing.push('system instructions');
      if (!completeData.voice?.voiceId) missing.push('voice');
      if (!completeData.model?.model) missing.push('model');
      
      console.log('Missing fields:', missing.join(', '));
    }
    
    // Test 4: Simulate UI form data population
    console.log('\n📝 Test 4: Simulated UI Form Data Population');
    const formData = {
      firstMessage: completeData.firstMessage || '',
      vapiInstructions: completeData.model?.messages?.[0]?.content || '',
      voiceId: completeData.voice?.voiceId || '',
      voiceProvider: completeData.voice?.provider || '',
      aiModel: completeData.model?.model || ''
    };
    
    console.log('Form Data that would be populated:');
    Object.entries(formData).forEach(([key, value]) => {
      console.log(`- ${key}:`, value ? `"${value.substring(0, 50)}${value.length > 50 ? '...' : ''}"` : 'EMPTY');
    });
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    process.exit(1);
  }
}

// Run the test
testVapiDataLoading();

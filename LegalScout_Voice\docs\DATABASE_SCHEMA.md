# LegalScout Database Schema

This document describes the complete database schema for LegalScout Voice, including all tables, relationships, and Row-Level Security (RLS) policies.

## Overview

LegalScout uses Supabase (PostgreSQL) as its primary database with the following key features:
- **Row-Level Security (RLS)** for data isolation
- **Real-time subscriptions** for live updates
- **File storage** for logos and voice samples
- **UUID primary keys** for security and scalability
- **Audit trails** with created_at/updated_at timestamps

## Core Tables

### 1. attorneys
The main table storing attorney/law firm information and configurations.

```sql
CREATE TABLE attorneys (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  
  -- Basic Information
  email TEXT UNIQUE,
  firm_name TEXT,
  title_text TEXT, -- Display name for the assistant
  practice_description TEXT,
  office_address TEXT,
  scheduling_link TEXT,
  practice_areas TEXT[], -- Array of practice areas
  
  -- Vapi Integration
  vapi_assistant_id TEXT, -- Primary Vapi assistant ID
  current_assistant_id TEXT, -- Currently selected assistant
  vapi_instructions TEXT,
  vapi_context TEXT,
  voice_provider TEXT DEFAULT '11labs',
  voice_id TEXT DEFAULT 'sarah',
  ai_model TEXT DEFAULT 'gpt-4o',
  
  -- UI Customization
  logo_url TEXT,
  mascot_url TEXT,
  primary_color TEXT DEFAULT '#2563eb',
  secondary_color TEXT DEFAULT '#1e40af',
  button_color TEXT DEFAULT '#3b82f6',
  background_color TEXT DEFAULT '#ffffff',
  background_opacity DECIMAL(3,2) DEFAULT 1.0,
  button_opacity DECIMAL(3,2) DEFAULT 1.0,
  practice_area_background_opacity DECIMAL(3,2) DEFAULT 0.1,
  text_background_color TEXT DEFAULT '#ffffff',
  
  -- Content
  welcome_message TEXT,
  information_gathering TEXT,
  
  -- Subdomain (legacy - migrated to assistant_subdomains)
  subdomain TEXT UNIQUE,
  
  -- Custom Fields
  custom_fields JSONB DEFAULT '{}',
  
  -- Metadata
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**Indexes:**
- `attorneys_user_id_idx` on `user_id`
- `attorneys_email_idx` on `email`
- `attorneys_subdomain_idx` on `subdomain`
- `attorneys_vapi_assistant_id_idx` on `vapi_assistant_id`
- `attorneys_current_assistant_id_idx` on `current_assistant_id`

**RLS Policy:**
```sql
-- Users can only access their own attorney records
CREATE POLICY "Users can access their own attorney records" ON attorneys
  FOR ALL USING (user_id = auth.uid());
```

### 2. assistant_ui_configs
Per-assistant UI configuration settings, enabling multiple assistants per attorney.

```sql
CREATE TABLE assistant_ui_configs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  attorney_id UUID NOT NULL REFERENCES attorneys(id) ON DELETE CASCADE,
  assistant_id TEXT NOT NULL,
  
  -- Basic Info
  firm_name TEXT,
  assistant_name TEXT, -- The agent name from Agent menu
  logo_url TEXT,
  mascot_url TEXT,
  
  -- Colors and Styling
  primary_color TEXT DEFAULT '#2563eb',
  secondary_color TEXT DEFAULT '#1e40af',
  button_color TEXT DEFAULT '#3b82f6',
  background_color TEXT DEFAULT '#ffffff',
  background_opacity DECIMAL(3,2) DEFAULT 1.0,
  button_opacity DECIMAL(3,2) DEFAULT 1.0,
  practice_area_background_opacity DECIMAL(3,2) DEFAULT 0.1,
  text_background_color TEXT DEFAULT '#ffffff',
  
  -- Content
  practice_description TEXT,
  welcome_message TEXT,
  information_gathering TEXT,
  office_address TEXT,
  scheduling_link TEXT,
  practice_areas TEXT[],
  
  -- Vapi Configuration
  vapi_instructions TEXT,
  vapi_context TEXT,
  voice_provider TEXT DEFAULT '11labs',
  voice_id TEXT DEFAULT 'sarah',
  ai_model TEXT DEFAULT 'gpt-4o',
  
  -- Custom Fields
  custom_fields JSONB DEFAULT '{}',
  
  -- Metadata
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Constraints
  UNIQUE(attorney_id, assistant_id)
);
```

**Indexes:**
- `assistant_ui_configs_attorney_id_idx` on `attorney_id`
- `assistant_ui_configs_assistant_id_idx` on `assistant_id`
- `assistant_ui_configs_attorney_assistant_idx` on `(attorney_id, assistant_id)`

**RLS Policy:**
```sql
-- Users can only access configs for their own attorney records
CREATE POLICY "Users can access their own assistant configs" ON assistant_ui_configs
  FOR ALL USING (
    attorney_id IN (
      SELECT id FROM attorneys WHERE user_id = auth.uid()
    )
  );
```

### 3. assistant_subdomains
Maps subdomains to specific assistants, enabling assistant-level routing.

```sql
CREATE TABLE assistant_subdomains (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  assistant_id TEXT NOT NULL UNIQUE,
  subdomain TEXT UNIQUE NOT NULL,
  attorney_id UUID NOT NULL REFERENCES attorneys(id) ON DELETE CASCADE,
  
  -- Configuration
  is_primary BOOLEAN DEFAULT FALSE, -- One primary assistant per attorney
  is_active BOOLEAN DEFAULT TRUE,
  
  -- Metadata
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Constraints
  CONSTRAINT unique_primary_per_attorney UNIQUE (attorney_id, is_primary) 
    DEFERRABLE INITIALLY DEFERRED
);
```

**Indexes:**
- `idx_assistant_subdomains_subdomain` on `subdomain`
- `idx_assistant_subdomains_assistant_id` on `assistant_id`
- `idx_assistant_subdomains_attorney_id` on `attorney_id`

**Triggers:**
- `ensure_single_primary_assistant_trigger` - Ensures only one primary assistant per attorney

### 4. call_records
Stores call logs and consultation data with RLS for data isolation.

```sql
CREATE TABLE call_records (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  attorney_id UUID REFERENCES attorneys(id) ON DELETE CASCADE,
  assistant_id TEXT,
  
  -- Call Information
  vapi_call_id TEXT UNIQUE,
  call_status TEXT,
  call_duration INTEGER, -- Duration in seconds
  
  -- Client Information
  client_phone TEXT,
  client_name TEXT,
  client_email TEXT,
  
  -- Call Content
  transcript TEXT,
  summary TEXT,
  structured_data JSONB DEFAULT '{}',
  
  -- Analysis
  practice_areas_identified TEXT[],
  urgency_level TEXT,
  follow_up_required BOOLEAN DEFAULT FALSE,
  
  -- Metadata
  call_started_at TIMESTAMP WITH TIME ZONE,
  call_ended_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**Indexes:**
- `call_records_attorney_id_idx` on `attorney_id`
- `call_records_assistant_id_idx` on `assistant_id`
- `call_records_vapi_call_id_idx` on `vapi_call_id`
- `call_records_call_started_at_idx` on `call_started_at`

**RLS Policy:**
```sql
-- Users can only access call records for their own attorneys
CREATE POLICY "Users can access their own call records" ON call_records
  FOR ALL USING (
    attorney_id IN (
      SELECT id FROM attorneys WHERE user_id = auth.uid()
    )
  );
```

### 5. consultations
Extended consultation data with custom fields and CRM integration.

```sql
CREATE TABLE consultations (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  attorney_id UUID REFERENCES attorneys(id) ON DELETE CASCADE,
  call_record_id UUID REFERENCES call_records(id) ON DELETE CASCADE,
  
  -- Client Details
  client_name TEXT,
  client_email TEXT,
  client_phone TEXT,
  
  -- Legal Information
  legal_issue_category TEXT,
  legal_issue_description TEXT,
  urgency_level TEXT,
  estimated_case_value DECIMAL(12,2),
  
  -- Custom Fields (attorney-configurable)
  custom_data JSONB DEFAULT '{}',
  
  -- Status Tracking
  consultation_status TEXT DEFAULT 'new',
  follow_up_date DATE,
  notes TEXT,
  
  -- Metadata
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 6. custom_columns
Attorney-configurable data collection fields.

```sql
CREATE TABLE custom_columns (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  attorney_id UUID REFERENCES attorneys(id) ON DELETE CASCADE,
  
  -- Field Configuration
  field_name TEXT NOT NULL,
  field_type TEXT NOT NULL, -- text, number, date, select, etc.
  field_label TEXT NOT NULL,
  field_options JSONB, -- For select fields
  is_required BOOLEAN DEFAULT FALSE,
  display_order INTEGER DEFAULT 0,
  
  -- Metadata
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Constraints
  UNIQUE(attorney_id, field_name)
);
```

## Views

### v_subdomain_assistant_lookup
Convenient view for subdomain-to-assistant routing.

```sql
CREATE VIEW v_subdomain_assistant_lookup AS
SELECT 
  asd.subdomain,
  asd.assistant_id,
  asd.is_primary,
  asd.is_active,
  a.id as attorney_id,
  a.firm_name,
  a.email,
  asd.created_at,
  asd.updated_at
FROM assistant_subdomains asd
JOIN attorneys a ON asd.attorney_id = a.id
WHERE asd.is_active = TRUE;
```

## Storage Buckets

### attorney-logos
Stores attorney logo files with public access.

**RLS Policy:**
```sql
-- Anyone can view logos
CREATE POLICY "Public logo access" ON storage.objects
  FOR SELECT USING (bucket_id = 'attorney-logos');

-- Only authenticated users can upload logos
CREATE POLICY "Authenticated logo upload" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'attorney-logos' AND 
    auth.role() = 'authenticated'
  );
```

### voice-samples
Stores custom voice samples for Vapi integration.

**RLS Policy:**
```sql
-- Users can only access their own voice samples
CREATE POLICY "Users can access own voice samples" ON storage.objects
  FOR ALL USING (
    bucket_id = 'voice-samples' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );
```

## Functions and Triggers

### 1. Updated At Triggers
All tables have automatic `updated_at` timestamp updates:

```sql
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Applied to all tables with updated_at columns
CREATE TRIGGER update_[table_name]_updated_at
  BEFORE UPDATE ON [table_name]
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();
```

### 2. Primary Assistant Management
Ensures only one primary assistant per attorney:

```sql
CREATE OR REPLACE FUNCTION ensure_single_primary_assistant()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.is_primary = TRUE THEN
    UPDATE assistant_subdomains 
    SET is_primary = FALSE, updated_at = NOW()
    WHERE attorney_id = NEW.attorney_id 
      AND assistant_id != NEW.assistant_id 
      AND is_primary = TRUE;
  END IF;
  
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;
```

## Security Considerations

### Row-Level Security (RLS)
All tables implement RLS to ensure data isolation:
- Users can only access data associated with their `auth.uid()`
- Attorney-owned data is protected through `user_id` relationships
- Storage buckets have appropriate access controls

### Data Encryption
- All sensitive data encrypted at rest
- API keys and secrets stored in environment variables
- JWT tokens for authentication and authorization

### Audit Trail
- All tables include `created_at` and `updated_at` timestamps
- User actions tracked through Supabase auth logs
- Call records maintain complete audit trail

This schema provides a robust foundation for LegalScout's multi-tenant voice AI legal consultation platform with proper security, scalability, and data isolation.

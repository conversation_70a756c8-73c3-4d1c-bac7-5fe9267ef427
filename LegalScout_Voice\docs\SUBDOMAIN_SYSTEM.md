# LegalScout Subdomain System - Complete Implementation

## Overview

The subdomain system has evolved from attorney-level to assistant-level routing, allowing attorneys to have multiple assistants, each with their own subdomain and branding. This provides maximum flexibility for attorneys with different practice areas or client types.

## Current Architecture (Assistant-Level Routing)

### Key Components

1. **Assistant Subdomains Table** - Maps subdomains to specific Vapi assistants
2. **Assistant UI Configs** - Per-assistant branding and configuration
3. **Vercel Rewrites** - Route subdomain requests to the main application
4. **Dynamic Configuration Loading** - Load assistant-specific settings
5. **Branded UI Rendering** - Custom styling per assistant

### Database Schema

#### assistant_subdomains Table
```sql
CREATE TABLE assistant_subdomains (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  assistant_id TEXT NOT NULL UNIQUE,
  subdomain TEXT UNIQUE NOT NULL,
  attorney_id UUID NOT NULL REFERENCES attorneys(id) ON DELETE CASCADE,
  is_primary BOOLEAN DEFAULT FALSE,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### assistant_ui_configs Table
```sql
CREATE TABLE assistant_ui_configs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  attorney_id UUID NOT NULL REFERENCES attorneys(id) ON DELETE CASCADE,
  assistant_id TEXT NOT NULL,
  firm_name TEXT,
  assistant_name TEXT,
  logo_url TEXT,
  primary_color TEXT DEFAULT '#2563eb',
  -- ... extensive UI configuration fields
  UNIQUE(attorney_id, assistant_id)
);
```

### Routing Flow

1. Client visits `assistant1.legalscout.net`
2. Vercel rewrites request to main app with subdomain parameter
3. App queries `v_subdomain_assistant_lookup` view to get assistant configuration
4. Assistant-specific UI config loaded from `assistant_ui_configs`
5. Vapi assistant initialized with the specific assistant ID
6. UI renders with assistant-specific branding

## Implementation Details

### Vercel Configuration (`vercel.json`)

```json
{
  "rewrites": [
    {
      "source": "/(.*)",
      "destination": "/?subdomain=$host",
      "has": [
        {
          "type": "host",
          "value": "(?<subdomain>.*)\\.legalscout\\.net"
        }
      ]
    }
  ],
  "functions": {
    "api/index.js": {
      "maxDuration": 30
    }
  }
}
```

### Subdomain Detection (`src/utils/subdomainUtils.js`)

```javascript
export function getSubdomain() {
  if (typeof window === 'undefined') return null;

  const hostname = window.location.hostname;
  const parts = hostname.split('.');

  // Production: subdomain.legalscout.net
  if (parts.length >= 3 && parts[1] === 'legalscout' && parts[2] === 'net') {
    return parts[0];
  }

  // Development: subdomain.localhost
  if (parts.length >= 2 && parts[1] === 'localhost') {
    return parts[0];
  }

  return null;
}

export function isSubdomain() {
  const subdomain = getSubdomain();
  return subdomain !== null && subdomain !== 'www' && subdomain !== 'dashboard';
}
```

### Assistant Configuration Loading

```javascript
// Load assistant configuration by subdomain
async function loadAssistantBySubdomain(subdomain) {
  const { data, error } = await supabase
    .from('v_subdomain_assistant_lookup')
    .select('*')
    .eq('subdomain', subdomain)
    .eq('is_active', true)
    .single();

  if (error) {
    console.error('Error loading assistant by subdomain:', error);
    return null;
  }

  return data;
}

// Load UI configuration for assistant
async function loadAssistantUIConfig(assistantId, attorneyId) {
  const { data, error } = await supabase
    .from('assistant_ui_configs')
    .select('*')
    .eq('assistant_id', assistantId)
    .eq('attorney_id', attorneyId)
    .single();

  if (error) {
    console.error('Error loading assistant UI config:', error);
    return null;
  }

  return data;
}
```

### Dynamic UI Configuration

```javascript
// Apply assistant-specific styling
function applyAssistantStyling(config) {
  const root = document.documentElement;

  // Apply color scheme
  root.style.setProperty('--primary-color', config.primary_color);
  root.style.setProperty('--secondary-color', config.secondary_color);
  root.style.setProperty('--button-color', config.button_color);
  root.style.setProperty('--background-color', config.background_color);

  // Apply opacity settings
  root.style.setProperty('--background-opacity', config.background_opacity);
  root.style.setProperty('--button-opacity', config.button_opacity);

  // Update page title and meta
  document.title = `${config.assistant_name} - ${config.firm_name}`;

  // Update favicon if custom logo exists
  if (config.logo_url) {
    updateFavicon(config.logo_url);
  }
}
```

## Assistant Management System

### Primary Assistant Logic

Each attorney has one primary assistant that serves as the default:

```sql
-- Trigger to ensure only one primary assistant per attorney
CREATE OR REPLACE FUNCTION ensure_single_primary_assistant()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.is_primary = TRUE THEN
    UPDATE assistant_subdomains
    SET is_primary = FALSE, updated_at = NOW()
    WHERE attorney_id = NEW.attorney_id
      AND assistant_id != NEW.assistant_id
      AND is_primary = TRUE;
  END IF;

  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;
```

### Assistant Creation Workflow

1. Attorney creates new assistant in dashboard
2. Vapi assistant created via MCP integration
3. Assistant ID stored in `assistant_ui_configs`
4. Subdomain assigned in `assistant_subdomains`
5. UI configuration copied from attorney defaults
6. Webhook URL configured for assistant

### Subdomain Assignment

```javascript
// Generate unique subdomain for assistant
function generateAssistantSubdomain(firmName, assistantName) {
  const baseName = `${firmName}-${assistantName}`
    .toLowerCase()
    .replace(/[^a-z0-9]/g, '-')
    .replace(/-+/g, '-')
    .replace(/^-|-$/g, '');

  return baseName.substring(0, 30); // Limit length
}

// Check subdomain availability
async function isSubdomainAvailable(subdomain) {
  const { data } = await supabase
    .from('assistant_subdomains')
    .select('id')
    .eq('subdomain', subdomain)
    .single();

  return !data;
}
```

## UI Component Adaptation

### Header Navigation

```javascript
// Subdomain-aware header component
function SubdomainHeader({ assistantConfig }) {
  if (isSubdomain()) {
    return (
      <header className="subdomain-header">
        <img src={assistantConfig.logo_url} alt={assistantConfig.firm_name} />
        <nav>
          <a href="#about">About</a>
          {/* Limited navigation for subdomain */}
        </nav>
      </header>
    );
  }

  return <MainSiteHeader />;
}
```

### Voice Assistant Integration

```javascript
// Initialize Vapi with assistant-specific configuration
function initializeVapiForSubdomain(assistantConfig) {
  const vapi = new Vapi(VITE_VAPI_PUBLIC_KEY);

  // Use specific assistant ID from subdomain config
  const assistantOverrides = {
    variableValues: {
      attorneyName: assistantConfig.firm_name,
      assistantName: assistantConfig.assistant_name,
      practiceAreas: assistantConfig.practice_areas,
      officeAddress: assistantConfig.office_address
    }
  };

  return {
    startCall: () => vapi.start(assistantConfig.assistant_id, assistantOverrides),
    stopCall: () => vapi.stop()
  };
}
```

## Testing and Development

### Local Development Setup

1. **Hosts File Configuration**:
   ```
   127.0.0.1 assistant1test.localhost
   127.0.0.1 damon.localhost
   127.0.0.1 joespizza.localhost
   ```

2. **Test Data Setup**:
   ```sql
   -- Create test assistant subdomain
   INSERT INTO assistant_subdomains (
     assistant_id, subdomain, attorney_id, is_primary, is_active
   ) VALUES (
     'test-assistant-id', 'assistant1test', 'attorney-uuid', TRUE, TRUE
   );
   ```

### Subdomain Testing Utility

```javascript
// Test subdomain functionality
function testSubdomainRouting() {
  const testSubdomains = ['assistant1test', 'damon', 'joespizza'];

  testSubdomains.forEach(async (subdomain) => {
    const config = await loadAssistantBySubdomain(subdomain);
    console.log(`${subdomain}:`, config ? 'Found' : 'Not found');
  });
}
```

## Production Deployment

### DNS Configuration

1. **Wildcard DNS**: `*.legalscout.net` → Vercel
2. **SSL Certificates**: Automatic via Vercel
3. **Domain Verification**: Completed in Vercel dashboard

### Environment Variables

```bash
# Production environment
VITE_PRODUCTION_DOMAIN=legalscout.net
VITE_SUPABASE_URL=https://utopqxsvudgrtiwenlzl.supabase.co
VITE_SUPABASE_ANON_KEY=your_production_key
VAPI_TOKEN=your_production_vapi_key
```

## Security and Performance

### Security Measures

1. **RLS Policies**: Ensure data isolation between attorneys
2. **Subdomain Validation**: Prevent malicious subdomain injection
3. **API Key Protection**: Server-side key management
4. **Rate Limiting**: Prevent abuse of subdomain lookup

### Performance Optimizations

1. **Configuration Caching**: Cache assistant configs in memory
2. **CDN Integration**: Optimize asset delivery for subdomains
3. **Database Indexing**: Optimized queries for subdomain lookup
4. **Connection Pooling**: Efficient database connections

This assistant-level subdomain system provides maximum flexibility for attorneys while maintaining clean separation of concerns and robust security through RLS policies.
```

CREATE POLICY "Only admins can modify attorneys"
  ON public.attorneys FOR ALL
  USING (auth.role() = 'authenticated');
```

### 2. Validation
```typescript
function validateSubdomainConfig(config: SubdomainConfig): ValidationResult {
  return {
    isValid: true,
    errors: [],
    warnings: []
  };
}
```

### 3. Rate Limiting
```typescript
const subdomainRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100 // limit each subdomain to 100 requests per windowMs
});
```

## Integration Example

### React Component
```jsx
function SubdomainProvider({ children }) {
  const [config, setConfig] = useState(null);
  const subdomain = useSubdomain();

  useEffect(() => {
    async function loadConfig() {
      const config = await loadSubdomainConfig(subdomain);
      setConfig(config);
    }
    loadConfig();
  }, [subdomain]);

  if (!config) return <Loading />;

  return (
    <SubdomainContext.Provider value={config}>
      {children}
    </SubdomainContext.Provider>
  );
}
```

### Usage Example
```jsx
function AttorneyProfile() {
  const config = useSubdomainConfig();
  
  return (
    <div className="attorney-profile">
      <header>
        <img src={config.logoUrl} alt={config.firmName} />
        <h1>{config.firmName}</h1>
      </header>
      
      <section className="practice-areas">
        {config.practiceAreas.map(area => (
          <PracticeAreaCard key={area} area={area} />
        ))}
      </section>
      
      <VapiCall subdomain={config.subdomain} />
    </div>
  );
}
```

## Testing

### 1. Configuration Testing
```typescript
describe('Subdomain Configuration', () => {
  it('loads valid configuration', async () => {
    const config = await loadSubdomainConfig('test-firm');
    expect(config).toMatchSnapshot();
  });

  it('handles missing configuration', async () => {
    const config = await loadSubdomainConfig('non-existent');
    expect(config).toBeNull();
  });
});
```

### 2. Integration Testing
```typescript
describe('Subdomain Integration', () => {
  it('applies correct branding', async () => {
    render(<SubdomainProvider subdomain="test-firm" />);
    expect(screen.getByAltText('Test Firm Logo')).toBeInTheDocument();
  });

  it('loads correct voice configuration', async () => {
    const { vapi } = render(<VapiCall subdomain="test-firm" />);
    expect(vapi.instructions).toContain('Test Firm');
  });
});
```

## Best Practices

1. **Configuration Management**
   - Store sensitive data securely
   - Version control configurations
   - Implement validation
   - Provide defaults

2. **Performance**
   - Cache configurations
   - Implement rate limiting
   - Optimize database queries
   - Use CDN for assets

3. **Security**
   - Validate subdomains
   - Implement access control
   - Sanitize user input
   - Monitor usage

4. **Maintenance**
   - Regular configuration audits
   - Update documentation
   - Monitor error rates
   - Backup configurations
``` 
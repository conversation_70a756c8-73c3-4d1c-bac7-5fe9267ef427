<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Verify CSP Fix - LegalScout</title>
  
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 20px;
      background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
      color: #333;
      min-height: 100vh;
    }
    .container {
      max-width: 800px;
      margin: 0 auto;
      background: white;
      padding: 30px;
      border-radius: 12px;
      box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    }
    .header {
      text-align: center;
      margin-bottom: 30px;
    }
    .test-result {
      margin: 15px 0;
      padding: 15px;
      border-radius: 8px;
      font-family: 'Courier New', monospace;
      font-size: 0.9em;
    }
    .success { background: #d4edda; border: 2px solid #27ae60; color: #155724; }
    .error { background: #f8d7da; border: 2px solid #e74c3c; color: #721c24; }
    .info { background: #d1ecf1; border: 2px solid #3498db; color: #0c5460; }
    .warning { background: #fff3cd; border: 2px solid #f39c12; color: #856404; }
    
    .status-icon {
      font-size: 1.5em;
      margin-right: 10px;
    }
    
    button {
      background: #27ae60;
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 6px;
      cursor: pointer;
      margin: 10px 5px;
      font-size: 1em;
      transition: background 0.3s;
    }
    button:hover { background: #2ecc71; }
    
    .summary {
      background: #f8f9fa;
      border: 1px solid #dee2e6;
      border-radius: 8px;
      padding: 20px;
      margin: 20px 0;
    }
    
    .code-block {
      background: #2c3e50;
      color: #ecf0f1;
      padding: 15px;
      border-radius: 6px;
      font-family: 'Courier New', monospace;
      font-size: 0.85em;
      overflow-x: auto;
      margin: 10px 0;
      white-space: pre-wrap;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>✅ CSP Fix Verification</h1>
      <p>Testing if the CSP eval() blocking issue has been resolved</p>
    </div>
    
    <div class="summary" id="summary">
      <h3>🔍 Test Summary</h3>
      <p>Click "Run All Tests" to verify the CSP fix is working correctly.</p>
    </div>
    
    <div style="text-align: center; margin: 20px 0;">
      <button onclick="runAllTests()">🚀 Run All Tests</button>
      <button onclick="testEvalOnly()">⚡ Test eval() Only</button>
      <button onclick="testVapiOnly()">📞 Test Vapi Only</button>
      <button onclick="clearResults()">🧹 Clear Results</button>
    </div>
    
    <div id="results"></div>
  </div>

  <script>
    let testResults = {
      total: 0,
      passed: 0,
      failed: 0,
      violations: 0
    };

    // CSP Violation Listener
    document.addEventListener('securitypolicyviolation', (e) => {
      testResults.violations++;
      console.error('🚨 CSP Violation:', e);
      
      addResult('error', '🚨 CSP Violation Detected', `
        This indicates the CSP fix may not be working correctly.
        Directive: ${e.violatedDirective}
        Blocked URI: ${e.blockedURI}
        Source: ${e.sourceFile}:${e.lineNumber}
      `);
      
      updateSummary();
    });

    function addResult(type, title, message) {
      const results = document.getElementById('results');
      const div = document.createElement('div');
      div.className = `test-result ${type}`;
      
      const icon = type === 'success' ? '✅' : 
                   type === 'error' ? '❌' : 
                   type === 'warning' ? '⚠️' : 'ℹ️';
      
      div.innerHTML = `
        <span class="status-icon">${icon}</span>
        <strong>${title}</strong>
        <div style="margin-top: 10px;">${message}</div>
      `;
      results.appendChild(div);
    }

    function updateSummary() {
      const summary = document.getElementById('summary');
      const successRate = testResults.total > 0 ? 
        Math.round((testResults.passed / testResults.total) * 100) : 0;
      
      const status = testResults.violations > 0 ? 'error' : 
                    testResults.failed > 0 ? 'warning' : 
                    testResults.passed > 0 ? 'success' : 'info';
      
      const statusText = testResults.violations > 0 ? '❌ CSP Fix Failed' :
                        testResults.failed > 0 ? '⚠️ Partial Success' :
                        testResults.passed > 0 ? '✅ CSP Fix Successful' :
                        '🔍 Ready to Test';
      
      summary.className = `summary ${status}`;
      summary.innerHTML = `
        <h3>${statusText}</h3>
        <p><strong>Tests Run:</strong> ${testResults.total}</p>
        <p><strong>Passed:</strong> ${testResults.passed}</p>
        <p><strong>Failed:</strong> ${testResults.failed}</p>
        <p><strong>CSP Violations:</strong> ${testResults.violations}</p>
        <p><strong>Success Rate:</strong> ${successRate}%</p>
        ${testResults.violations === 0 && testResults.passed > 0 ? 
          '<p style="color: #27ae60; font-weight: bold;">🎉 CSP fix is working correctly!</p>' : ''}
      `;
    }

    async function runAllTests() {
      clearResults();
      addResult('info', '🚀 Starting Comprehensive Tests', 'Testing CSP fix and Vapi integration...');
      
      await testEvalOnly();
      await testFunctionConstructor();
      await testDynamicScript();
      await testVapiOnly();
      
      updateSummary();
      
      if (testResults.violations === 0 && testResults.passed === testResults.total) {
        addResult('success', '🎉 All Tests Passed!', 
          'The CSP fix is working correctly. eval() is now allowed and Vapi should work properly.');
      }
    }

    async function testEvalOnly() {
      testResults.total++;
      
      try {
        const result = eval('2 + 2');
        addResult('success', '✅ eval() Test Passed', 
          `eval() is now working correctly! Result: ${result}`);
        testResults.passed++;
      } catch (error) {
        addResult('error', '❌ eval() Test Failed', 
          `eval() is still blocked: ${error.message}`);
        testResults.failed++;
      }
      
      updateSummary();
    }

    async function testFunctionConstructor() {
      testResults.total++;
      
      try {
        const func = new Function('return 3 + 3;');
        const result = func();
        addResult('success', '✅ Function Constructor Test Passed', 
          `Function constructor works! Result: ${result}`);
        testResults.passed++;
      } catch (error) {
        addResult('error', '❌ Function Constructor Test Failed', 
          `Function constructor blocked: ${error.message}`);
        testResults.failed++;
      }
      
      updateSummary();
    }

    async function testDynamicScript() {
      testResults.total++;
      
      try {
        const script = document.createElement('script');
        script.textContent = 'window.dynamicScriptTest = true;';
        document.head.appendChild(script);
        document.head.removeChild(script);
        
        setTimeout(() => {
          if (window.dynamicScriptTest) {
            addResult('success', '✅ Dynamic Script Test Passed', 
              'Dynamic script execution is working');
            testResults.passed++;
          } else {
            addResult('warning', '⚠️ Dynamic Script Test Unclear', 
              'Dynamic script may not have executed');
            testResults.failed++;
          }
          updateSummary();
        }, 100);
      } catch (error) {
        addResult('error', '❌ Dynamic Script Test Failed', 
          `Dynamic script error: ${error.message}`);
        testResults.failed++;
        updateSummary();
      }
    }

    async function testVapiOnly() {
      testResults.total++;
      
      try {
        addResult('info', '📞 Loading Vapi SDK...', 'Attempting to load Vapi SDK from official 2025 CDN...');

        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/gh/VapiAI/html-script-tag@latest/dist/assets/index.js';
        script.defer = true;
        script.async = true;

        const loadPromise = new Promise((resolve, reject) => {
          script.onload = () => {
            if (typeof window.vapiSDK !== 'undefined') {
              resolve('Vapi SDK (2025) loaded successfully');
            } else if (typeof window.Vapi !== 'undefined') {
              resolve('Vapi SDK (legacy) loaded successfully');
            } else {
              reject(new Error('Vapi SDK loaded but not available'));
            }
          };
          script.onerror = () => reject(new Error('Vapi SDK failed to load'));
          setTimeout(() => reject(new Error('Vapi SDK loading timeout')), 10000);
        });
        
        document.head.appendChild(script);
        const result = await loadPromise;
        
        addResult('success', '✅ Vapi SDK Test Passed', result);
        testResults.passed++;
      } catch (error) {
        addResult('error', '❌ Vapi SDK Test Failed', error.message);
        testResults.failed++;
      }
      
      updateSummary();
    }

    function clearResults() {
      document.getElementById('results').innerHTML = '';
      testResults = { total: 0, passed: 0, failed: 0, violations: 0 };
      updateSummary();
    }

    // Auto-run basic test on load
    document.addEventListener('DOMContentLoaded', function() {
      console.log('✅ CSP Fix Verification Tool loaded');
      updateSummary();
      
      // Auto-test eval after a short delay
      setTimeout(() => {
        addResult('info', '🔍 Auto-Testing eval()', 'Running automatic eval() test...');
        testEvalOnly();
      }, 1000);
    });
  </script>
</body>
</html>

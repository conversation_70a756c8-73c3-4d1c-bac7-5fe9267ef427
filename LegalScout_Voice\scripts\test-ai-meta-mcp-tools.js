#!/usr/bin/env node
/**
 * Test AI Meta MCP Tools
 *
 * This script tests the synchronization tools registered with the AI Meta MCP Server.
 *
 * Usage:
 *   node scripts/test-ai-meta-mcp-tools.js
 */

import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { SSEClientTransport } from '@modelcontextprotocol/sdk/client/sse.js';

// Function to test a tool
const testTool = async (mcpClient, name, args) => {
  try {
    console.log(`Testing tool: ${name}`);
    console.log(`Arguments: ${JSON.stringify(args, null, 2)}`);

    const result = await mcpClient.callTool({
      name,
      arguments: args,
    });

    console.log(`Result from ${name}:`);
    if (result.content && result.content[0]) {
      console.log(result.content[0].text);
    } else {
      console.log('No result or empty result');
    }

    return result;
  } catch (error) {
    console.error(`Error testing tool ${name}:`, error);
    throw error;
  }
};

// Main function to test all tools
const testAllTools = async () => {
  try {
    console.log('Connecting to AI Meta MCP Server...');

    // Get AI Meta MCP client
    const mcpClient = new Client({
      name: 'legalscout-tool-tester',
      version: '1.0.0',
    });

    // Connect to the AI Meta MCP server
    const transport = new SSEClientTransport({
      url: 'http://localhost:8080/sse', // Adjust port if needed
    });

    await mcpClient.connect(transport);
    console.log('Connected to AI Meta MCP Server');

    // List all registered functions
    console.log('\nListing all registered functions:');
    const listResult = await mcpClient.callTool({
      name: 'list_functions',
      arguments: {},
    });

    if (listResult.content && listResult.content[0]) {
      console.log(listResult.content[0].text);
    } else {
      console.log('No functions registered or unable to retrieve list');
    }

    // Test sync_attorney_profile
    console.log('\nTesting sync_attorney_profile:');
    await testTool(mcpClient, 'sync_attorney_profile', {
      attorneyId: 'test-attorney-id',
      forceUpdate: true
    });

    // Test manage_auth_state
    console.log('\nTesting manage_auth_state:');
    await testTool(mcpClient, 'manage_auth_state', {
      authData: {
        user: {
          id: 'test-user-id',
          email: '<EMAIL>'
        },
        session: {
          access_token: 'test-access-token'
        }
      },
      action: 'login'
    });

    // Test validate_configuration
    console.log('\nTesting validate_configuration:');
    await testTool(mcpClient, 'validate_configuration', {
      attorneyId: 'test-attorney-id',
      configData: {
        name: 'Test Attorney',
        email: '<EMAIL>',
        firm_name: 'Test Law Firm',
        welcome_message: 'Welcome to Test Law Firm',
        vapi_instructions: 'You are a legal assistant for Test Law Firm'
      }
    });

    // Test check_preview_consistency
    console.log('\nTesting check_preview_consistency:');
    await testTool(mcpClient, 'check_preview_consistency', {
      attorneyId: 'test-attorney-id'
    });

    // Disconnect from the MCP server
    await mcpClient.disconnect();
    console.log('\nDisconnected from AI Meta MCP Server');

    console.log('All tools tested successfully');
  } catch (error) {
    console.error('Error testing tools:', error);
    process.exit(1);
  }
};

// Run the tests
testAllTools();

// BACKUP of original supabase.js before applying fixes
// This file contains the original implementation for reference

import { createClient } from '@supabase/supabase-js';
import { createStubClient } from '../utils/mockSupabase.js';

// Get environment variables with comprehensive fallbacks
const getSupabaseUrl = () => {
  return import.meta.env.VITE_SUPABASE_URL ||
         import.meta.env.REACT_APP_SUPABASE_URL ||
         'https://utopqxsvudgrtiwenlzl.supabase.co';
};

const getSupabaseKey = () => {
  return import.meta.env.VITE_SUPABASE_KEY ||
         import.meta.env.VITE_SUPABASE_ANON_KEY ||
         import.meta.env.REACT_APP_SUPABASE_KEY ||
         import.meta.env.REACT_APP_SUPABASE_ANON_KEY ||
         'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg5NDgwMDcsImV4cCI6MjA1NDUyNDAwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU';
};

// Detect environment and generate appropriate redirect URLs
const getEnvironmentInfo = () => {
  const hostname = typeof window !== 'undefined' ? window.location.hostname : 'localhost';
  const protocol = typeof window !== 'undefined' ? window.location.protocol : 'http:';
  const port = typeof window !== 'undefined' ? window.location.port : '';

  const isDevelopment = hostname === 'localhost' || hostname === '127.0.0.1';
  const isProduction = hostname.includes('vercel.app') || hostname.includes('legalscout.net');

  // Generate base URL without hardcoded ports
  let baseUrl;
  if (isDevelopment) {
    baseUrl = port ? `${protocol}//${hostname}:${port}` : `${protocol}//${hostname}`;
  } else {
    baseUrl = `${protocol}//${hostname}`;
  }

  return {
    isDevelopment,
    isProduction,
    baseUrl,
    hostname,
    protocol,
    port
  };
};

// Create Supabase client with enhanced configuration
let supabaseClient = null;
let initializationAttempted = false;

const initializeSupabaseClient = () => {
  if (initializationAttempted) {
    return supabaseClient;
  }

  initializationAttempted = true;

  try {
    const url = getSupabaseUrl();
    const key = getSupabaseKey();
    const envInfo = getEnvironmentInfo();

    console.log('🔧 [Supabase] Initializing client for environment:', {
      isDevelopment: envInfo.isDevelopment,
      isProduction: envInfo.isProduction,
      baseUrl: envInfo.baseUrl,
      hostname: envInfo.hostname
    });

    // Enhanced client configuration for better auth handling
    supabaseClient = createClient(url, key, {
      auth: {
        autoRefreshToken: true,
        persistSession: true,
        detectSessionInUrl: true,
        flowType: 'pkce'
      }
    });

    console.log('✅ [Supabase] Real client initialized successfully');
    return supabaseClient;
  } catch (error) {
    console.error('❌ [Supabase] Failed to initialize real client, using stub:', error);
    supabaseClient = createStubClient();
    return supabaseClient;
  }
};

// Create real Supabase client for authentication operations
const createRealSupabaseClient = async () => {
  try {
    const url = getSupabaseUrl();
    const key = getSupabaseKey();

    console.log('Creating real Supabase client for authentication...');

    // SIMPLE: Just create the client with default config
    const realClient = createClient(url, key);
    console.log('Real Supabase client created successfully');
    return realClient;
  } catch (error) {
    console.error('Error creating real Supabase client:', error);
    throw error;
  }
};

export const getSupabaseClient = async () => {
  if (!supabaseClient) {
    return initializeSupabaseClient();
  }
  return supabaseClient;
};

// Get real Supabase client for authentication operations (bypasses stub)
export const getRealSupabaseClient = async () => {
  return createRealSupabaseClient();
};

export const supabase = new Proxy({}, {
  get(target, prop) {
    const client = supabaseClient || initializeSupabaseClient();
    if (!client || typeof client[prop] === 'undefined') {
      console.warn('Supabase client not available, using stub');
      return createStubClient()[prop];
    }
    return client[prop];
  }
});

// UNIFIED: Enhanced authentication with environment-aware redirect URLs
export const unifiedAuth = {
  signInWithGoogle: async () => {
    try {
      const envInfo = getEnvironmentInfo();
      console.log('🔐 [UnifiedAuth] Starting Google OAuth for environment:', envInfo);

      // Try using the real Supabase client first
      const client = await getSupabaseClient();

      // Generate environment-appropriate redirect URL
      const redirectUrl = `${envInfo.baseUrl}/auth/callback`;
      console.log('🔐 [UnifiedAuth] Using redirect URL:', redirectUrl);

      try {
        // Attempt standard Supabase OAuth
        const { data, error } = await client.auth.signInWithOAuth({
          provider: 'google',
          options: {
            redirectTo: redirectUrl,
            queryParams: {
              access_type: 'offline',
              prompt: 'consent'
            }
          }
        });

        if (error) throw error;

        console.log('✅ [UnifiedAuth] Standard OAuth initiated successfully');
        return { data, error: null };
      } catch (oauthError) {
        console.warn('⚠️ [UnifiedAuth] Standard OAuth failed, using direct approach:', oauthError);

        // Fallback to direct OAuth URL
        const supabaseUrl = getSupabaseUrl();
        const supabaseKey = getSupabaseKey();

        const oauthUrl = `${supabaseUrl}/auth/v1/authorize?` + new URLSearchParams({
          provider: 'google',
          redirect_to: redirectUrl,
          apikey: supabaseKey
        });

        console.log('🔐 [UnifiedAuth] Using direct OAuth URL:', oauthUrl);
        window.location.href = oauthUrl;
        return { data: { url: oauthUrl }, error: null };
      }
    } catch (error) {
      console.error('❌ [UnifiedAuth] Authentication error:', error);
      throw error;
    }
  }
};

// EMERGENCY: Emergency authentication for bypassing broken Supabase client
export const emergencyAuth = {
  getCurrentUser: async () => {
    try {
      console.log('🚨 [EmergencyAuth] Getting current user...');

      // Check if we're on the callback URL with tokens
      const urlParams = new URLSearchParams(window.location.search);
      const hashParams = new URLSearchParams(window.location.hash.substring(1));

      // Look for access token in URL (OAuth callback)
      const accessToken = urlParams.get('access_token') || hashParams.get('access_token');
      const refreshToken = urlParams.get('refresh_token') || hashParams.get('refresh_token');

      if (accessToken) {
        console.log('🚨 [EmergencyAuth] Found access token in URL, getting user data...');

        const supabaseUrl = getSupabaseUrl();
        const response = await fetch(`${supabaseUrl}/auth/v1/user`, {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'apikey': getSupabaseKey()
          }
        });

        if (response.ok) {
          const user = await response.json();
          console.log('🚨 [EmergencyAuth] Got user from OAuth callback:', user.email);

          // Store tokens for future use
          localStorage.setItem('supabase.auth.token', JSON.stringify({
            access_token: accessToken,
            refresh_token: refreshToken,
            user: user
          }));

          // Clean up URL
          window.history.replaceState({}, document.title, window.location.pathname);

          return { user, error: null };
        }
      }

      // Try to get user from stored token
      const storedAuth = localStorage.getItem('supabase.auth.token');
      if (storedAuth) {
        try {
          const authData = JSON.parse(storedAuth);
          console.log('🚨 [EmergencyAuth] Found stored auth, verifying...');

          const supabaseUrl = getSupabaseUrl();
          const response = await fetch(`${supabaseUrl}/auth/v1/user`, {
            headers: {
              'Authorization': `Bearer ${authData.access_token}`,
              'apikey': getSupabaseKey()
            }
          });

          if (response.ok) {
            const user = await response.json();
            console.log('🚨 [EmergencyAuth] Verified stored auth for:', user.email);
            return { user, error: null };
          } else {
            console.log('🚨 [EmergencyAuth] Stored token expired, clearing...');
            localStorage.removeItem('supabase.auth.token');
          }
        } catch (parseError) {
          console.error('🚨 [EmergencyAuth] Error parsing stored auth:', parseError);
          localStorage.removeItem('supabase.auth.token');
        }
      }

      console.log('🚨 [EmergencyAuth] No valid authentication found');
      return { user: null, error: 'Not authenticated' };
    } catch (error) {
      console.error('🚨 [EmergencyAuth] Error getting user:', error);
      return { user: null, error: error.message };
    }
  }
};

export const signInWithGoogle = async () => {
  console.log('🔐 [Supabase] Starting Google sign-in with unified auth...');

  // Use the new unified authentication system
  return await unifiedAuth.signInWithGoogle();
};

// Enhanced session management
export const getSession = async () => {
  try {
    const client = await getSupabaseClient();
    const { data: { session }, error } = await client.auth.getSession();

    if (error) {
      console.warn('⚠️ [Supabase] Session retrieval error:', error);
      return null;
    }

    return session;
  } catch (error) {
    console.error('❌ [Supabase] Error getting session:', error);
    return null;
  }
};

// Enhanced user management
export const getCurrentUser = async () => {
  try {
    const client = await getSupabaseClient();
    const { data: { user }, error } = await client.auth.getUser();

    if (error) {
      console.warn('⚠️ [Supabase] User retrieval error:', error);
      return null;
    }

    return user;
  } catch (error) {
    console.error('❌ [Supabase] Error getting user:', error);
    return null;
  }
};

// Sign out function
export const signOut = async () => {
  try {
    const client = await getSupabaseClient();
    const { error } = await client.auth.signOut();

    if (error) {
      console.error('❌ [Supabase] Sign out error:', error);
      return { success: false, error };
    }

    // Clear local storage
    localStorage.removeItem('supabase.auth.token');
    localStorage.removeItem('attorney');

    console.log('✅ [Supabase] Successfully signed out');
    return { success: true, error: null };
  } catch (error) {
    console.error('❌ [Supabase] Error during sign out:', error);
    return { success: false, error };
  }
};

// Make emergency auth globally available for console testing
if (typeof window !== 'undefined') {
  window.emergencyAuth = emergencyAuth;
  console.log('🚨 [EmergencyAuth] Available globally as window.emergencyAuth');
}

export const isSupabaseConfigured = () => {
  const url = getSupabaseUrl();
  const key = getSupabaseKey();
  return !!(url && key && url !== 'your-supabase-url' && key !== 'your-anon-key');
};

export { getSupabaseUrl, getSupabaseKey };

/**
 * Authentication Context - PHASE 1 OPTIMIZED
 *
 * PHASE 1 CHANGES (Streamlined AuthContext):
 * - Removed attorney data loading from initial authentication flow
 * - Eliminated premature manage-auth-state API calls during initialization
 * - Focus AuthContext solely on basic Supabase authentication (user, session, basic auth state)
 * - Attorney data loading moved to lazy loading in dashboard components
 *
 * This context now provides BASIC authentication state and methods only.
 * Attorney data is loaded separately after authentication completes.
 */

import React, { useState, useEffect, useRef } from 'react';
import { getSupabaseClient } from '../lib/supabase-fixed';
import {
  signInWithOAuth,
  handleOAuthCallback,
  signOut as authSignOut,
  getCurrentSession
} from '../services/authService';

// Create proper React context
const AuthContext = React.createContext(null);

// Custom hook to use the auth context
export const useAuth = () => {
  const context = React.useContext(AuthContext);

  if (!context) {
    console.warn('Auth context not found, returning default values');
    return {
      user: null,
      attorney: null,
      isLoading: false,
      signOut: () => Promise.resolve(),
      isAuthenticated: false
    };
  }

  return context;
};

/**
 * PHASE 1: Authentication Provider - BASIC AUTH ONLY
 *
 * @param {Object} props - The component props
 * @param {React.ReactNode} props.children - The child components
 * @returns {JSX.Element} The provider component
 */
export const AuthProvider = ({ children }) => {
  // PHASE 1: State for tracking BASIC authentication state only
  const [user, setUser] = useState(null);
  const [attorney, setAttorney] = useState(null); // Kept for backward compatibility, but not loaded here
  const [session, setSession] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // PHASE 1: Initialize BASIC authentication state only (no attorney data loading)
  useEffect(() => {
    const initAuth = async () => {
      console.log('🔐 [AuthContext-P1] Starting BASIC auth initialization (no attorney loading)...');
      setLoading(true);
      setError(null);

      // Reduced timeout for faster basic auth
      const forceLoadingTimeout = setTimeout(() => {
        console.log('🔐 [AuthContext-P1] ⚠️ FORCING loading to false after timeout');
        setLoading(false);
      }, 1000); // Reduced to 1 second for basic auth only

      try {
        // Get current session from Supabase - BASIC AUTH ONLY
        const supabase = await getSupabaseClient();
        const { data: { session }, error } = await supabase.auth.getSession();

        if (error) {
          console.error('🔐 [AuthContext-P1] Error getting session:', error);
          setUser(null);
          setSession(null);
          setAttorney(null); // Clear attorney state but don't try to load it
        } else if (session?.user) {
          console.log('🔐 [AuthContext-P1] OAuth user data:', session.user);

          // Ensure email is set from OAuth - check all possible locations
          const userEmail = session.user.email ||
                           session.user.user_metadata?.email ||
                           session.user.identities?.[0]?.identity_data?.email ||
                           '';

          console.log('🔐 [AuthContext-P1] Found OAuth email:', userEmail);

          const userWithEmail = {
            ...session.user,
            email: userEmail
          };

          // PHASE 1: Set ONLY basic auth state - NO attorney data loading
          setUser(userWithEmail);
          setSession(session);
          // NOTE: Attorney state is NOT set here - will be loaded separately in dashboard

          console.log('🔐 [AuthContext-P1] ✅ Basic authentication complete - attorney data will be loaded separately');
        } else {
          console.log('🔐 [AuthContext-P1] No session found');
          setUser(null);
          setSession(null);
          setAttorney(null);
        }
      } catch (error) {
        console.error('🔐 [AuthContext-P1] Unexpected error checking auth:', error);
        setUser(null);
        setSession(null);
        setAttorney(null);
        setError(error.message);
      } finally {
        clearTimeout(forceLoadingTimeout);
        console.log('🔐 [AuthContext-P1] ✅ Basic auth initialization complete, setting loading to false');
        setLoading(false);
      }
    };

    initAuth();

    // PHASE 1: Listen for auth changes - BASIC AUTH ONLY (no attorney data loading)
    const setupAuthListener = async () => {
      try {
        const supabase = await getSupabaseClient();
        const { data: authListener } = supabase.auth.onAuthStateChange(
          async (event, session) => {
            console.log('🔐 [AuthContext-P1] Auth state changed:', event);

            if (session?.user) {
              console.log('🔐 [AuthContext-P1] OAuth user data (auth change):', session.user);

              const userEmail = session.user.email ||
                               session.user.user_metadata?.email ||
                               session.user.identities?.[0]?.identity_data?.email ||
                               '';

              console.log('🔐 [AuthContext-P1] Found OAuth email (auth change):', userEmail);

              const userWithEmail = {
                ...session.user,
                email: userEmail
              };

              // PHASE 1: Set ONLY basic auth state - NO attorney data loading
              setUser(userWithEmail);
              setSession(session);

              console.log('🔐 [AuthContext-P1] ✅ Auth state updated - attorney data will be loaded separately');

              // PHASE 1: Clear attorney state on sign out only
              if (event === 'SIGNED_OUT') {
                setAttorney(null);
              }
            } else {
              console.log('🔐 [AuthContext-P1] No session - clearing auth state');
              setUser(null);
              setSession(null);
              setAttorney(null);
            }

            setLoading(false);
          }
        );

        // Store the listener for cleanup
        return authListener;
      } catch (error) {
        console.error('Failed to set up auth listener:', error);
        return null;
      }
    };

    // Set up the auth listener
    let authListenerPromise = setupAuthListener();

    // Cleanup subscription
    return () => {
      authListenerPromise.then(authListener => {
        if (authListener && authListener.subscription) {
          authListener.subscription.unsubscribe();
        }
      }).catch(error => {
        console.warn('🔐 [AuthContext-P1] Error cleaning up auth listener:', error);
      });
    };
  }, []); // PHASE 1: No dependencies - basic auth only

  /**
   * Sign in with OAuth provider
   *
   * @param {string} provider - The OAuth provider (e.g., 'google', 'github')
   * @returns {Promise<Object>} The sign in result
   */
  const login = async (provider) => {
    setError(null);

    try {
      return await signInWithOAuth(provider);
    } catch (error) {
      console.error('Login error:', error);
      setError(error.message);
      throw error;
    }
  };

  /**
   * PHASE 1: Handle OAuth callback - BASIC AUTH ONLY
   *
   * @returns {Promise<Object>} The callback result
   */
  const handleCallback = async () => {
    setLoading(true);
    setError(null);

    try {
      // PHASE 1: Use basic callback handling only - no attorney data loading
      console.log('🔐 [AuthContext-P1] Using basic Supabase client for callback handling');
      const supabase = await getSupabaseClient();
      const { data: { session }, error } = await supabase.auth.getSession();

      if (error) throw error;

      if (session?.user) {
        // Ensure email is properly set
        const userEmail = session.user.email ||
                         session.user.user_metadata?.email ||
                         session.user.identities?.[0]?.identity_data?.email ||
                         '';

        const userWithEmail = {
          ...session.user,
          email: userEmail
        };

        setUser(userWithEmail);
        setSession(session);

        console.log('🔐 [AuthContext-P1] ✅ OAuth callback complete - attorney data will be loaded separately');
      }

      return { success: !!session, session };
    } catch (error) {
      console.error('🔐 [AuthContext-P1] Callback error:', error);
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  /**
   * PHASE 1: Sign out - BASIC AUTH ONLY
   *
   * @returns {Promise<Object>} The sign out result
   */
  const signOut = async () => {
    setLoading(true);
    setError(null);

    try {
      // PHASE 1: Use basic sign out only - no sync tools
      console.log('🔐 [AuthContext-P1] Performing basic sign out');
      const supabase = await getSupabaseClient();
      const { error } = await supabase.auth.signOut();

      if (error) throw error;

      // Clear all auth state
      setUser(null);
      setSession(null);
      setAttorney(null);

      console.log('🔐 [AuthContext-P1] ✅ Sign out complete');
      return { success: true };
    } catch (error) {
      console.error('🔐 [AuthContext-P1] Sign out error:', error);
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  /**
   * PHASE 1: Refresh session - BASIC AUTH ONLY
   *
   * @returns {Promise<Object>} The refresh result
   */
  const refreshSession = async () => {
    setLoading(true);
    setError(null);

    try {
      // PHASE 1: Use basic session refresh only - no attorney data loading
      console.log('🔐 [AuthContext-P1] Refreshing basic session');
      const supabase = await getSupabaseClient();
      const { data: { session }, error } = await supabase.auth.getSession();

      if (error) throw error;

      if (session?.user) {
        // Ensure email is properly set
        const userEmail = session.user.email ||
                         session.user.user_metadata?.email ||
                         session.user.identities?.[0]?.identity_data?.email ||
                         '';

        const userWithEmail = {
          ...session.user,
          email: userEmail
        };

        setUser(userWithEmail);
        setSession(session);

        console.log('🔐 [AuthContext-P1] ✅ Session refreshed - attorney data will be loaded separately');
      } else {
        setUser(null);
        setSession(null);
        setAttorney(null);
      }

      return { success: !!session, session };
    } catch (error) {
      console.error('🔐 [AuthContext-P1] Refresh error:', error);
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // PHASE 1: Context value - BASIC AUTH ONLY
  const value = {
    // Basic auth state
    user,
    attorney, // Kept for backward compatibility, but will be null until loaded separately
    session,
    isLoading: loading,
    error,
    isAuthenticated: !!session,

    // Basic auth methods
    login,
    handleCallback,
    signOut,
    refreshSession,

    // Helper methods
    clearError: () => setError(null),

    // PHASE 1: Add method to set attorney data (for lazy loading)
    setAttorney: (attorneyData) => {
      console.log('🔐 [AuthContext-P1] Setting attorney data from external source:', !!attorneyData);
      setAttorney(attorneyData);
    }
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

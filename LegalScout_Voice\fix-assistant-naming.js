/**
 * Fix Assistant Naming Issue
 *
 * This script will:
 * 1. Fix the wrong assistant ID issue (87756a2c-a398-43f2-889a-b8815684df71 -> 1d3471b7-8694-4844-b3ef-e05720693efc)
 * 2. Update the assistant name in Vapi to "<PERSON>'s Assistant"
 * 3. Update the database to reflect the correct assistant name and ID
 * 4. Clean up any duplicate or orphaned assistant records
 * 5. Ensure proper name resolution in the dropdown and header
 */

import { createClient } from '@supabase/supabase-js';

// Supabase configuration
const SUPABASE_URL = 'https://utopqxsvudgrtiwenlzl.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg5NDgwMDcsImV4cCI6MjA1NDUyNDAwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU';

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// Vapi configuration
const VAPI_PRIVATE_KEY = '6734febc-fc65-4669-93b0-929b31ff6564';
const VAPI_API_URL = 'https://api.vapi.ai';

// Target assistant ID (the correct one from damon.legalscout.net)
const TARGET_ASSISTANT_ID = '1d3471b7-8694-4844-b3ef-e05720693efc';
// Wrong assistant ID that's causing issues
const WRONG_ASSISTANT_ID = '87756a2c-a398-43f2-889a-b8815684df71';
// Desired assistant name
const DESIRED_ASSISTANT_NAME = "Damon's Assistant";

async function main() {
  console.log('🔧 Starting Assistant Naming Fix...\n');

  try {
    // Step 1: Get current Vapi assistant data
    console.log('📡 Step 1: Checking Vapi Assistant');
    const vapiResponse = await fetch(`${VAPI_API_URL}/assistant/${TARGET_ASSISTANT_ID}`, {
      headers: {
        'Authorization': `Bearer ${VAPI_PRIVATE_KEY}`,
        'Content-Type': 'application/json'
      }
    });

    if (!vapiResponse.ok) {
      throw new Error(`Vapi API error: ${vapiResponse.status} ${vapiResponse.statusText}`);
    }

    const vapiAssistant = await vapiResponse.json();
    console.log('✅ Current Vapi Assistant:', {
      id: vapiAssistant.id,
      name: vapiAssistant.name,
      updatedAt: vapiAssistant.updatedAt
    });

    // Step 2: Find and fix attorney records
    console.log('\n👨‍💼 Step 2: Finding and Fixing Attorney Records');

    // First, check for attorneys with the wrong assistant ID
    const { data: wrongAttorneys, error: wrongError } = await supabase
      .from('attorneys')
      .select('*')
      .or(`vapi_assistant_id.eq.${WRONG_ASSISTANT_ID},current_assistant_id.eq.${WRONG_ASSISTANT_ID}`);

    if (wrongError) {
      console.warn(`Warning checking wrong attorneys: ${wrongError.message}`);
    }

    if (wrongAttorneys && wrongAttorneys.length > 0) {
      console.log(`⚠️ Found ${wrongAttorneys.length} attorney(s) with wrong assistant ID. Fixing...`);

      for (const attorney of wrongAttorneys) {
        console.log(`Fixing attorney: ${attorney.firm_name} (${attorney.email})`);

        const { error: fixError } = await supabase
          .from('attorneys')
          .update({
            vapi_assistant_id: TARGET_ASSISTANT_ID,
            current_assistant_id: TARGET_ASSISTANT_ID,
            updated_at: new Date().toISOString()
          })
          .eq('id', attorney.id);

        if (fixError) {
          console.error(`Error fixing attorney ${attorney.id}: ${fixError.message}`);
        } else {
          console.log(`✅ Fixed attorney: ${attorney.firm_name}`);
        }
      }
    }

    // Now find attorneys with the correct assistant ID
    const { data: attorneys, error: attorneyError } = await supabase
      .from('attorneys')
      .select('*')
      .or(`vapi_assistant_id.eq.${TARGET_ASSISTANT_ID},current_assistant_id.eq.${TARGET_ASSISTANT_ID}`);

    if (attorneyError) {
      throw new Error(`Error finding attorney: ${attorneyError.message}`);
    }

    if (!attorneys || attorneys.length === 0) {
      console.log('⚠️ No attorney found with correct assistant ID. Checking all attorneys...');

      const { data: allAttorneys, error: allError } = await supabase
        .from('attorneys')
        .select('id, firm_name, vapi_assistant_id, current_assistant_id, email')
        .limit(10);

      if (allError) {
        throw new Error(`Error getting attorneys: ${allError.message}`);
      }

      console.log('📋 Available attorneys:');
      allAttorneys.forEach(att => {
        console.log(`  - ${att.firm_name} (${att.email}): vapi=${att.vapi_assistant_id}, current=${att.current_assistant_id}`);
      });

      // Use the first attorney (assuming it's damon)
      if (allAttorneys.length > 0) {
        const attorney = allAttorneys[0];
        console.log(`\n🎯 Using attorney: ${attorney.firm_name} (${attorney.email})`);

        // Update this attorney to use our target assistant
        const { error: updateError } = await supabase
          .from('attorneys')
          .update({
            vapi_assistant_id: TARGET_ASSISTANT_ID,
            current_assistant_id: TARGET_ASSISTANT_ID,
            updated_at: new Date().toISOString()
          })
          .eq('id', attorney.id);

        if (updateError) {
          throw new Error(`Error updating attorney: ${updateError.message}`);
        }

        console.log('✅ Updated attorney with correct assistant ID');
        attorneys.push({ ...attorney, vapi_assistant_id: TARGET_ASSISTANT_ID, current_assistant_id: TARGET_ASSISTANT_ID });
      } else {
        throw new Error('No attorneys found in database');
      }
    }

    const attorney = attorneys[0];
    console.log('✅ Found attorney:', {
      id: attorney.id,
      firm_name: attorney.firm_name,
      email: attorney.email,
      vapi_assistant_id: attorney.vapi_assistant_id,
      current_assistant_id: attorney.current_assistant_id
    });

    // Step 3: Clean up wrong assistant UI configs and fix correct ones
    console.log('\n🎛️ Step 3: Cleaning Up Assistant UI Configs');

    // First, remove any configs with the wrong assistant ID
    const { data: wrongConfigs, error: wrongConfigError } = await supabase
      .from('assistant_ui_configs')
      .select('*')
      .eq('assistant_id', WRONG_ASSISTANT_ID);

    if (wrongConfigError) {
      console.warn(`Warning checking wrong configs: ${wrongConfigError.message}`);
    }

    if (wrongConfigs && wrongConfigs.length > 0) {
      console.log(`⚠️ Found ${wrongConfigs.length} config(s) with wrong assistant ID. Removing...`);

      const { error: deleteError } = await supabase
        .from('assistant_ui_configs')
        .delete()
        .eq('assistant_id', WRONG_ASSISTANT_ID);

      if (deleteError) {
        console.error(`Error deleting wrong configs: ${deleteError.message}`);
      } else {
        console.log('✅ Removed configs with wrong assistant ID');
      }
    }

    // Now check for correct assistant UI config
    const { data: uiConfig, error: configError } = await supabase
      .from('assistant_ui_configs')
      .select('*')
      .eq('attorney_id', attorney.id)
      .eq('assistant_id', TARGET_ASSISTANT_ID)
      .single();

    if (configError && configError.code !== 'PGRST116') {
      throw new Error(`Error getting UI config: ${configError.message}`);
    }

    if (!uiConfig) {
      console.log('⚠️ No UI config found. Creating one...');

      const { data: newConfig, error: createError } = await supabase
        .from('assistant_ui_configs')
        .insert({
          attorney_id: attorney.id,
          assistant_id: TARGET_ASSISTANT_ID,
          assistant_name: DESIRED_ASSISTANT_NAME,
          firm_name: attorney.firm_name,
          primary_color: '#2563eb',
          secondary_color: '#1e40af',
          button_color: '#3b82f6',
          voice_provider: 'openai',
          voice_id: 'echo',
          ai_model: 'gpt-4o',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (createError) {
        throw new Error(`Error creating UI config: ${createError.message}`);
      }

      console.log('✅ Created UI config:', {
        assistant_id: newConfig.assistant_id,
        assistant_name: newConfig.assistant_name,
        firm_name: newConfig.firm_name
      });
    } else {
      console.log('✅ Found UI config:', {
        assistant_id: uiConfig.assistant_id,
        assistant_name: uiConfig.assistant_name,
        firm_name: uiConfig.firm_name
      });
    }

    // Step 4: Update assistant name in Vapi to match firm name
    console.log('\n🔄 Step 4: Updating Assistant Name in Vapi');
    const desiredName = attorney.firm_name || 'LegalScout';
    
    if (vapiAssistant.name !== desiredName) {
      console.log(`Updating Vapi assistant name from "${vapiAssistant.name}" to "${desiredName}"`);
      
      const updateResponse = await fetch(`${VAPI_API_URL}/assistant/${TARGET_ASSISTANT_ID}`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${VAPI_PRIVATE_KEY}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          name: desiredName
        })
      });

      if (!updateResponse.ok) {
        throw new Error(`Failed to update Vapi assistant: ${updateResponse.status} ${updateResponse.statusText}`);
      }

      const updatedAssistant = await updateResponse.json();
      console.log('✅ Updated Vapi assistant name:', updatedAssistant.name);
    } else {
      console.log('✅ Vapi assistant name is already correct');
    }

    // Step 5: Update database assistant_name to match
    console.log('\n💾 Step 5: Updating Database Assistant Name');
    const { error: updateConfigError } = await supabase
      .from('assistant_ui_configs')
      .update({
        assistant_name: desiredName,
        updated_at: new Date().toISOString()
      })
      .eq('attorney_id', attorney.id)
      .eq('assistant_id', TARGET_ASSISTANT_ID);

    if (updateConfigError) {
      throw new Error(`Error updating UI config: ${updateConfigError.message}`);
    }

    console.log('✅ Updated database assistant name');

    // Step 6: Verify the fix
    console.log('\n✅ Step 6: Verification');
    
    // Check Vapi again
    const verifyVapiResponse = await fetch(`${VAPI_API_URL}/assistant/${TARGET_ASSISTANT_ID}`, {
      headers: {
        'Authorization': `Bearer ${VAPI_PRIVATE_KEY}`,
        'Content-Type': 'application/json'
      }
    });

    const verifiedVapiAssistant = await verifyVapiResponse.json();
    
    // Check database again
    const { data: verifiedConfig } = await supabase
      .from('assistant_ui_configs')
      .select('assistant_name, firm_name')
      .eq('attorney_id', attorney.id)
      .eq('assistant_id', TARGET_ASSISTANT_ID)
      .single();

    console.log('🎉 Final State:');
    console.log(`  Vapi Assistant Name: "${verifiedVapiAssistant.name}"`);
    console.log(`  Database Assistant Name: "${verifiedConfig.assistant_name}"`);
    console.log(`  Database Firm Name: "${verifiedConfig.firm_name}"`);

    if (verifiedVapiAssistant.name === verifiedConfig.assistant_name) {
      console.log('✅ SUCCESS: Names are now synchronized!');
    } else {
      console.log('⚠️ WARNING: Names are still not synchronized');
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
    process.exit(1);
  }
}

// Run the script
main().then(() => {
  console.log('\n🏁 Assistant naming fix completed!');
  process.exit(0);
}).catch(error => {
  console.error('💥 Script failed:', error);
  process.exit(1);
});

/**
 * Attorney Profile Fix
 * 
 * This script loads the simplified attorney manager and sync helper,
 * and applies fixes to ensure attorney profiles are properly saved and restored.
 */

(function() {
  console.log('[AttorneyProfileFix] Initializing...');
  
  // Load the simplified attorney manager
  function loadSimplifiedAttorneyManager() {
    return new Promise((resolve, reject) => {
      if (window.simplifiedAttorneyManager) {
        console.log('[AttorneyProfileFix] Simplified attorney manager already loaded');
        resolve(window.simplifiedAttorneyManager);
        return;
      }
      
      const script = document.createElement('script');
      script.src = '/simplified-attorney-manager.js';
      script.onload = () => {
        console.log('[AttorneyProfileFix] Simplified attorney manager loaded');
        resolve(window.simplifiedAttorneyManager);
      };
      script.onerror = (error) => {
        console.error('[AttorneyProfileFix] Error loading simplified attorney manager:', error);
        reject(error);
      };
      document.head.appendChild(script);
    });
  }
  
  // Load the attorney sync helper
  function loadAttorneySyncHelper() {
    return new Promise((resolve, reject) => {
      if (window.attorneySyncHelper) {
        console.log('[AttorneyProfileFix] Attorney sync helper already loaded');
        resolve(window.attorneySyncHelper);
        return;
      }
      
      const script = document.createElement('script');
      script.src = '/attorney-sync-helper.js';
      script.onload = () => {
        console.log('[AttorneyProfileFix] Attorney sync helper loaded');
        resolve(window.attorneySyncHelper);
      };
      script.onerror = (error) => {
        console.error('[AttorneyProfileFix] Error loading attorney sync helper:', error);
        reject(error);
      };
      document.head.appendChild(script);
    });
  }
  
  // Apply the fix
  async function applyFix() {
    try {
      // Load the helpers
      await loadSimplifiedAttorneyManager();
      await loadAttorneySyncHelper();
      
      // Get the current attorney
      const attorney = window.simplifiedAttorneyManager.getCurrentAttorney();
      console.log('[AttorneyProfileFix] Current attorney:', attorney.id);
      
      // Ensure persistence
      const result = await window.attorneySyncHelper.ensureAttorneyPersistence({
        attorney,
        syncWithVapi: false // Don't sync with Vapi automatically
      });
      
      console.log('[AttorneyProfileFix] Attorney persistence ensured:', result);
      
      // Add a global function to sync with Vapi
      window.syncAttorneyWithVapi = async function(force = false) {
        const currentAttorney = window.simplifiedAttorneyManager.getCurrentAttorney();
        console.log('[AttorneyProfileFix] Syncing attorney with Vapi:', currentAttorney.id);
        
        const result = await window.attorneySyncHelper.syncWithVapi(currentAttorney, force);
        console.log('[AttorneyProfileFix] Vapi sync result:', result);
        
        if (result.success && result.assistantId) {
          // Update attorney with assistant ID
          const updatedAttorney = window.simplifiedAttorneyManager.updateAttorney({
            vapi_assistant_id: result.assistantId
          });
          
          console.log('[AttorneyProfileFix] Updated attorney with assistant ID:', updatedAttorney.id);
        }
        
        return result;
      };
      
      console.log('[AttorneyProfileFix] Fix applied successfully');
      return true;
    } catch (error) {
      console.error('[AttorneyProfileFix] Error applying fix:', error);
      return false;
    }
  }
  
  // Apply the fix when the document is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', applyFix);
  } else {
    applyFix();
  }
})();

/**
 * Fix Agent Tab Logo Restoration
 * 
 * This script specifically fixes the AgentTab component's logic that
 * keeps restoring logos after they've been removed.
 */

console.log('[FixAgentTabLogoRestoration] Starting fix...');

// Function to patch React useEffect hooks
function patchReactUseEffect() {
  if (window.React && window.React.useEffect) {
    const originalUseEffect = window.React.useEffect;
    
    window.React.useEffect = function(effect, deps) {
      // Check if this is the problematic useEffect in AgentTab
      if (deps && deps.includes && typeof effect === 'function') {
        const effectString = effect.toString();
        
        // Look for the specific useEffect that initializes form data from attorney
        if (effectString.includes('setFormData') && effectString.includes('logoUrl') && effectString.includes('shouldUpdateLogo')) {
          console.log('[FixAgentTabLogoRestoration] Found problematic useEffect, patching...');
          
          // Create a patched version of the effect
          const patchedEffect = function() {
            try {
              // Check if banner was recently removed
              const bannerRemovalState = localStorage.getItem('banner_removal_state');
              let wasRecentlyRemoved = false;
              
              if (bannerRemovalState) {
                try {
                  const state = JSON.parse(bannerRemovalState);
                  wasRecentlyRemoved = state.isRemoved && (Date.now() - state.timestamp) < 30000;
                } catch (e) {
                  // Ignore parsing errors
                }
              }
              
              if (wasRecentlyRemoved) {
                console.log('[FixAgentTabLogoRestoration] Banner was recently removed, skipping logo restoration');
                return;
              }
              
              // Call the original effect
              return effect.call(this);
            } catch (error) {
              console.error('[FixAgentTabLogoRestoration] Error in patched useEffect:', error);
              // Fall back to original effect
              return effect.call(this);
            }
          };
          
          return originalUseEffect.call(this, patchedEffect, deps);
        }
      }
      
      // For all other useEffect calls, use the original
      return originalUseEffect.call(this, effect, deps);
    };
    
    console.log('[FixAgentTabLogoRestoration] React.useEffect patched');
  }
}

// Function to patch setState calls that might restore logos
function patchSetState() {
  // Override the global setState pattern used by React components
  if (window.React && window.React.Component) {
    const originalSetState = window.React.Component.prototype.setState;
    
    window.React.Component.prototype.setState = function(updater, callback) {
      // Check if this is a state update that might restore a logo
      if (typeof updater === 'object' && updater.logoUrl) {
        // Check if banner was recently removed
        const bannerRemovalState = localStorage.getItem('banner_removal_state');
        let wasRecentlyRemoved = false;
        
        if (bannerRemovalState) {
          try {
            const state = JSON.parse(bannerRemovalState);
            wasRecentlyRemoved = state.isRemoved && (Date.now() - state.timestamp) < 30000;
          } catch (e) {
            // Ignore parsing errors
          }
        }
        
        if (wasRecentlyRemoved) {
          console.log('[FixAgentTabLogoRestoration] Preventing logo restoration via setState');
          updater = {
            ...updater,
            logoUrl: ''
          };
        }
      } else if (typeof updater === 'function') {
        // Wrap function updaters
        const originalUpdater = updater;
        updater = function(prevState) {
          const result = originalUpdater.call(this, prevState);
          
          if (result && result.logoUrl) {
            // Check if banner was recently removed
            const bannerRemovalState = localStorage.getItem('banner_removal_state');
            let wasRecentlyRemoved = false;
            
            if (bannerRemovalState) {
              try {
                const state = JSON.parse(bannerRemovalState);
                wasRecentlyRemoved = state.isRemoved && (Date.now() - state.timestamp) < 30000;
              } catch (e) {
                // Ignore parsing errors
              }
            }
            
            if (wasRecentlyRemoved) {
              console.log('[FixAgentTabLogoRestoration] Preventing logo restoration via function setState');
              return {
                ...result,
                logoUrl: ''
              };
            }
          }
          
          return result;
        };
      }
      
      return originalSetState.call(this, updater, callback);
    };
    
    console.log('[FixAgentTabLogoRestoration] React setState patched');
  }
}

// Function to patch React hooks directly
function patchReactHooks() {
  // Look for React hooks in the global scope
  if (window.React) {
    // Patch useState
    if (window.React.useState) {
      const originalUseState = window.React.useState;
      
      window.React.useState = function(initialState) {
        const [state, setState] = originalUseState.call(this, initialState);
        
        // Create a patched setState
        const patchedSetState = function(updater) {
          if (typeof updater === 'object' && updater.logoUrl) {
            // Check if banner was recently removed
            const bannerRemovalState = localStorage.getItem('banner_removal_state');
            let wasRecentlyRemoved = false;
            
            if (bannerRemovalState) {
              try {
                const state = JSON.parse(bannerRemovalState);
                wasRecentlyRemoved = state.isRemoved && (Date.now() - state.timestamp) < 30000;
              } catch (e) {
                // Ignore parsing errors
              }
            }
            
            if (wasRecentlyRemoved) {
              console.log('[FixAgentTabLogoRestoration] Preventing logo restoration via useState');
              updater = {
                ...updater,
                logoUrl: ''
              };
            }
          }
          
          return setState.call(this, updater);
        };
        
        return [state, patchedSetState];
      };
      
      console.log('[FixAgentTabLogoRestoration] React.useState patched');
    }
  }
}

// Function to directly patch form data updates
function patchFormDataUpdates() {
  // Look for form data setters in the global scope
  setTimeout(() => {
    try {
      // Find all React components that might have setFormData
      const allElements = document.querySelectorAll('*');
      
      for (let element of allElements) {
        // Check if this element has React fiber
        const fiber = element._reactInternalFiber || element._reactInternals;
        if (fiber) {
          let currentFiber = fiber;
          
          // Walk up the fiber tree to find components
          while (currentFiber) {
            if (currentFiber.stateNode && currentFiber.stateNode.setFormData) {
              const component = currentFiber.stateNode;
              const originalSetFormData = component.setFormData;
              
              component.setFormData = function(updater) {
                console.log('[FixAgentTabLogoRestoration] Intercepting setFormData call');
                
                if (typeof updater === 'function') {
                  const wrappedUpdater = (prev) => {
                    const result = updater(prev);
                    
                    // Check if banner was recently removed
                    const bannerRemovalState = localStorage.getItem('banner_removal_state');
                    let wasRecentlyRemoved = false;
                    
                    if (bannerRemovalState) {
                      try {
                        const state = JSON.parse(bannerRemovalState);
                        wasRecentlyRemoved = state.isRemoved && (Date.now() - state.timestamp) < 30000;
                      } catch (e) {
                        // Ignore parsing errors
                      }
                    }
                    
                    if (wasRecentlyRemoved && result.logoUrl) {
                      console.log('[FixAgentTabLogoRestoration] Preventing logo restoration in setFormData');
                      return {
                        ...result,
                        logoUrl: ''
                      };
                    }
                    
                    return result;
                  };
                  
                  return originalSetFormData.call(this, wrappedUpdater);
                } else {
                  // Direct object update
                  if (updater && updater.logoUrl) {
                    // Check if banner was recently removed
                    const bannerRemovalState = localStorage.getItem('banner_removal_state');
                    let wasRecentlyRemoved = false;
                    
                    if (bannerRemovalState) {
                      try {
                        const state = JSON.parse(bannerRemovalState);
                        wasRecentlyRemoved = state.isRemoved && (Date.now() - state.timestamp) < 30000;
                      } catch (e) {
                        // Ignore parsing errors
                      }
                    }
                    
                    if (wasRecentlyRemoved) {
                      console.log('[FixAgentTabLogoRestoration] Preventing logo restoration in direct setFormData');
                      updater = {
                        ...updater,
                        logoUrl: ''
                      };
                    }
                  }
                  
                  return originalSetFormData.call(this, updater);
                }
              };
              
              console.log('[FixAgentTabLogoRestoration] Patched setFormData on component');
            }
            
            currentFiber = currentFiber.child || currentFiber.sibling || currentFiber.return;
          }
        }
      }
    } catch (error) {
      console.error('[FixAgentTabLogoRestoration] Error patching form data updates:', error);
    }
  }, 3000);
}

// Function to apply all fixes
function applyFixes() {
  try {
    patchReactUseEffect();
    patchSetState();
    patchReactHooks();
    patchFormDataUpdates();
    
    console.log('[FixAgentTabLogoRestoration] All Agent Tab logo restoration fixes applied');
    
  } catch (error) {
    console.error('[FixAgentTabLogoRestoration] Error applying fixes:', error);
  }
}

// Apply fixes immediately
applyFixes();

// Apply fixes when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', applyFixes);
} else {
  setTimeout(applyFixes, 100);
}

// Reapply fixes when React components mount
setTimeout(applyFixes, 2000);
setTimeout(applyFixes, 5000);

console.log('[FixAgentTabLogoRestoration] Fix script loaded');

/**
 * Assistant Selection Prompt
 * 
 * Shown when no valid assistant context is available.
 * Provides a clean way to select an assistant instead of corrupting data.
 */

import React, { useState, useEffect } from 'react';
import { FaRobot, FaExclamationTriangle, FaPlus } from 'react-icons/fa';
import { assistantDataService } from '../services/assistantDataService';
import { useAssistantAware } from '../contexts/AssistantAwareContext';

const AssistantSelectionPrompt = ({ 
  attorney, 
  onAssistantSelected,
  reason = 'no_valid_assistant',
  className = ''
}) => {
  const [assistants, setAssistants] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const { setCurrentAssistant } = useAssistantAware();

  useEffect(() => {
    loadAssistants();
  }, [attorney?.id]);

  const loadAssistants = async () => {
    if (!attorney?.id) {
      setError('No attorney information available');
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      const assistantData = await assistantDataService.loadAssistantsForAttorney(attorney.id);
      setAssistants(assistantData || []);
      setError(null);
    } catch (err) {
      console.error('Error loading assistants:', err);
      setError('Failed to load assistants');
      setAssistants([]);
    } finally {
      setLoading(false);
    }
  };

  const handleSelectAssistant = (assistant) => {
    console.log('🎯 [AssistantSelectionPrompt] Assistant selected:', assistant.id);
    
    // Update the assistant context
    setCurrentAssistant(assistant.id);
    
    // Notify parent component
    if (onAssistantSelected) {
      onAssistantSelected(assistant);
    }
  };

  const getReasonMessage = () => {
    const messages = {
      no_valid_assistant: 'No valid assistant found for your account.',
      attorney_id_used: 'Invalid assistant configuration detected.',
      corrupted_data: 'Assistant data needs to be refreshed.',
      missing_context: 'Assistant context is missing.'
    };
    
    return messages[reason] || 'Please select an assistant to continue.';
  };

  if (loading) {
    return (
      <div className={`assistant-selection-prompt loading ${className}`}>
        <div className="prompt-content">
          <FaRobot className="prompt-icon spinning" />
          <h3>Loading Assistants...</h3>
          <p>Please wait while we load your available assistants.</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`assistant-selection-prompt error ${className}`}>
        <div className="prompt-content">
          <FaExclamationTriangle className="prompt-icon error" />
          <h3>Error Loading Assistants</h3>
          <p>{error}</p>
          <button 
            className="retry-button"
            onClick={loadAssistants}
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`assistant-selection-prompt ${className}`}>
      <div className="prompt-content">
        <FaRobot className="prompt-icon" />
        <h3>Select an Assistant</h3>
        <p className="reason-message">{getReasonMessage()}</p>
        
        {assistants.length > 0 ? (
          <div className="assistant-list">
            {assistants.map((assistant) => (
              <div 
                key={assistant.id}
                className="assistant-option"
                onClick={() => handleSelectAssistant(assistant)}
              >
                <div className="assistant-info">
                  <div className="assistant-name">
                    {assistant.assistant_name || assistant.name || 'Unnamed Assistant'}
                  </div>
                  <div className="assistant-id">
                    ID: {assistant.id}
                  </div>
                  {assistant.subdomain && (
                    <div className="assistant-subdomain">
                      URL: {assistant.subdomain}.legalscout.net
                    </div>
                  )}
                </div>
                <FaRobot className="assistant-icon" />
              </div>
            ))}
          </div>
        ) : (
          <div className="no-assistants">
            <p>No assistants found for your account.</p>
            <button className="create-assistant-button">
              <FaPlus /> Create New Assistant
            </button>
          </div>
        )}
      </div>
      
      <style jsx>{`
        .assistant-selection-prompt {
          display: flex;
          align-items: center;
          justify-content: center;
          min-height: 400px;
          padding: 2rem;
          background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
          border-radius: 12px;
          border: 2px dashed #cbd5e0;
        }
        
        .prompt-content {
          text-align: center;
          max-width: 500px;
          width: 100%;
        }
        
        .prompt-icon {
          font-size: 3rem;
          color: #4a90e2;
          margin-bottom: 1rem;
        }
        
        .prompt-icon.error {
          color: #e53e3e;
        }
        
        .prompt-icon.spinning {
          animation: spin 2s linear infinite;
        }
        
        @keyframes spin {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }
        
        .prompt-content h3 {
          font-size: 1.5rem;
          color: #2d3748;
          margin-bottom: 0.5rem;
        }
        
        .reason-message {
          color: #718096;
          margin-bottom: 2rem;
        }
        
        .assistant-list {
          display: flex;
          flex-direction: column;
          gap: 1rem;
        }
        
        .assistant-option {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 1rem;
          background: white;
          border: 2px solid #e2e8f0;
          border-radius: 8px;
          cursor: pointer;
          transition: all 0.2s ease;
        }
        
        .assistant-option:hover {
          border-color: #4a90e2;
          box-shadow: 0 4px 12px rgba(74, 144, 226, 0.15);
          transform: translateY(-2px);
        }
        
        .assistant-info {
          text-align: left;
        }
        
        .assistant-name {
          font-weight: 600;
          color: #2d3748;
          margin-bottom: 0.25rem;
        }
        
        .assistant-id {
          font-size: 0.875rem;
          color: #718096;
          font-family: monospace;
        }
        
        .assistant-subdomain {
          font-size: 0.875rem;
          color: #4a90e2;
          margin-top: 0.25rem;
        }
        
        .assistant-icon {
          font-size: 1.5rem;
          color: #4a90e2;
        }
        
        .no-assistants {
          padding: 2rem;
        }
        
        .create-assistant-button,
        .retry-button {
          display: inline-flex;
          align-items: center;
          gap: 0.5rem;
          padding: 0.75rem 1.5rem;
          background: #4a90e2;
          color: white;
          border: none;
          border-radius: 6px;
          cursor: pointer;
          font-weight: 500;
          transition: background-color 0.2s ease;
        }
        
        .create-assistant-button:hover,
        .retry-button:hover {
          background: #357abd;
        }
      `}</style>
    </div>
  );
};

export default AssistantSelectionPrompt;

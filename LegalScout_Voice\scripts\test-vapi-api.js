/**
 * Test Vapi API
 *
 * This script tests the Vapi API with the configured API key.
 * It makes direct API calls to Vapi endpoints to verify connectivity.
 */

import { config } from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, resolve } from 'path';
import fetch from 'node-fetch';

// Get current file path
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables from .env file
config({ path: resolve(__dirname, '../.env') });

// Get Vapi API keys from environment variables
const VAPI_PUBLIC_KEY = process.env.VITE_VAPI_PUBLIC_KEY || process.env.VAPI_PUBLIC_KEY || '6734febc-fc65-4669-93b0-929b31ff6564';
const VAPI_PRIVATE_KEY = process.env.VAPI_TOKEN || process.env.VAPI_SECRET_KEY || '6734febc-fc65-4669-93b0-929b31ff6564';

if (!VAPI_PUBLIC_KEY || !VAPI_PRIVATE_KEY) {
  console.error('❌ One or more Vapi API keys not found in environment variables');
  process.exit(1);
}

console.log(`🔑 Using Vapi Public API key: ${VAPI_PUBLIC_KEY.substring(0, 5)}...`);
console.log(`🔑 Using Vapi Private API key: ${VAPI_PRIVATE_KEY.substring(0, 5)}...`);

// Test Vapi API endpoints
async function testVapiApi() {
  console.log('🧪 Testing Vapi API endpoints...');

  // Define endpoints to test
  const endpoints = [
    { name: 'Assistants', url: 'https://api.vapi.ai/assistant' },
    { name: 'Calls', url: 'https://api.vapi.ai/call' },
    { name: 'Phone Numbers', url: 'https://api.vapi.ai/phone-number' },
    { name: 'Tools', url: 'https://api.vapi.ai/tool' }
  ];

  // Test with public key
  console.log('\n🔍 Testing with Public API Key:');
  await testEndpoints(endpoints, VAPI_PUBLIC_KEY);

  // Test with private key
  console.log('\n🔍 Testing with Private API Key:');
  await testEndpoints(endpoints, VAPI_PRIVATE_KEY);
}

// Test endpoints with a specific API key
async function testEndpoints(endpoints, apiKey) {
  // Test each endpoint
  for (const endpoint of endpoints) {
    console.log(`\n🔍 Testing ${endpoint.name} endpoint: ${endpoint.url}`);

    try {
      const response = await fetch(endpoint.url, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        console.log(`✅ ${endpoint.name} endpoint test successful`);
        console.log(`📊 Found ${Array.isArray(data) ? data.length : 1} items`);
        // Don't log the full response to keep the output clean
        // console.log(`📊 Response: ${JSON.stringify(data, null, 2)}`);
      } else {
        console.error(`❌ ${endpoint.name} endpoint test failed: ${response.status} ${response.statusText}`);

        // Try to get more details from the response
        try {
          const errorData = await response.text();
          console.error(`Error details: ${errorData}`);
        } catch (e) {
          // Ignore if we can't parse the response
        }
      }
    } catch (error) {
      console.error(`❌ ${endpoint.name} endpoint test error: ${error.message}`);
    }
  }
}

// Run the tests
testVapiApi().catch(error => {
  console.error(`❌ Error running tests: ${error.message}`);
  process.exit(1);
});

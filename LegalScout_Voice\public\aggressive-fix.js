/**
 * Aggressive Fix for Framer Motion Context Issues
 * 
 * This script runs before any other scripts and ensures that React.createContext
 * and all necessary context objects are available globally.
 */

(function() {
  console.log('[AggressiveFix] Applying aggressive fix for framer-motion context issues');

  // STEP 1: Create global React object if it doesn't exist
  if (typeof window.React === 'undefined') {
    console.log('[AggressiveFix] React not found, creating global object');
    window.React = {};
  }

  // STEP 2: Define createContext if it doesn't exist
  if (typeof window.React.createContext === 'undefined') {
    console.log('[AggressiveFix] createContext not found, creating polyfill');
    window.React.createContext = function(defaultValue) {
      console.log('[AggressiveFix] Using polyfill createContext');
      return {
        Provider: function(props) { return props.children || null; },
        Consumer: function(props) { return props.children ? props.children({}) : null; },
        displayName: 'MockContext',
        _currentValue: defaultValue,
        _currentValue2: defaultValue,
        _threadCount: 0,
        _defaultValue: defaultValue
      };
    };
  }

  // STEP 3: Define MotionConfigContext globally
  window.MotionConfigContext = {
    Provider: function(props) { return props.children || null; },
    Consumer: function(props) { return props.children ? props.children({}) : null; },
    displayName: 'MotionConfigContext',
    _currentValue: {},
    _currentValue2: {},
    _threadCount: 0,
    _defaultValue: {}
  };

  // STEP 4: Define LayoutGroupContext globally
  window.LayoutGroupContext = {
    Provider: function(props) { return props.children || null; },
    Consumer: function(props) { return props.children ? props.children({}) : null; },
    displayName: 'LayoutGroupContext',
    _currentValue: {},
    _currentValue2: {},
    _threadCount: 0,
    _defaultValue: {}
  };

  // STEP 5: Create mock modules for direct imports
  window.__framer_motion_LayoutGroupContext_mjs__ = {
    LayoutGroupContext: window.LayoutGroupContext,
    default: window.LayoutGroupContext
  };

  window.__framer_motion_MotionConfigContext_mjs__ = {
    MotionConfigContext: window.MotionConfigContext,
    default: window.MotionConfigContext
  };

  // STEP 6: Override the import function to intercept framer-motion imports
  const originalImport = window.import || function() {
    return Promise.reject(new Error('Import not supported'));
  };

  window.import = function(moduleId) {
    if (moduleId.includes('LayoutGroupContext')) {
      console.log('[AggressiveFix] Intercepted import for LayoutGroupContext:', moduleId);
      return Promise.resolve(window.__framer_motion_LayoutGroupContext_mjs__);
    }
    
    if (moduleId.includes('MotionConfigContext')) {
      console.log('[AggressiveFix] Intercepted import for MotionConfigContext:', moduleId);
      return Promise.resolve(window.__framer_motion_MotionConfigContext_mjs__);
    }
    
    return originalImport(moduleId);
  };

  // STEP 7: Override dynamic import
  if (typeof window.__vite__import === 'undefined') {
    window.__vite__import = function(moduleId) {
      if (moduleId.includes('LayoutGroupContext')) {
        console.log('[AggressiveFix] Intercepted __vite__import for LayoutGroupContext:', moduleId);
        return Promise.resolve(window.__framer_motion_LayoutGroupContext_mjs__);
      }
      
      if (moduleId.includes('MotionConfigContext')) {
        console.log('[AggressiveFix] Intercepted __vite__import for MotionConfigContext:', moduleId);
        return Promise.resolve(window.__framer_motion_MotionConfigContext_mjs__);
      }
      
      if (typeof originalImport === 'function') {
        return originalImport(moduleId);
      }
      
      return Promise.reject(new Error(`[AggressiveFix] Cannot import module: ${moduleId}`));
    };
  }

  // STEP 8: Intercept script loading to prevent framer-motion from loading
  const originalCreateElement = document.createElement;
  document.createElement = function(tagName) {
    const element = originalCreateElement.apply(document, arguments);
    if (tagName.toLowerCase() === 'script') {
      const originalSetAttribute = element.setAttribute;
      element.setAttribute = function(name, value) {
        if (name === 'src' && value && (
          value.includes('framer-motion') || 
          value.includes('LayoutGroupContext') || 
          value.includes('MotionConfigContext')
        )) {
          console.log('[AggressiveFix] Blocking script:', value);
          return;
        }
        return originalSetAttribute.apply(this, arguments);
      };
    }
    return element;
  };

  console.log('[AggressiveFix] Aggressive fix applied successfully');
})();

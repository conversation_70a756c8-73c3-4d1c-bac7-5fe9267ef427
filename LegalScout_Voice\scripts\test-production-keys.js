#!/usr/bin/env node

/**
 * Production-Specific Vapi Key Test
 * 
 * This script simulates the exact production environment to test key resolution.
 */

import fetch from 'node-fetch';

// Simulate production environment variables (what <PERSON><PERSON><PERSON> should have)
const PRODUCTION_ENV = {
  NODE_ENV: 'production',
  VITE_VAPI_PUBLIC_KEY: '310f0d43-27c2-47a5-a76d-e55171d024f7',
  VITE_VAPI_SECRET_KEY: '6734febc-fc65-4669-93b0-929b31ff6564',
  VAPI_TOKEN: '6734febc-fc65-4669-93b0-929b31ff6564',
  VAPI_SECRET_KEY: '6734febc-fc65-4669-93b0-929b31ff6564',
  VAPI_PUBLIC_KEY: '310f0d43-27c2-47a5-a76d-e55171d024f7'
};

// Simulate missing environment variables (what might be happening in production)
const BROKEN_ENV = {
  NODE_ENV: 'production'
  // Missing all VAPI keys
};

/**
 * Simulate the getEnvVar function from vapiConfig.js
 */
function simulateGetEnvVar(key, envVars = process.env) {
  // Try import.meta.env first (would be undefined in Node.js)
  // Try process.env
  if (envVars[key]) {
    return envVars[key];
  }
  // Try window (would be undefined in Node.js)
  return null;
}

/**
 * Simulate the vapiConfig.js key resolution logic
 */
function simulateVapiConfig(envVars = process.env) {
  console.log('\n🔧 Simulating vapiConfig.js key resolution...');
  
  const getEnvVar = (key) => simulateGetEnvVar(key, envVars);
  
  // Simulate the exact logic from vapiConfig.js
  const VAPI_PUBLIC_KEY = getEnvVar('VITE_VAPI_PUBLIC_KEY') || '310f0d43-27c2-47a5-a76d-e55171d024f7';
  const VAPI_SECRET_KEY = getEnvVar('VITE_VAPI_SECRET_KEY') ||
                         getEnvVar('VITE_VAPI_PRIVATE_KEY') ||
                         getEnvVar('VAPI_SECRET_KEY') ||
                         getEnvVar('VAPI_TOKEN') ||
                         getEnvVar('VAPI_PRIVATE_KEY') ||
                         '6734febc-fc65-4669-93b0-929b31ff6564';

  const FINAL_PUBLIC_KEY = VAPI_PUBLIC_KEY || '310f0d43-27c2-47a5-a76d-e55171d024f7';
  const FINAL_SECRET_KEY = VAPI_SECRET_KEY || '6734febc-fc65-4669-93b0-929b31ff6564';

  console.log('Environment variable resolution:');
  console.log(`  VITE_VAPI_PUBLIC_KEY: ${getEnvVar('VITE_VAPI_PUBLIC_KEY') ? 'SET' : 'NOT SET'}`);
  console.log(`  VITE_VAPI_SECRET_KEY: ${getEnvVar('VITE_VAPI_SECRET_KEY') ? 'SET' : 'NOT SET'}`);
  console.log(`  VAPI_SECRET_KEY: ${getEnvVar('VAPI_SECRET_KEY') ? 'SET' : 'NOT SET'}`);
  console.log(`  VAPI_TOKEN: ${getEnvVar('VAPI_TOKEN') ? 'SET' : 'NOT SET'}`);
  
  console.log('Final resolved keys:');
  console.log(`  Public Key: ${FINAL_PUBLIC_KEY ? FINAL_PUBLIC_KEY.substring(0, 8) + '...' : 'NONE'}`);
  console.log(`  Secret Key: ${FINAL_SECRET_KEY ? FINAL_SECRET_KEY.substring(0, 8) + '...' : 'NONE'}`);

  // Simulate getVapiApiKey function
  const getVapiApiKey = (operationType = 'client') => {
    if (operationType === 'server') {
      const key = FINAL_SECRET_KEY || FINAL_PUBLIC_KEY;
      console.log(`  getVapiApiKey('server') returning: ${key ? key.substring(0, 8) + '...' : 'NONE'}`);
      return key;
    }
    
    if (operationType === 'client') {
      const key = FINAL_PUBLIC_KEY;
      console.log(`  getVapiApiKey('client') returning: ${key ? key.substring(0, 8) + '...' : 'NONE'}`);
      return key;
    }
    
    return FINAL_PUBLIC_KEY;
  };

  return {
    publicKey: FINAL_PUBLIC_KEY,
    secretKey: FINAL_SECRET_KEY,
    getVapiApiKey
  };
}

/**
 * Test API call with specific key
 */
async function testApiCall(key, keyType, description) {
  console.log(`\n🧪 Testing ${description}`);
  console.log(`   Key: ${key ? key.substring(0, 8) + '...' : 'NONE'}`);
  
  if (!key) {
    console.log('   ❌ No key available');
    return { success: false, error: 'No key' };
  }
  
  try {
    const response = await fetch('https://api.vapi.ai/phone-number', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${key}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (response.ok) {
      const data = await response.json();
      console.log(`   ✅ Success! Found ${Array.isArray(data) ? data.length : 0} phone numbers`);
      return { success: true, data };
    } else {
      const errorText = await response.text();
      console.log(`   ❌ Failed: ${response.status} ${response.statusText}`);
      console.log(`   Error: ${errorText}`);
      return { success: false, status: response.status, error: errorText };
    }
  } catch (error) {
    console.log(`   ❌ Network error: ${error.message}`);
    return { success: false, error: error.message };
  }
}

/**
 * Main test function
 */
async function runProductionKeyTest() {
  console.log('🔍 Production Vapi Key Resolution Test');
  console.log('=' .repeat(50));
  
  // Test 1: Working production environment
  console.log('\n📋 Test 1: Simulating WORKING production environment');
  console.log('Environment variables properly set in Vercel');
  
  const workingConfig = simulateVapiConfig(PRODUCTION_ENV);
  const workingServerKey = workingConfig.getVapiApiKey('server');
  const workingClientKey = workingConfig.getVapiApiKey('client');
  
  await testApiCall(workingServerKey, 'SECRET', 'Working Production - Server Key');
  await testApiCall(workingClientKey, 'PUBLIC', 'Working Production - Client Key (should fail)');
  
  // Test 2: Broken production environment
  console.log('\n📋 Test 2: Simulating BROKEN production environment');
  console.log('Environment variables missing in Vercel (current issue)');
  
  const brokenConfig = simulateVapiConfig(BROKEN_ENV);
  const brokenServerKey = brokenConfig.getVapiApiKey('server');
  const brokenClientKey = brokenConfig.getVapiApiKey('client');
  
  await testApiCall(brokenServerKey, 'SECRET', 'Broken Production - Server Key (fallback)');
  await testApiCall(brokenClientKey, 'PUBLIC', 'Broken Production - Client Key (fallback, should fail)');
  
  // Test 3: Current local environment
  console.log('\n📋 Test 3: Current local environment');
  
  const localConfig = simulateVapiConfig(process.env);
  const localServerKey = localConfig.getVapiApiKey('server');
  const localClientKey = localConfig.getVapiApiKey('client');
  
  await testApiCall(localServerKey, 'SECRET', 'Local Environment - Server Key');
  await testApiCall(localClientKey, 'PUBLIC', 'Local Environment - Client Key (should fail)');
  
  // Analysis
  console.log('\n📊 Analysis and Recommendations');
  console.log('=' .repeat(50));
  
  console.log('\n🎯 Key Findings:');
  console.log('1. The vapiConfig.js fallback logic should work even without env vars');
  console.log('2. If production is failing, the issue is likely:');
  console.log('   a) Environment variables not set in Vercel');
  console.log('   b) Import/module resolution issues in production build');
  console.log('   c) Timing issues with when the config is loaded');
  
  console.log('\n💡 Immediate Actions:');
  console.log('1. Check Vercel Environment Variables:');
  console.log('   - VITE_VAPI_PUBLIC_KEY = 310f0d43-27c2-47a5-a76d-e55171d024f7');
  console.log('   - VITE_VAPI_SECRET_KEY = 6734febc-fc65-4669-93b0-929b31ff6564');
  
  console.log('\n2. Add production debugging:');
  console.log('   - Add console.log in vapiConfig.js to see what keys are resolved');
  console.log('   - Add console.log in vapiMcpService.js before API calls');
  
  console.log('\n3. Test the browser diagnostic tool:');
  console.log('   - Open /vapi-diagnostics.html in production');
  console.log('   - Run the diagnostic tests to see actual environment state');
  
  console.log('\n🔧 Next Steps:');
  console.log('1. Deploy the diagnostic tools to production');
  console.log('2. Run diagnostics in production browser');
  console.log('3. Check Vercel environment variable configuration');
  console.log('4. Add production-specific logging to identify the exact issue');
}

// Run the test
runProductionKeyTest().catch(error => {
  console.error('❌ Test failed:', error);
  process.exit(1);
});

#!/usr/bin/env node

/**
 * Test MCP Integration
 * 
 * This script tests the MCP integration with available servers
 * and demonstrates how they can enhance LegalScout Voice.
 */

import { mcpIntegrationService } from '../src/services/McpIntegrationService.js';
import { getEnabledServers } from '../src/config/mcp-servers.config.js';

console.log('🧪 Testing MCP Integration for LegalScout Voice...');

async function testMcpIntegration() {
  try {
    console.log('\n📋 Available MCP Servers:');
    const enabledServers = getEnabledServers();
    
    Object.entries(enabledServers).forEach(([name, config]) => {
      console.log(`✅ ${config.name}`);
      console.log(`   📝 ${config.description}`);
      console.log(`   🔧 Command: ${config.command} ${config.args.join(' ')}`);
      if (config.tools) {
        console.log(`   🛠️ Tools: ${config.tools.join(', ')}`);
      }
      console.log('');
    });

    console.log('🚀 Initializing MCP Integration Service...');
    const result = await mcpIntegrationService.initialize();
    
    console.log(`✅ Initialization complete: ${result.successful} successful, ${result.failed} failed`);
    
    // Test connection status
    console.log('\n📊 Connection Status:');
    const status = mcpIntegrationService.getConnectionStatus();
    Object.entries(status).forEach(([name, info]) => {
      console.log(`${info.connected ? '🟢' : '🔴'} ${name}: ${info.connected ? 'Connected' : 'Disconnected'}`);
    });

    // Test tool calls
    console.log('\n🔧 Testing Tool Calls...');
    
    // Test Vapi MCP Server
    if (status.vapi?.connected) {
      console.log('Testing Vapi MCP Server...');
      const vapiResult = await mcpIntegrationService.callTool('vapi', 'list_assistants', {});
      console.log('Vapi result:', vapiResult);
    }

    // Test Browser Tools MCP
    if (status.browserTools?.connected) {
      console.log('Testing Browser Tools MCP...');
      const browserResult = await mcpIntegrationService.callTool('browserTools', 'capture_screenshot', {
        url: 'https://legalscout.net',
        viewport: { width: 1200, height: 800 }
      });
      console.log('Browser Tools result:', browserResult);
    }

    // Test AI Meta MCP
    if (status.aiMeta?.connected) {
      console.log('Testing AI Meta MCP...');
      const aiMetaResult = await mcpIntegrationService.callTool('aiMeta', 'create_tool', {
        name: 'legal_research',
        description: 'Research legal precedents and case law'
      });
      console.log('AI Meta result:', aiMetaResult);
    }

    console.log('\n🎯 Legal Use Case Examples:');
    
    // Example 1: Legal Research Workflow
    console.log('\n📚 Example 1: Automated Legal Research');
    console.log('1. Use Browser Tools MCP to scrape legal databases');
    console.log('2. Use AI Meta MCP to create custom legal analysis tools');
    console.log('3. Use Vapi MCP to integrate findings into voice responses');
    
    // Example 2: Client Communication
    console.log('\n📧 Example 2: Attorney Notification System');
    console.log('1. Client completes voice consultation via Vapi');
    console.log('2. Supabase MCP stores consultation data');
    console.log('3. Gmail MCP sends notification to attorney');
    console.log('4. Slack MCP alerts legal team');
    
    // Example 3: Document Processing
    console.log('\n📄 Example 3: Legal Document Analysis');
    console.log('1. Client uploads legal document');
    console.log('2. PDF MCP extracts and analyzes content');
    console.log('3. AI Meta MCP creates custom analysis tools');
    console.log('4. Vapi MCP provides voice summary to client');

    console.log('\n🔮 Future Enhancements:');
    console.log('• Calendar integration for appointment scheduling');
    console.log('• Stripe integration for payment processing');
    console.log('• Knowledge graph for legal precedent tracking');
    console.log('• Advanced browser automation for court record retrieval');

    // Demonstrate integration with existing Vapi assistant
    console.log('\n🎤 Integration with Vapi Assistant:');
    console.log('Your assistant (f9b97d13-f9c4-40af-a660-62ba5925ff2a) can now:');
    console.log('• Access browser tools for real-time legal research');
    console.log('• Create custom tools for specific legal scenarios');
    console.log('• Integrate with external services via MCP');
    console.log('• Provide enhanced responses with rich data sources');

  } catch (error) {
    console.error('❌ MCP Integration test failed:', error.message);
  } finally {
    // Clean up
    await mcpIntegrationService.disconnect();
    console.log('\n🧹 Cleanup completed');
  }
}

// Demonstrate Fast-Agent integration potential
function demonstrateFastAgentIntegration() {
  console.log('\n🚀 Fast-Agent Integration Potential:');
  console.log('Based on https://fast-agent.ai capabilities:');
  console.log('');
  
  console.log('📋 Agent Workflow Examples:');
  console.log('1. Legal Consultation Agent:');
  console.log('   • Multi-step legal analysis');
  console.log('   • Document review and summarization');
  console.log('   • Precedent research and citation');
  console.log('');
  
  console.log('2. Attorney Notification Agent:');
  console.log('   • Monitor consultation queue');
  console.log('   • Prioritize urgent cases');
  console.log('   • Send contextual notifications');
  console.log('');
  
  console.log('3. Legal Research Agent:');
  console.log('   • Automated case law research');
  console.log('   • Cross-reference multiple databases');
  console.log('   • Generate research summaries');
  console.log('');
  
  console.log('🛠️ Implementation Steps:');
  console.log('1. Install fast-agent: pip install fast-agent-mcp');
  console.log('2. Create agent definitions for legal workflows');
  console.log('3. Integrate with existing Vapi assistants');
  console.log('4. Deploy agents as MCP servers');
  console.log('');
  
  console.log('📚 Resources:');
  console.log('• Fast-Agent Documentation: https://fast-agent.ai');
  console.log('• MCP State Transfer Guide: https://fast-agent.ai/mcp/state_transfer/');
  console.log('• Agent Prompting Guide: https://fast-agent.ai/agents/prompting/');
}

// Main execution
async function main() {
  console.log('🎯 MCP Integration Test for LegalScout Voice');
  console.log('Leveraging PulseMCP.com directory and Fast-Agent.ai framework\n');
  
  await testMcpIntegration();
  demonstrateFastAgentIntegration();
  
  console.log('\n🎉 MCP Integration test completed!');
  console.log('\n📝 Next Steps:');
  console.log('1. Configure environment variables for desired services');
  console.log('2. Enable additional MCP servers in the configuration');
  console.log('3. Integrate MCP tools with your Vapi assistant');
  console.log('4. Explore Fast-Agent framework for advanced workflows');
  console.log('5. Visit PulseMCP.com to discover more specialized servers');
}

// Run the test
main().catch(console.error);

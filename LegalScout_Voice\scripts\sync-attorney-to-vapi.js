/**
 * Sync Attorney Instructions to Vap<PERSON>
 * 
 * This script syncs the attorney's custom instructions from Supabase to their Vapi assistant
 */

const VAPI_API_URL = 'https://api.vapi.ai';
const VAPI_PRIVATE_KEY = '6734febc-fc65-4669-93b0-929b31ff6564';

// Attorney data from Supabase
const ATTORNEY_DATA = {
  id: "571390ac-5a83-46b2-ad3a-18b9cf39d701",
  subdomain: "damon",
  firm_name: "LegalScout",
  email: "<EMAIL>",
  vapi_assistant_id: "eb8533fa-902e-46be-8ce9-df20f5c550d7",
  vapi_instructions: "You will guide the user through jurrasic park",
  welcome_message: "Mr. <PERSON>, The phones are working."
};

async function updateVapiAssistant() {
  try {
    console.log('🔄 Syncing attorney instructions to Vap<PERSON> assistant...');
    console.log(`Assistant ID: ${ATTORNEY_DATA.vapi_assistant_id}`);
    console.log(`Instructions: "${ATTORNEY_DATA.vapi_instructions}"`);
    console.log(`Welcome Message: "${ATTORNEY_DATA.welcome_message}"`);
    
    // Create the update configuration
    const updateConfig = {
      name: `${ATTORNEY_DATA.firm_name} Assistant`,
      firstMessage: ATTORNEY_DATA.welcome_message,
      firstMessageMode: "assistant-speaks-first",
      model: {
        provider: "openai",
        model: "gpt-4o",
        messages: [
          {
            role: "system",
            content: ATTORNEY_DATA.vapi_instructions
          }
        ]
      },
      voice: {
        provider: "11labs",
        voiceId: "sarah",
        model: "eleven_turbo_v2_5"
      },
      transcriber: {
        provider: "deepgram",
        model: "nova-3"
      }
    };
    
    console.log('\n📤 Sending update to Vapi...');
    
    const response = await fetch(`${VAPI_API_URL}/assistant/${ATTORNEY_DATA.vapi_assistant_id}`, {
      method: 'PATCH',
      headers: {
        'Authorization': `Bearer ${VAPI_PRIVATE_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(updateConfig)
    });
    
    console.log(`Response status: ${response.status} ${response.statusText}`);
    
    if (response.ok) {
      const updatedAssistant = await response.json();
      console.log('\n✅ SUCCESS: Assistant updated successfully!');
      console.log(`Assistant Name: ${updatedAssistant.name}`);
      console.log(`First Message: ${updatedAssistant.firstMessage}`);
      console.log(`Instructions: ${updatedAssistant.model?.messages?.[0]?.content || 'Not visible in response'}`);
      
      return updatedAssistant;
    } else {
      const errorText = await response.text();
      console.log('\n❌ ERROR: Failed to update assistant');
      console.log(`Error: ${errorText}`);
      return null;
    }
    
  } catch (error) {
    console.error('\n💥 Exception occurred:', error);
    return null;
  }
}

async function verifyUpdate() {
  try {
    console.log('\n🔍 Verifying the update...');
    
    const response = await fetch(`${VAPI_API_URL}/assistant/${ATTORNEY_DATA.vapi_assistant_id}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${VAPI_PRIVATE_KEY}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (response.ok) {
      const assistant = await response.json();
      console.log('\n📋 Current Assistant State:');
      console.log(`ID: ${assistant.id}`);
      console.log(`Name: ${assistant.name}`);
      console.log(`First Message: ${assistant.firstMessage || 'Not set'}`);
      console.log(`Instructions: ${assistant.model?.messages?.[0]?.content || 'Not visible'}`);
      console.log(`Voice: ${assistant.voice?.provider} - ${assistant.voice?.voiceId}`);
      console.log(`Updated: ${assistant.updatedAt}`);
      
      return assistant;
    } else {
      console.log('❌ Failed to verify update');
      return null;
    }
    
  } catch (error) {
    console.error('Error verifying update:', error);
    return null;
  }
}

async function main() {
  console.log('🚀 Attorney-to-Vapi Sync Tool\n');
  console.log('This will sync your custom instructions from Supabase to your Vapi assistant.');
  console.log('═'.repeat(70));
  
  // Update the assistant
  const updateResult = await updateVapiAssistant();
  
  if (updateResult) {
    // Verify the update
    await verifyUpdate();
    
    console.log('\n🎉 Sync completed successfully!');
    console.log('Your custom instructions should now be active in your Vapi assistant.');
    console.log('\nNext steps:');
    console.log('1. Test your voice interface to confirm the custom instructions are working');
    console.log('2. Check that the welcome message is correct');
    console.log('3. The assistant should now guide users through Jurassic Park as instructed');
  } else {
    console.log('\n❌ Sync failed. Please check the error messages above.');
  }
}

// Handle fetch for Node.js
if (typeof fetch === 'undefined') {
  global.fetch = (await import('node-fetch')).default;
}

main().catch(error => {
  console.error('Script failed:', error);
  process.exit(1);
});

import React, { useEffect, useState, useRef, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { supabase } from '../lib/supabase-fixed';
import { processImageUrl } from '../utils/imageStorage';
import './Dashboard.css';
import CrmViewSelector from '../components/crm/CrmViewSelector';
import { mapDatabaseToPreview, mapPreviewToDatabase } from '../utils/configMapping';
import { createAttorneyPreviewConfig, syncAttorneyWithVapiAssistant } from '../utils/previewConfigHandler';
import { dashboardSyncCheck } from '../utils/vapiSyncChecker';
import { getAuthenticatedUserAttorneyData, getStrictAuthenticatedAssistantId } from '../utils/dynamicAssistantLoader';
import { runSystemStatusCheck } from '../utils/systemStatusChecker';
import VapiTestComponent from '../components/VapiTestComponent';
import VapiDiagnosticTest from '../components/VapiDiagnosticTest';
import { useAssistantAware } from '../contexts/AssistantAwareContext';

const Dashboard = () => {
  // Get current assistant context
  const { currentAssistant, isAssistantSelected } = useAssistantAware();

  const [attorney, setAttorney] = useState(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('profile');
  const [showPreview, setShowPreview] = useState(true);
  const [isDarkTheme, setIsDarkTheme] = useState(false);
  const [crmViewMode, setCrmViewMode] = useState('table'); // 'table', 'card', or 'map'
  const [showColumnModal, setShowColumnModal] = useState(false);
  const [showForwardingModal, setShowForwardingModal] = useState(false);
  const [copySuccess, setCopySuccess] = useState('');

  // Agent configuration state
  const [welcomeMessage, setWelcomeMessage] = useState('');
  const [informationGathering, setInformationGathering] = useState('');
  const [voiceId, setVoiceId] = useState('sarah');
  const [aiModel, setAiModel] = useState('gpt-4o');
  const [practiceAreas, setPracticeAreas] = useState([]);
  const [logoUrl, setLogoUrl] = useState('');
  const [buttonImageUrl, setButtonImageUrl] = useState('');
  const [uploadedImage, setUploadedImage] = useState(null);
  const [uploadedImageUrl, setUploadedImageUrl] = useState('');

  // Appearance configuration state
  const [primaryColor, setPrimaryColor] = useState('#4B74AA');
  const [secondaryColor, setSecondaryColor] = useState('#607D8B');
  const [buttonColor, setButtonColor] = useState('#D85722');
  const [backgroundColor, setBackgroundColor] = useState('#ffffff');
  const [backgroundOpacity, setBackgroundOpacity] = useState(0.9);
  const [buttonText, setButtonText] = useState('Start Consultation');
  const [buttonOpacity, setButtonOpacity] = useState(1);
  const [practiceAreaBackgroundOpacity, setPracticeAreaBackgroundOpacity] = useState(0.8);
  const [textBackgroundColor, setTextBackgroundColor] = useState('#634C38');

  // Contact information state
  const [attorneyAddress, setAttorneyAddress] = useState('');
  const [attorneyPhone, setAttorneyPhone] = useState('');
  const [schedulingLink, setSchedulingLink] = useState('');

  const iframeRef = useRef(null);
  const navigate = useNavigate();

  // State for tracking Vapi assistant data and changes
  const [vapiAssistantData, setVapiAssistantData] = useState(null);
  const [hasVapiChanges, setHasVapiChanges] = useState(false);
  const [vapiFieldsChanged, setVapiFieldsChanged] = useState({});
  const [isLoadingVapi, setIsLoadingVapi] = useState(false);

  // Function to fetch Vapi assistant data
  const fetchVapiAssistantData = async (assistantId) => {
    setIsLoadingVapi(true);
    try {
      // If no assistant ID is provided, check if we have an attorney to create one
      if (!assistantId) {
        if (!attorney) {
          console.log('No assistant ID and no attorney data available');
          return null;
        }

        console.log('No assistant ID provided, using existing assistant for authenticated user');

        // CRITICAL FIX: Use existing assistant ID instead of creating new ones
        const { data: { user } } = await supabase.auth.getUser();
        if (user?.email === '<EMAIL>') {
          const existingAssistantId = 'f9b97d13-f9c4-40af-a660-62ba5925ff2a';
          console.log('Using existing assistant <NAME_EMAIL>:', existingAssistantId);

          // Update attorney record with correct assistant ID
          const { error } = await supabase
            .from('attorneys')
            .update({ vapi_assistant_id: existingAssistantId })
            .eq('email', user.email);

          if (!error) {
            console.log('Updated attorney record with existing assistant ID');
          }

          return {
            id: existingAssistantId,
            name: attorney?.firm_name ? `${attorney.firm_name} Assistant` : 'LegalScout Assistant',
            isExistingAssistant: true
          };
        }

        // For other users, skip assistant creation for now
        console.log('Skipping assistant creation to prevent overrun');
        return null;

        if (newAssistant) {
          console.log('Created new assistant:', newAssistant.id);

          // Use the protected method to update attorney record with the new assistant ID
          // This method includes validation to prevent mock IDs from being saved
          const updatedAttorney = await vapiAssistantService.updateAttorneyAssistantId(attorney.id, newAssistant.id);

          if (updatedAttorney) {
            console.log('Updated attorney with new assistant ID:', newAssistant.id);

            // Update the attorney state with the new assistant ID
            setAttorney(prev => ({
              ...prev,
              vapi_assistant_id: newAssistant.id
            }));
          } else {
            console.error('Failed to update attorney with new assistant ID - this may be a mock assistant');
          }

          return newAssistant;
        }

        return null;
      }

      console.log('Fetching Vapi assistant data for ID:', assistantId);

      // Import the Vapi assistant service
      const { vapiAssistantService } = await import('../services/vapiAssistantService');

      // Get the assistant data
      const assistantData = await vapiAssistantService.getAssistant(assistantId);

      if (!assistantData) {
        console.log('No assistant found with ID:', assistantId);

        // If we have attorney data, create a new assistant
        if (attorney) {
          console.log('Creating a new assistant for attorney:', attorney.id);

          // Create a new assistant for the attorney
          const newAssistant = await vapiAssistantService.createAssistant({
            name: `${attorney.firm_name} Assistant`,
            instructions: attorney.vapi_instructions || 'You are a helpful legal assistant.',
            firstMessage: attorney.welcome_message || `Hello, I'm Scout from ${attorney.firm_name}. How can I help you today?`,
            firstMessageMode: "assistant-speaks-first",
            llm: {
              provider: attorney.ai_model?.includes('gpt') ? 'openai' : 'anthropic',
              model: attorney.ai_model || 'gpt-4o'
            },
            voice: {
              provider: '11labs',
              voiceId: attorney.voice_id || 'sarah'
            }
          });

          if (newAssistant) {
            console.log('Created new assistant:', newAssistant.id);

            // Use the protected method to update attorney record with the new assistant ID
            // This method includes validation to prevent mock IDs from being saved
            const updatedAttorney = await vapiAssistantService.updateAttorneyAssistantId(attorney.id, newAssistant.id);

            if (updatedAttorney) {
              console.log('Updated attorney with new assistant ID:', newAssistant.id);

              // Update the attorney state with the new assistant ID
              setAttorney(prev => ({
                ...prev,
                vapi_assistant_id: newAssistant.id
              }));
            } else {
              console.error('Failed to update attorney with new assistant ID - this may be a mock assistant');
            }

            return newAssistant;
          }
        }

        return null;
      }

      console.log('Vapi assistant data:', assistantData);
      return assistantData;
    } catch (error) {
      console.error('Error fetching Vapi assistant data:', error);
      return null;
    } finally {
      setIsLoadingVapi(false);
    }
  };

  // Function to create or update Vapi assistant
  const createOrUpdateVapiAssistant = async (attorneyData) => {
    if (!attorneyData) return null;

    try {
      console.log('Creating or updating Vapi assistant for attorney:', attorneyData.id);

      // Import the Vapi assistant service
      const { vapiAssistantService } = await import('../services/vapiAssistantService');

      // Use the syncAttorneyWithVapiAssistant utility function
      const syncedAttorney = await syncAttorneyWithVapiAssistant(attorneyData, vapiAssistantService);

      // If the assistant ID changed, update the attorney state
      if (syncedAttorney.vapi_assistant_id !== attorneyData.vapi_assistant_id) {
        console.log('Assistant ID changed, updating attorney state:', syncedAttorney.vapi_assistant_id);
        setAttorney(prev => ({
          ...prev,
          vapi_assistant_id: syncedAttorney.vapi_assistant_id
        }));
      }

      // Fetch the assistant data
      const assistantData = await fetchVapiAssistantData(syncedAttorney.vapi_assistant_id);
      return assistantData;
    } catch (error) {
      console.error('Error creating or updating Vapi assistant:', error);
      return null;
    }
  };

  // Load attorney data using robust state handler pattern
  useEffect(() => {
    const loadAttorneyData = async () => {
      // Set a timeout to prevent infinite loading
      const loadingTimeout = setTimeout(() => {
        console.warn('[Dashboard] Loading timeout reached, forcing loading to complete');
        setLoading(false);
      }, 10000); // 10 second timeout

      try {
        console.log('🎯 [Dashboard] Using Robust State Handler Pattern');
        console.log('🎯 [Dashboard] Timestamp:', new Date().toISOString());

        // Get authenticated user
        const { data: { user } } = await supabase.auth.getUser();
        if (!user?.email) {
          console.error('[Dashboard] ❌ No authenticated user found');
          navigate('/');
          return;
        }

        console.log('[Dashboard] ✅ Found authenticated user:', user.email);

        // Try robust state handler first, but don't fail if it doesn't work
        let attorneyData = null;
        let stateResult = null;

        if (typeof window.resolveAttorneyState === 'function') {
          console.log('[Dashboard] 🛡️ Trying robust state handler...');

          try {
            stateResult = await window.resolveAttorneyState(user.email);

            if (stateResult.success && stateResult.attorney) {
              console.log('[Dashboard] ✅ Attorney state resolved via robust handler:', {
                firm: stateResult.attorney.firm_name,
                email: stateResult.attorney.email,
                assistantId: stateResult.attorney.vapi_assistant_id,
                needsCreation: stateResult.needsCreation
              });

              attorneyData = stateResult.attorney;

              // Handle assistant state
              if (stateResult.needsCreation) {
                console.log('[Dashboard] 📝 Assistant creation needed');
                setVapiAssistantData(null);
              } else if (stateResult.selectedAssistant) {
                console.log('[Dashboard] ✅ Assistant loaded:', stateResult.selectedAssistant.id);
                setVapiAssistantData(stateResult.selectedAssistant);
              }
            } else {
              console.warn('[Dashboard] ⚠️ Robust state handler failed, will try fallback:', stateResult?.error);
            }
          } catch (error) {
            console.warn('[Dashboard] ⚠️ Robust state handler threw error, will try fallback:', error.message);
          }
        } else {
          console.warn('[Dashboard] ⚠️ Robust state handler not available');
        }

        // If robust handler didn't work, fall back to direct Supabase query
        if (!attorneyData) {
          console.log('[Dashboard] 🔄 Using direct Supabase fallback...');

          const { data: fallbackAttorneyData, error } = await supabase
            .from('attorneys')
            .select('*')
            .eq('email', user.email)
            .single();

          if (!error && fallbackAttorneyData) {
            console.log('[Dashboard] ✅ Attorney data loaded via Supabase fallback');
            attorneyData = fallbackAttorneyData;
          } else {
            console.error('[Dashboard] ❌ Error loading attorney data via fallback:', error);

            // Only redirect if we truly can't find the attorney
            if (error?.code === 'PGRST116') {
              console.error('[Dashboard] ❌ No attorney record found for user:', user.email);
              navigate('/');
              return;
            } else {
              console.warn('[Dashboard] ⚠️ Database error, but continuing with limited functionality');
              // Continue without attorney data - show a message to the user
            }
          }
        }

        // Set attorney data if we found it
        if (attorneyData) {
          setAttorney(attorneyData);
        }

        // Now load current configuration from Vapi (source of truth for UI)
        const currentAttorneyData = attorney || stateResult?.attorney;

        // FIXED: Use assistant-level data instead of attorney-level fields
        // Check if we have a current assistant selected in context
        if (currentAssistant?.id) {
          console.log('[Dashboard] 🔄 Loading current configuration from Vapi using assistant context...');

          try {
            // Fetch current assistant data from Vapi to populate UI
            const assistantData = await fetchVapiAssistantData(currentAssistant.id);

            if (assistantData) {
              console.log('[Dashboard] ✅ Loaded current Vapi configuration');
              setVapiAssistantData(assistantData);

              // Populate UI with current Vapi data (Vapi is source of truth)
              if (assistantData.firstMessage) {
                setWelcomeMessage(assistantData.firstMessage);
              }

              if (assistantData.voice?.voiceId) {
                console.log(`🎵 Setting voice from Vapi: ${assistantData.voice.voiceId}`);
                setVoiceId(assistantData.voice.voiceId);
              }

              if (assistantData.model?.model) {
                setAiModel(assistantData.model.model);
              }

            } else {
              console.warn('[Dashboard] ⚠️ Could not load Vapi configuration, using Supabase defaults');
            }

          } catch (error) {
            console.error('[Dashboard] ❌ Error loading Vapi configuration:', error);
          }
        }

        // Populate UI with attorney data (fallback values if Vapi data not loaded)
        if (currentAttorneyData) {
          // Use the mapping function to convert database fields to preview properties
          const previewConfig = mapDatabaseToPreview(currentAttorneyData);

          // Populate state variables with mapped data (only if not already set from Vapi)
          if (!welcomeMessage) {
            setWelcomeMessage(previewConfig.welcomeMessage || '');
          }
          setInformationGathering(previewConfig.informationGathering || '');

          // Voice and AI model - use Vapi data if available, otherwise Supabase defaults
          if (!voiceId || voiceId === 'sarah') {
            setVoiceId(currentAttorneyData.voice_id || 'echo'); // Default to echo
          }
          if (!aiModel) {
            setAiModel(currentAttorneyData.ai_model || 'gpt-4o');
          }

          setPracticeAreas(previewConfig.practiceAreas || []);
          setLogoUrl(previewConfig.logoUrl || '');
          setButtonImageUrl(previewConfig.buttonImageUrl || '');

          // Appearance settings
          setPrimaryColor(previewConfig.primaryColor || '#4B74AA');
          setSecondaryColor(previewConfig.secondaryColor || '#607D8B');
          setButtonColor(previewConfig.buttonColor || '#D85722');
          setBackgroundColor(previewConfig.backgroundColor || '#ffffff');
          setBackgroundOpacity(previewConfig.backgroundOpacity || 0.9);
          setButtonText(previewConfig.buttonText || 'Start Consultation');
          setButtonOpacity(previewConfig.buttonOpacity || 1);
          setPracticeAreaBackgroundOpacity(previewConfig.practiceAreaBackgroundOpacity || 0.8);
          setTextBackgroundColor(previewConfig.textBackgroundColor || '#634C38');

          // Contact information
          setAttorneyAddress(previewConfig.address || '');
          setAttorneyPhone(previewConfig.phone || '');
          setSchedulingLink(previewConfig.schedulingLink || '');
        }

      } catch (error) {
        console.error('[Dashboard] ❌ Error loading attorney data:', error);
        navigate('/');
      } finally {
        // Clear the timeout and set loading to false
        clearTimeout(loadingTimeout);
        setLoading(false);
      }
    };

    loadAttorneyData();
  }, [navigate]); // Only depend on navigate


  // Initialize preview when component mounts or activeTab changes
  useEffect(() => {
    if (!loading && activeTab !== 'consultations' && iframeRef.current) {
      // Small delay to ensure the attorney data is loaded
      const timer = setTimeout(() => {
        updatePreviewIframe();
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [loading, activeTab]); // Only depend on loading and activeTab

  // Function to send real-time updates to the preview iframe
  const sendPreviewUpdate = useCallback(() => {
    if (!iframeRef.current || !iframeRef.current.contentWindow || loading || activeTab === 'consultations' || !attorney) return;

    try {
      // Log the Vapi assistant ID being used for the preview
      console.log('Using Vapi assistant ID for real-time preview update:', attorney?.vapi_assistant_id);

      // Create attorney data object for preview configuration
      const attorneyData = {
        firm_name: attorney?.firm_name || 'Your Law Firm',
        title_text: attorney?.title_text || '',
        name: attorney?.name || 'Your Name',
        practice_areas: practiceAreas || [],
        state: attorney?.state || '',
        practice_description: attorney?.practice_description || 'Your AI legal assistant is ready to help',
        primary_color: primaryColor,
        secondary_color: secondaryColor,
        button_color: buttonColor,
        background_color: backgroundColor,
        background_opacity: backgroundOpacity,
        button_text: buttonText,
        button_opacity: buttonOpacity,
        practice_area_background_opacity: practiceAreaBackgroundOpacity,
        text_background_color: textBackgroundColor,
        welcome_message: welcomeMessage,
        information_gathering: informationGathering,
        logo_url: logoUrl,
        button_image: buttonImageUrl,
        profile_image: logoUrl,
        vapi_instructions: attorney?.vapi_instructions || '',
        vapi_context: attorney?.vapi_context || '',
        // Always use the attorney's Vapi assistant ID for the preview
        // This ensures the preview uses the same assistant as the live site
        vapi_assistant_id: attorney?.vapi_assistant_id || '',
        theme: isDarkTheme ? 'dark' : 'light',
        voice_id: voiceId,
        ai_model: aiModel
      };

      // Use our utility function to create the preview configuration
      const { previewConfig } = createAttorneyPreviewConfig(attorneyData);

      // Ensure the Vapi assistant ID is set
      if (attorney?.vapi_assistant_id) {
        previewConfig.vapi_assistant_id = attorney.vapi_assistant_id;
      }

      iframeRef.current.contentWindow.postMessage({
        type: 'UPDATE_PREVIEW_CONFIG',
        config: previewConfig
      }, '*');

      console.log('Sent real-time update to preview iframe using previewConfigHandler');
      console.log('Preview config sent:', {
        firmName: previewConfig.firmName,
        titleText: previewConfig.titleText,
        practiceDescription: previewConfig.practiceDescription
      });
    } catch (error) {
      console.error('Error sending real-time update to preview iframe:', error);
    }
  }, [
    loading,
    activeTab,
    attorney,
    primaryColor,
    secondaryColor,
    buttonColor,
    backgroundColor,
    backgroundOpacity,
    buttonText,
    buttonOpacity,
    practiceAreaBackgroundOpacity,
    textBackgroundColor,
    welcomeMessage,
    informationGathering,
    logoUrl,
    buttonImageUrl,
    isDarkTheme,
    practiceAreas,
    voiceId,
    aiModel
  ]);

  // Update preview when settings change
  useEffect(() => {
    // Skip during loading or if we're on the consultations tab
    if (loading || activeTab === 'consultations' || !attorney) return;

    // Send update to preview iframe
    sendPreviewUpdate();
  }, [
    loading,
    activeTab,
    attorney,
    primaryColor,
    secondaryColor,
    buttonColor,
    backgroundColor,
    backgroundOpacity,
    buttonText,
    buttonOpacity,
    practiceAreaBackgroundOpacity,
    textBackgroundColor,
    welcomeMessage,
    informationGathering,
    logoUrl,
    isDarkTheme,
    practiceAreas,
    voiceId,
    aiModel,
    buttonImageUrl,
    sendPreviewUpdate
  ]);

  const handleSignOut = async () => {
    try {
      // Sign out from Supabase
      await supabase.auth.signOut();

      // Clear local storage
      localStorage.removeItem('attorney');

      // Clear any other attorney-related data from localStorage
      localStorage.removeItem('attorney_id');
      localStorage.removeItem('attorney_version');

      // Redirect to the main site (not a subdomain)
      window.location.href = window.location.protocol + '//' + window.location.hostname + (window.location.port ? ':' + window.location.port : '');
    } catch (error) {
      console.error('Error signing out:', error);
      // Even if there's an error, still try to redirect to clear the state
      window.location.href = window.location.protocol + '//' + window.location.hostname + (window.location.port ? ':' + window.location.port : '');
    }
  };

  /**
   * Refresh the preview iframe with data directly from Supabase
   * This ensures the preview shows exactly what will be displayed on the subdomain site
   */
  const refreshPreviewFromSupabase = async () => {
    if (!iframeRef.current || !attorney?.subdomain) return;

    try {
      console.log('Refreshing preview with data from Supabase for subdomain:', attorney.subdomain);

      // Get the latest attorney data from Supabase
      const { data: latestAttorneyData, error } = await supabase
        .from('attorneys')
        .select('*')
        .eq('subdomain', attorney.subdomain)
        .single();

      if (error) {
        console.error('Error fetching latest attorney data:', error);
        return;
      }

      if (!latestAttorneyData) {
        console.error('No attorney data found for subdomain:', attorney.subdomain);
        return;
      }

      // Update the local attorney state with the latest data
      setAttorney(latestAttorneyData);

      // Update localStorage
      localStorage.setItem('attorney', JSON.stringify(latestAttorneyData));

      // Map database fields to preview properties
      const previewConfig = mapDatabaseToPreview(latestAttorneyData);

      // Update all the state variables with the latest data
      setPrimaryColor(previewConfig.primaryColor || '#4B74AA');
      setSecondaryColor(previewConfig.secondaryColor || '#607D8B');
      setButtonColor(previewConfig.buttonColor || '#D85722');
      setBackgroundColor(previewConfig.backgroundColor || '#ffffff');
      setBackgroundOpacity(previewConfig.backgroundOpacity || 0.9);
      setButtonText(previewConfig.buttonText || 'Start Consultation');
      setButtonOpacity(previewConfig.buttonOpacity || 1);
      setPracticeAreaBackgroundOpacity(previewConfig.practiceAreaBackgroundOpacity || 0.8);
      setTextBackgroundColor(previewConfig.textBackgroundColor || '#634C38');
      setWelcomeMessage(previewConfig.welcomeMessage || "Hello, I'm your legal assistant. How can I help you today?");
      setInformationGathering(previewConfig.informationGathering || "Tell me about your situation, and I'll help find the right solution for you.");
      setLogoUrl(previewConfig.logoUrl || '');
      setButtonImageUrl(previewConfig.buttonImageUrl || '');
      setPracticeAreas(previewConfig.practiceAreas || []);
      setAttorneyAddress(previewConfig.address || '');
      setAttorneyPhone(previewConfig.phone || '');
      setSchedulingLink(previewConfig.schedulingLink || '');
      setVoiceId(latestAttorneyData.voice_id || 'alloy');
      setAiModel(latestAttorneyData.ai_model || 'gpt-4o');

      // Check if attorney has a Vapi assistant ID and fetch the latest data
      if (latestAttorneyData.vapi_assistant_id) {
        console.log('Fetching latest Vapi assistant data for ID:', latestAttorneyData.vapi_assistant_id);
        const assistantData = await fetchVapiAssistantData(latestAttorneyData.vapi_assistant_id);

        if (assistantData) {
          console.log('Successfully fetched latest Vapi assistant data');
          setVapiAssistantData(assistantData);

          // Update form fields with data from Vapi if available
          if (assistantData.firstMessage) {
            setWelcomeMessage(assistantData.firstMessage);
          }

          if (assistantData.voice?.voiceId) {
            setVoiceId(assistantData.voice.voiceId);
          }

          if (assistantData.llm?.model) {
            setAiModel(assistantData.llm.model);
          }

          if (assistantData.instructions) {
            setAttorney(prev => ({
              ...prev,
              vapi_instructions: assistantData.instructions
            }));
          }
        } else {
          console.log('Failed to fetch latest Vapi assistant data');
        }
      } else {
        console.log('No Vapi assistant ID found in latest attorney data');
        setVapiAssistantData(null);
      }

      // Update the preview iframe with a special flag to load from Supabase
      const previewUrl = `/simple-preview?subdomain=${encodeURIComponent(attorney.subdomain)}&theme=${isDarkTheme ? 'dark' : 'light'}&loadFromSupabase=true`;
      iframeRef.current.src = previewUrl;

      console.log('Preview refreshed with latest data from Supabase');
    } catch (error) {
      console.error('Error refreshing preview from Supabase:', error);
    }
  };

  // Function to update Vapi assistant directly
  const updateVapiAssistant = async (assistantId, updatedFields) => {
    if (!assistantId) {
      // If no assistant ID is provided, create a new assistant
      if (!attorney) {
        throw new Error('No attorney data available to create assistant');
      }

      console.log('No assistant ID provided, creating a new assistant');

      // Import the Vapi assistant service
      const { vapiAssistantService } = await import('../services/vapiAssistantService');

      // Create a new assistant for the attorney
      const newAssistant = await vapiAssistantService.createAssistant({
        name: `${attorney.firm_name} Assistant`,
        instructions: attorney.vapi_instructions || 'You are a helpful legal assistant.',
        firstMessage: attorney.welcome_message || `Hello, I'm Scout from ${attorney.firm_name}. How can I help you today?`,
        firstMessageMode: "assistant-speaks-first",
        llm: {
          provider: attorney.ai_model?.includes('gpt') ? 'openai' : 'anthropic',
          model: attorney.ai_model || 'gpt-4o'
        },
        voice: {
          provider: '11labs',
          voiceId: attorney.voice_id || 'sarah'
        },
        ...updatedFields
      });

      if (newAssistant) {
        console.log('Created new assistant:', newAssistant.id);

        // Update the attorney record with the new assistant ID
        const { error } = await supabase
          .from('attorneys')
          .update({ vapi_assistant_id: newAssistant.id })
          .eq('id', attorney.id);

        if (error) {
          console.error('Error updating attorney with new assistant ID:', error);
        } else {
          console.log('Updated attorney with new assistant ID:', newAssistant.id);

          // Update the attorney state with the new assistant ID
          setAttorney(prev => ({
            ...prev,
            vapi_assistant_id: newAssistant.id
          }));
        }

        // Update the local state
        setVapiAssistantData(newAssistant);

        // Reset the changed fields
        setVapiFieldsChanged({});
        setHasVapiChanges(false);

        return newAssistant;
      }

      throw new Error('Failed to create new assistant');
    }

    try {
      console.log('Updating Vapi assistant directly:', assistantId);
      console.log('Updated fields:', updatedFields);

      // Import the Vapi assistant service
      const { vapiAssistantService } = await import('../services/vapiAssistantService');

      // Get the current assistant data
      const currentAssistant = await vapiAssistantService.getAssistant(assistantId);

      if (!currentAssistant) {
        console.log('Assistant not found, creating a new one');
        return updateVapiAssistant(null, updatedFields);
      }

      // Create update configuration with new settings
      const updateConfig = {
        ...currentAssistant,
        ...updatedFields
      };

      // Update the assistant
      const updatedAssistant = await vapiAssistantService.updateAssistant(assistantId, updateConfig);

      // Update the local state
      setVapiAssistantData(updatedAssistant);

      // Reset the changed fields
      setVapiFieldsChanged({});
      setHasVapiChanges(false);

      return updatedAssistant;
    } catch (error) {
      console.error('Error updating Vapi assistant directly:', error);
      throw error;
    }
  };

  // Import the VapiIntegration component
  const VapiIntegration = React.lazy(() => import('../components/dashboard/VapiIntegration'));

  // Function to handle changes to Vapi fields
  const handleVapiFieldChange = (field, value) => {
    // Track the changed field
    setVapiFieldsChanged(prev => ({
      ...prev,
      [field]: value
    }));

    // Set the flag indicating there are changes
    setHasVapiChanges(true);
  };

  const handleSaveChanges = async () => {
    try {
      setLoading(true);

      // Validate that we have a subdomain
      if (!attorney?.subdomain) {
        // Generate a subdomain if it doesn't exist
        const firmName = attorney?.firm_name || 'Your Law Firm';
        const generatedSubdomain = firmName
          .toLowerCase()
          .replace(/[^a-z0-9]/g, '-') // Replace non-alphanumeric with hyphens
          .replace(/-+/g, '-') // Replace multiple hyphens with a single one
          .replace(/^-|-$/g, ''); // Remove leading/trailing hyphens

        // Update the attorney object with the generated subdomain
        setAttorney(prev => ({
          ...prev,
          subdomain: generatedSubdomain
        }));

        // Alert the user
        alert(`A subdomain "${generatedSubdomain}" has been generated based on your firm name. You can change this in the Profile tab.`);

        // Return early to allow the user to continue with the generated subdomain
        setLoading(false);
        return;
      }

      // Create a preview config object with all the form fields
      const previewConfig = {
        firmName: attorney.firm_name,
        titleText: attorney.title_text,
        attorneyName: attorney.name,
        welcomeMessage: welcomeMessage,
        informationGathering: informationGathering,
        practiceAreas: practiceAreas,
        primaryColor: primaryColor,
        secondaryColor: secondaryColor,
        buttonColor: buttonColor,
        backgroundColor: backgroundColor,
        backgroundOpacity: backgroundOpacity,
        buttonText: buttonText,
        buttonOpacity: buttonOpacity,
        practiceAreaBackgroundOpacity: practiceAreaBackgroundOpacity,
        textBackgroundColor: textBackgroundColor,
        logoUrl: logoUrl,
        buttonImageUrl: buttonImageUrl,
        address: attorneyAddress,
        phone: attorneyPhone,
        schedulingLink: schedulingLink
      };

      // Map the preview config to database fields
      const dbConfig = mapPreviewToDatabase(previewConfig);

      // Add fields that aren't in the mapping
      dbConfig.voice_id = voiceId;
      dbConfig.ai_model = aiModel;
      dbConfig.office_address = attorneyAddress; // Include both for backward compatibility

      // Create updated attorney object with only the fields that exist in the database
      // Exclude the ID to avoid UUID format issues
      const updatedAttorney = {
        // Include all attorney properties except id
        ...Object.fromEntries(
          Object.entries(attorney).filter(([key]) => key !== 'id')
        ),
        // Add all the mapped fields
        ...dbConfig
      };

      console.log('Saving attorney data:', updatedAttorney);
      console.log('Attorney ID:', attorney.id);
      console.log('Attorney subdomain:', attorney.subdomain);

      // Since we're having issues with the ID, let's use the subdomain as the primary key
      // This is more reliable for development purposes
      let error;

      try {
        console.log('Using subdomain to find/update attorney:', attorney.subdomain);

        // First, check if an attorney with this subdomain exists
        const { data: existingAttorneys, error: findError } = await supabase
          .from('attorneys')
          .select('id')
          .eq('subdomain', attorney.subdomain);

        if (findError) {
          console.error('Error finding attorney by subdomain:', findError);
          error = findError;
        } else if (existingAttorneys && existingAttorneys.length > 0) {
          // Update the existing attorney by subdomain
          console.log('Found existing attorney with subdomain:', attorney.subdomain);

          // Create a clean object without the ID for the update
          const cleanUpdateObject = { ...updatedAttorney };
          delete cleanUpdateObject.id; // Remove the ID to avoid UUID errors

          // Ensure we're not sending any unexpected properties
          // Only include properties that match the database schema
          const safeUpdateObject = {
            firm_name: cleanUpdateObject.firm_name,
            title_text: cleanUpdateObject.title_text,
            name: cleanUpdateObject.name,
            subdomain: cleanUpdateObject.subdomain,
            practice_areas: cleanUpdateObject.practice_areas || [],
            practice_description: cleanUpdateObject.practice_description,
            primary_color: cleanUpdateObject.primary_color,
            secondary_color: cleanUpdateObject.secondary_color,
            button_color: cleanUpdateObject.button_color,
            background_color: cleanUpdateObject.background_color,
            background_opacity: cleanUpdateObject.background_opacity,
            button_text: cleanUpdateObject.button_text,
            button_opacity: cleanUpdateObject.button_opacity,
            practice_area_background_opacity: cleanUpdateObject.practice_area_background_opacity,
            text_background_color: cleanUpdateObject.text_background_color,
            logo_url: cleanUpdateObject.logo_url,
            profile_image: cleanUpdateObject.profile_image,
            button_image: cleanUpdateObject.button_image,
            welcome_message: cleanUpdateObject.welcome_message,
            information_gathering: cleanUpdateObject.information_gathering,
            vapi_instructions: cleanUpdateObject.vapi_instructions,
            vapi_context: cleanUpdateObject.vapi_context,
            voice_id: cleanUpdateObject.voice_id,
            ai_model: cleanUpdateObject.ai_model,
            office_address: cleanUpdateObject.office_address,
            phone: cleanUpdateObject.phone,
            scheduling_link: cleanUpdateObject.scheduling_link,
            email: cleanUpdateObject.email
          };

          console.log('Safe update object:', safeUpdateObject);

          const response = await supabase
            .from('attorneys')
            .update(safeUpdateObject)
            .eq('subdomain', attorney.subdomain);

          error = response.error;
          console.log('Update response:', response);

          // Sync attorney with Vapi assistant (create or update)
          if (!error) {
            try {
              // Import the Vapi assistant service
              const { vapiAssistantService } = await import('../services/vapiAssistantService');

              // Use our utility function to sync the attorney with Vapi
              const syncedAttorney = await syncAttorneyWithVapiAssistant({
                ...updatedAttorney,
                id: existingAttorneys[0].id // Use the existing ID
              }, vapiAssistantService);

              // If the assistant ID changed, update the attorney record
              if (syncedAttorney.vapi_assistant_id !== updatedAttorney.vapi_assistant_id) {
                console.log('Assistant ID changed, updating attorney record:', syncedAttorney.vapi_assistant_id);

                // Update the attorney record with the new assistant ID
                const assistantUpdateResponse = await supabase
                  .from('attorneys')
                  .update({ vapi_assistant_id: syncedAttorney.vapi_assistant_id })
                  .eq('subdomain', attorney.subdomain);

                if (assistantUpdateResponse.error) {
                  console.error('Error updating attorney with assistant ID:', assistantUpdateResponse.error);
                } else {
                  console.log('Updated attorney with new assistant ID:', syncedAttorney.vapi_assistant_id);
                  // Update the local attorney object with the assistant ID
                  updatedAttorney.vapi_assistant_id = syncedAttorney.vapi_assistant_id;
                }
              }

              // Fetch the updated assistant data
              const assistantData = await fetchVapiAssistantData(syncedAttorney.vapi_assistant_id);
              setVapiAssistantData(assistantData);

              // Reset Vapi changes
              setVapiFieldsChanged({});
              setHasVapiChanges(false);
            } catch (vapiError) {
              console.error('Error syncing attorney with Vapi assistant:', vapiError);
              // Don't fail the whole save operation if Vapi sync fails
            }
          }
        } else {
          // Create a new attorney record
          console.log('No attorney found with subdomain:', attorney.subdomain, 'Creating new record');

          // Create a clean object without the ID for the insert
          const cleanInsertObject = { ...updatedAttorney };
          delete cleanInsertObject.id; // Remove the ID to let Supabase generate a UUID

          // Ensure we're not sending any unexpected properties
          // Only include properties that match the database schema
          const safeInsertObject = {
            firm_name: cleanInsertObject.firm_name,
            title_text: cleanInsertObject.title_text,
            name: cleanInsertObject.name,
            subdomain: cleanInsertObject.subdomain,
            practice_areas: cleanInsertObject.practice_areas || [],
            practice_description: cleanInsertObject.practice_description,
            primary_color: cleanInsertObject.primary_color,
            secondary_color: cleanInsertObject.secondary_color,
            button_color: cleanInsertObject.button_color,
            background_color: cleanInsertObject.background_color,
            background_opacity: cleanInsertObject.background_opacity,
            button_text: cleanInsertObject.button_text,
            button_opacity: cleanInsertObject.button_opacity,
            practice_area_background_opacity: cleanInsertObject.practice_area_background_opacity,
            text_background_color: cleanInsertObject.text_background_color,
            logo_url: cleanInsertObject.logo_url,
            profile_image: cleanInsertObject.profile_image,
            button_image: cleanInsertObject.button_image,
            welcome_message: cleanInsertObject.welcome_message,
            information_gathering: cleanInsertObject.information_gathering,
            vapi_instructions: cleanInsertObject.vapi_instructions,
            vapi_context: cleanInsertObject.vapi_context,
            voice_id: cleanInsertObject.voice_id,
            ai_model: cleanInsertObject.ai_model,
            office_address: cleanInsertObject.office_address,
            phone: cleanInsertObject.phone,
            scheduling_link: cleanInsertObject.scheduling_link,
            email: cleanInsertObject.email
          };

          console.log('Safe insert object:', safeInsertObject);

          const response = await supabase
            .from('attorneys')
            .insert([safeInsertObject]);

          error = response.error;
          console.log('Insert response:', response);

          // Sync attorney with Vapi assistant (create or update)
          if (!error) {
            try {
              // Import the Vapi assistant service
              const { vapiAssistantService } = await import('../services/vapiAssistantService');

              // Use our utility function to sync the attorney with Vapi
              const syncedAttorney = await syncAttorneyWithVapiAssistant({
                ...safeInsertObject,
                id: response.data[0].id // Use the new ID from the insert response
              }, vapiAssistantService);

              // Update the attorney record with the new assistant ID
              if (syncedAttorney.vapi_assistant_id) {
                const assistantUpdateResponse = await supabase
                  .from('attorneys')
                  .update({ vapi_assistant_id: syncedAttorney.vapi_assistant_id })
                  .eq('subdomain', attorney.subdomain);

                if (assistantUpdateResponse.error) {
                  console.error('Error updating attorney with assistant ID:', assistantUpdateResponse.error);
                } else {
                  console.log('Updated attorney with new assistant ID:', syncedAttorney.vapi_assistant_id);
                  // Update the local attorney object with the assistant ID
                  updatedAttorney.vapi_assistant_id = syncedAttorney.vapi_assistant_id;
                }
              }

              // Fetch the updated assistant data
              const assistantData = await fetchVapiAssistantData(syncedAttorney.vapi_assistant_id);
              setVapiAssistantData(assistantData);

              // Reset Vapi changes
              setVapiFieldsChanged({});
              setHasVapiChanges(false);
            } catch (vapiError) {
              console.error('Error syncing attorney with Vapi assistant:', vapiError);
              // Don't fail the whole save operation if Vapi sync fails
            }
          }
        }
      } catch (updateError) {
        console.error('Error during update process:', updateError);
        error = updateError;
      }

      if (error) throw error;

      // Update local storage
      localStorage.setItem('attorney', JSON.stringify(updatedAttorney));

      // Update state
      setAttorney(updatedAttorney);

      // Refresh the preview with data directly from Supabase
      // This ensures the preview shows exactly what will be displayed on the subdomain site
      await refreshPreviewFromSupabase();

      alert('Changes saved successfully! Your agent configuration has been updated in Supabase and will be used when clients visit your subdomain.');
    } catch (error) {
      console.error('Error saving changes:', error);

      // Create a more detailed error message
      let errorMessage = 'An unknown error occurred.';

      if (error.message) {
        errorMessage = error.message;
      }

      if (error.code) {
        errorMessage += ` (Code: ${error.code})`;
      }

      if (error.details) {
        errorMessage += `\nDetails: ${error.details}`;
      }

      if (error.hint) {
        errorMessage += `\nHint: ${error.hint}`;
      }

      alert(`Error saving changes: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  };

  const handleResetDefaults = () => {
    // Reset to default values
    setWelcomeMessage("Hello, I'm your legal assistant. How can I help you today?");
    setInformationGathering("Tell me about your situation, and I'll help find the right solution for you.");
    setVoiceId('alloy'); // OpenAI default voice
    setAiModel('gpt-4o');
    setPrimaryColor('#4B74AA');
    setSecondaryColor('#607D8B');
    setButtonColor('#D85722');
    setBackgroundColor('#ffffff');
    setBackgroundOpacity(0.9);
    setButtonText('Start Consultation');
    setButtonOpacity(1);
    setPracticeAreaBackgroundOpacity(0.8);
    setTextBackgroundColor('#634C38');
    setLogoUrl('');
    setButtonImageUrl('');
  };

  // Handle file upload for logo
  const handleLogoUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        // Store the image data as a data URL
        setLogoUrl(reader.result);
        // Update preview after logo is loaded
        setTimeout(sendPreviewUpdate, 100);
      };
      reader.readAsDataURL(file);
    }
  };

  // Handle logo removal
  const handleRemoveLogo = async () => {
    setLogoUrl('');
    // Reset the file input to allow re-uploading the same file
    const fileInput = document.getElementById('logo-upload');
    if (fileInput) {
      fileInput.value = '';
    }

    // Save the change to Supabase immediately
    if (attorney?.subdomain) {
      try {
        const response = await supabase
          .from('attorneys')
          .update({
            logo_url: '',
            profile_image: '' // Also clear profile_image since it's often used as a fallback
          })
          .eq('subdomain', attorney.subdomain);

        if (response.error) {
          console.error('Error removing banner from database:', response.error);
          alert('Error removing banner. Please try again.');
          return;
        }

        // Update the attorney object in state
        setAttorney(prev => ({
          ...prev,
          logo_url: '',
          profile_image: ''
        }));

        console.log('Banner removed successfully from database');
      } catch (error) {
        console.error('Error removing banner:', error);
        alert('Error removing banner. Please try again.');
        return;
      }
    }

    // Update preview after logo is removed
    setTimeout(sendPreviewUpdate, 0);
  };

  // Handle button image removal
  const handleRemoveButtonImage = async () => {
    setButtonImageUrl('');
    // Reset the file input to allow re-uploading the same file
    const fileInput = document.getElementById('button-image-upload');
    if (fileInput) {
      fileInput.value = '';
    }

    // Save the change to Supabase immediately
    if (attorney?.subdomain) {
      try {
        const response = await supabase
          .from('attorneys')
          .update({
            button_image: ''
          })
          .eq('subdomain', attorney.subdomain);

        if (response.error) {
          console.error('Error removing button image from database:', response.error);
          alert('Error removing button image. Please try again.');
          return;
        }

        // Update the attorney object in state
        setAttorney(prev => ({
          ...prev,
          button_image: ''
        }));

        console.log('Button image removed successfully from database');
      } catch (error) {
        console.error('Error removing button image:', error);
        alert('Error removing button image. Please try again.');
        return;
      }
    }

    // Update preview after button image is removed
    setTimeout(sendPreviewUpdate, 0);
  };

  if (loading) {
    return (
      <div className="dashboard-container">
        <div className="loading-spinner"></div>
        <p>Loading dashboard...</p>
      </div>
    );
  }

  // Show a helpful message if we couldn't load attorney data
  if (!attorney) {
    return (
      <div className="dashboard-container">
        <div className="dashboard-header">
          <div className="header-logo">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M12 2L2 7l10 5 10-5-10-5z"></path>
              <path d="M2 17l10 5 10-5"></path>
              <path d="M2 12l10 5 10-5"></path>
            </svg>
            <h1>Attorney Dashboard</h1>
          </div>
          <div className="header-actions">
            <button className="sign-out-button" onClick={handleSignOut}>
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
                <polyline points="16 17 21 12 16 7"></polyline>
                <line x1="21" y1="12" x2="9" y2="12"></line>
              </svg>
              Sign Out
            </button>
          </div>
        </div>
        <div className="dashboard-content">
          <div className="dashboard-card">
            <h2>Welcome to LegalScout</h2>
            <p>We're setting up your attorney profile. This may take a moment.</p>
            <div className="setup-status">
              <div className="status-item">
                <span className="status-icon">⏳</span>
                <span>Setting up your account...</span>
              </div>
              <div className="status-item">
                <span className="status-icon">🔧</span>
                <span>Configuring voice assistant...</span>
              </div>
            </div>
            <button
              className="dashboard-button"
              onClick={() => window.location.reload()}
              style={{ marginTop: '1rem' }}
            >
              Refresh Page
            </button>
            <p style={{ marginTop: '1rem', fontSize: '0.9rem', color: '#666' }}>
              If this issue persists, please contact support.
            </p>
          </div>
        </div>
      </div>
    );
  }

  // Toggle theme function
  const toggleTheme = () => {
    setIsDarkTheme(prev => !prev);
  };

  // Function to update the preview iframe with current settings
  const updatePreviewIframe = (useEnhancedPreview = false) => {
    if (!iframeRef.current || !attorney) return;

    try {
      // Log the Vapi assistant ID being used for the preview
      console.log('Using Vapi assistant ID for preview:', attorney?.vapi_assistant_id);

      // Build URL with minimal settings to avoid URI_TOO_LONG errors
      const previewUrl = `/simple-preview?subdomain=${encodeURIComponent(attorney?.subdomain || 'default')}&theme=${isDarkTheme ? 'dark' : 'light'}&useEnhancedPreview=${useEnhancedPreview}`;

      // Update iframe src
      iframeRef.current.src = previewUrl;

      // After iframe loads, send the complete config via postMessage
      iframeRef.current.onload = () => {
        try {
          if (!iframeRef.current || !iframeRef.current.contentWindow) return;

          // Create attorney data object for preview configuration
          const attorneyData = {
            firm_name: attorney?.firm_name || 'Your Law Firm',
            title_text: attorney?.title_text || '',
            name: attorney?.name || 'Your Name',
            practice_areas: practiceAreas || [],
            state: attorney?.state || '',
            practice_description: attorney?.practice_description || 'Your AI legal assistant is ready to help',
            primary_color: primaryColor,
            secondary_color: secondaryColor,
            button_color: buttonColor,
            background_color: backgroundColor,
            background_opacity: backgroundOpacity,
            button_text: buttonText,
            button_opacity: buttonOpacity,
            practice_area_background_opacity: practiceAreaBackgroundOpacity,
            text_background_color: textBackgroundColor,
            welcome_message: welcomeMessage,
            information_gathering: informationGathering,
            logo_url: logoUrl,
            button_image: buttonImageUrl,
            profile_image: logoUrl,
            vapi_instructions: attorney?.vapi_instructions || '',
            vapi_context: attorney?.vapi_context || '',
            // Always use the attorney's Vapi assistant ID for the preview
            // This ensures the preview uses the same assistant as the live site
            vapi_assistant_id: attorney?.vapi_assistant_id || '',
            theme: isDarkTheme ? 'dark' : 'light',
            voice_id: voiceId,
            ai_model: aiModel
          };

          // Use our utility function to create the preview configuration
          const { previewConfig } = createAttorneyPreviewConfig(attorneyData);

          // Add the useEnhancedPreview flag
          previewConfig.useEnhancedPreview = useEnhancedPreview;

          // Ensure the Vapi assistant ID is set
          if (attorney?.vapi_assistant_id) {
            previewConfig.vapi_assistant_id = attorney.vapi_assistant_id;
          }

          // Send the complete config via postMessage
          iframeRef.current.contentWindow.postMessage({
            type: 'UPDATE_PREVIEW_CONFIG',
            config: previewConfig
          }, '*');

          console.log('Sent complete config to preview iframe via postMessage using previewConfigHandler');
        } catch (error) {
          console.error('Error sending config via postMessage:', error);
        }
      };

      console.log('Updated preview iframe with URL:', previewUrl);
    } catch (error) {
      console.error('Error updating preview iframe:', error);
    }
  };

  // Reload iframe function
  const reloadIframe = () => {
    if (iframeRef.current) {
      iframeRef.current.src = iframeRef.current.src;
    }
  };

  // Copy text to clipboard function
  const copyToClipboard = (text, type) => {
    navigator.clipboard.writeText(text)
      .then(() => {
        setCopySuccess(`${type} copied!`);
        setTimeout(() => setCopySuccess(''), 2000);
      })
      .catch(err => {
        console.error('Failed to copy: ', err);
        setCopySuccess('Failed to copy');
      });
  };

  return (
    <div className={`dashboard-container ${activeTab !== 'agent' ? 'no-preview' : ''}`} data-theme={isDarkTheme ? 'dark' : 'light'}>
      <div className="dashboard-header">
        <div className="header-logo">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M12 2L2 7l10 5 10-5-10-5z"></path>
            <path d="M2 17l10 5 10-5"></path>
            <path d="M2 12l10 5 10-5"></path>
          </svg>
          <h1>Attorney Dashboard</h1>
        </div>
        <div className="header-actions">
          <button className="sign-out-button" onClick={handleSignOut}>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
              <polyline points="16 17 21 12 16 7"></polyline>
              <line x1="21" y1="12" x2="9" y2="12"></line>
            </svg>
            Sign Out
          </button>
        </div>
      </div>

      {/* Config Panel */}
      <div className={`config-panel ${showPreview ? '' : 'preview-active'}`}>
        {/* Config Tabs */}
        <div className="config-tabs">
          <button
            className={`config-tab ${activeTab === 'profile' ? 'active' : ''}`}
            onClick={() => setActiveTab('profile')}
          >
            <div className="tab-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                <circle cx="12" cy="7" r="4"></circle>
              </svg>
            </div>
            Profile
          </button>
          <button
            className={`config-tab ${activeTab === 'agent' ? 'active' : ''}`}
            onClick={() => setActiveTab('agent')}
          >
            <div className="tab-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z"></path>
                <path d="M19 10v2a7 7 0 0 1-14 0v-2"></path>
                <line x1="12" y1="19" x2="12" y2="23"></line>
                <line x1="8" y1="23" x2="16" y2="23"></line>
              </svg>
            </div>
            Agent
          </button>
          <button
            className={`config-tab ${activeTab === 'consultations' ? 'active' : ''}`}
            onClick={() => setActiveTab('consultations')}
          >
            <div className="tab-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                <line x1="3" y1="9" x2="21" y2="9"></line>
                <line x1="3" y1="15" x2="21" y2="15"></line>
                <line x1="12" y1="9" x2="12" y2="21"></line>
              </svg>
            </div>
            Consultations
          </button>
          <button
            className={`config-tab ${activeTab === 'share' ? 'active' : ''}`}
            onClick={() => setActiveTab('share')}
          >
            <div className="tab-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <circle cx="18" cy="5" r="3"></circle>
                <circle cx="6" cy="12" r="3"></circle>
                <circle cx="18" cy="19" r="3"></circle>
                <line x1="8.59" y1="13.51" x2="15.42" y2="17.49"></line>
                <line x1="15.41" y1="6.51" x2="8.59" y2="10.49"></line>
              </svg>
            </div>
            Share
          </button>
          <button
            className={`config-tab ${activeTab === 'settings' ? 'active' : ''}`}
            onClick={() => setActiveTab('settings')}
          >
            <div className="tab-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <circle cx="12" cy="12" r="3"></circle>
                <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
              </svg>
            </div>
            Settings
          </button>
        </div>

        {/* Tab Content */}
        <div className="tab-content">
          {/* Profile Tab */}
          {activeTab === 'profile' && (
            <div>
              <div className="dashboard-card">
                <h2>Your Profile</h2>
                {attorney && (
                  <div className="profile-details">
                    <div className="form-group">
                      <label>Firm Name</label>
                      <input
                        type="text"
                        className="form-control"
                        value={attorney.firm_name || ''}
                        onChange={(e) => {
                          setAttorney({...attorney, firm_name: e.target.value});
                          // Update preview when firm name changes
                          setTimeout(sendPreviewUpdate, 0);
                        }}
                      />
                    </div>
                    <div className="form-group">
                      <label>Subdomain</label>
                      <div className="input-group">
                        <input
                          type="text"
                          className="form-control"
                          value={attorney.subdomain || ''}
                          onChange={(e) => {
                            setAttorney({...attorney, subdomain: e.target.value});
                            // Update preview when subdomain changes
                            setTimeout(sendPreviewUpdate, 0);
                          }}
                        />
                        <span className="input-group-text">.legalscout.net</span>
                      </div>
                    </div>
                    <div className="form-group">
                      <label>Email</label>
                      <input type="email" className="form-control" value={attorney.email || ''} readOnly />
                      <small className="form-text">Email cannot be changed</small>
                    </div>
                    <div className="form-group">
                      <label>Created</label>
                      <input type="text" className="form-control" value={attorney.created_at ? new Date(attorney.created_at).toLocaleDateString() : ''} readOnly />
                    </div>
                  </div>
                )}
              </div>

              {/* Contact Information Card - Moved from Agent tab */}
              <div className="dashboard-card">
                <h2>Contact Information</h2>
                <div className="form-group">
                  <label>Office Address</label>
                  <input
                    type="text"
                    className="form-control"
                    value={attorneyAddress}
                    onChange={(e) => setAttorneyAddress(e.target.value)}
                    placeholder="123 Legal Street, City, State ZIP"
                  />
                </div>

                <div className="form-group">
                  <label>Phone Number</label>
                  <input
                    type="tel"
                    className="form-control"
                    value={attorneyPhone}
                    onChange={(e) => setAttorneyPhone(e.target.value)}
                    placeholder="(*************"
                  />
                </div>

                <div className="form-group">
                  <label>Scheduling Link</label>
                  <input
                    type="url"
                    className="form-control"
                    value={schedulingLink}
                    onChange={(e) => setSchedulingLink(e.target.value)}
                    placeholder="https://calendly.com/your-link"
                  />
                  <small className="form-text">Link to your online scheduling system (Calendly, Acuity, etc.)</small>
                </div>
              </div>

              <div className="dashboard-card">
                <h2>Vapi Assistant Status</h2>
                {isLoadingVapi ? (
                  <div className="loading-spinner"></div>
                ) : vapiAssistantData ? (
                  <>
                    <div className="status-item">
                      <span className="status-label">ID:</span>
                      <span className="status-value">{attorney?.vapi_assistant_id}</span>
                    </div>
                    <div className="status-item">
                      <span className="status-label">Name:</span>
                      <span className="status-value">{vapiAssistantData.name}</span>
                    </div>
                    <div className="status-item">
                      <span className="status-label">Created:</span>
                      <span className="status-value">{new Date(vapiAssistantData.createdAt).toLocaleString()}</span>
                    </div>
                    <div className="status-item">
                      <span className="status-label">Last Updated:</span>
                      <span className="status-value">{new Date(vapiAssistantData.updatedAt).toLocaleString()}</span>
                    </div>
                    <div className="status-item">
                      <span className="status-label">Model:</span>
                      <span className="status-value">{vapiAssistantData.llm?.provider} / {vapiAssistantData.llm?.model}</span>
                    </div>
                    <div className="status-item">
                      <span className="status-label">Voice:</span>
                      <span className="status-value">{vapiAssistantData.voice?.provider} / {vapiAssistantData.voice?.voiceId?.split('/').pop()?.replace('.json', '')}</span>
                    </div>
                  </>
                ) : (
                  <>
                    <p>No Vapi assistant found or unable to load assistant data.</p>
                    <button
                      className="dashboard-button"
                      onClick={() => fetchVapiAssistantData(attorney?.vapi_assistant_id)}
                      disabled={isLoadingVapi}
                    >
                      {isLoadingVapi ? 'Loading...' : 'Refresh Assistant Data'}
                    </button>
                    {!attorney?.vapi_assistant_id && (
                      <button
                        className="dashboard-button"
                        onClick={() => updateVapiAssistant(null, {})}
                        disabled={isLoadingVapi}
                        style={{ marginLeft: '0.5rem' }}
                      >
                        {isLoadingVapi ? 'Creating...' : 'Create New Assistant'}
                      </button>
                    )}
                  </>
                )}
              </div>

              <div className="action-buttons">
                <button
                  className="dashboard-button"
                  onClick={handleSaveChanges}
                  disabled={loading}
                >
                  {loading ? 'Saving...' : 'Save Changes'}
                </button>
              </div>
            </div>
          )}

          {/* Agent Config Tab */}
          {activeTab === 'agent' && (
            <div>
              {/* AI Assistant Configuration Card */}
              <div className="dashboard-card">
                <h2>AI Assistant Configuration</h2>

                {/* Vapi Assistant ID and Status */}
                <div className="form-group">
                  <label>Assistant ID</label>
                  <div className="input-group">
                    <input
                      type="text"
                      className="form-control"
                      value={attorney?.vapi_assistant_id || 'Not configured'}
                      readOnly
                    />
                    {isLoadingVapi && (
                      <div className="input-group-append">
                        <span className="input-group-text">
                          <div className="loading-spinner-small"></div>
                        </span>
                      </div>
                    )}
                  </div>
                  <small className="form-text">
                    {vapiAssistantData ?
                      `Assistant "${vapiAssistantData.name}" is configured and ready to use.` :
                      'No assistant configured yet.'}
                  </small>
                  {(!attorney?.vapi_assistant_id || attorney.vapi_assistant_id.startsWith('mock-')) && (
                    <button
                      className="dashboard-button"
                      onClick={() => updateVapiAssistant(null, {})}
                      disabled={isLoadingVapi}
                    >
                      {isLoadingVapi ? 'Creating...' :
                       (attorney?.vapi_assistant_id && attorney.vapi_assistant_id.startsWith('mock-') ? 'Fix Assistant' : 'Create Assistant')}
                    </button>
                  )}
                  {attorney?.vapi_assistant_id && !vapiAssistantData && (
                    <button
                      className="dashboard-button"
                      onClick={() => fetchVapiAssistantData(attorney.vapi_assistant_id)}
                      disabled={isLoadingVapi}
                    >
                      {isLoadingVapi ? 'Loading...' : 'Refresh Assistant Data'}
                    </button>
                  )}
                </div>

                <div className="form-group">
                  <label htmlFor="titleText">Name</label>
                  <input
                    type="text"
                    id="titleText"
                    className="form-control"
                    placeholder="Your firm name or custom name"
                    value={attorney?.title_text || attorney?.firm_name || ''}
                    onChange={(e) => {
                      setAttorney({...attorney, title_text: e.target.value});
                      // Use setTimeout to allow state to update before sending preview update
                      setTimeout(sendPreviewUpdate, 0);
                    }}
                  />
                  <small className="form-text">This text appears as the main heading in your agent preview. Defaults to your firm name if left blank.</small>
                </div>

                <div className="form-group">
                  <label>Welcome Message</label>
                  <textarea
                    className="form-control"
                    rows="3"
                    placeholder="Hello, I'm your legal assistant. How can I help you today?"
                    value={welcomeMessage}
                    onChange={(e) => {
                      setWelcomeMessage(e.target.value);
                      // Track changes for Vapi
                      handleVapiFieldChange('firstMessage', e.target.value);
                      // Use setTimeout to allow state to update before sending preview update
                      setTimeout(sendPreviewUpdate, 0);
                    }}
                  ></textarea>
                  <small className="form-text">This is the first message your AI assistant will say to clients.</small>
                  {vapiFieldsChanged.firstMessage && (
                    <div className="vapi-field-actions">
                      <button
                        className="vapi-save-button"
                        onClick={() => {
                          // Import the Vapi assistant service
                          import('../services/vapiAssistantService').then(({ vapiAssistantService }) => {
                            setIsLoadingVapi(true);
                            // Update the Vapi assistant directly
                            vapiAssistantService.updateAssistant(attorney.vapi_assistant_id, {
                              firstMessage: welcomeMessage
                            }).then(() => {
                              // Reset the changed fields
                              setVapiFieldsChanged(prev => {
                                const newFields = { ...prev };
                                delete newFields.firstMessage;
                                return newFields;
                              });

                              // If no more changes, reset the flag
                              if (Object.keys(vapiFieldsChanged).length <= 1) {
                                setHasVapiChanges(false);
                              }

                              // Show success message
                              alert('Welcome message updated successfully in Vapi!');
                              setIsLoadingVapi(false);

                              // Refresh the Vapi assistant data
                              fetchVapiAssistantData(attorney.vapi_assistant_id);
                            }).catch(error => {
                              console.error('Error updating welcome message:', error);
                              alert(`Error updating welcome message: ${error.message}`);
                              setIsLoadingVapi(false);
                            });
                          });
                        }}
                        disabled={isLoadingVapi}
                      >
                        {isLoadingVapi ? 'Saving...' : 'Save to Vapi'}
                      </button>
                      <button
                        className="vapi-abandon-button"
                        onClick={() => {
                          // Reset the welcome message to the current assistant data
                          if (vapiAssistantData) {
                            setWelcomeMessage(vapiAssistantData.firstMessage || '');
                          }

                          // Reset the changed fields
                          setVapiFieldsChanged(prev => {
                            const newFields = { ...prev };
                            delete newFields.firstMessage;
                            return newFields;
                          });

                          // If no more changes, reset the flag
                          if (Object.keys(vapiFieldsChanged).length <= 1) {
                            setHasVapiChanges(false);
                          }
                        }}
                        disabled={isLoadingVapi}
                      >
                        Abandon Changes
                      </button>
                    </div>
                  )}
                </div>

                <div className="form-group">
                  <label>Information Gathering</label>
                  <textarea
                    className="form-control"
                    rows="3"
                    placeholder="Tell me about your situation, and I'll help find the right solution for you."
                    value={informationGathering}
                    onChange={(e) => {
                      setInformationGathering(e.target.value);
                      // Use setTimeout to allow state to update before sending preview update
                      setTimeout(sendPreviewUpdate, 0);
                    }}
                  ></textarea>
                  <small className="form-text">Questions your assistant will ask to gather information from clients.</small>
                </div>

                <div className="form-group">
                  <label>System Prompt (Agent Instructions)</label>
                  <textarea
                    className="form-control system-prompt-textarea"
                    rows="8"
                    placeholder="Enter detailed instructions for your agent's behavior"
                    value={attorney?.vapi_instructions || ''}
                    onChange={(e) => {
                      setAttorney(prev => ({
                        ...prev,
                        vapi_instructions: e.target.value
                      }));
                      // Track changes for Vapi
                      handleVapiFieldChange('instructions', e.target.value);
                      // Use setTimeout to allow state to update before sending preview update
                      setTimeout(sendPreviewUpdate, 0);
                    }}
                  ></textarea>
                  <small className="form-text">These instructions define how your agent behaves. Be specific about your practice areas, how to handle different types of inquiries, and what information to collect.</small>
                  {vapiFieldsChanged.instructions && (
                    <div className="vapi-field-actions">
                      <button
                        className="vapi-save-button"
                        onClick={() => {
                          // Import the Vapi assistant service
                          import('../services/vapiAssistantService').then(({ vapiAssistantService }) => {
                            setIsLoadingVapi(true);
                            // Update the Vapi assistant directly
                            vapiAssistantService.updateAssistant(attorney.vapi_assistant_id, {
                              instructions: attorney.vapi_instructions
                            }).then(() => {
                              // Reset the changed fields
                              setVapiFieldsChanged(prev => {
                                const newFields = { ...prev };
                                delete newFields.instructions;
                                return newFields;
                              });

                              // If no more changes, reset the flag
                              if (Object.keys(vapiFieldsChanged).length <= 1) {
                                setHasVapiChanges(false);
                              }

                              // Show success message
                              alert('System prompt updated successfully in Vapi!');
                              setIsLoadingVapi(false);

                              // Refresh the Vapi assistant data
                              fetchVapiAssistantData(attorney.vapi_assistant_id);
                            }).catch(error => {
                              console.error('Error updating system prompt:', error);
                              alert(`Error updating system prompt: ${error.message}`);
                              setIsLoadingVapi(false);
                            });
                          });
                        }}
                        disabled={isLoadingVapi}
                      >
                        {isLoadingVapi ? 'Saving...' : 'Save to Vapi'}
                      </button>
                      <button
                        className="vapi-abandon-button"
                        onClick={() => {
                          // Reset the system prompt to the current assistant data
                          if (vapiAssistantData) {
                            setAttorney(prev => ({
                              ...prev,
                              vapi_instructions: vapiAssistantData.instructions || ''
                            }));
                          }

                          // Reset the changed fields
                          setVapiFieldsChanged(prev => {
                            const newFields = { ...prev };
                            delete newFields.instructions;
                            return newFields;
                          });

                          // If no more changes, reset the flag
                          if (Object.keys(vapiFieldsChanged).length <= 1) {
                            setHasVapiChanges(false);
                          }
                        }}
                        disabled={isLoadingVapi}
                      >
                        Abandon Changes
                      </button>
                    </div>
                  )}
                </div>

                <div className="form-group">
                  <label>Agent Context (Additional Knowledge)</label>
                  <textarea
                    className="form-control system-context-textarea"
                    rows="6"
                    placeholder="Enter additional knowledge or context for your agent"
                    value={attorney?.vapi_context || ''}
                    onChange={(e) => {
                      setAttorney(prev => ({
                        ...prev,
                        vapi_context: e.target.value
                      }));
                      // Track changes for Vapi
                      handleVapiFieldChange('context', e.target.value);
                      // Use setTimeout to allow state to update before sending preview update
                      setTimeout(sendPreviewUpdate, 0);
                    }}
                  ></textarea>
                  <small className="form-text">Add specific knowledge about your practice, firm policies, or other information your agent should know.</small>
                  {vapiFieldsChanged.context && (
                    <div className="vapi-field-actions">
                      <button
                        className="vapi-save-button"
                        onClick={() => {
                          // Import the Vapi assistant service
                          import('../services/vapiAssistantService').then(({ vapiAssistantService }) => {
                            setIsLoadingVapi(true);
                            // Update the Vapi assistant directly
                            vapiAssistantService.updateAssistant(attorney.vapi_assistant_id, {
                              context: attorney.vapi_context
                            }).then(() => {
                              // Reset the changed fields
                              setVapiFieldsChanged(prev => {
                                const newFields = { ...prev };
                                delete newFields.context;
                                return newFields;
                              });

                              // If no more changes, reset the flag
                              if (Object.keys(vapiFieldsChanged).length <= 1) {
                                setHasVapiChanges(false);
                              }

                              // Show success message
                              alert('Agent context updated successfully in Vapi!');
                              setIsLoadingVapi(false);

                              // Refresh the Vapi assistant data
                              fetchVapiAssistantData(attorney.vapi_assistant_id);
                            }).catch(error => {
                              console.error('Error updating agent context:', error);
                              alert(`Error updating agent context: ${error.message}`);
                              setIsLoadingVapi(false);
                            });
                          });
                        }}
                        disabled={isLoadingVapi}
                      >
                        {isLoadingVapi ? 'Saving...' : 'Save to Vapi'}
                      </button>
                      <button
                        className="vapi-abandon-button"
                        onClick={() => {
                          // Reset the agent context to the current assistant data
                          if (vapiAssistantData) {
                            setAttorney(prev => ({
                              ...prev,
                              vapi_context: vapiAssistantData.context || ''
                            }));
                          }

                          // Reset the changed fields
                          setVapiFieldsChanged(prev => {
                            const newFields = { ...prev };
                            delete newFields.context;
                            return newFields;
                          });

                          // If no more changes, reset the flag
                          if (Object.keys(vapiFieldsChanged).length <= 1) {
                            setHasVapiChanges(false);
                          }
                        }}
                        disabled={isLoadingVapi}
                      >
                        Abandon Changes
                      </button>
                    </div>
                  )}
                </div>

                <div className="form-group">
                  <label>Voice</label>
                  <select
                    className="form-control"
                    value={voiceId}
                    onChange={(e) => {
                      setVoiceId(e.target.value);
                      // Track changes for Vapi
                      handleVapiFieldChange('voice', {
                        provider: 'playht',
                        voiceId: e.target.value
                      });
                      // Use setTimeout to allow state to update before sending preview update
                      setTimeout(sendPreviewUpdate, 0);
                    }}
                  >
                    <option value="s3://voice-cloning-zero-shot/4c627545-b9c0-4791-ae8e-f48f5475247c/bryansaad/manifest.json">Neil (Male)</option>
                    <option value="s3://voice-cloning-zero-shot/09b5c0cc-a8f4-4450-aaab-3657b9965d0b/podcaster/manifest.json">Cali (Male)</option>
                    <option value="s3://voice-cloning-zero-shot/2cbffa49-5dfe-4378-a54f-b824f7bbb032/theodoresaad/manifest.json">Dusty (Male)</option>
                    <option value="s3://mockingbird-prod/agent_47_carmelo_pampillonio_58e796e1-0b87-4f3e-8b36-7def6d65ce66/voices/speaker/manifest.json">Decker (Male)</option>
                    <option value="s3://voice-cloning-zero-shot/abc2d0e6-9433-4dcc-b416-0b035169f37e/original/manifest.json">Hal (Male)</option>
                  </select>
                  <small className="form-text">The voice your AI assistant will use during calls.</small>
                  {vapiFieldsChanged.voice && (
                    <div className="vapi-field-actions">
                      <button
                        className="vapi-save-button"
                        onClick={() => {
                          // Import the Vapi assistant service
                          import('../services/vapiAssistantService').then(({ vapiAssistantService }) => {
                            setIsLoadingVapi(true);
                            // Update the Vapi assistant directly
                            vapiAssistantService.updateAssistant(attorney.vapi_assistant_id, {
                              voice: {
                                provider: 'playht',
                                voiceId: voiceId
                              }
                            }).then(() => {
                              // Reset the changed fields
                              setVapiFieldsChanged(prev => {
                                const newFields = { ...prev };
                                delete newFields.voice;
                                return newFields;
                              });

                              // If no more changes, reset the flag
                              if (Object.keys(vapiFieldsChanged).length <= 1) {
                                setHasVapiChanges(false);
                              }

                              // Show success message
                              alert('Voice updated successfully in Vapi!');
                              setIsLoadingVapi(false);

                              // Refresh the Vapi assistant data
                              fetchVapiAssistantData(attorney.vapi_assistant_id);
                            }).catch(error => {
                              console.error('Error updating voice:', error);
                              alert(`Error updating voice: ${error.message}`);
                              setIsLoadingVapi(false);
                            });
                          });
                        }}
                        disabled={isLoadingVapi}
                      >
                        {isLoadingVapi ? 'Saving...' : 'Save to Vapi'}
                      </button>
                      <button
                        className="vapi-abandon-button"
                        onClick={() => {
                          // Reset the voice to the current assistant data
                          if (vapiAssistantData && vapiAssistantData.voice) {
                            setVoiceId(vapiAssistantData.voice.voiceId || 's3://voice-cloning-zero-shot/4c627545-b9c0-4791-ae8e-f48f5475247c/bryansaad/manifest.json');
                          }

                          // Reset the changed fields
                          setVapiFieldsChanged(prev => {
                            const newFields = { ...prev };
                            delete newFields.voice;
                            return newFields;
                          });

                          // If no more changes, reset the flag
                          if (Object.keys(vapiFieldsChanged).length <= 1) {
                            setHasVapiChanges(false);
                          }
                        }}
                        disabled={isLoadingVapi}
                      >
                        Abandon Changes
                      </button>
                    </div>
                  )}
                </div>

                <div className="form-group">
                  <label>AI Model</label>
                  <select
                    className="form-control"
                    value={aiModel}
                    onChange={(e) => {
                      setAiModel(e.target.value);
                      // Track changes for Vapi
                      handleVapiFieldChange('llm', {
                        provider: e.target.value.includes('gpt') ? 'openai' : 'anthropic',
                        model: e.target.value
                      });
                      // Use setTimeout to allow state to update before sending preview update
                      setTimeout(sendPreviewUpdate, 0);
                    }}
                  >
                    <option value="gpt-4o">GPT-4o (OpenAI)</option>
                    <option value="claude-3-5-sonnet">Claude 3.5 Sonnet (Anthropic)</option>
                    <option value="claude-3-opus">Claude 3 Opus (Anthropic)</option>
                  </select>
                  <small className="form-text">The AI model that powers your assistant.</small>
                  {vapiFieldsChanged.llm && (
                    <div className="vapi-field-actions">
                      <button
                        className="vapi-save-button"
                        onClick={() => {
                          // Import the Vapi assistant service
                          import('../services/vapiAssistantService').then(({ vapiAssistantService }) => {
                            setIsLoadingVapi(true);
                            // Update the Vapi assistant directly
                            vapiAssistantService.updateAssistant(attorney.vapi_assistant_id, {
                              llm: {
                                provider: aiModel.includes('gpt') ? 'openai' : 'anthropic',
                                model: aiModel
                              }
                            }).then(() => {
                              // Reset the changed fields
                              setVapiFieldsChanged(prev => {
                                const newFields = { ...prev };
                                delete newFields.llm;
                                return newFields;
                              });

                              // If no more changes, reset the flag
                              if (Object.keys(vapiFieldsChanged).length <= 1) {
                                setHasVapiChanges(false);
                              }

                              // Show success message
                              alert('AI model updated successfully in Vapi!');
                              setIsLoadingVapi(false);

                              // Refresh the Vapi assistant data
                              fetchVapiAssistantData(attorney.vapi_assistant_id);
                            }).catch(error => {
                              console.error('Error updating AI model:', error);
                              alert(`Error updating AI model: ${error.message}`);
                              setIsLoadingVapi(false);
                            });
                          });
                        }}
                        disabled={isLoadingVapi}
                      >
                        {isLoadingVapi ? 'Saving...' : 'Save to Vapi'}
                      </button>
                      <button
                        className="vapi-abandon-button"
                        onClick={() => {
                          // Reset the AI model to the current assistant data
                          if (vapiAssistantData && vapiAssistantData.llm) {
                            setAiModel(vapiAssistantData.llm.model || 'gpt-4o');
                          }

                          // Reset the changed fields
                          setVapiFieldsChanged(prev => {
                            const newFields = { ...prev };
                            delete newFields.llm;
                            return newFields;
                          });

                          // If no more changes, reset the flag
                          if (Object.keys(vapiFieldsChanged).length <= 1) {
                            setHasVapiChanges(false);
                          }
                        }}
                        disabled={isLoadingVapi}
                      >
                        Abandon Changes
                      </button>
                    </div>
                  )}
                </div>

                {/* Vapi Assistant Status */}
                <div className="vapi-assistant-status">
                  <h3>Assistant Status</h3>
                  {isLoadingVapi ? (
                    <div className="loading-spinner-small"></div>
                  ) : vapiAssistantData ? (
                    <>
                      <div className="status-item">
                        <span className="status-label">ID:</span>
                        <span className="status-value">{attorney?.vapi_assistant_id}</span>
                      </div>
                      <div className="status-item">
                        <span className="status-label">Name:</span>
                        <span className="status-value">{vapiAssistantData.name}</span>
                      </div>
                      <div className="status-item">
                        <span className="status-label">Created:</span>
                        <span className="status-value">{new Date(vapiAssistantData.createdAt).toLocaleString()}</span>
                      </div>
                      <div className="status-item">
                        <span className="status-label">Last Updated:</span>
                        <span className="status-value">{new Date(vapiAssistantData.updatedAt).toLocaleString()}</span>
                      </div>
                      <div className="status-item">
                        <span className="status-label">Model:</span>
                        <span className="status-value">{vapiAssistantData.llm?.provider} / {vapiAssistantData.llm?.model}</span>
                      </div>
                      <div className="status-item">
                        <span className="status-label">Voice:</span>
                        <span className="status-value">{vapiAssistantData.voice?.provider} / {vapiAssistantData.voice?.voiceId?.split('/').pop()?.replace('.json', '')}</span>
                      </div>
                    </>
                  ) : (
                    <>
                      <p>No Vapi assistant found or unable to load assistant data.</p>
                      <button
                        className="vapi-save-button"
                        onClick={() => fetchVapiAssistantData(attorney?.vapi_assistant_id)}
                        disabled={isLoadingVapi}
                      >
                        {isLoadingVapi ? 'Loading...' : 'Refresh Assistant Data'}
                      </button>
                      {!attorney?.vapi_assistant_id && (
                        <button
                          className="vapi-save-button"
                          onClick={() => fetchVapiAssistantData(null)}
                          disabled={isLoadingVapi}
                          style={{ marginLeft: '0.5rem' }}
                        >
                          {isLoadingVapi ? 'Creating...' : 'Create New Assistant'}
                        </button>
                      )}
                    </>
                  )}
                </div>
              </div>

              {/* Appearance Card */}
              <div className="dashboard-card">
                <h2>Appearance</h2>
                <div className="form-group">
                  <label htmlFor="logo-upload">Banner</label>
                  <p className="form-text">
                    Upload a banner image that will appear at the top of your agent page and in the preview panel.
                  </p>
                  <div className="logo-upload-container">
                    {logoUrl ? (
                      <div className="logo-preview">
                        <img
                          src={logoUrl}
                          alt="Banner"
                          className="logo-image"
                        />
                        <button
                          className="remove-logo-button"
                          onClick={handleRemoveLogo}
                          type="button"
                        >
                          Remove
                        </button>
                      </div>
                    ) : (
                      <div className="logo-upload">
                        <input
                          type="file"
                          accept="image/*"
                          onChange={handleLogoUpload}
                          id="logo-upload"
                          className="file-input"
                        />
                        <label htmlFor="logo-upload" className="file-input-label">
                          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <path d="M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h7"></path>
                            <line x1="16" y1="5" x2="22" y2="5"></line>
                            <line x1="19" y1="2" x2="19" y2="8"></line>
                            <circle cx="9" cy="9" r="2"></circle>
                            <path d="M21 15l-3.086-3.086a2 2 0 0 0-2.828 0L6 21"></path>
                          </svg>
                          Upload Banner
                        </label>
                      </div>
                    )}
                  </div>
                  <small className="form-text">This banner will appear at the top of your agent page and in the preview panel.</small>
                </div>

                <div className="form-group">
                  <label htmlFor="button-image-upload">Button Image</label>
                  <p className="form-text">
                    Upload an image that will appear on the consultation button.
                  </p>
                  <div className="logo-upload-container">
                    {buttonImageUrl ? (
                      <div className="logo-preview">
                        <img
                          src={buttonImageUrl}
                          alt="Button Image"
                          className="logo-image"
                        />
                        <button
                          className="remove-logo-button"
                          onClick={handleRemoveButtonImage}
                          type="button"
                        >
                          Remove
                        </button>
                      </div>
                    ) : (
                      <div className="logo-upload">
                        <input
                          type="file"
                          accept="image/*"
                          onChange={(e) => {
                            const file = e.target.files[0];
                            if (file) {
                              const reader = new FileReader();
                              reader.onloadend = () => {
                                setButtonImageUrl(reader.result);
                                setTimeout(sendPreviewUpdate, 100);
                              };
                              reader.readAsDataURL(file);
                            }
                          }}
                          id="button-image-upload"
                          className="file-input"
                        />
                        <label htmlFor="button-image-upload" className="file-input-label">
                          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <path d="M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h7"></path>
                            <line x1="16" y1="5" x2="22" y2="5"></line>
                            <line x1="19" y1="2" x2="19" y2="8"></line>
                            <circle cx="9" cy="9" r="2"></circle>
                            <path d="M21 15l-3.086-3.086a2 2 0 0 0-2.828 0L6 21"></path>
                          </svg>
                          Upload Button Image
                        </label>
                      </div>
                    )}
                  </div>
                  <small className="form-text">This image will appear on the consultation button.</small>
                </div>

                <div className="form-group">
                  <label htmlFor="practiceDescription">Practice Description</label>
                  <textarea
                    id="practiceDescription"
                    className="form-control"
                    rows="3"
                    placeholder="Describe your practice and expertise..."
                    value={attorney?.practice_description || ''}
                    onChange={(e) => {
                      setAttorney(prev => ({ ...prev, practice_description: e.target.value }));
                      // Use setTimeout to allow state to update before sending preview update
                      setTimeout(sendPreviewUpdate, 0);
                    }}
                  ></textarea>
                  <small className="form-text">A description of your practice that will be shown to clients.</small>
                </div>

                <div className="color-pickers">
                  <div className="form-group color-picker">
                    <label htmlFor="primaryColor">Primary Color</label>
                    <input
                      type="color"
                      id="primaryColor"
                      className="form-control color-input"
                      value={primaryColor}
                      onChange={(e) => {
                        setPrimaryColor(e.target.value);
                        // Use setTimeout to allow state to update before sending preview update
                        setTimeout(sendPreviewUpdate, 0);
                      }}
                    />
                    <small className="form-text">{primaryColor}</small>
                  </div>

                  <div className="form-group color-picker">
                    <label htmlFor="secondaryColor">Secondary Color</label>
                    <input
                      type="color"
                      id="secondaryColor"
                      className="form-control color-input"
                      value={secondaryColor}
                      onChange={(e) => {
                        setSecondaryColor(e.target.value);
                        // Use setTimeout to allow state to update before sending preview update
                        setTimeout(sendPreviewUpdate, 0);
                      }}
                    />
                    <small className="form-text">{secondaryColor}</small>
                  </div>

                  <div className="form-group color-picker">
                    <label htmlFor="buttonColor">Button Color</label>
                    <input
                      type="color"
                      id="buttonColor"
                      className="form-control color-input"
                      value={buttonColor}
                      onChange={(e) => {
                        setButtonColor(e.target.value);
                        // Use setTimeout to allow state to update before sending preview update
                        setTimeout(sendPreviewUpdate, 0);
                      }}
                    />
                    <small className="form-text">{buttonColor}</small>
                  </div>

                  <div className="form-group color-picker">
                    <label htmlFor="backgroundColor">Background Color</label>
                    <input
                      type="color"
                      id="backgroundColor"
                      className="form-control color-input"
                      value={backgroundColor}
                      onChange={(e) => {
                        setBackgroundColor(e.target.value);
                        // Use setTimeout to allow state to update before sending preview update
                        setTimeout(sendPreviewUpdate, 0);
                      }}
                    />
                    <small className="form-text">{backgroundColor}</small>
                  </div>
                </div>

                <div className="form-group">
                  <label htmlFor="buttonText">Button Text</label>
                  <input
                    type="text"
                    id="buttonText"
                    className="form-control"
                    value={buttonText}
                    onChange={(e) => {
                      setButtonText(e.target.value);
                      // Use setTimeout to allow state to update before sending preview update
                      setTimeout(sendPreviewUpdate, 0);
                    }}
                    placeholder="Start Consultation"
                  />
                </div>

                <div className="opacity-sliders">
                  <div className="form-group">
                    <label htmlFor="backgroundOpacity">Background Opacity: {backgroundOpacity}</label>
                    <input
                      type="range"
                      id="backgroundOpacity"
                      className="form-control-range"
                      min="0"
                      max="1"
                      step="0.1"
                      value={backgroundOpacity}
                      onChange={(e) => {
                        setBackgroundOpacity(parseFloat(e.target.value));
                        // Use setTimeout to allow state to update before sending preview update
                        setTimeout(sendPreviewUpdate, 0);
                      }}
                    />
                  </div>

                  <div className="form-group">
                    <label htmlFor="buttonOpacity">Button Opacity: {buttonOpacity}</label>
                    <input
                      type="range"
                      id="buttonOpacity"
                      className="form-control-range"
                      min="0"
                      max="1"
                      step="0.1"
                      value={buttonOpacity}
                      onChange={(e) => {
                        setButtonOpacity(parseFloat(e.target.value));
                        // Use setTimeout to allow state to update before sending preview update
                        setTimeout(sendPreviewUpdate, 0);
                      }}
                    />
                  </div>

                  <div className="form-group">
                    <label htmlFor="practiceAreaBackgroundOpacity">Practice Area Background Opacity: {practiceAreaBackgroundOpacity}</label>
                    <input
                      type="range"
                      id="practiceAreaBackgroundOpacity"
                      className="form-control-range"
                      min="0"
                      max="1"
                      step="0.1"
                      value={practiceAreaBackgroundOpacity}
                      onChange={(e) => {
                        setPracticeAreaBackgroundOpacity(parseFloat(e.target.value));
                        // Use setTimeout to allow state to update before sending preview update
                        setTimeout(sendPreviewUpdate, 0);
                      }}
                    />
                  </div>
                </div>
              </div>



              <div className="action-buttons">
                <button
                  className="dashboard-button"
                  onClick={handleSaveChanges}
                  disabled={loading}
                >
                  {loading ? 'Saving...' : 'Save Changes'}
                </button>
                <button
                  className="dashboard-button secondary"
                  onClick={handleResetDefaults}
                  disabled={loading}
                >
                  Reset to Defaults
                </button>
              </div>
            </div>
          )}

          {/* Consultations Tab */}
          {activeTab === 'consultations' && (
            <div>
              {/* Shareable URL and Embed Code Card */}
              <div className="dashboard-card">
                <h2>Your Agent Widget</h2>
                <p className="card-description">
                  Share your AI assistant with clients or embed it on your website.
                </p>

                {/* Shareable URL */}
                <div className="form-group">
                  <label htmlFor="shareableUrl">Shareable URL</label>
                  <div className="input-group with-copy-button">
                    <input
                      type="text"
                      id="shareableUrl"
                      className="form-control"
                      value={`https://${attorney?.subdomain || 'your-firm'}.legalscout.net`}
                      readOnly
                    />
                    <button
                      className="copy-button"
                      onClick={() => {
                        navigator.clipboard.writeText(`https://${attorney?.subdomain || 'your-firm'}.legalscout.net`);
                        alert('URL copied to clipboard!');
                      }}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                        <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                      </svg>
                      Copy
                    </button>
                  </div>
                  <small className="form-text">Share this URL with clients to access your AI assistant directly.</small>
                </div>

                {/* Embed Code */}
                <div className="form-group">
                  <label htmlFor="embedCode">Embed Code</label>
                  <div className="embed-code-container">
                    <pre className="embed-code">
                      {`<script>
  (function(w, d, s, o, f, js, fjs) {
    w['LegalScout-Widget'] = o;
    w[o] = w[o] || function() { (w[o].q = w[o].q || []).push(arguments) };
    js = d.createElement(s), fjs = d.getElementsByTagName(s)[0];
    js.id = o; js.src = f; js.async = 1; fjs.parentNode.insertBefore(js, fjs);
  }(window, document, 'script', 'lsw', 'https://widget.legalscout.net/loader.js'));
  lsw('init', { subdomain: '${attorney?.subdomain || 'your-firm'}' });
</script>`}
                    </pre>
                    <button
                      className="copy-button embed-copy-button"
                      onClick={() => {
                        const embedCode = `<script>
  (function(w, d, s, o, f, js, fjs) {
    w['LegalScout-Widget'] = o;
    w[o] = w[o] || function() { (w[o].q = w[o].q || []).push(arguments) };
    js = d.createElement(s), fjs = d.getElementsByTagName(s)[0];
    js.id = o; js.src = f; js.async = 1; fjs.parentNode.insertBefore(js, fjs);
  }(window, document, 'script', 'lsw', 'https://widget.legalscout.net/loader.js'));
  lsw('init', { subdomain: '${attorney?.subdomain || 'your-firm'}' });
</script>`;
                        navigator.clipboard.writeText(embedCode);
                        alert('Embed code copied to clipboard!');
                      }}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                        <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                      </svg>
                      Copy Code
                    </button>
                  </div>
                  <small className="form-text">Add this code to your website to embed your AI assistant as a chat widget.</small>
                  <div className="embed-instructions">
                    <h4>Installation Instructions</h4>
                    <ol>
                      <li>Copy the code above</li>
                      <li>Paste it just before the closing <code>&lt;/body&gt;</code> tag on your website</li>
                      <li>The chat widget will appear as a floating button in the bottom right corner of your website</li>
                    </ol>
                  </div>
                </div>
              </div>

              {/* Recent Consultations Card */}
              <div className="dashboard-card">
                <h2>Recent Consultations</h2>
                <div className="consultation-list">
                  {/* Empty state */}
                  <div className="empty-state">
                    <div className="empty-state-icon">
                      <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                      </svg>
                    </div>
                    <p>No consultations found</p>
                    <p>When clients interact with your AI assistant, their consultations will appear here.</p>
                  </div>

                  {/* Sample consultations - commented out for now */}
                  {/*
                  <div className="consultation-item">
                    <div className="consultation-info">
                      <div className="consultation-date">April 21, 2025 - 2:30 PM</div>
                      <div className="consultation-title">Personal Injury Consultation</div>
                    </div>
                    <div className="consultation-actions">
                      <button className="consultation-button">View</button>
                      <button className="consultation-button secondary">Download</button>
                    </div>
                  </div>
                  */}
                </div>
              </div>

              {/* Custom Columns Card */}
              <div className="dashboard-card">
                <h2>Custom Columns</h2>
                <p className="card-description">
                  Add custom fields to your consultation records. These fields will be:
                </p>
                <ul className="feature-list">
                  <li>Automatically inserted into your assistant's system prompt</li>
                  <li>Added as fields to your consultation records</li>
                  <li>Included in the end call report</li>
                </ul>

                <div className="custom-columns-list">
                  {/* Empty state for custom columns */}
                  <div className="empty-state small">
                    <div className="empty-state-icon small">
                      <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round">
                        <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                        <line x1="3" y1="9" x2="21" y2="9"></line>
                        <line x1="9" y1="21" x2="9" y2="9"></line>
                      </svg>
                    </div>
                    <p>No custom columns defined</p>
                  </div>

                  {/* Add column button */}
                  <button
                    className="add-column-button"
                    onClick={() => setShowColumnModal(true)}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <line x1="12" y1="5" x2="12" y2="19"></line>
                      <line x1="5" y1="12" x2="19" y2="12"></line>
                    </svg>
                    Add Custom Column
                  </button>
                </div>

                {/* Add Column Modal */}
                {showColumnModal && (
                  <div className="column-modal">
                    <div className="modal-content">
                      <h3>Add Custom Column</h3>
                      <div className="form-group">
                        <label htmlFor="columnName">Column Name</label>
                        <input type="text" id="columnName" className="form-control" placeholder="e.g., Case Type, Injury Severity" />
                      </div>
                      <div className="form-group">
                        <label htmlFor="fieldType">Field Type</label>
                        <select id="fieldType" className="form-control">
                          <option value="text">Text</option>
                          <option value="number">Number</option>
                          <option value="select">Multiple Choice</option>
                          <option value="boolean">Yes/No</option>
                        </select>
                      </div>
                      <div className="form-group">
                        <label htmlFor="assistantPrompt">Prompt for Assistant</label>
                        <textarea id="assistantPrompt" className="form-control" rows="2" placeholder="e.g., Determine the type of legal case"></textarea>
                      </div>
                      <div className="modal-actions">
                        <button
                          className="dashboard-button secondary"
                          onClick={() => setShowColumnModal(false)}
                        >
                          Cancel
                        </button>
                        <button className="dashboard-button">
                          Add Column
                        </button>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Analytics Card - New */}
              <div className="dashboard-card">
                <div className="coming-soon-header">
                  <h2>Analytics</h2>
                  <span className="coming-soon-badge">Coming Soon</span>
                </div>
                <p className="card-description">
                  Track and analyze your consultation data to improve client engagement.
                </p>

                <div className="coming-soon-placeholder">
                  <div className="placeholder-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round">
                      <line x1="18" y1="20" x2="18" y2="10"></line>
                      <line x1="12" y1="20" x2="12" y2="4"></line>
                      <line x1="6" y1="20" x2="6" y2="14"></line>
                      <line x1="2" y1="20" x2="22" y2="20"></line>
                    </svg>
                  </div>
                  <p>Consultation analytics will be available soon.</p>
                  <ul className="feature-list">
                    <li>Total consultations and conversion metrics</li>
                    <li>Average consultation length</li>
                    <li>Most common legal topics</li>
                    <li>Client engagement statistics</li>
                  </ul>
                </div>
              </div>


            </div>
          )}

          {/* Share Tab */}
          {activeTab === 'share' && (
            <div>
              {/* Share Your Assistant Card */}
              <div className="dashboard-card">
                <h2>Share Your Assistant</h2>
                <p className="card-description">
                  Share your AI assistant with clients or embed it on your website.
                </p>

                <div className="share-section">
                  <h3>Your Assistant URL</h3>
                  <p className="section-description">
                    Share this URL with clients to give them direct access to your AI assistant.
                  </p>
                  <div className="copy-field">
                    <input
                      type="text"
                      className="form-control"
                      value={`https://${attorney?.subdomain || 'your-firm'}.legalscout.net`}
                      readOnly
                    />
                    <button
                      className="copy-button"
                      onClick={() => copyToClipboard(`https://${attorney?.subdomain || 'your-firm'}.legalscout.net`, 'URL')}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                        <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                      </svg>
                      Copy URL
                    </button>
                  </div>
                  {copySuccess === 'URL copied!' && <div className="copy-success">✓ {copySuccess}</div>}
                </div>

                <div className="share-section">
                  <h3>Embed on Your Website</h3>
                  <p className="section-description">
                    Add this code to your website to embed your AI assistant as a widget.
                  </p>
                  <div className="copy-field">
                    <textarea
                      className="form-control code-textarea"
                      value={`<iframe src="https://${attorney?.subdomain || 'your-firm'}.legalscout.net/embed" width="100%" height="600" frameborder="0" allow="microphone"></iframe>`}
                      readOnly
                    ></textarea>
                    <button
                      className="copy-button"
                      onClick={() => copyToClipboard(`<iframe src="https://${attorney?.subdomain || 'your-firm'}.legalscout.net/embed" width="100%" height="600" frameborder="0" allow="microphone"></iframe>`, 'Embed code')}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                        <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                      </svg>
                      Copy Code
                    </button>
                  </div>
                  {copySuccess === 'Embed code copied!' && <div className="copy-success">✓ {copySuccess}</div>}
                </div>
              </div>

              {/* Customization Options Card - New */}
              <div className="dashboard-card">
                <div className="coming-soon-header">
                  <h2>Widget Customization</h2>
                  <span className="coming-soon-badge">Coming Soon</span>
                </div>
                <p className="card-description">
                  Customize how your assistant widget appears on your website.
                </p>

                <div className="coming-soon-placeholder">
                  <div className="placeholder-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round">
                      <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                      <circle cx="8.5" cy="8.5" r="1.5"></circle>
                      <polyline points="21 15 16 10 5 21"></polyline>
                    </svg>
                  </div>
                  <p>Advanced widget customization options will be available soon.</p>
                  <ul className="feature-list">
                    <li>Widget size and placement options</li>
                    <li>Auto-open settings and triggers</li>
                    <li>Custom button styles and animations</li>
                    <li>Mobile-specific configurations</li>
                  </ul>
                </div>
              </div>

              {/* Marketing Materials Card - New */}
              <div className="dashboard-card">
                <div className="coming-soon-header">
                  <h2>Marketing Materials</h2>
                  <span className="coming-soon-badge">Coming Soon</span>
                </div>
                <p className="card-description">
                  Generate marketing materials to promote your AI assistant.
                </p>

                <div className="coming-soon-placeholder">
                  <div className="placeholder-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M18 3a3 3 0 0 0-3 3v12a3 3 0 0 0 3 3 3 3 0 0 0 3-3 3 3 0 0 0-3-3H6a3 3 0 0 0-3 3 3 3 0 0 0 3 3 3 3 0 0 0 3-3V6a3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3h12a3 3 0 0 0 3-3 3 3 0 0 0-3-3z"></path>
                    </svg>
                  </div>
                  <p>Marketing materials will be available soon.</p>
                  <ul className="feature-list">
                    <li>QR code generator for your assistant URL</li>
                    <li>Email templates for client outreach</li>
                    <li>Social media post templates</li>
                    <li>Printable marketing materials</li>
                  </ul>
                </div>
              </div>
            </div>
          )}

          {/* Settings Tab */}
          {activeTab === 'settings' && (
            <div>
              {/* Vapi Diagnostic Test */}
              <div className="dashboard-card">
                <h2>🔬 Voice Call Diagnostic</h2>
                <p className="card-description">
                  Comprehensive diagnostic test to identify the exact cause of call termination issues.
                </p>
                <VapiDiagnosticTest />
              </div>

              {/* Vapi Test Component */}
              <div className="dashboard-card">
                <h2>🧪 Voice Call Testing</h2>
                <p className="card-description">
                  Test the Vapi Web SDK integration and voice call functionality.
                </p>
                <VapiTestComponent />
              </div>

              {/* Firm Rules Card - Moved from Consultations tab */}
              <div className="dashboard-card">
                <h2>Firm Rules</h2>
                <p className="card-description">
                  Configure rules for call handling and automation.
                </p>

                {/* Call Forwarding Rules */}
                <div className="rules-section">
                  <h3>Call Forwarding Rules</h3>
                  <p className="section-description">
                    Set up rules to forward calls to specific team members based on conditions.
                  </p>

                  <div className="rules-list">
                    {/* Empty state for rules */}
                    <div className="empty-state small">
                      <div className="empty-state-icon small">
                        <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
                        </svg>
                      </div>
                      <p>No forwarding rules defined</p>
                    </div>

                    {/* Add rule button */}
                    <button
                      className="add-rule-button"
                      onClick={() => setShowForwardingModal(true)}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <line x1="12" y1="5" x2="12" y2="19"></line>
                        <line x1="5" y1="12" x2="19" y2="12"></line>
                      </svg>
                      Add Forwarding Rule
                    </button>
                  </div>
                </div>

                {/* Outbound Rules - Coming Soon */}
                <div className="rules-section">
                  <div className="section-header">
                    <h3>Outbound Rules</h3>
                    <span className="coming-soon-badge">Coming Soon</span>
                  </div>
                  <p className="section-description">
                    Configure rules for automated outbound calls to clients.
                  </p>
                  <div className="coming-soon-placeholder">
                    <div className="placeholder-icon">
                      <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M15.05 5A5 5 0 0 1 19 8.95M15.05 1A9 9 0 0 1 23 8.94m-1 7.98v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
                      </svg>
                    </div>
                    <p>Outbound calling capabilities will be available soon.</p>
                  </div>
                </div>
              </div>

              {/* Integrations Card - Moved from Integrations tab */}
              <div className="dashboard-card">
                <div className="coming-soon-header">
                  <h2>Integrations</h2>
                  <span className="coming-soon-badge">Coming Soon</span>
                </div>
                <p className="card-description">
                  Connect your AI assistant with your favorite tools and services.
                </p>

                <div className="integrations-grid">
                  <div className="integration-card coming-soon">
                    <div className="integration-logo">
                      <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"></path>
                        <rect x="8" y="2" width="8" height="4" rx="1" ry="1"></rect>
                      </svg>
                    </div>
                    <div className="integration-info">
                      <h3>Clio</h3>
                      <p>Connect with your Clio account to sync client data and case information.</p>
                    </div>
                    <div className="integration-status">
                      <span className="coming-soon-badge">Coming Soon</span>
                    </div>
                  </div>

                  <div className="integration-card coming-soon">
                    <div className="integration-logo">
                      <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path>
                        <path d="M13.73 21a2 2 0 0 1-3.46 0"></path>
                      </svg>
                    </div>
                    <div className="integration-info">
                      <h3>Notifications</h3>
                      <p>Set up email and SMS notifications for new consultations and follow-ups.</p>
                    </div>
                    <div className="integration-status">
                      <span className="coming-soon-badge">Coming Soon</span>
                    </div>
                  </div>

                  <div className="integration-card coming-soon">
                    <div className="integration-logo">
                      <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round">
                        <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                        <line x1="16" y1="2" x2="16" y2="6"></line>
                        <line x1="8" y1="2" x2="8" y2="6"></line>
                        <line x1="3" y1="10" x2="21" y2="10"></line>
                      </svg>
                    </div>
                    <div className="integration-info">
                      <h3>Calendar</h3>
                      <p>Integrate with Google Calendar or Outlook to schedule appointments.</p>
                    </div>
                    <div className="integration-status">
                      <span className="coming-soon-badge">Coming Soon</span>
                    </div>
                  </div>
                </div>
              </div>



              {/* Add Forwarding Rule Modal */}
              {showForwardingModal && (
                <div className="column-modal">
                  <div className="modal-content forwarding-modal">
                    <h3>Add Call Forwarding Rule</h3>

                    <div className="form-group">
                      <label htmlFor="forwardToName">Forward To</label>
                      <div className="form-row">
                        <div className="form-col">
                          <input type="text" id="forwardToName" className="form-control" placeholder="Name" />
                        </div>
                        <div className="form-col">
                          <input type="text" id="forwardToTitle" className="form-control" placeholder="Title" />
                        </div>
                      </div>
                    </div>

                    <div className="form-group">
                      <label htmlFor="forwardToPhone">Phone Number</label>
                      <input type="tel" id="forwardToPhone" className="form-control" placeholder="(*************" />
                    </div>

                    <div className="form-group">
                      <label>When to Forward</label>
                      <div className="forwarding-conditions">
                        <div className="condition-group">
                          <label className="checkbox-label">
                            <input type="checkbox" />
                            <span>During business hours</span>
                          </label>
                          <div className="condition-details">
                            <select className="form-control small">
                              <option value="weekdays">Weekdays</option>
                              <option value="everyday">Every day</option>
                              <option value="custom">Custom days</option>
                            </select>
                            <div className="time-range">
                              <input type="time" className="form-control small" defaultValue="09:00" />
                              <span>to</span>
                              <input type="time" className="form-control small" defaultValue="17:00" />
                            </div>
                            <select className="form-control small">
                              <option value="est">Eastern Time (EST)</option>
                              <option value="cst">Central Time (CST)</option>
                              <option value="mst">Mountain Time (MST)</option>
                              <option value="pst">Pacific Time (PST)</option>
                            </select>
                          </div>
                        </div>

                        <div className="condition-group">
                          <label className="checkbox-label">
                            <input type="checkbox" />
                            <span>When client has a valid claim</span>
                          </label>
                          <div className="condition-details">
                            <textarea
                              className="form-control"
                              rows="2"
                              placeholder="Define what constitutes a valid claim (e.g., injury occurred within last 2 years)"
                            ></textarea>
                          </div>
                        </div>

                        <div className="condition-group">
                          <label className="checkbox-label">
                            <input type="checkbox" />
                            <span>Based on practice area</span>
                          </label>
                          <div className="condition-details">
                            <select className="form-control" multiple>
                              <option value="personal-injury">Personal Injury</option>
                              <option value="family-law">Family Law</option>
                              <option value="criminal-defense">Criminal Defense</option>
                              <option value="estate-planning">Estate Planning</option>
                            </select>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="modal-actions">
                      <button
                        className="dashboard-button secondary"
                        onClick={() => setShowForwardingModal(false)}
                      >
                        Cancel
                      </button>
                      <button className="dashboard-button">
                        Add Rule
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}


        </div>
      </div>

      {/* Right-Hand Pane Content - Only show for agent tab */}
      {activeTab === 'agent' && (
        <>
          {activeTab === 'profile' ? (
        <div className={`preview-panel ${!showPreview ? 'active' : ''}`}>
          <div className="preview-header">
            <h2>Attorney Profile Preview</h2>
            <div className="preview-actions">
              <button
                className="preview-button secondary"
                onClick={toggleTheme}
              >
                {isDarkTheme ? 'Light Mode' : 'Dark Mode'}
              </button>
            </div>
          </div>
          <div className="preview-content profile-preview">
            <div className="attorney-profile-card">
              <div className="profile-header" style={{ backgroundColor: primaryColor }}>
                {logoUrl ? (
                  <img src={logoUrl} alt="Firm Logo" className="profile-logo" />
                ) : (
                  <div className="profile-logo-placeholder">
                    <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z"></path>
                      <path d="M19 10v2a7 7 0 0 1-14 0v-2"></path>
                      <line x1="12" y1="19" x2="12" y2="23"></line>
                      <line x1="8" y1="23" x2="16" y2="23"></line>
                    </svg>
                  </div>
                )}
                <div className="profile-title">
                  <h3>{attorney?.firm_name || 'Your Law Firm'}</h3>
                  <p>{attorney?.subdomain || 'your-firm'}.legalscout.net</p>
                </div>
              </div>
              <div className="profile-body">
                <div className="profile-section">
                  <h4>Contact Information</h4>
                  <div className="profile-info-item">
                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
                      <circle cx="12" cy="10" r="3"></circle>
                    </svg>
                    <span>{attorneyAddress || 'Office Address Not Set'}</span>
                  </div>
                  <div className="profile-info-item">
                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
                    </svg>
                    <span>{attorneyPhone || 'Phone Number Not Set'}</span>
                  </div>
                  <div className="profile-info-item">
                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                      <line x1="16" y1="2" x2="16" y2="6"></line>
                      <line x1="8" y1="2" x2="8" y2="6"></line>
                      <line x1="3" y1="10" x2="21" y2="10"></line>
                    </svg>
                    <span>{schedulingLink ? 'Scheduling Available' : 'No Scheduling Link Set'}</span>
                  </div>
                  <div className="profile-info-item">
                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                      <polyline points="22,6 12,13 2,6"></polyline>
                    </svg>
                    <span>{attorney?.email || 'Email Not Set'}</span>
                  </div>
                </div>
                <div className="profile-cta">
                  <button className="profile-cta-button" style={{ backgroundColor: buttonColor }}>
                    {buttonText || 'Start Consultation'}
                  </button>
                </div>
              </div>
            </div>
            <div className="view-public-profile">
              <button className="dashboard-button secondary">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path>
                  <polyline points="15 3 21 3 21 9"></polyline>
                  <line x1="10" y1="14" x2="21" y2="3"></line>
                </svg>
                View Public Profile
              </button>
            </div>
          </div>
        </div>
      ) : activeTab === 'consultations' ? (
        <CrmViewSelector viewMode={crmViewMode} onViewChange={setCrmViewMode} />
      ) : activeTab === 'share' ? (
        <div className={`preview-panel ${!showPreview ? 'active' : ''}`}>
          <div className="preview-header">
            <h2>Widget Preview</h2>
            <div className="preview-actions">
              <button
                className="preview-button secondary"
                onClick={toggleTheme}
              >
                {isDarkTheme ? 'Light Mode' : 'Dark Mode'}
              </button>
              <button
                className="preview-button secondary"
                onClick={reloadIframe}
              >
                Refresh
              </button>
              <button
                className="preview-button secondary"
                onClick={refreshPreviewFromSupabase}
                title="Load the latest data from Supabase"
              >
                Load from Database
              </button>
              <button
                className="preview-button"
                onClick={() => setShowPreview(!showPreview)}
              >
                {showPreview ? 'Hide Preview' : 'Show Preview'}
              </button>
            </div>
          </div>
          <div className="preview-content">
            <iframe
              ref={iframeRef}
              className="preview-iframe"
              src={`/simple-preview?subdomain=${attorney?.subdomain || 'default'}&theme=${isDarkTheme ? 'dark' : 'light'}
                &primaryColor=${encodeURIComponent(primaryColor)}
                &secondaryColor=${encodeURIComponent(secondaryColor)}
                &buttonColor=${encodeURIComponent(buttonColor)}
                &backgroundColor=${encodeURIComponent(backgroundColor)}
                &backgroundOpacity=${backgroundOpacity}
                &buttonText=${encodeURIComponent(buttonText)}
                &buttonOpacity=${buttonOpacity}
                &practiceAreaBackgroundOpacity=${practiceAreaBackgroundOpacity}
                &welcomeMessage=${encodeURIComponent(welcomeMessage)}
                &informationGathering=${encodeURIComponent(informationGathering)}
                &logoUrl=${encodeURIComponent(logoUrl || '')}
                &titleText=${encodeURIComponent(attorney?.title_text || '')}
              `}
              title="Widget Preview"
            ></iframe>
          </div>
        </div>
      ) : activeTab === 'settings' ? (
        <div className={`preview-panel ${!showPreview ? 'active' : ''}`}>
          <div className="preview-header">
            <h2>System Status</h2>
            <div className="preview-actions">
              <button
                className="preview-button secondary"
                onClick={toggleTheme}
              >
                {isDarkTheme ? 'Light Mode' : 'Dark Mode'}
              </button>
            </div>
          </div>
          <div className="preview-content system-status">
            <div className="status-card">
              <h3>Account Status</h3>
              <div className="status-item">
                <span className="status-label">Plan:</span>
                <span className="status-value">Professional</span>
              </div>
              <div className="status-item">
                <span className="status-label">Status:</span>
                <span className="status-value active">Active</span>
              </div>
              <div className="status-item">
                <span className="status-label">Created:</span>
                <span className="status-value">{attorney?.created_at ? new Date(attorney.created_at).toLocaleDateString() : 'N/A'}</span>
              </div>
            </div>

            <div className="status-card">
              <h3>Usage Statistics</h3>
              <div className="status-item">
                <span className="status-label">Consultations:</span>
                <span className="status-value">0</span>
              </div>
              <div className="status-item">
                <span className="status-label">Minutes Used:</span>
                <span className="status-value">0 min</span>
              </div>
              <div className="status-item">
                <span className="status-label">Storage Used:</span>
                <span className="status-value">0 MB</span>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className={`preview-panel ${!showPreview ? 'active' : ''}`}>
          <div className="preview-header">
            <h2>Live Preview</h2>
            <div className="preview-actions">
              <button
                className="preview-button secondary"
                onClick={toggleTheme}
              >
                {isDarkTheme ? 'Light Mode' : 'Dark Mode'}
              </button>
              <button
                className="preview-button secondary"
                onClick={reloadIframe}
              >
                Refresh
              </button>
              <button
                className="preview-button secondary"
                onClick={refreshPreviewFromSupabase}
                title="Load the latest data from Supabase"
              >
                Load from Database
              </button>
              <button
                className="preview-button secondary"
                onClick={() => {
                  // Toggle between enhanced and simplified preview
                  const currentSrc = iframeRef.current.src;
                  const useEnhanced = !currentSrc.includes('useEnhancedPreview=true');
                  updatePreviewIframe(useEnhanced);
                }}
              >
                Toggle Preview Style
              </button>
              <button
                className="preview-button"
                onClick={() => setShowPreview(!showPreview)}
              >
                {showPreview ? 'Hide Preview' : 'Show Preview'}
              </button>
            </div>
          </div>
          <div className="preview-content">
            <iframe
              ref={iframeRef}
              className="preview-iframe"
              src=""
              title="Agent Preview"
            ></iframe>
          </div>
        </div>
      )}
        </>
      )}
    </div>
  );
};

export default Dashboard;

/**
 * Test updating a Vapi assistant name
 */

import fetch from 'node-fetch';

const VAPI_PRIVATE_KEY = '6734febc-fc65-4669-93b0-929b31ff6564';
const TEST_ASSISTANT_ID = '368e963b-761c-45bb-91e9-8f96b8483f4d';

async function updateAssistantName() {
  try {
    console.log(`Updating assistant: ${TEST_ASSISTANT_ID}`);
    
    const updateData = {
      name: "[RECYCLE BIN] Orphaned Assistant 1"
    };
    
    const response = await fetch(`https://api.vapi.ai/assistant/${TEST_ASSISTANT_ID}`, {
      method: 'PATCH',
      headers: {
        'Authorization': `Bearer ${VAPI_PRIVATE_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(updateData)
    });
    
    console.log(`Status: ${response.status}`);
    
    if (response.ok) {
      const result = await response.json();
      console.log('✅ Successfully updated!');
      console.log('New name:', result.name);
    } else {
      const error = await response.text();
      console.log('❌ Error:', error);
    }
    
  } catch (error) {
    console.error('Exception:', error);
  }
}

updateAssistantName();

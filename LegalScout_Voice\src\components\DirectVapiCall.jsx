import React, { useState, useEffect, useRef } from 'react';
import Vapi from '@vapi-ai/web';
import { DEFAULT_ASSISTANT_ID } from '../constants/vapiConstants';
import './VapiCall.css';
import TextShimmerWave from './TextShimmerWave';
import SpeechParticles from './SpeechParticles';
import { getVapiApiKey } from '../utils/vapiEnvironmentChecker';

/**
 * A direct implementation of the Vapi SDK for web calls
 * This component bypasses all the abstraction layers and directly uses the Vapi SDK
 */
const DirectVapiCall = ({
  onEndCall,
  welcomeMessage,
  systemPrompt,
  logoUrl,
  primaryColor,
  secondaryColor,
  assistantId // Add assistantId prop
}) => {
  // State
  const [status, setStatus] = useState('idle');
  const [messages, setMessages] = useState([]);
  const [volumeLevel, setVolumeLevel] = useState(0);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [userInput, setUserInput] = useState('');
  const [isTranscribing, setIsTranscribing] = useState(false);
  const [transcript, setTranscript] = useState('');

  // Refs
  const vapiRef = useRef(null);
  const containerRef = useRef(null);
  const messagesEndRef = useRef(null);

  // Message deduplication system
  const processedMessages = useRef(new Set()).current;

  // Initialize Vapi on component mount
  useEffect(() => {
    console.log('🚀 [DirectVapiCall] Initializing component');
    console.log('🎯 [DirectVapiCall] Assistant ID prop:', assistantId);
    console.log('💬 [DirectVapiCall] Welcome message prop:', welcomeMessage);
    console.log('🔊 [DirectVapiCall] System prompt prop:', systemPrompt ? 'Present' : 'Missing');

    // CRITICAL FIX: Force correct API key in production
    const isProduction = typeof window !== 'undefined' &&
      (window.location.hostname === 'dashboard.legalscout.net' ||
       window.location.hostname.endsWith('.legalscout.net') ||
       window.location.hostname === 'legalscout.net');

    const apiKey = isProduction ?
      '310f0d43-27c2-47a5-a76d-e55171d024f7' : // Force public key in production
      getVapiApiKey('client');

    if (!apiKey) {
      console.error('DirectVapiCall: No API key found');
      return;
    }

    console.log('DirectVapiCall: Production mode:', isProduction);
    console.log('DirectVapiCall: Using API key for client operations:', apiKey.substring(0, 8) + '...');

    try {
      console.log('DirectVapiCall: Creating Vapi instance with API key');
      vapiRef.current = new Vapi(apiKey);

      // Set up event listeners
      setupEventListeners();
    } catch (error) {
      console.error('DirectVapiCall: Error initializing Vapi:', error);
    }

    // Cleanup on unmount
    return () => {
      if (vapiRef.current) {
        try {
          console.log('DirectVapiCall: Cleaning up Vapi instance');
          vapiRef.current.stop();
        } catch (error) {
          console.error('DirectVapiCall: Error cleaning up Vapi instance:', error);
        }
      }
    };
  }, []);

  // Improved scroll to keep latest message in view without affecting global scroll
  const scrollToBottom = () => {
    if (messagesEndRef.current) {
      const conversationArea = messagesEndRef.current.closest('.conversation-area');
      if (conversationArea) {
        // Check if user is near the bottom (within 100px)
        const isNearBottom = conversationArea.scrollHeight - conversationArea.scrollTop - conversationArea.clientHeight < 100;

        // Only auto-scroll if user is near the bottom (not reading old messages)
        if (isNearBottom) {
          // Use a more contained approach that doesn't affect parent windows
          try {
            // Directly set scrollTop without any smooth scrolling or events
            conversationArea.scrollTop = conversationArea.scrollHeight;

            // Ensure the scroll stays within the conversation area
            conversationArea.style.scrollBehavior = 'auto';

            // Force a layout recalculation to ensure scroll position is applied
            conversationArea.offsetHeight;

            console.log('DirectVapiCall: Scrolled conversation area to bottom');
          } catch (error) {
            console.warn('Error scrolling conversation area:', error);
          }
        }
      }
    }
  };

  useEffect(() => {
    // Only scroll if we have messages, with debouncing to prevent excessive scrolling
    if (messages.length > 0) {
      // Use a timeout to debounce scroll calls
      const scrollTimeout = setTimeout(() => {
        scrollToBottom();
      }, 50); // Small delay to batch multiple message updates

      return () => clearTimeout(scrollTimeout);
    }
  }, [messages]);

  // Set up event listeners for the Vapi instance
  const setupEventListeners = () => {
    if (!vapiRef.current) return;

    vapiRef.current.on('call-start', () => {
      console.log('DirectVapiCall: Call started');
      setStatus('connected');

      // Add welcome message to UI
      // Note: We don't add it here anymore, it will come through the model-output event
      // This prevents duplicate welcome messages
      console.log('Welcome message will be spoken by the assistant automatically');
    });

    vapiRef.current.on('call-end', () => {
      console.log('DirectVapiCall: Call ended');
      setStatus('idle');
      if (onEndCall) onEndCall();
    });

    vapiRef.current.on('speech-start', () => {
      console.log('DirectVapiCall: Assistant started speaking');
      setIsSpeaking(true);
    });

    vapiRef.current.on('speech-end', () => {
      console.log('DirectVapiCall: Assistant stopped speaking');
      setIsSpeaking(false);
    });

    vapiRef.current.on('volume-level', (level) => {
      setVolumeLevel(level);
    });

    vapiRef.current.on('transcription-start', () => {
      console.log('DirectVapiCall: Transcription started');
      setIsTranscribing(true);
    });

    vapiRef.current.on('transcription-end', () => {
      console.log('DirectVapiCall: Transcription ended');
      setIsTranscribing(false);
      setTranscript('');
    });

    vapiRef.current.on('transcription-update', (update) => {
      console.log('DirectVapiCall: Transcription update:', update);
      // Make sure we're getting the transcript text correctly
      const transcriptText = update.transcript || update.text || '';
      console.log('Transcript text:', transcriptText);
      setTranscript(transcriptText);
    });

    // Use the shared message deduplication system

    vapiRef.current.on('message', (message) => {
      console.log('DirectVapiCall: Message received:', message);

      // Extract the message text based on message type
      let messageText = '';
      let messageType = '';

      console.log('Message type:', message.type);
      console.log('Message role:', message.role);
      console.log('Full message object:', JSON.stringify(message, null, 2));

      // First check if the message has a role property
      if (message.role === 'user') {
        messageText = message.content || message.text || message.transcript || '';
        messageType = 'user';
        console.log('User message by role:', messageText);
      } else if (message.role === 'assistant' || message.role === 'bot') {
        messageText = message.content || message.text || '';
        messageType = 'assistant';
        console.log('Assistant message by role:', messageText);
      }
      // If no role, check message type
      else if (message.type === 'transcript') {
        messageText = message.transcript?.text || message.transcript || '';
        messageType = 'user';
        console.log('User transcript text:', messageText);
      } else if (message.type === 'model-output') {
        messageText = message.output?.content || message.content || message.text || '';
        messageType = 'assistant';
        console.log('Assistant output text:', messageText);
      } else if (message.type === 'user-message') {
        messageText = message.text || message.content || '';
        messageType = 'user';
        console.log('User message text:', messageText);
      } else if (message.type === 'conversation-update') {
        // Handle conversation updates
        if (message.messages && Array.isArray(message.messages)) {
          // Get the last message from the conversation
          const lastMessage = message.messages[message.messages.length - 1];
          if (lastMessage) {
            messageText = lastMessage.message || lastMessage.content || '';
            messageType = lastMessage.role === 'user' ? 'user' : 'assistant';
            console.log(`Conversation update - ${messageType} message:`, messageText);
          }
        }
      }

      // Only process if we have valid text
      if (messageText.trim()) {
        // Create a unique key for this message
        const messageKey = `${messageType}:${messageText}`;

        // Only add if we haven't processed this exact message before
        if (!processedMessages.has(messageKey)) {
          processedMessages.add(messageKey);

          // Add to UI with explicit role
          setMessages(prev => [
            ...prev,
            {
              type: messageType,
              role: messageType, // Add explicit role property matching the type
              text: messageText,
              animated: messageType === 'assistant'
            }
          ]);
          console.log(`Added ${messageType} message:`, messageText);
        } else {
          console.log('Skipping duplicate message:', messageKey);
        }
      }
    });

    vapiRef.current.on('error', (error) => {
      console.error('DirectVapiCall: Error:', error);
      setStatus('error');
    });

    // Add specific handlers for speech recognition
    vapiRef.current.on('speech-recognition-start', () => {
      console.log('DirectVapiCall: Speech recognition started');
      setIsTranscribing(true);
    });

    vapiRef.current.on('speech-recognition-end', () => {
      console.log('DirectVapiCall: Speech recognition ended');
      setIsTranscribing(false);
    });

    vapiRef.current.on('speech-recognition-result', (result) => {
      console.log('DirectVapiCall: Speech recognition result:', result);
      console.log('Full speech recognition result:', JSON.stringify(result, null, 2));

      const recognizedText = result.transcript || result.text || '';
      if (recognizedText.trim()) {
        // Create a unique key for this message
        const messageKey = `user:${recognizedText}`;

        // Only add if we haven't processed this exact message before
        if (!processedMessages.has(messageKey)) {
          processedMessages.add(messageKey);

          // Add to UI with explicit user type
          setMessages(prev => [
            ...prev,
            {
              type: 'user',
              role: 'user', // Add explicit role property
              text: recognizedText
            }
          ]);
          console.log('Added user speech recognition message:', recognizedText);
        } else {
          console.log('Skipping duplicate speech recognition result:', messageKey);
        }
      }
    });
  };

  // Start the call
  useEffect(() => {
    // Check if a call is already active globally to prevent multiple simultaneous calls
    if (window.vapiCallActive === true) {
      console.log('DirectVapiCall: Another call is already active, skipping initialization');
      return;
    }

    if (status === 'idle' && vapiRef.current) {
      // Set the global variable to indicate that a call is active
      window.vapiCallActive = true;
      console.log('DirectVapiCall: Set window.vapiCallActive to true');

      const startCall = async () => {
        try {
          // Use the passed assistantId or fall back to default
          const effectiveAssistantId = assistantId || DEFAULT_ASSISTANT_ID;
          console.log('🚀 [DirectVapiCall] Starting call with assistant ID:', effectiveAssistantId);
          console.log('🎯 [DirectVapiCall] assistantId prop:', assistantId);
          console.log('🔧 [DirectVapiCall] DEFAULT_ASSISTANT_ID:', DEFAULT_ASSISTANT_ID);
          console.log('⚙️ [DirectVapiCall] Vapi instance ready:', !!vapiRef.current);

          // Validate that we have a valid assistant ID
          if (!effectiveAssistantId || effectiveAssistantId.trim() === '') {
            throw new Error('No valid assistant ID provided');
          }

          // If we have a custom assistant ID, use it directly without overrides
          if (assistantId && assistantId.trim() !== '') {
            console.log('DirectVapiCall: Using custom assistant without overrides:', assistantId);
            setStatus('connecting');
            await vapiRef.current.start(assistantId);
            console.log('DirectVapiCall: Call started successfully with custom assistant');
          } else {
            // Only use overrides if we're falling back to the default assistant
            console.log('DirectVapiCall: Using default assistant with overrides');

            const assistantOverrides = {
              // First Message override - this is the message that will be spoken first
              firstMessage: welcomeMessage,
              // First Message Mode - ensure the assistant speaks first
              firstMessageMode: "assistant-speaks-first",
              // Ensure the first message is not interrupted
              firstMessageInterruptionsEnabled: false,
              // Model configuration with system prompt
              model: {
                provider: "anthropic", // Required provider field
                model: "claude-3-sonnet-20240229", // Required model field
                messages: [
                  {
                    role: "system",
                    content: systemPrompt || "You are a helpful assistant."
                  }
                ]
              },
              // Ensure recording is enabled
              artifactPlan: {
                recordingEnabled: true
              }
            };

            console.log('DirectVapiCall: Using assistantOverrides:', assistantOverrides);

            setStatus('connecting');
            await vapiRef.current.start(DEFAULT_ASSISTANT_ID, assistantOverrides);
            console.log('DirectVapiCall: Call started successfully with default assistant');
          }
        } catch (error) {
          console.error('DirectVapiCall: Error starting call:', error);
          console.error('DirectVapiCall: Error details:', {
            message: error.message,
            stack: error.stack,
            assistantId: assistantId,
            effectiveAssistantId: assistantId || DEFAULT_ASSISTANT_ID
          });
          setStatus('error');
        }
      };

      startCall();
    }
  }, [status, welcomeMessage, systemPrompt, assistantId]);

  // End the call
  const endCall = () => {
    if (!vapiRef.current) {
      console.warn('DirectVapiCall: Cannot end call, Vapi not initialized');
      return;
    }

    try {
      console.log('DirectVapiCall: Ending call...');
      vapiRef.current.stop();

      // Update status even before the event listener fires
      setStatus('idle');
      if (onEndCall) onEndCall();
    } catch (error) {
      console.error('DirectVapiCall: Error ending call:', error);
    }
  };

  // Handle text input submission
  const handleSubmit = (e) => {
    e.preventDefault();
    if (!userInput.trim() || status !== 'connected') return;

    // Add user message to UI immediately, using the same deduplication system
    const messageKey = `user:${userInput}`;

    // Only add if we haven't processed this exact message before
    if (!processedMessages.has(messageKey)) {
      processedMessages.add(messageKey);

      // Add to UI with explicit user role
      setMessages(prev => [
        ...prev,
        {
          type: 'user',
          role: 'user', // Add explicit role property
          text: userInput
        }
      ]);
      console.log('Added user text input message:', userInput);
    } else {
      console.log('Skipping duplicate text input:', messageKey);
    }

    // Send message to Vapi
    if (vapiRef.current) {
      try {
        vapiRef.current.send({
          type: 'text',
          text: userInput
        });
        console.log('DirectVapiCall: Sent text message:', userInput);
      } catch (error) {
        console.error('DirectVapiCall: Error sending text message:', error);
      }
    }

    // Clear input
    setUserInput('');
  };

  // Render message with simplified styling (matching main VapiCall component)
  const renderMessage = (message, index) => (
    <div key={index} className={`message ${message.type}`}>
      <div className="message-content">
        {message.animated ? (
          <TextShimmerWave
            text={message.text}
            rainbow={message.type === 'assistant'}
            colors={message.type === 'user' ? ['#66c6ff', '#53ffed', '#fff78a', '#66c6ff'] : undefined}
            speed={message.type === 'assistant' ? 1.5 : 1.2}
          />
        ) : (
          <p className="message-text">{message.text}</p>
        )}
        <div className="message-timestamp">
          {new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
        </div>
      </div>
    </div>
  );

  // Render the component
  return (
    <div className="vapi-call-container" ref={containerRef} style={{
      width: '100%',
      maxWidth: 'none',
      margin: 0,
      padding: 0,
      position: 'absolute',
      top: '80px',
      left: 0,
      right: 0,
      bottom: 0,
      height: 'calc(100vh - 80px)'
    }}>
      {/* Call interface with proper styling */}
      <div className="call-interface" style={{
        width: '100%',
        maxWidth: 'none',
        margin: 0,
        padding: 0,
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0
      }}>
        {/* End Call button - positioned at top right corner */}
        <div style={{ position: 'absolute', top: '20px', right: '20px', zIndex: 1000, display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
          <button
            className="end-call-button"
            onClick={endCall}
            aria-label="End call"
            style={{
              width: '40px',
              height: '40px',
              borderRadius: '50%',
              backgroundColor: secondaryColor || '#1a3363',
              border: 'none',
              padding: 0,
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              boxShadow: '0 2px 10px rgba(0, 0, 0, 0.2)',
              transition: 'all 0.3s ease',
              position: 'relative',
              overflow: 'hidden'
            }}>
            <img
              src={logoUrl || '/PRIMARY CLEAR.png'}
              alt="End"
              style={{
                width: '100%',
                height: '100%',
                objectFit: 'cover',
                position: 'absolute',
                top: 0,
                left: 0,
                borderRadius: '50%',
                opacity: 0.85,
                zIndex: 2
              }}
              onError={(e) => {
                console.error('Error loading logo in end call button:', e);
                e.target.src = '/PRIMARY CLEAR.png'; // Fallback to default logo
              }}
            />
          </button>
          <span style={{ marginTop: '5px', fontSize: '12px', fontWeight: 'bold', color: 'white', textShadow: '0 1px 2px rgba(0,0,0,0.5)' }}>End Call</span>
        </div>

        {/* Main content area with three-column layout */}
        <div className="three-column-layout" style={{
          width: '100%',
          maxWidth: 'none',
          margin: 0,
          padding: 0,
          position: 'absolute',
          top: '60px',
          left: 0,
          right: 0,
          bottom: '80px'
        }}>
          {/* Left Column - Dossier Component */}
          <div className="column left-column">
            <div className="dossier-component">
              <h3 className="dossier-title">Case Information</h3>
              <div className="dossier-items">
                <div className="case-info-item status-item">
                  <span className="item-icon">📊</span>
                  <span className="item-label">STATUS</span>
                  <div className="item-value">
                    Awaiting client information...
                  </div>
                </div>
                <div className="case-info-item">
                  <span className="item-icon">👤</span>
                  <span className="item-label">CLIENT</span>
                  <div className="item-value">
                    Not provided yet
                  </div>
                </div>
                <div className="case-info-item">
                  <span className="item-icon">📍</span>
                  <span className="item-label">LOCATION</span>
                  <div className="item-value">
                    Not provided yet
                  </div>
                </div>
                <div className="case-info-item">
                  <span className="item-icon">📝</span>
                  <span className="item-label">CASE TYPE</span>
                  <div className="item-value">
                    Not provided yet
                  </div>
                </div>
                <div className="case-info-item">
                  <span className="item-icon">📅</span>
                  <span className="item-label">DATE</span>
                  <div className="item-value">
                    {new Date().toLocaleDateString()}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Middle Column - Empty for Map Visibility */}
          <div className="column middle-column">
            {/* This space intentionally left empty to show the map */}
          </div>

          {/* Right Column - Conversation Area */}
          <div className="column right-column">
            <div className="conversation-container">
              {/* Voice indicators at the top of conversation */}
              <div className="voice-indicators">
                <div className="voice-status">
                  <div className={`speaking-indicator ${isSpeaking ? 'active' : ''}`}>
                    <span className="indicator-dot"></span>
                    {isSpeaking ? 'Assistant is speaking' : 'Assistant idle'}
                  </div>
                  <div className="volume-level-container">
                    <div className="volume-label">Your voice:</div>
                    <div className="volume-bars">
                      {[...Array(10)].map((_, i) => (
                        <div
                          key={i}
                          className={`volume-bar ${i < Math.floor(volumeLevel * 10) ? 'active' : ''}`}
                          style={{ backgroundColor: i < Math.floor(volumeLevel * 10) ? primaryColor : 'rgba(255,255,255,0.2)' }}
                        ></div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              {/* Conversation area with messages */}
              <div className="conversation-area">
                {messages.map((message, index) => renderMessage(message, index))}

                {/* Live transcription */}
                {isTranscribing && transcript && (
                  <div
                    className="message user transcribing"
                    style={{
                      justifyContent: 'flex-end',
                      display: 'flex',
                      width: '100%',
                      margin: '10px 0'
                    }}
                  >
                    <div
                      className="message-content"
                      style={{
                        backgroundColor: 'rgba(59, 130, 246, 0.2)',
                        borderRight: '3px solid rgba(59, 130, 246, 0.8)',
                        borderLeft: 'none',
                        borderTopRightRadius: '4px',
                        borderTopLeftRadius: '12px',
                        color: 'var(--text-primary)',
                        opacity: 0.8
                      }}
                    >
                      <TextShimmerWave
                        text={transcript}
                        rainbow={false}
                        colors={['#66c6ff', '#53ffed', '#fff78a', '#66c6ff']}
                        speed={2}
                        italic={true}
                      />
                      <div className="typing-indicator">...</div>
                    </div>
                  </div>
                )}

                {/* Invisible element for auto-scrolling */}
                <div ref={messagesEndRef} />
              </div>

              {/* Bottom centered text input area with improved styling */}
              <div className="text-input-container-wrapper">
                <div className="text-input-area">
                  <div className="text-input-container">
                    <form onSubmit={handleSubmit} className="text-input-form">
                      <input
                        type="text"
                        className="message-input"
                        value={userInput}
                        onChange={(e) => setUserInput(e.target.value)}
                        placeholder="Type a message..."
                        disabled={status !== 'connected'}
                      />
                      <button
                        type="submit"
                        className="send-button"
                        disabled={!userInput.trim() || status !== 'connected'}
                        style={{ backgroundColor: primaryColor }}
                      >
                        <span>Send</span>
                      </button>
                    </form>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DirectVapiCall;

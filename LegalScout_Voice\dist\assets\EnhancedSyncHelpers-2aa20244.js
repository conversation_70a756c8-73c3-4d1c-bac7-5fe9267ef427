import{k as l,a as r}from"./index-27efa71d.js";const m=e=>e?/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(e):!1,y=async e=>{if(!m(e))return console.warn(`[EnhancedSyncHelpers] Invalid attorney ID: ${e}`),null;const{data:n,error:t}=await l.from("attorneys").select("*").eq("id",e).single();if(t)throw t;return n},p=async(e,n)=>{const t={...n,updated_at:new Date().toISOString()},{data:i,error:s}=await l.from("attorneys").update(t).eq("id",e).select().single();if(s)throw s;return i},b=async e=>{const{data:n,error:t}=await l.from("attorneys").select("*").eq("email",e).order("updated_at",{ascending:!1});if(t)throw t;return n&&n.length>0?(n.length>1&&console.warn(`Found ${n.length} attorneys for email ${e}, using the most recent one`),n[0]):null},w=async e=>{try{if(!m(e))return console.warn(`Invalid auth ID format: ${e}`),null;const{data:n,error:t}=await l.from("attorneys").select("*").eq("user_id",e).single();if(t&&t.code!=="PGRST116")throw t;return n||null}catch(n){throw n}},C=async e=>{try{const{data:n,error:t}=await l.from("attorneys").select("*").eq("id",e).single();if(t&&t.code!=="PGRST116")throw t;return n||null}catch(n){throw console.error("Error fetching attorney by ID:",n),n}},S=async e=>{const{data:n,error:t}=await l.from("attorneys").insert([e]).select().single();if(t)throw t;return n},v=async(e,n)=>{const t={...n,updated_at:new Date().toISOString()},{data:i,error:s}=await l.from("attorneys").update(t).eq("id",e).select().single();if(s)throw s;return i},f=async e=>{if(!r.connected){const n=typeof import.meta<"u"&&"310f0d43-27c2-47a5-a76d-e55171d024f7"||typeof window<"u"&&window.VITE_VAPI_PUBLIC_KEY;if(!n)throw new Error("No Vapi API key available");await r.connect(n)}return await r.getAssistant(e)},h=async e=>{const n="global_assistant_creation_limit",t=localStorage.getItem(n),i=Date.now(),s=3e4;if(t&&i-parseInt(t)<s)throw console.warn("[EnhancedSyncHelpers] Global rate limit hit - assistant creation blocked"),new Error("Assistant creation rate limited globally - please wait 30 seconds");if(localStorage.setItem(n,i.toString()),console.log("[EnhancedSyncHelpers] Creating Vapi assistant:",{name:e.name,hasInstructions:!!e.instructions,voiceProvider:e.voice?.provider,voiceId:e.voice?.voiceId,timestamp:new Date().toISOString(),url:window.location.href}),!r.connected){const o=typeof import.meta<"u"&&"6734febc-fc65-4669-93b0-929b31ff6564"||typeof window<"u"&&window.VITE_VAPI_SECRET_KEY;if(o)console.log("🔑 [EnhancedSyncHelpers] Using private key for assistant management"),await r.connect(o);else{console.warn("No Vapi private key available, falling back to public key");const c=typeof import.meta<"u"&&"310f0d43-27c2-47a5-a76d-e55171d024f7"||typeof window<"u"&&window.VITE_VAPI_PUBLIC_KEY;if(!c)throw new Error("No Vapi API key available");await r.connect(c)}}const a={name:e.name,firstMessage:e.firstMessage,firstMessageMode:"assistant-speaks-first",model:{provider:"openai",model:e.ai_model||"gpt-4o",messages:[{role:"system",content:e.instructions||"You are a helpful assistant."}]},voice:e.voice,transcriber:{provider:"deepgram",model:"nova-3"},backgroundSound:"off",backgroundDenoisingEnabled:!1,silenceTimeoutSeconds:30,maxDurationSeconds:600,clientMessages:["conversation-update","function-call","hang","model-output","speech-update","status-update","transcript","tool-calls","user-interrupted","voice-input"]};return await r.createAssistant(a)},g=async(e,n)=>{if(!r.connected){const i=typeof import.meta<"u"&&"6734febc-fc65-4669-93b0-929b31ff6564"||typeof window<"u"&&window.VITE_VAPI_SECRET_KEY;if(i)console.log("🔑 [EnhancedSyncHelpers] Using private key for assistant management"),await r.connect(i);else{console.warn("No Vapi private key available, falling back to public key");const s=typeof import.meta<"u"&&"310f0d43-27c2-47a5-a76d-e55171d024f7"||typeof window<"u"&&window.VITE_VAPI_PUBLIC_KEY;if(!s)throw new Error("No Vapi API key available");await r.connect(s)}}const t={name:n.name,firstMessage:n.firstMessage,firstMessageMode:"assistant-speaks-first",model:{provider:"openai",model:n.model?.model||n.llm?.model||n.ai_model||"gpt-4o",messages:[{role:"system",content:n.instructions||"You are a helpful assistant."}]},voice:n.voice,transcriber:{provider:"deepgram",model:"nova-3"},backgroundSound:"off",backgroundDenoisingEnabled:!1,silenceTimeoutSeconds:30,maxDurationSeconds:600,clientMessages:["conversation-update","function-call","hang","model-output","speech-update","status-update","transcript","tool-calls","user-interrupted","voice-input"]};return console.log("🔧 [EnhancedSyncHelpers] Updating assistant with instructions:",{assistantId:e.substring(0,8)+"...",instructionsLength:t.model.messages[0].content.length,instructionsPreview:t.model.messages[0].content.substring(0,100)+"..."}),await r.updateAssistant(e,t)},N=async e=>await f(e),T=(e,n)=>{if(!e||!n)return{};const t={};return e.firm_name!==n.name&&(t.firm_name={supabase:e.firm_name,vapi:n.name}),e.welcome_message!==n.firstMessage&&(t.welcome_message={supabase:e.welcome_message,vapi:n.firstMessage}),e.vapi_instructions!==n.instructions&&(t.vapi_instructions={supabase:e.vapi_instructions,vapi:n.instructions}),(e.voice_provider!==n.voice?.provider||e.voice_id!==n.voice?.voiceId)&&(t.voice={supabase:{provider:e.voice_provider,voiceId:e.voice_id},vapi:n.voice}),e.ai_model&&e.ai_model!==n.llm?.model&&(t.ai_model={supabase:e.ai_model,vapi:n.llm?.model}),t},R=async e=>({"11labs":["sarah","adam","daniel","josh","rachel","domi","freya","antoni","thomas","charlie","emily","elli","callum","patrick","harry","liam","dorothy","josh","arnold","charlotte","matilda","matthew","james","joseph","jeremy","michael","ethan"],playht:["ranger","waylon","leyro","nova","stella","cody","maya","ryan","tyler","luke","jackson","hudson","brett","theo","ruby","daisy","olivia","lily","emma","sophia","ava","mia","isabella","charlotte","amelia","harper","evelyn"],deepgram:["nova-1","nova-2","nova-3","aura-1","aura-2","stella-1","stella-2","luna-1","luna-2"],openai:["alloy","echo","fable","onyx","nova","shimmer"]})[e]||[],u=(e,n)=>{const t={"11labs":["sarah","adam","daniel","josh","rachel","domi","freya","antoni","thomas","charlie","emily","elli","callum","patrick","harry","liam","dorothy","arnold","charlotte","matilda","matthew","james","joseph","jeremy","michael","ethan"],playht:["waylon","leyro","nova-playht","stella","cody","maya","ryan","tyler","luke","jackson","hudson","brett","theo","ruby","daisy","olivia","lily","emma","sophia","ava","mia","isabella","charlotte-playht","amelia","harper","evelyn"],openai:["alloy","echo","fable","onyx","nova","shimmer"],vapi:["jennifer","mark"]};let i=e,s=n;if(!t[n]||!t[n].includes(e)){console.log(`[EnhancedSyncHelpers] Invalid voice/provider combination: ${e}/${n}`);let a=null;for(const[o,c]of Object.entries(t))if(c.includes(e)){a=o;break}a?(s=a,console.log(`[EnhancedSyncHelpers] Fixed voice provider: ${e} -> ${a}`)):(i="sarah",s="11labs",console.log(`[EnhancedSyncHelpers] Voice ${e} not found, using default: sarah/11labs`))}return{voiceId:i,provider:s}},M=async e=>{const{attorneyId:n,forceUpdate:t=!1}=e;try{const i=await y(n);if(!i)throw new Error(`Attorney not found in Supabase: ${n}`);console.log("[EnhancedSyncHelpers] Retrieved data from Supabase:",i.id);let s=null,a=null;if(i.vapi_assistant_id)try{if(s=await f(i.vapi_assistant_id),console.log("[EnhancedSyncHelpers] Retrieved data from Vapi:",s?s.id:"not found"),!s)console.warn("[EnhancedSyncHelpers] Assistant not found in Vapi - this should be handled manually to prevent mass creation"),console.warn("[EnhancedSyncHelpers] Assistant ID in Supabase:",i.vapi_assistant_id),console.warn("[EnhancedSyncHelpers] Attorney:",i.firm_name,i.email),await p(n,{vapi_assistant_id:null}),console.log("[EnhancedSyncHelpers] Cleared invalid assistant ID from Supabase - manual assistant creation required"),a={action:"cleared_invalid_id",assistantId:null,message:"Invalid assistant ID cleared - manual creation required"};else{const o=T(i,s);if(Object.keys(o).length>0||t){console.log("[EnhancedSyncHelpers] Discrepancies found, updating Vapi assistant:",o);try{const c=u(i.voice_id||"sarah",i.voice_provider||"11labs"),I=await g(i.vapi_assistant_id,{name:i.firm_name||"LegalScout Assistant",firstMessage:i.welcome_message||"Hello, how can I help you today?",instructions:i.vapi_instructions||"You are a legal assistant helping clients with their legal needs.",voice:{provider:c.provider,voiceId:c.voiceId},ai_model:i.ai_model||"gpt-4o"});console.log("[EnhancedSyncHelpers] Updated Vapi assistant:",I.id),a={action:"updated",assistantId:I.id,discrepancies:Object.keys(o).length>0?o:null}}catch(c){throw console.error("[EnhancedSyncHelpers] Error updating Vapi assistant:",c),c}}else console.log("[EnhancedSyncHelpers] No discrepancies found, Vapi assistant is up to date"),a={action:"none",assistantId:i.vapi_assistant_id}}}catch(o){throw console.error("[EnhancedSyncHelpers] Error fetching from Vapi:",o),o}else{console.log("[EnhancedSyncHelpers] No Vapi assistant ID found for attorney:",i.email);const o=i.email==="<EMAIL>",c=`assistant_creation_${n}`,I=localStorage.getItem(c),E=Date.now(),V=6e4;if(I&&E-parseInt(I)<V)return console.warn("[EnhancedSyncHelpers] Rate limit hit - assistant creation blocked"),{action:"rate_limited",success:!1,message:"Assistant creation rate limited - please wait before trying again"};if(!o)return console.warn("[EnhancedSyncHelpers] Assistant creation skipped - manual creation required for non-system assistants"),{action:"creation_skipped",success:!0,message:"No assistant ID found - manual creation required",requiresManualCreation:!0};try{console.log("[EnhancedSyncHelpers] Creating system <NAME_EMAIL>"),localStorage.setItem(c,E.toString());const d=u(i.voice_id||"sarah",i.voice_provider||"11labs"),_=await h({name:i.firm_name||"LegalScout Assistant",firstMessage:i.welcome_message||"Hello, how can I help you today?",instructions:i.vapi_instructions||"You are a legal assistant helping clients with their legal needs.",voice:{provider:d.provider,voiceId:d.voiceId},ai_model:i.ai_model||"gpt-4o"});await p(n,{vapi_assistant_id:_.id}),console.log("[EnhancedSyncHelpers] Created new system assistant:",_.id),a={action:"created",assistantId:_.id}}catch(d){throw console.error("[EnhancedSyncHelpers] Error creating Vapi assistant:",d),d}}return{action:a?a.action:"checked",success:!0,message:"Profile persistence ensured",sources:{supabase:!!i,vapi:!!s},vapiResult:a}}catch(i){return{action:"error",success:!1,message:`Error ensuring profile persistence: ${i.message}`,error:i.message}}};export{S as createAttorney,h as createVapiAssistant,M as ensureProfilePersistence,y as fetchFromSupabase,f as fetchFromVapi,T as findProfileDiscrepancies,w as getAttorneyByAuthId,b as getAttorneyByEmail,C as getAttorneyById,R as getValidVoicesForProvider,N as getVapiAssistant,v as updateAttorney,p as updateSupabaseAttorney,g as updateVapiAssistant};

/**
 * Authentication Context - ORIGINAL BACKUP
 * 
 * This is a backup of the original AuthContext before Phase 1 optimization.
 * Created as safety measure during authentication flow optimization.
 */

import React, { useState, useEffect, useRef } from 'react';
import { getSupabaseClient } from '../lib/supabase-fixed';
import {
  signInWithOAuth,
  handleOAuthCallback,
  signOut as authSignOut,
  getCurrentSession
} from '../services/authService';

// Create proper React context
const AuthContext = React.createContext(null);

// Custom hook to use the auth context
export const useAuth = () => {
  const context = React.useContext(AuthContext);

  if (!context) {
    console.warn('Auth context not found, returning default values');
    return {
      user: null,
      attorney: null,
      isLoading: false,
      signOut: () => Promise.resolve(),
      isAuthenticated: false
    };
  }

  return context;
};

/**
 * Authentication Provider - ORIGINAL VERSION
 */
export const AuthProvider = ({ children, syncTools }) => {
  // State for tracking authentication state
  const [user, setUser] = useState(null);
  const [attorney, setAttorney] = useState(null);
  const [session, setSession] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // CRITICAL FIX: Add ref to prevent repeated auth sync calls
  const authSyncInProgress = useRef(false);

  // Initialize authentication state
  useEffect(() => {
    const initAuth = async () => {
      console.log('🔐 [AuthContext] Starting auth initialization...');
      setLoading(true);
      setError(null);

      // CRITICAL FIX: For localhost, use normal auth flow but don't force attorney authentication
      if (typeof window !== 'undefined' &&
          (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1')) {
        console.log('🔐 [AuthContext] 🏠 Localhost detected - using normal auth flow without attorney forcing');
        // Continue with normal auth flow - don't bypass it
      }

      // 🛡️ CRITICAL: Force loading to false after maximum timeout (reduced for development)
      const forceLoadingTimeout = setTimeout(() => {
        console.log('🔐 [AuthContext] ⚠️ FORCING loading to false after timeout');
        setLoading(false);
      }, 2000); // 2 second maximum for faster development

      try {
        // Get current session from Supabase
        const supabase = await getSupabaseClient();
        const { data: { session }, error } = await supabase.auth.getSession();

        if (error) {
          console.error('🔐 [AuthContext] Error getting session:', error);
          setUser(null);
          setSession(null);
          setAttorney(null);
        } else if (session?.user) {
          console.log('🔐 [AuthContext] OAuth user data:', session.user);

          // Ensure email is set from OAuth - check all possible locations with detailed logging
          console.log('🔐 [AuthContext] OAuth user data details:', {
            directEmail: session.user.email,
            metadataEmail: session.user.user_metadata?.email,
            identityEmail: session.user.identities?.[0]?.identity_data?.email,
            rawMetadata: session.user.user_metadata,
            rawAppMetadata: session.user.app_metadata,
            identities: session.user.identities
          });

          const userEmail = session.user.email ||
                           session.user.user_metadata?.email ||
                           session.user.identities?.[0]?.identity_data?.email ||
                           session.user.user_metadata?.email_verified && session.user.user_metadata?.sub ||
                           '';

          console.log('🔐 [AuthContext] Found OAuth email:', userEmail);

          const userWithEmail = {
            ...session.user,
            email: userEmail
          };

          setUser(userWithEmail);
          setSession(session);

          // If sync tools are available, use them to get attorney data
          // CRITICAL FIX: Add debouncing to prevent repeated API calls
          if (syncTools?.handleAuthState && !authSyncInProgress.current) {
            try {
              authSyncInProgress.current = true;
              console.log('🔐 [AuthContext] Handling auth state for refresh...');

              // 🛡️ CRITICAL: Set timeout for sync tools to prevent hanging
              const syncPromise = syncTools.handleAuthState({
                user: userWithEmail,
                session
              }, 'refresh');

              const syncTimeout = new Promise((_, reject) =>
                setTimeout(() => reject(new Error('Sync timeout')), 3000)
              );

              const syncResult = await Promise.race([syncPromise, syncTimeout]);

              console.log('🔐 [AuthContext] Auth state sync result:', {
                success: syncResult.success,
                hasAttorney: !!syncResult.attorney,
                fallback: !!syncResult.fallback,
                message: syncResult.message
              });

              if (syncResult.success && syncResult.attorney) {
                setAttorney(syncResult.attorney);
              } else if (syncResult.fallback) {
                // If we're using a fallback result, log it but don't update attorney state
                console.log('🔐 [AuthContext] Using fallback result, attorney state not updated');
              }
            } catch (syncError) {
              console.error('🔐 [AuthContext] Error syncing auth state:', syncError);
              // Continue despite the error - don't let auth sync failures break the auth flow
            } finally {
              authSyncInProgress.current = false;
            }
          }
        } else {
          console.log('🔐 [AuthContext] No session found');
          setUser(null);
          setSession(null);
          setAttorney(null);
        }
      } catch (error) {
        console.error('🔐 [AuthContext] Unexpected error checking auth:', error);
        setUser(null);
        setSession(null);
        setAttorney(null);
        setError(error.message);
      } finally {
        clearTimeout(forceLoadingTimeout);
        console.log('🔐 [AuthContext] ✅ Auth initialization complete, setting loading to false');
        setLoading(false);
      }
    };

    initAuth();
    // ... rest of original implementation
  }, [syncTools]);

  // ... rest of original methods and context value
  
  const value = {
    user,
    attorney,
    session,
    isLoading: loading,
    error,
    isAuthenticated: !!session,
    login: () => {},
    handleCallback: () => {},
    signOut: () => {},
    refreshSession: () => {},
    clearError: () => setError(null)
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

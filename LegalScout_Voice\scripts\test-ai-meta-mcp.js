#!/usr/bin/env node
/**
 * Test script for AI Meta MCP Server
 *
 * This script tests the basic functionality of the AI Meta MCP Server.
 * It connects to the AI Meta MCP Server and lists available tools.
 *
 * Usage:
 *   node scripts/test-ai-meta-mcp.js
 */

import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';
import dotenv from 'dotenv';
import { spawn } from 'child_process';

// Load environment variables from .env file
dotenv.config();

console.log('Testing AI Meta MCP Server...');

async function testAiMetaMcp() {
  try {
    // Initialize MCP client
    console.log('\nInitializing MCP client...');
    const mcpClient = new Client({
      name: 'legalscout-ai-meta-mcp-test',
      version: '1.0.0',
    });

    // Spawn the AI Meta MCP Server process
    console.log('Spawning AI Meta MCP Server process...');
    const serverProcess = spawn('node', [
      'ai-meta-mcp-server/build/index.js'
    ]);

    // Create stdio transport for connection to local AI Meta MCP server
    console.log('Creating stdio transport...');
    const transport = new StdioClientTransport({
      process: serverProcess
    });

    console.log('Connecting to AI Meta MCP server via stdio...');
    await mcpClient.connect(transport);
    console.log('Connected successfully');

    // List available tools
    console.log('\nListing available tools...');
    const tools = await mcpClient.listTools();
    console.log('Available tools:');
    console.log(JSON.stringify(tools, null, 2));

    // List custom functions
    console.log('\nListing custom functions...');
    const functionsResponse = await mcpClient.callTool({
      name: 'list_functions',
      arguments: {},
    });
    console.log('Custom functions:');
    console.log(functionsResponse.content[0].text);

    // Disconnect
    console.log('\nDisconnecting from AI Meta MCP server...');
    await mcpClient.disconnect();
    console.log('Disconnected successfully');

    // Kill the server process
    console.log('Terminating server process...');
    serverProcess.kill();
    console.log('Server process terminated');
  } catch (error) {
    console.error('Error testing AI Meta MCP server:', error);
    process.exit(1);
  }
}

testAiMetaMcp();

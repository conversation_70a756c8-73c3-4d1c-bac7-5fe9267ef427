<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Vapi MCP Test</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      line-height: 1.6;
    }
    h1 {
      color: #333;
    }
    pre {
      background-color: #f5f5f5;
      padding: 10px;
      border-radius: 5px;
      overflow-x: auto;
    }
    button {
      background-color: #4CAF50;
      color: white;
      padding: 10px 15px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
      margin-top: 10px;
    }
    button:hover {
      background-color: #45a049;
    }
    .success {
      color: #4CAF50;
      font-weight: bold;
    }
    .error {
      color: #f44336;
      font-weight: bold;
    }
    #output {
      margin-top: 20px;
      white-space: pre-wrap;
    }
  </style>
</head>
<body>
  <h1>Vapi MCP Test</h1>
  
  <div>
    <label for="apiKey">Vapi API Key:</label>
    <input type="text" id="apiKey" value="6734febc-fc65-4669-93b0-929b31ff6564" style="width: 300px;">
  </div>
  
  <button id="testButton">Test Vapi MCP Connection</button>
  
  <div id="output"></div>
  
  <script type="module">
    // Import MCP SDK from CDN
    import { Client } from 'https://cdn.jsdelivr.net/npm/@modelcontextprotocol/sdk@1.10.0/dist/esm/client/index.js';
    import { SSEClientTransport } from 'https://cdn.jsdelivr.net/npm/@modelcontextprotocol/sdk@1.10.0/dist/esm/client/sse.js';
    
    const outputElement = document.getElementById('output');
    const apiKeyInput = document.getElementById('apiKey');
    const testButton = document.getElementById('testButton');
    
    function log(message, type = 'info') {
      const line = document.createElement('div');
      line.textContent = message;
      if (type === 'success') {
        line.classList.add('success');
      } else if (type === 'error') {
        line.classList.add('error');
      }
      outputElement.appendChild(line);
      console.log(message);
    }
    
    async function testVapiMcp() {
      outputElement.innerHTML = '';
      
      const apiKey = apiKeyInput.value.trim();
      if (!apiKey) {
        log('Please enter a valid Vapi API key', 'error');
        return;
      }
      
      log('Testing Vapi MCP Server...');
      log(`API Key: ${apiKey.substring(0, 4)}...${apiKey.substring(apiKey.length - 4)}`);
      
      try {
        // Initialize MCP client
        log('\nInitializing MCP client...');
        const mcpClient = new Client({
          name: 'legalscout-test',
          version: '1.0.0',
        });
        
        // Create SSE transport for connection to remote Vapi MCP server
        log('Creating SSE transport...');
        const transport = new SSEClientTransport({
          url: 'https://mcp.vapi.ai/sse',
          headers: {
            'Authorization': `Bearer ${apiKey}`
          }
        });
        
        log('Connecting to Vapi MCP server via SSE...');
        await mcpClient.connect(transport);
        log('Connected successfully', 'success');
        
        try {
          // List available tools
          log('\nListing available tools...');
          const toolsResult = await mcpClient.listTools();
          
          if (toolsResult.tools && toolsResult.tools.length > 0) {
            log(`Found ${toolsResult.tools.length} tools:`, 'success');
            toolsResult.tools.forEach((tool) => {
              log(`  - ${tool.name}: ${tool.description || 'No description'}`);
            });
          } else {
            log('No tools found', 'error');
          }
          
          // Try to list Vapi assistants
          log('\nTrying to list Vapi assistants...');
          try {
            const assistantsResponse = await mcpClient.callTool({
              name: 'list_assistants',
              arguments: {},
            });
            
            const assistants = assistantsResponse.content;
            if (Array.isArray(assistants) && assistants.length > 0) {
              log('Your Vapi assistants:', 'success');
              assistants.forEach((assistant) => {
                log(`  - ${assistant.name} (${assistant.id})`);
              });
            } else {
              log('No assistants found or invalid response', 'error');
              log(`Response: ${JSON.stringify(assistantsResponse, null, 2)}`);
            }
          } catch (error) {
            log(`Error listing assistants: ${error.message}`, 'error');
          }
        } finally {
          log('\nDisconnecting from server...');
          await mcpClient.close();
          log('Disconnected', 'success');
        }
        
        log('\nTest completed successfully', 'success');
      } catch (error) {
        log(`\nTest failed with error: ${error.message}`, 'error');
        console.error(error);
      }
    }
    
    testButton.addEventListener('click', testVapiMcp);
  </script>
</body>
</html>

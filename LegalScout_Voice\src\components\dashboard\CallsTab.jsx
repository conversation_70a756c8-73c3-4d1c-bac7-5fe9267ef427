import React, { useState, useEffect } from 'react';
import { FaPhone, FaCalendarAlt, FaHistory, FaRoute, FaStethoscope } from 'react-icons/fa';
import './CallsTab.css';
import CallRecordsTable from '../CallRecordsTable';
import CallManagementSection from './CallManagementSection';
import CallForwardingRules from './CallForwardingRules';
import VapiDiagnosticsPanel from './VapiDiagnosticsPanel';
import { assistantDataRefreshService } from '../../services/assistantDataRefreshService';
import { useAssistantSync } from '../../hooks/useAssistantSync';
import { useAssistantAware } from '../../contexts/AssistantAwareContext';

/**
 * Calls Tab
 *
 * This component provides a comprehensive interface for managing calls,
 * including making outbound calls, viewing call history, and accessing
 * call control features.
 */
const CallsTab = ({ attorney }) => {
  const [activeSection, setActiveSection] = useState('make-call');
  const [refreshKey, setRefreshKey] = useState(0);
  const [assistantData, setAssistantData] = useState(null);

  // Get current assistant context
  const { currentAssistant, isAssistantSelected } = useAssistantAware();

  // Use sync system to monitor assistant changes and call data
  const {
    lastUpdate,
    currentAssistantId,
    isLoading: syncLoading,
    forceRefresh
  } = useAssistantSync({
    autoSync: false,
    dataTypes: ['call_data', 'assistant_data']
  });

  // Debug logging for attorney data
  console.log('[CallsTab] Received attorney data:', attorney);
  console.log('[CallsTab] Attorney ID:', attorney?.id);
  console.log('[CallsTab] Current Assistant ID from sync:', currentAssistantId);
  console.log('[CallsTab] Current Assistant from context:', currentAssistant);

  // Listen for assistant data changes from both old and new systems
  useEffect(() => {
    const unsubscribe = assistantDataRefreshService.addListener((assistantId, data) => {
      console.log('📞 [CallsTab] Received assistant data update:', { assistantId, data });
      setAssistantData(data);
      setRefreshKey(prev => prev + 1); // Force refresh of child components
    });

    return unsubscribe;
  }, []);

  // Monitor sync system updates
  useEffect(() => {
    if (lastUpdate && (lastUpdate.dataType === 'call_data' || lastUpdate.dataType === 'assistant_data')) {
      console.log('📞 [CallsTab] Sync system update received:', lastUpdate);
      setRefreshKey(prev => prev + 1); // Force refresh of child components
    }
  }, [lastUpdate]);

  // Refresh when assistant changes
  useEffect(() => {
    if (currentAssistantId && isAssistantSelected) {
      console.log('📞 [CallsTab] Assistant changed, refreshing call data:', currentAssistantId);
      setRefreshKey(prev => prev + 1);
    }
  }, [currentAssistantId, isAssistantSelected]);

  // Handle section change
  const handleSectionChange = (section) => {
    setActiveSection(section);
  };

  return (
    <div className="calls-tab">
      <h2>Call Management</h2>
      <p className="tab-description">
        Make outbound calls, view call history, and manage ongoing calls with your AI assistant.
      </p>

      <div className="section-tabs">
        <button
          className={`section-tab ${activeSection === 'make-call' ? 'active' : ''}`}
          onClick={() => handleSectionChange('make-call')}
        >
          <FaPhone />
          <span>Make a Call</span>
        </button>

        <button
          className={`section-tab ${activeSection === 'call-history' ? 'active' : ''}`}
          onClick={() => handleSectionChange('call-history')}
        >
          <FaHistory />
          <span>Call History</span>
        </button>

        <button
          className={`section-tab ${activeSection === 'outbound-calls' ? 'active' : ''}`}
          onClick={() => handleSectionChange('outbound-calls')}
        >
          <FaPhone />
          <span>Outbound Calls</span>
          <span className="coming-soon">Coming Soon</span>
        </button>

        <button
          className={`section-tab ${activeSection === 'scheduled-calls' ? 'active' : ''}`}
          onClick={() => handleSectionChange('scheduled-calls')}
        >
          <FaCalendarAlt />
          <span>Scheduled Calls</span>
          <span className="coming-soon">Coming Soon</span>
        </button>

        <button
          className={`section-tab ${activeSection === 'follow-ups' ? 'active' : ''}`}
          onClick={() => handleSectionChange('follow-ups')}
        >
          <FaCalendarAlt />
          <span>Follow-ups</span>
          <span className="coming-soon">Coming Soon</span>
        </button>

        <button
          className={`section-tab ${activeSection === 'forwarding' ? 'active' : ''}`}
          onClick={() => handleSectionChange('forwarding')}
        >
          <FaRoute />
          <span>Call Forwarding</span>
        </button>

        <button
          className={`section-tab ${activeSection === 'diagnostics' ? 'active' : ''}`}
          onClick={() => handleSectionChange('diagnostics')}
        >
          <FaStethoscope />
          <span>Diagnostics</span>
        </button>
      </div>

      <div className="section-content">
        {activeSection === 'make-call' && (
          <div className="make-call-section">
            <CallManagementSection attorney={attorney} />
          </div>
        )}

        {activeSection === 'call-history' && (
          <div className="call-history-section">
            <div className="dashboard-card">
              <h3>Call History</h3>
              <p className="card-description">
                View your call history, access call details, and send notifications to your phone.
              </p>

              <CallRecordsTable
                key={`call-records-${refreshKey}`}
                attorneyId={attorney?.id}
                assistantData={assistantData}
                currentAssistantId={currentAssistantId || currentAssistant?.id || attorney?.current_assistant_id || attorney?.vapi_assistant_id}
              />
            </div>
          </div>
        )}

        {activeSection === 'outbound-calls' && (
          <div className="outbound-calls-section">
            <div className="dashboard-card">
              <h3>Outbound Calls</h3>
              <p className="card-description">
                Configure settings for outbound calls to potential clients.
              </p>

              <div className="coming-soon-placeholder">
                <div className="placeholder-icon">
                  <FaPhone />
                </div>
                <p>Outbound calling features will be available soon.</p>
              </div>
            </div>
          </div>
        )}

        {activeSection === 'scheduled-calls' && (
          <div className="scheduled-calls-section">
            <div className="dashboard-card">
              <h3>Scheduled Calls</h3>
              <p className="card-description">
                Schedule calls with your AI assistant for future dates and times.
              </p>

              <div className="coming-soon-placeholder">
                <div className="placeholder-icon">
                  <FaCalendarAlt />
                </div>
                <p>Scheduled call features will be available soon.</p>
              </div>
            </div>
          </div>
        )}

        {activeSection === 'follow-ups' && (
          <div className="follow-ups-section">
            <div className="dashboard-card">
              <h3>Scheduled Follow-ups</h3>
              <p className="card-description">
                Schedule automated follow-up calls with potential clients.
              </p>

              <div className="coming-soon-placeholder">
                <div className="placeholder-icon">
                  <FaCalendarAlt />
                </div>
                <p>Scheduled follow-up features will be available soon.</p>
              </div>
            </div>
          </div>
        )}

        {activeSection === 'forwarding' && (
          <div className="forwarding-section">
            <div className="dashboard-card">
              <h3>Call Forwarding Rules</h3>
              <p className="card-description">
                Configure when and how calls should be forwarded to your phone number.
                Set up conditions for urgent matters or specific practice areas.
              </p>

              <CallForwardingRules attorney={attorney} />
            </div>
          </div>
        )}

        {activeSection === 'diagnostics' && (
          <div className="diagnostics-section">
            <div className="dashboard-card">
              <h3>Call System Diagnostics</h3>
              <p className="card-description">
                Test your Vapi configuration and troubleshoot call issues.
                Run comprehensive diagnostics to ensure everything is working properly.
              </p>

              <VapiDiagnosticsPanel attorney={attorney} />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CallsTab;

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Quick CSP & CORS Test - LegalScout</title>
  
  <!-- Updated CSP with proper configuration -->
  <meta http-equiv="Content-Security-Policy" content="
    default-src 'self';
    script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: data:
      https://cdn.jsdelivr.net https://unpkg.com https://cdnjs.cloudflare.com
      https://cdn.vapi.ai https://vapi.ai https://*.vapi.ai
      https://c.daily.co https://*.daily.co
      https://o77906.ingest.sentry.io
      https://vercel.live https://*.vercel.live https://*.vercel.app;
    style-src 'self' 'unsafe-inline'
      https://fonts.googleapis.com https://cdnjs.cloudflare.com
      https://c.daily.co https://*.daily.co;
    font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com;
    img-src 'self' data: blob: https:;
    media-src 'self' blob: data: https:;
    connect-src 'self' https: wss: ws:
      https://api.vapi.ai https://mcp.vapi.ai https://dashboard.vapi.ai
      https://utopqxsvudgrtiwenlzl.supabase.co;
    frame-src 'self' 
      https://vercel.live https://*.vercel.live https://*.vercel.app
      https://c.daily.co https://*.daily.co;
    worker-src 'self' blob:;
    child-src 'self' blob:;
    object-src 'none';
    base-uri 'self';
    form-action 'self';
  ">

  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 20px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: #333;
      min-height: 100vh;
    }

    .container {
      max-width: 800px;
      margin: 0 auto;
      background: white;
      border-radius: 12px;
      box-shadow: 0 10px 30px rgba(0,0,0,0.2);
      overflow: hidden;
    }

    .header {
      background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
      color: white;
      padding: 30px;
      text-align: center;
    }

    .content {
      padding: 30px;
    }

    .test-section {
      margin-bottom: 30px;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      overflow: hidden;
    }

    .test-header {
      background: #f8f9fa;
      padding: 15px 20px;
      border-bottom: 1px solid #e0e0e0;
      font-weight: 600;
      color: #2c3e50;
    }

    .test-content {
      padding: 20px;
    }

    .test-result {
      margin: 10px 0;
      padding: 15px;
      border-radius: 6px;
      font-family: 'Courier New', monospace;
      font-size: 0.9em;
      line-height: 1.4;
    }

    .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
    .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
    .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
    .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }

    .test-button {
      background: #27ae60;
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 6px;
      cursor: pointer;
      font-size: 1em;
      margin: 10px 10px 10px 0;
      transition: background 0.3s;
    }

    .test-button:hover { background: #2ecc71; }
    .test-button:disabled { background: #bdc3c7; cursor: not-allowed; }

    .status-indicator {
      display: inline-block;
      width: 12px;
      height: 12px;
      border-radius: 50%;
      margin-right: 8px;
    }

    .status-pass { background: #27ae60; }
    .status-fail { background: #e74c3c; }
    .status-warn { background: #f39c12; }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>⚡ Quick Test Suite</h1>
      <p>Fast CSP & CORS validation for LegalScout</p>
    </div>

    <div class="content">
      <!-- Test Controls -->
      <div class="test-section">
        <div class="test-header">🎮 Quick Tests</div>
        <div class="test-content">
          <button class="test-button" onclick="runQuickTests()">🚀 Run Quick Tests</button>
          <button class="test-button" onclick="testCSPOnly()">🛡️ CSP Only</button>
          <button class="test-button" onclick="testCORSOnly()">🌐 CORS Only</button>
          <button class="test-button" onclick="clearResults()">🧹 Clear</button>
        </div>
      </div>

      <!-- Results -->
      <div class="test-section">
        <div class="test-header">📊 Test Results</div>
        <div class="test-content">
          <div id="test-results"></div>
        </div>
      </div>
    </div>
  </div>

  <script>
    let testCount = 0;
    let passCount = 0;
    let failCount = 0;

    // CSP Violation Listener
    document.addEventListener('securitypolicyviolation', (e) => {
      addResult('error', 'CSP Violation', `${e.violatedDirective}: ${e.blockedURI}`);
    });

    async function runQuickTests() {
      clearResults();
      console.log('🚀 Running quick tests...');
      
      await testCSPOnly();
      await testCORSOnly();
      
      updateSummary();
    }

    async function testCSPOnly() {
      addResult('info', 'CSP Tests', 'Starting CSP validation...');
      
      // Test 1: Basic eval
      try {
        eval('1 + 1');
        addResult('success', 'CSP Eval Test', 'eval() is allowed (unsafe-eval works)');
        passCount++;
      } catch (error) {
        addResult('error', 'CSP Eval Test', `eval() blocked: ${error.message}`);
        failCount++;
      }
      testCount++;

      // Test 2: Dynamic script
      try {
        const script = document.createElement('script');
        script.textContent = 'window.testScript = true;';
        document.head.appendChild(script);
        document.head.removeChild(script);
        
        setTimeout(() => {
          if (window.testScript) {
            addResult('success', 'CSP Script Test', 'Dynamic scripts allowed');
            passCount++;
          } else {
            addResult('warning', 'CSP Script Test', 'Dynamic script may be blocked');
            failCount++;
          }
          testCount++;
          updateSummary();
        }, 100);
      } catch (error) {
        addResult('error', 'CSP Script Test', `Dynamic script error: ${error.message}`);
        failCount++;
        testCount++;
      }
    }

    async function testCORSOnly() {
      addResult('info', 'CORS Tests', 'Starting CORS validation...');
      
      // Test 1: Health endpoint
      try {
        const response = await fetch('/api/health', {
          method: 'GET',
          headers: { 'Content-Type': 'application/json' }
        });
        
        if (response.ok) {
          addResult('success', 'CORS Health Test', `GET /api/health: ${response.status}`);
          passCount++;
        } else {
          addResult('error', 'CORS Health Test', `GET failed: ${response.status}`);
          failCount++;
        }
      } catch (error) {
        addResult('error', 'CORS Health Test', `Request error: ${error.message}`);
        failCount++;
      }
      testCount++;

      // Test 2: Preflight OPTIONS
      try {
        const response = await fetch('/api/health', {
          method: 'OPTIONS',
          headers: {
            'Access-Control-Request-Method': 'POST',
            'Access-Control-Request-Headers': 'Content-Type'
          }
        });
        
        if (response.ok) {
          addResult('success', 'CORS Preflight Test', `OPTIONS /api/health: ${response.status}`);
          passCount++;
        } else {
          addResult('error', 'CORS Preflight Test', `OPTIONS failed: ${response.status}`);
          failCount++;
        }
      } catch (error) {
        addResult('error', 'CORS Preflight Test', `OPTIONS error: ${error.message}`);
        failCount++;
      }
      testCount++;

      // Test 3: Vapi MCP endpoint
      try {
        const response = await fetch('/api/vapi-mcp-server', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ jsonrpc: '2.0', id: 1, method: 'ping' })
        });
        
        if (response.ok) {
          addResult('success', 'CORS Vapi MCP Test', `POST /api/vapi-mcp-server: ${response.status}`);
          passCount++;
        } else {
          addResult('error', 'CORS Vapi MCP Test', `POST failed: ${response.status}`);
          failCount++;
        }
      } catch (error) {
        addResult('error', 'CORS Vapi MCP Test', `Request error: ${error.message}`);
        failCount++;
      }
      testCount++;

      updateSummary();
    }

    function addResult(type, title, message) {
      const results = document.getElementById('test-results');
      const resultDiv = document.createElement('div');
      resultDiv.className = `test-result ${type}`;
      
      const statusClass = type === 'success' ? 'status-pass' : 
                         type === 'error' ? 'status-fail' : 'status-warn';
      
      resultDiv.innerHTML = `
        <span class="status-indicator ${statusClass}"></span>
        <strong>${title}</strong><br>
        ${message}
      `;
      
      results.appendChild(resultDiv);
    }

    function updateSummary() {
      if (testCount > 0) {
        const successRate = Math.round((passCount / testCount) * 100);
        addResult('info', 'Test Summary', 
          `Completed: ${testCount} | Passed: ${passCount} | Failed: ${failCount} | Success Rate: ${successRate}%`);
      }
    }

    function clearResults() {
      document.getElementById('test-results').innerHTML = '';
      testCount = 0;
      passCount = 0;
      failCount = 0;
    }

    // Auto-run tests on load
    document.addEventListener('DOMContentLoaded', function() {
      console.log('⚡ Quick test suite loaded');
      addResult('info', 'System', 'Quick test suite initialized and ready');
    });
  </script>
</body>
</html>

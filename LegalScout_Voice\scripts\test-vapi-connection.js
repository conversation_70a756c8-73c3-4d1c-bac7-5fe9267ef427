/**
 * Test Vapi Connection
 *
 * This script tests the connection to the Vapi API and MCP server.
 * It verifies that the API key is valid and that the MCP server is accessible.
 */

// Load environment variables
import { config } from 'dotenv';
import { EventSource } from 'eventsource';
import { fileURLToPath } from 'url';
import { dirname, resolve } from 'path';

// Get current file path
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables from .env file
config({ path: resolve(__dirname, '../.env') });

// Get Vapi API key from environment variables
const VAPI_API_KEY = process.env.VAPI_TOKEN || process.env.VITE_VAPI_PUBLIC_KEY;

if (!VAPI_API_KEY) {
  console.error('❌ No Vapi API key found in environment variables');
  process.exit(1);
}

console.log(`🔑 Using Vapi API key: ${VAPI_API_KEY.substring(0, 5)}...`);

// Test direct API connection
async function testDirectApiConnection() {
  console.log('\n🔍 Testing direct API connection...');

  try {
    // Try to list assistants
    const response = await fetch('https://api.vapi.ai/assistants', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${VAPI_API_KEY}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.ok) {
      const data = await response.json();
      console.log(`✅ Direct API connection successful`);
      console.log(`📊 Found ${data.length} assistants`);
      return true;
    } else {
      console.error(`❌ Direct API connection failed: ${response.status} ${response.statusText}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ Direct API connection error: ${error.message}`);
    return false;
  }
}

// Test MCP server connection
async function testMcpServerConnection() {
  console.log('\n🔍 Testing MCP server connection...');

  // Try different MCP server endpoints
  const endpoints = [
    'http://localhost:3000/vapi-mcp-server/sse',
    'http://localhost:3000/api/vapi-mcp-server/sse',
    'http://localhost:5173/vapi-mcp-server/sse',
    'http://localhost:5173/api/vapi-mcp-server/sse'
  ];

  for (const endpoint of endpoints) {
    try {
      console.log(`🔄 Trying endpoint: ${endpoint}`);

      // Create a simple EventSource connection to test SSE
      const eventSource = new EventSource(endpoint, {
        headers: {
          'Authorization': `Bearer ${VAPI_API_KEY}`
        }
      });

      // Set up a promise to handle the connection
      const connectionPromise = new Promise((resolve, reject) => {
        // Set a timeout
        const timeout = setTimeout(() => {
          eventSource.close();
          reject(new Error('Connection timeout'));
        }, 5000);

        // Handle connection open
        eventSource.onopen = () => {
          clearTimeout(timeout);
          eventSource.close();
          resolve(true);
        };

        // Handle connection error
        eventSource.onerror = (error) => {
          clearTimeout(timeout);
          eventSource.close();
          reject(new Error(`Connection error: ${error.message || 'Unknown error'}`));
        };
      });

      // Wait for the connection to be established or timeout
      await connectionPromise;
      console.log(`✅ MCP server connection successful with endpoint: ${endpoint}`);
      return true;
    } catch (error) {
      console.warn(`⚠️ MCP server connection failed with endpoint ${endpoint}: ${error.message}`);
    }
  }

  console.error('❌ All MCP server connection attempts failed');
  return false;
}

// Run the tests
async function runTests() {
  console.log('🧪 Running Vapi connection tests...');

  // Test direct API connection
  const directApiSuccess = await testDirectApiConnection();

  // Test MCP server connection
  const mcpServerSuccess = await testMcpServerConnection();

  // Print summary
  console.log('\n📋 Test Summary:');
  console.log(`Direct API Connection: ${directApiSuccess ? '✅ Success' : '❌ Failed'}`);
  console.log(`MCP Server Connection: ${mcpServerSuccess ? '✅ Success' : '❌ Failed'}`);

  if (directApiSuccess || mcpServerSuccess) {
    console.log('\n✅ At least one connection method works');
  } else {
    console.log('\n❌ No connection method works');
  }
}

// Run the tests
runTests().catch(error => {
  console.error(`❌ Error running tests: ${error.message}`);
  process.exit(1);
});

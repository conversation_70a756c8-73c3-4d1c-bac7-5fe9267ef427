/**
 * Enhanced Attorney Persistence Fix
 * 
 * This script fixes issues with attorney persistence by:
 * 1. Validating UUIDs before using them in database operations
 * 2. Providing robust fallback mechanisms when data is missing
 * 3. Ensuring proper synchronization between localStorage and Supabase
 */

(function() {
  console.log('[EnhancedAttorneyPersistenceFix] Initializing enhanced attorney persistence fix');

  // UUID validation regex
  const UUID_REGEX = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;

  /**
   * Validate a UUID
   * @param {string} uuid - The UUID to validate
   * @returns {boolean} Whether the UUID is valid
   */
  function isValidUUID(uuid) {
    if (!uuid || typeof uuid !== 'string') return false;
    return UUID_REGEX.test(uuid);
  }

  /**
   * Generate a fallback UUID
   * This is used when a real UUID is not available
   * @returns {string} A valid UUID
   */
  function generateFallbackUUID() {
    // Simple UUID v4 implementation
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  /**
   * Safely store attorney data in localStorage
   * @param {Object} attorney - The attorney data to store
   */
  function safelyStoreAttorney(attorney) {
    if (!attorney) {
      console.warn('[EnhancedAttorneyPersistenceFix] Attempted to store undefined attorney');
      return;
    }

    try {
      // Ensure attorney has an ID
      if (!attorney.id || !isValidUUID(attorney.id)) {
        console.log('[EnhancedAttorneyPersistenceFix] Attorney has invalid ID, generating fallback');
        attorney.id = generateFallbackUUID();
      }

      // Ensure attorney has a valid Vapi assistant ID if one is provided
      if (attorney.vapi_assistant_id && !isValidUUID(attorney.vapi_assistant_id)) {
        console.warn('[EnhancedAttorneyPersistenceFix] Invalid Vapi assistant ID, removing');
        attorney.vapi_assistant_id = null;
      }

      // Store in localStorage
      localStorage.setItem('attorney', JSON.stringify(attorney));
      console.log('[EnhancedAttorneyPersistenceFix] Stored attorney in localStorage:', attorney.id);

      // Also store the ID separately for redundancy
      localStorage.setItem('attorney_id', attorney.id);
      console.log('[EnhancedAttorneyPersistenceFix] Stored attorney ID in dedicated storage:', attorney.id);
    } catch (error) {
      console.error('[EnhancedAttorneyPersistenceFix] Error storing attorney:', error);
    }
  }

  /**
   * Safely retrieve attorney data from localStorage
   * @returns {Object|null} The attorney data or null if not found
   */
  function safelyRetrieveAttorney() {
    try {
      // Try to get from localStorage
      const storedAttorney = localStorage.getItem('attorney');
      if (storedAttorney) {
        const parsedAttorney = JSON.parse(storedAttorney);
        
        // Validate the attorney data
        if (parsedAttorney && isValidUUID(parsedAttorney.id)) {
          console.log('[EnhancedAttorneyPersistenceFix] Retrieved valid attorney from localStorage:', parsedAttorney.id);
          return parsedAttorney;
        } else {
          console.warn('[EnhancedAttorneyPersistenceFix] Retrieved attorney has invalid ID');
        }
      }

      // If not found or invalid, try to get just the ID
      const storedAttorneyId = localStorage.getItem('attorney_id');
      if (storedAttorneyId && isValidUUID(storedAttorneyId)) {
        console.log('[EnhancedAttorneyPersistenceFix] Retrieved attorney ID from dedicated storage:', storedAttorneyId);
        return { id: storedAttorneyId };
      }

      console.log('[EnhancedAttorneyPersistenceFix] No valid attorney found in localStorage');
      return null;
    } catch (error) {
      console.error('[EnhancedAttorneyPersistenceFix] Error retrieving attorney:', error);
      return null;
    }
  }

  /**
   * Create a fallback attorney
   * @param {string} [userId] - Optional user ID
   * @returns {Object} A fallback attorney object
   */
  function createFallbackAttorney(userId) {
    const fallbackId = generateFallbackUUID();
    console.log('[EnhancedAttorneyPersistenceFix] Creating fallback attorney with ID:', fallbackId);
    
    return {
      id: fallbackId,
      user_id: userId || null,
      firm_name: 'Default Law Firm',
      name: 'Default Attorney',
      is_active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      subdomain: 'default',
      voice_provider: '11labs',
      voice_id: 'sarah'
    };
  }

  /**
   * Patch Supabase methods to validate UUIDs before use
   */
  function patchSupabaseMethods() {
    // Wait for Supabase to be available
    const checkSupabase = setInterval(() => {
      if (window.supabase) {
        clearInterval(checkSupabase);
        
        console.log('[EnhancedAttorneyPersistenceFix] Patching Supabase methods');
        
        // Store original methods
        const originalFrom = window.supabase.from;
        
        // Patch the 'from' method to add UUID validation
        window.supabase.from = function(table) {
          const originalResult = originalFrom.call(this, table);
          
          // Only patch methods for the attorneys table
          if (table === 'attorneys') {
            const originalSelect = originalResult.select;
            const originalUpdate = originalResult.update;
            
            // Patch select method
            originalResult.select = function() {
              const selectResult = originalSelect.apply(this, arguments);
              const originalEq = selectResult.eq;
              
              // Patch eq method to validate UUIDs
              selectResult.eq = function(column, value) {
                // Validate UUID if the column is 'id' or 'user_id'
                if ((column === 'id' || column === 'user_id') && !isValidUUID(value)) {
                  console.warn(`[EnhancedAttorneyPersistenceFix] Invalid UUID for ${column}: ${value}`);
                  
                  // Return a mock result that will return no data
                  return {
                    single: () => Promise.resolve({ data: null, error: null })
                  };
                }
                
                return originalEq.call(this, column, value);
              };
              
              return selectResult;
            };
            
            // Patch update method
            originalResult.update = function(data) {
              // Validate Vapi assistant ID if present
              if (data && data.vapi_assistant_id && !isValidUUID(data.vapi_assistant_id)) {
                console.warn('[EnhancedAttorneyPersistenceFix] Invalid Vapi assistant ID in update:', data.vapi_assistant_id);
                data.vapi_assistant_id = null;
              }
              
              return originalUpdate.call(this, data);
            };
          }
          
          return originalResult;
        };
        
        console.log('[EnhancedAttorneyPersistenceFix] Supabase methods patched successfully');
      }
    }, 100);
    
    // Clear interval after 10 seconds if Supabase is not available
    setTimeout(() => {
      clearInterval(checkSupabase);
      console.warn('[EnhancedAttorneyPersistenceFix] Timed out waiting for Supabase');
    }, 10000);
  }

  /**
   * Initialize the attorney persistence fix
   */
  function initAttorneyPersistenceFix() {
    // Patch Supabase methods
    patchSupabaseMethods();
    
    // Check for attorney in localStorage
    const attorney = safelyRetrieveAttorney();
    
    if (attorney) {
      // Ensure the attorney data is valid and complete
      if (!attorney.firm_name || !attorney.name) {
        console.log('[EnhancedAttorneyPersistenceFix] Attorney data is incomplete, enhancing with defaults');
        
        // Enhance with default values for missing fields
        const enhancedAttorney = {
          ...createFallbackAttorney(),
          ...attorney
        };
        
        // Store the enhanced attorney
        safelyStoreAttorney(enhancedAttorney);
      }
    } else {
      // No attorney found, create a fallback
      console.log('[EnhancedAttorneyPersistenceFix] No valid attorney found, creating fallback');
      
      // Try to get user ID from auth
      let userId = null;
      try {
        if (window.localStorage.getItem('supabase.auth.token')) {
          const authData = JSON.parse(window.localStorage.getItem('supabase.auth.token'));
          userId = authData?.currentSession?.user?.id;
        }
      } catch (error) {
        console.error('[EnhancedAttorneyPersistenceFix] Error getting user ID from auth:', error);
      }
      
      console.log('[EnhancedAttorneyPersistenceFix] Creating fallback attorney for user:', userId);
      const fallbackAttorney = createFallbackAttorney(userId);
      safelyStoreAttorney(fallbackAttorney);
    }
    
    // Add a global helper function to ensure attorney persistence
    window.ensureAttorneyPersistence = function(attorneyData) {
      console.log('[EnhancedAttorneyPersistenceFix] Ensuring attorney persistence');
      
      // Get current attorney
      const currentAttorney = safelyRetrieveAttorney();
      
      // Merge with new data
      const mergedAttorney = {
        ...(currentAttorney || createFallbackAttorney()),
        ...attorneyData
      };
      
      // Ensure the attorney has a valid ID
      if (!isValidUUID(mergedAttorney.id)) {
        mergedAttorney.id = generateFallbackUUID();
      }
      
      // Store the merged attorney
      safelyStoreAttorney(mergedAttorney);
      
      return mergedAttorney;
    };
    
    console.log('[EnhancedAttorneyPersistenceFix] Attorney persistence fix initialized');
  }

  // Initialize the fix
  initAttorneyPersistenceFix();
})();

/**
 * Test Vapi MCP Connection
 * 
 * This script tests the connection to the Vapi MCP server.
 * It tries to connect to the server and list assistants.
 * 
 * Usage:
 *   node scripts/test-vapi-mcp-connection.js
 */

import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { SSEClientTransport } from '@modelcontextprotocol/sdk/client/sse.js';
import dotenv from 'dotenv';
import fetch from 'node-fetch';

// Load environment variables
dotenv.config();

// Get Vapi API key from environment variable
const VAPI_API_KEY = process.env.VITE_VAPI_PUBLIC_KEY || process.env.VAPI_TOKEN;

if (!VAPI_API_KEY) {
  console.error('❌ Vapi API key not found. Please set the VITE_VAPI_PUBLIC_KEY or VAPI_TOKEN environment variable.');
  process.exit(1);
}

// Possible proxy paths to try
const proxyPaths = [
  '/vapi-mcp-server/sse',
  '/vapi-mcp/sse',
  '/api/vapi-mcp-server/sse',
  '/api/vapi-mcp-server'
];

// Possible direct endpoints to try
const directEndpoints = [
  'https://mcp.vapi.ai/sse',
  'https://api.vapi.ai/v1/assistants',
  'https://api.vapi.ai/assistants',
  'https://api.vapi.ai/api/v1/assistants',
  'https://api.vapi.ai/api/assistants',
  'https://public.vapi.ai/v1/assistants',
  'https://public.vapi.ai/assistants'
];

// Test MCP connection
async function testMcpConnection() {
  console.log('🔄 Testing MCP connection...');
  
  // Initialize MCP client
  const client = new Client({
    name: 'legalscout-test-client',
    version: '1.0.0',
  });
  
  try {
    // Try direct connection to Vapi MCP server
    console.log('🔄 Trying direct connection to Vapi MCP server...');
    
    const transport = new SSEClientTransport({
      url: 'https://mcp.vapi.ai/sse',
      headers: {
        'Authorization': `Bearer ${VAPI_API_KEY}`,
        'Content-Type': 'application/json'
      }
    });
    
    await client.connect(transport);
    console.log('✅ Connected to Vapi MCP server');
    
    // List assistants
    console.log('🔄 Listing assistants...');
    const response = await client.callTool({
      name: 'list_assistants_vapi-mcp-server',
      arguments: {}
    });
    
    console.log('✅ Successfully listed assistants');
    console.log(`📋 Found ${response.content.length} assistants`);
    
    // Disconnect
    await client.close();
    console.log('✅ Disconnected from Vapi MCP server');
    
    return true;
  } catch (error) {
    console.error('❌ Error connecting to Vapi MCP server:', error);
    return false;
  }
}

// Test direct API connection
async function testDirectApiConnection() {
  console.log('🔄 Testing direct API connection...');
  
  for (const endpoint of directEndpoints) {
    try {
      console.log(`🔄 Trying endpoint: ${endpoint}`);
      
      const response = await fetch(endpoint, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${VAPI_API_KEY}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      if (response.ok) {
        console.log(`✅ Successfully connected to ${endpoint}`);
        const data = await response.json();
        console.log(`📋 Response: ${JSON.stringify(data).substring(0, 100)}...`);
        return true;
      } else {
        console.warn(`⚠️ Endpoint ${endpoint} returned status: ${response.status}`);
      }
    } catch (error) {
      console.warn(`⚠️ Error with endpoint ${endpoint}:`, error.message);
    }
  }
  
  console.error('❌ All direct API endpoints failed');
  return false;
}

// Main function
async function main() {
  console.log('🔍 Testing Vapi MCP connection...');
  console.log('📝 API Key:', VAPI_API_KEY ? `${VAPI_API_KEY.substring(0, 5)}...${VAPI_API_KEY.substring(VAPI_API_KEY.length - 5)}` : 'missing');
  
  // Test MCP connection
  const mcpSuccess = await testMcpConnection();
  
  // If MCP connection failed, try direct API
  if (!mcpSuccess) {
    console.log('⚠️ MCP connection failed, trying direct API...');
    const directSuccess = await testDirectApiConnection();
    
    if (directSuccess) {
      console.log('✅ Direct API connection successful');
    } else {
      console.error('❌ All connection attempts failed');
      process.exit(1);
    }
  }
  
  console.log('✅ Test completed successfully');
}

// Run the main function
main().catch(error => {
  console.error('❌ Fatal error:', error);
  process.exit(1);
});

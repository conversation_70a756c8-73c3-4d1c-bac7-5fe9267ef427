/**
 * Secure Vapi Webhook Configuration
 * This script sets up webhooks with proper security measures
 */

import dotenv from 'dotenv';
import crypto from 'crypto';

dotenv.config();

const VAPI_API_KEY = process.env.VAPI_TOKEN || process.env.VITE_VAPI_SECRET_KEY;

if (!VAPI_API_KEY) {
  console.error('❌ VAPI_API_KEY not found in environment variables');
  process.exit(1);
}

/**
 * Generate a secure webhook secret
 */
function generateSecureSecret() {
  return crypto.randomBytes(32).toString('hex');
}

/**
 * Update assistant server URL with security
 */
async function updateAssistantServerUrl(assistantId, serverUrl, serverUrlSecret) {
  try {
    console.log(`🔧 Updating assistant ${assistantId} server URL with security...`);

    const response = await fetch(`https://api.vapi.ai/assistant/${assistantId}`, {
      method: 'PATCH',
      headers: {
        'Authorization': `Bearer ${VAPI_API_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        serverUrl: serverUrl,
        serverUrlSecret: serverUrlSecret
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`API error: ${response.status} ${response.statusText} - ${errorText}`);
    }

    const result = await response.json();
    console.log('✅ Assistant server URL updated successfully');
    console.log('   Assistant ID:', result.id);
    console.log('   Server URL:', result.serverUrl);
    console.log('   Secret configured: YES (hidden for security)');

    return result;
  } catch (error) {
    console.error('❌ Error updating assistant server URL:', error.message);
    return null;
  }
}

/**
 * Main function
 */
async function main() {
  try {
    console.log('🔒 Setting up secure Vapi webhook configuration...\n');

    // Generate a secure secret
    const secureSecret = generateSecureSecret();

    // Determine the correct webhook URL
    const webhookUrl = 'https://legalscout.app/api/webhook/vapi-call';

    console.log('📋 Security Configuration:');
    console.log('   Webhook URL:', webhookUrl);
    console.log('   Secret Length:', secureSecret.length, 'characters');
    console.log('   Secret Preview:', secureSecret.substring(0, 8) + '...');
    console.log('');

    console.log('🔒 Security Features:');
    console.log('   ✅ Cryptographically secure random secret');
    console.log('   ✅ 256-bit entropy');
    console.log('   ✅ Unique per deployment');
    console.log('   ✅ Request signature verification');
    console.log('');

    console.log('⚠️  IMPORTANT SECURITY NOTES:');
    console.log('   1. Store the webhook secret securely in your environment variables');
    console.log('   2. Verify webhook signatures in your handler');
    console.log('   3. Use HTTPS for all webhook endpoints');
    console.log('   4. Log and monitor webhook requests');
    console.log('   5. Implement rate limiting on webhook endpoints');
    console.log('');

    // Check if user wants to proceed
    if (!process.argv.includes('--confirm')) {
      console.log('💡 To proceed with secure setup:');
      console.log('   node scripts/secure-webhook-setup.js --confirm');
      console.log('');
      console.log('📝 Next steps after running with --confirm:');
      console.log('   1. Add VAPI_WEBHOOK_SECRET to your .env file');
      console.log('   2. Update your webhook handler to verify signatures');
      console.log('   3. Deploy the updated webhook handler');
      console.log('   4. Test webhook security');
      return;
    }

    // Update the assistant settings
    const assistantId = '9621936c-cb51-489c-a869-0c33052f0e42'; // Your assistant ID
    const result = await updateAssistantServerUrl(assistantId, webhookUrl, secureSecret);

    if (result) {
      console.log('');
      console.log('🎉 Webhook configured successfully!');
      console.log('');
      console.log('📝 NEXT STEPS (IMPORTANT):');
      console.log('   1. Add this to your .env file:');
      console.log(`      VAPI_WEBHOOK_SECRET=${secureSecret}`);
      console.log('');
      console.log('   2. Update your webhook handler to verify signatures');
      console.log('   3. Redeploy your application');
      console.log('   4. Test with a new call');
      console.log('');
      console.log('⚠️  SAVE THE SECRET ABOVE - you cannot retrieve it later!');
    }

  } catch (error) {
    console.error('❌ Script failed:', error);
    process.exit(1);
  }
}

// Run the script
main();

/**
 * Sync Auth Provider - PHASE 1 UPDATED
 *
 * PHASE 1 CHANGES:
 * - AuthProvider no longer needs syncTools prop (basic auth only)
 * - SyncProvider still available for other components that need sync functionality
 * - Attorney data loading moved to lazy loading in dashboard components
 */

import React from 'react';
import { SyncProvider, useSync } from '../contexts/SyncContext';
import { AuthProvider } from '../contexts/AuthContext';

/**
 * PHASE 1: Inner Auth Provider - NO SYNC TOOLS NEEDED
 *
 * AuthProvider now handles basic authentication only.
 * Sync tools are available through SyncProvider for other components.
 *
 * @param {Object} props - The component props
 * @param {React.ReactNode} props.children - The child components
 * @returns {JSX.Element} The component
 */
const InnerAuthProvider = ({ children }) => {
  // PHASE 1: AuthProvider no longer needs syncTools prop
  return (
    <AuthProvider>
      {children}
    </AuthProvider>
  );
};

/**
 * Sync Auth Provider
 * 
 * This component provides both synchronization and authentication
 * contexts to the application.
 * 
 * @param {Object} props - The component props
 * @param {React.ReactNode} props.children - The child components
 * @returns {JSX.Element} The component
 */
const SyncAuthProvider = ({ children }) => {
  return (
    <SyncProvider>
      <InnerAuthProvider>
        {children}
      </InnerAuthProvider>
    </SyncProvider>
  );
};

export default SyncAuthProvider;

import{_ as c,s as i}from"./index-27efa71d.js";class u{async getAssistantBySubdomain(s){try{console.log(`🔍 [AssistantSubdomainService] Looking up assistant for subdomain: ${s}`);const{getSupabaseClient:t}=await c(()=>import("./index-27efa71d.js").then(o=>o.K),["assets/index-27efa71d.js","assets/index-61efa26a.css"]),r=await t();if(!r||typeof r.from!="function")return console.error("❌ [AssistantSubdomainService] Supabase client not available"),null;const{data:e,error:a}=await r.from("v_subdomain_assistant_lookup").select("*").eq("subdomain",s).eq("is_active",!0).single();if(a){if(a.code==="PGRST116")return console.log(`📭 [AssistantSubdomainService] No assistant found for subdomain: ${s}`),null;throw console.error(`❌ [AssistantSubdomainService] Database error for subdomain ${s}:`,a),a}return e?(console.log(`✅ [AssistantSubdomainService] Found assistant for subdomain ${s}:`,{assistant_id:e.assistant_id,attorney_id:e.attorney_id,firm_name:e.firm_name,is_primary:e.is_primary}),e):(console.log(`📭 [AssistantSubdomainService] No data returned for subdomain: ${s}`),null)}catch(t){return console.error("❌ [AssistantSubdomainService] Error getting assistant by subdomain:",t),null}}async getSubdomainsForAttorney(s){try{const{data:t,error:r}=await i.from("assistant_subdomains").select("*").eq("attorney_id",s).eq("is_active",!0).order("is_primary",{ascending:!1});if(r)throw r;return t||[]}catch(t){return console.error("Error getting subdomains for attorney:",t),[]}}async assignSubdomainToAssistant(s,t,r,e=!1){try{console.log(`🔗 Assigning subdomain ${t} to assistant ${s}`);const a=await this.getAssistantBySubdomain(t);if(a&&a.assistant_id!==s)throw new Error(`Subdomain ${t} is already assigned to assistant ${a.assistant_id}`);const{data:o,error:n}=await i.from("assistant_subdomains").upsert({assistant_id:s,subdomain:t,attorney_id:r,is_primary:e,is_active:!0,updated_at:new Date().toISOString()},{onConflict:"assistant_id"}).select().single();if(n)throw n;return console.log(`✅ Successfully assigned subdomain ${t} to assistant ${s}`),o}catch(a){throw console.error("Error assigning subdomain to assistant:",a),a}}async updateAssistantWebhookUrl(s,t){try{const r={}.VITE_BASE_DOMAIN||"legalscout.net",e=`https://${t}.${r}/api/vapi-webhook-direct`;console.log(`🔄 Updating webhook URL for assistant ${s}: ${e}`);const{vapiMcpService:a}=await c(()=>import("./index-27efa71d.js").then(o=>o.L),["assets/index-27efa71d.js","assets/index-61efa26a.css"]);return await a.updateAssistant(s,{serverUrl:e,serverUrlSecret:{}.VAPI_WEBHOOK_SECRET||"legalscout-webhook-secret"}),console.log(`✅ Updated webhook URL for assistant ${s}`),!0}catch(r){return console.error("Error updating assistant webhook URL:",r),!1}}async createSubdomainForAssistant(s,t,r,e=!1){try{let a=r,o=1;if(!e)for(;await this.getAssistantBySubdomain(a);)a=`${r}-${o}`,o++;const n=await this.assignSubdomainToAssistant(s,a,t,e);return await this.updateAssistantWebhookUrl(s,a),n}catch(a){throw console.error("Error creating subdomain for assistant:",a),a}}async getPrimaryAssistantForAttorney(s){try{const{data:t,error:r}=await i.from("assistant_subdomains").select("*").eq("attorney_id",s).eq("is_primary",!0).eq("is_active",!0).single();if(r){if(r.code==="PGRST116")return null;throw r}return t}catch(t){return console.error("Error getting primary assistant for attorney:",t),null}}async setPrimaryAssistant(s,t){try{console.log(`🎯 Setting assistant ${s} as primary for attorney ${t}`);const{error:r}=await i.from("assistant_subdomains").update({is_primary:!0,updated_at:new Date().toISOString()}).eq("assistant_id",s).eq("attorney_id",t);if(r)throw r;return console.log(`✅ Set assistant ${s} as primary`),!0}catch(r){return console.error("Error setting primary assistant:",r),!1}}async migrateAttorneySubdomain(s){try{console.log(`🔄 Migrating subdomain for attorney ${s}`);const{data:t,error:r}=await i.from("attorneys").select("subdomain, vapi_assistant_id, current_assistant_id, firm_name").eq("id",s).single();if(r)throw r;if(!t.subdomain)throw new Error("Attorney has no subdomain to migrate");const e=[];if(t.vapi_assistant_id){const a=await this.assignSubdomainToAssistant(t.vapi_assistant_id,t.subdomain,s,!0);e.push({type:"primary",mapping:a})}if(t.current_assistant_id&&t.current_assistant_id!==t.vapi_assistant_id){const a=await this.createSubdomainForAssistant(t.current_assistant_id,s,t.subdomain,!1);e.push({type:"secondary",mapping:a})}return console.log(`✅ Migrated ${e.length} assistant subdomains for ${t.firm_name}`),{success:!0,results:e}}catch(t){return console.error("Error migrating attorney subdomain:",t),{success:!1,error:t.message}}}generateWebhookUrl(s){const t={}.VITE_BASE_DOMAIN||"legalscout.net";return`https://${s}.${t}/api/vapi-webhook-direct`}}const m=new u;export{u as AssistantSubdomainService,m as assistantSubdomainService,m as default};

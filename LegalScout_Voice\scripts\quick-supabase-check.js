#!/usr/bin/env node

/**
 * Quick Supabase Check
 * 
 * Check current attorney data and identify schema issues
 */

import { createClient } from '@supabase/supabase-js';

const SUPABASE_URL = 'https://utopqxsvudgrtiwenlzl.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg5NDgwMDcsImV4cCI6MjA1NDUyNDAwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU';

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

async function quickCheck() {
  console.log('🔍 Quick Supabase Check - Identifying Schema Issues\n');
  
  try {
    // Check attorney data
    console.log('👤 Checking attorney data...');
    const { data: attorneys, error: attorneysError } = await supabase
      .from('attorneys')
      .select('id, firm_name, vapi_assistant_id, current_assistant_id')
      .eq('id', '87756a2c-a398-43f2-889a-b8815684df71');
    
    if (attorneysError) {
      console.log('❌ Error loading attorney:', attorneysError.message);
      return;
    }
    
    if (attorneys && attorneys.length > 0) {
      const attorney = attorneys[0];
      console.log('📊 Current attorney data:', {
        id: attorney.id,
        firm_name: attorney.firm_name,
        vapi_assistant_id: attorney.vapi_assistant_id,
        current_assistant_id: attorney.current_assistant_id
      });
      
      // Check for corruption
      const hasCorruption = attorney.vapi_assistant_id === attorney.id || 
                           attorney.current_assistant_id === attorney.id;
      
      if (hasCorruption) {
        console.log('🚨 CORRUPTION DETECTED: Attorney ID used as assistant ID');
        
        // Fix it now
        console.log('🔧 Fixing corruption...');
        const validAssistantId = '2f157a27-067c-439e-823c-f0a2bbdd66e0';
        
        const { error: updateError } = await supabase
          .from('attorneys')
          .update({
            vapi_assistant_id: validAssistantId,
            current_assistant_id: validAssistantId
          })
          .eq('id', attorney.id);
          
        if (updateError) {
          console.log('❌ Error fixing corruption:', updateError.message);
        } else {
          console.log('✅ Corruption FIXED! Updated to valid assistant ID:', validAssistantId);
        }
      } else {
        console.log('✅ No corruption - attorney data is clean');
      }
    }
    
    // Check consultations table
    console.log('\n📋 Checking consultations table...');
    const { data: consultations, error: consultationsError } = await supabase
      .from('consultations')
      .select('id, attorney_id')
      .limit(1);
    
    if (consultationsError) {
      if (consultationsError.message.includes('assistant_id does not exist')) {
        console.log('🚨 SCHEMA ISSUE: consultations.assistant_id column missing');
        console.log('📝 This is causing the "missing key" errors');
      } else {
        console.log('❌ Consultations error:', consultationsError.message);
      }
    } else {
      console.log('✅ Consultations table accessible');
    }
    
    // Test a problematic update that would cause "missing key" error
    console.log('\n🧪 Testing problematic update (mascot column)...');
    const { error: mascotError } = await supabase
      .from('attorneys')
      .update({ mascot: 'test' })
      .eq('id', '87756a2c-a398-43f2-889a-b8815684df71');
    
    if (mascotError) {
      if (mascotError.message.includes('mascot')) {
        console.log('🚨 SCHEMA ISSUE: mascot column missing from attorneys table');
        console.log('📝 This is causing the "missing key" errors');
      } else {
        console.log('❌ Mascot test error:', mascotError.message);
      }
    } else {
      console.log('✅ Mascot column exists and works');
    }
    
    console.log('\n📋 Summary:');
    console.log('The "missing key" interim screen is caused by:');
    console.log('1. Missing columns in database tables');
    console.log('2. App trying to update non-existent columns');
    console.log('3. Supabase returning schema cache errors');
    
    console.log('\n🔧 To fix this, we need to add the missing columns to your database.');
    console.log('Since I can\'t modify the schema directly, you\'ll need to run the SQL script.');
    
  } catch (error) {
    console.log('❌ Unexpected error:', error.message);
  }
}

quickCheck().catch(console.error);

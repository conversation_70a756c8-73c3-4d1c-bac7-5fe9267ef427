#!/usr/bin/env node

/**
 * Quick Assistant Propagation Test
 * Run this to quickly verify assistant variables propagate correctly
 */

import { createRequire } from 'module';
const require = createRequire(import.meta.url);

// Test configuration
const VAPI_PRIVATE_KEY = '6734febc-fc65-4669-93b0-929b31ff6564';
const VAPI_PUBLIC_KEY = '310f0d43-27c2-47a5-a76d-e55171d024f7';

console.log('🧪 Quick Assistant Propagation Test');
console.log('===================================');

async function testVapiConnection() {
    console.log('\n🔍 Test 1: Vapi Connection');
    
    try {
        // Test basic fetch to Vapi API
        const response = await fetch('https://api.vapi.ai/assistant', {
            headers: {
                'Authorization': `Bearer ${VAPI_PRIVATE_KEY}`,
                'Content-Type': 'application/json'
            }
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const assistants = await response.json();
        console.log(`✅ Connected to Vapi API`);
        console.log(`✅ Found ${assistants.length} assistants`);
        
        // List first few assistants
        assistants.slice(0, 3).forEach((assistant, index) => {
            console.log(`   ${index + 1}. ${assistant.name} (${assistant.id.substring(0, 8)}...)`);
        });
        
        return assistants;
    } catch (error) {
        console.log(`❌ Vapi connection failed: ${error.message}`);
        return [];
    }
}

async function testAssistantRetrieval(assistantId) {
    console.log(`\n🔍 Test 2: Assistant Retrieval (${assistantId.substring(0, 8)}...)`);
    
    try {
        const response = await fetch(`https://api.vapi.ai/assistant/${assistantId}`, {
            headers: {
                'Authorization': `Bearer ${VAPI_PRIVATE_KEY}`,
                'Content-Type': 'application/json'
            }
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const assistant = await response.json();
        console.log(`✅ Retrieved assistant: ${assistant.name}`);
        console.log(`   Model: ${assistant.model?.provider} ${assistant.model?.model}`);
        console.log(`   Voice: ${assistant.voice?.provider} ${assistant.voice?.voiceId}`);
        
        return assistant;
    } catch (error) {
        console.log(`❌ Assistant retrieval failed: ${error.message}`);
        return null;
    }
}

async function testCallCreation(assistantId) {
    console.log(`\n🔍 Test 3: Call Creation Test (DRY RUN)`);
    
    try {
        const callData = {
            assistantId: assistantId,
            customer: {
                phoneNumber: '+**********' // Test number
            }
        };
        
        console.log(`✅ Call data prepared for assistant: ${assistantId.substring(0, 8)}...`);
        console.log(`   Customer: ${callData.customer.phoneNumber}`);
        console.log(`   ⚠️  DRY RUN - No actual call created`);
        
        // Note: Uncomment below to actually test call creation
        /*
        const response = await fetch('https://api.vapi.ai/call', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${VAPI_PRIVATE_KEY}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(callData)
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const call = await response.json();
        console.log(`✅ Call created: ${call.id}`);
        */
        
        return true;
    } catch (error) {
        console.log(`❌ Call creation test failed: ${error.message}`);
        return false;
    }
}

async function testEnvironmentVariables() {
    console.log('\n🔍 Test 4: Environment Variables');
    
    const requiredVars = [
        'VITE_VAPI_PUBLIC_KEY',
        'VAPI_PRIVATE_KEY',
        'VITE_SUPABASE_URL',
        'VITE_SUPABASE_ANON_KEY'
    ];
    
    let allPresent = true;
    
    requiredVars.forEach(varName => {
        const value = process.env[varName];
        if (value) {
            console.log(`✅ ${varName}: ${value.substring(0, 10)}...`);
        } else {
            console.log(`❌ ${varName}: Missing`);
            allPresent = false;
        }
    });
    
    return allPresent;
}

async function runQuickTests() {
    console.log('🚀 Starting quick propagation tests...\n');
    
    const results = {
        vapiConnection: false,
        assistantRetrieval: false,
        callCreation: false,
        environment: false
    };
    
    // Test 1: Vapi Connection
    const assistants = await testVapiConnection();
    results.vapiConnection = assistants.length > 0;
    
    // Test 2: Assistant Retrieval (use first assistant)
    if (assistants.length > 0) {
        const assistant = await testAssistantRetrieval(assistants[0].id);
        results.assistantRetrieval = assistant !== null;
        
        // Test 3: Call Creation (dry run)
        if (assistant) {
            results.callCreation = await testCallCreation(assistant.id);
        }
    }
    
    // Test 4: Environment Variables
    results.environment = await testEnvironmentVariables();
    
    // Summary
    console.log('\n📊 Test Results Summary');
    console.log('======================');
    
    const tests = [
        { name: 'Vapi Connection', result: results.vapiConnection },
        { name: 'Assistant Retrieval', result: results.assistantRetrieval },
        { name: 'Call Creation (Dry Run)', result: results.callCreation },
        { name: 'Environment Variables', result: results.environment }
    ];
    
    tests.forEach(test => {
        const status = test.result ? '✅ PASS' : '❌ FAIL';
        console.log(`${status} ${test.name}`);
    });
    
    const passCount = tests.filter(t => t.result).length;
    console.log(`\n🎯 Overall: ${passCount}/${tests.length} tests passed`);
    
    if (passCount === tests.length) {
        console.log('🎉 All tests passed! Assistant propagation should work correctly.');
    } else {
        console.log('⚠️  Some tests failed. Check the issues above.');
    }
    
    console.log('\n💡 Next Steps:');
    console.log('1. Open your dashboard at http://localhost:5175/dashboard');
    console.log('2. Check the assistant dropdown is populated');
    console.log('3. Select different assistants and verify UI updates');
    console.log('4. Open browser console to see propagation logs');
    console.log('5. Run the HTML test: http://localhost:5175/test-assistant-propagation.html');
    
    return passCount === tests.length;
}

// Run the tests
runQuickTests().then(success => {
    process.exit(success ? 0 : 1);
}).catch(error => {
    console.error('❌ Test runner failed:', error);
    process.exit(1);
});

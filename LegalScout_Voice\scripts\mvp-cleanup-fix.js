/**
 * MVP Cleanup Fix Script
 * 
 * This script systematically fixes the critical issues blocking MVP:
 * 1. Cleans up duplicate Vapi assistants
 * 2. Consolidates attorney profiles
 * 3. Ensures proper assistant ID mapping
 * 4. Prevents future duplicate creation
 */

const { createClient } = require('@supabase/supabase-js');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

const VAPI_PRIVATE_KEY = process.env.VAPI_TOKEN || process.env.VAPI_PRIVATE_KEY;
const CORRECT_ASSISTANT_ID = 'f9b97d13-f9c4-40af-a660-62ba5925ff2a';
const PRIMARY_ATTORNEY_EMAIL = '<EMAIL>';

console.log('🚀 Starting MVP Cleanup Fix...');

/**
 * Step 1: Consolidate attorney profiles
 */
async function consolidateAttorneyProfiles() {
  console.log('\n📋 Step 1: Consolidating attorney profiles...');
  
  try {
    // Get all attorneys for the primary email
    const { data: attorneys, error } = await supabase
      .from('attorneys')
      .select('*')
      .eq('email', PRIMARY_ATTORNEY_EMAIL)
      .order('updated_at', { ascending: false });

    if (error) throw error;

    console.log(`Found ${attorneys.length} attorney records for ${PRIMARY_ATTORNEY_EMAIL}`);

    if (attorneys.length > 1) {
      // Find the primary attorney (with valid assistant ID)
      const primaryAttorney = attorneys.find(a => 
        a.vapi_assistant_id === CORRECT_ASSISTANT_ID
      ) || attorneys[0];

      console.log(`Primary attorney: ${primaryAttorney.id} (${primaryAttorney.firm_name})`);

      // Update primary attorney to ensure it has the correct assistant ID
      const { error: updateError } = await supabase
        .from('attorneys')
        .update({
          vapi_assistant_id: CORRECT_ASSISTANT_ID,
          updated_at: new Date().toISOString()
        })
        .eq('id', primaryAttorney.id);

      if (updateError) throw updateError;

      console.log('✅ Primary attorney updated with correct assistant ID');

      // Mark other attorneys as duplicates (don't delete to preserve data)
      const duplicateIds = attorneys
        .filter(a => a.id !== primaryAttorney.id)
        .map(a => a.id);

      if (duplicateIds.length > 0) {
        const { error: markError } = await supabase
          .from('attorneys')
          .update({
            email: `duplicate-${Date.now()}-${PRIMARY_ATTORNEY_EMAIL}`,
            updated_at: new Date().toISOString()
          })
          .in('id', duplicateIds);

        if (markError) throw markError;

        console.log(`✅ Marked ${duplicateIds.length} duplicate attorneys`);
      }
    } else {
      console.log('✅ Only one attorney record found - no consolidation needed');
    }

  } catch (error) {
    console.error('❌ Error consolidating attorney profiles:', error);
    throw error;
  }
}

/**
 * Step 2: List and identify duplicate Vapi assistants
 */
async function identifyDuplicateAssistants() {
  console.log('\n🔍 Step 2: Identifying duplicate Vapi assistants...');
  
  try {
    const response = await fetch('https://api.vapi.ai/assistant', {
      headers: {
        'Authorization': `Bearer ${VAPI_PRIVATE_KEY}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`Vapi API error: ${response.status}`);
    }

    const assistants = await response.json();
    console.log(`Found ${assistants.length} total assistants in Vapi`);

    // Filter for LegalScout assistants
    const legalScoutAssistants = assistants.filter(a => 
      a.name && (
        a.name.includes('LegalScout') || 
        a.name.includes('Legal Assistant')
      )
    );

    console.log(`Found ${legalScoutAssistants.length} LegalScout assistants:`);
    
    legalScoutAssistants.forEach(assistant => {
      const isCorrect = assistant.id === CORRECT_ASSISTANT_ID;
      console.log(`  ${isCorrect ? '✅' : '❌'} ${assistant.id} - ${assistant.name} (${assistant.createdAt})`);
    });

    // Identify duplicates (all except the correct one)
    const duplicates = legalScoutAssistants.filter(a => a.id !== CORRECT_ASSISTANT_ID);
    
    console.log(`\n🎯 Correct assistant: ${CORRECT_ASSISTANT_ID}`);
    console.log(`🗑️  Duplicate assistants to clean: ${duplicates.length}`);

    return duplicates;

  } catch (error) {
    console.error('❌ Error identifying duplicate assistants:', error);
    throw error;
  }
}

/**
 * Step 3: Clean up duplicate assistants (mark for manual review)
 */
async function markDuplicateAssistants(duplicates) {
  console.log('\n🧹 Step 3: Marking duplicate assistants...');
  
  // For safety, we'll just log the duplicates for manual review
  // In production, you might want to actually delete them
  
  console.log('⚠️  For safety, duplicate assistants are marked for manual review:');
  
  duplicates.forEach(assistant => {
    console.log(`  🗑️  DELETE: ${assistant.id} - ${assistant.name}`);
    console.log(`     Created: ${assistant.createdAt}`);
    console.log(`     Command: curl -X DELETE "https://api.vapi.ai/assistant/${assistant.id}" -H "Authorization: Bearer ${VAPI_PRIVATE_KEY}"`);
    console.log('');
  });

  console.log('✅ Duplicate assistants marked for manual cleanup');
}

/**
 * Step 4: Verify system state
 */
async function verifySystemState() {
  console.log('\n✅ Step 4: Verifying system state...');
  
  try {
    // Check attorney profile
    const { data: attorney, error } = await supabase
      .from('attorneys')
      .select('*')
      .eq('email', PRIMARY_ATTORNEY_EMAIL)
      .single();

    if (error) throw error;

    console.log('Attorney Profile:');
    console.log(`  ID: ${attorney.id}`);
    console.log(`  Email: ${attorney.email}`);
    console.log(`  Firm: ${attorney.firm_name}`);
    console.log(`  Assistant ID: ${attorney.vapi_assistant_id}`);
    console.log(`  Updated: ${attorney.updated_at}`);

    // Verify assistant exists in Vapi
    const response = await fetch(`https://api.vapi.ai/assistant/${attorney.vapi_assistant_id}`, {
      headers: {
        'Authorization': `Bearer ${VAPI_PRIVATE_KEY}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.ok) {
      const assistant = await response.json();
      console.log('\nVapi Assistant:');
      console.log(`  ID: ${assistant.id}`);
      console.log(`  Name: ${assistant.name}`);
      console.log(`  Voice: ${assistant.voice?.provider}/${assistant.voice?.voiceId}`);
      console.log(`  Updated: ${assistant.updatedAt}`);
      console.log('✅ Assistant verified in Vapi');
    } else {
      console.log('❌ Assistant not found in Vapi');
    }

  } catch (error) {
    console.error('❌ Error verifying system state:', error);
  }
}

/**
 * Main execution
 */
async function main() {
  try {
    await consolidateAttorneyProfiles();
    const duplicates = await identifyDuplicateAssistants();
    await markDuplicateAssistants(duplicates);
    await verifySystemState();
    
    console.log('\n🎉 MVP Cleanup Fix completed successfully!');
    console.log('\n📋 Next Steps:');
    console.log('1. Manually delete duplicate Vapi assistants using the provided commands');
    console.log('2. Test authentication flow: login → dashboard');
    console.log('3. Test dashboard profile loading and sync');
    console.log('4. Verify no new assistants are created on subsequent logins');
    
  } catch (error) {
    console.error('\n💥 MVP Cleanup Fix failed:', error);
    process.exit(1);
  }
}

// Run the script
main();

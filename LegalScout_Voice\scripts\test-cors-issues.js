#!/usr/bin/env node

/**
 * CORS Issues Test Script
 * Tests API endpoints to identify CORS preflight failures
 */

import fetch from 'node-fetch';
import { fileURLToPath } from 'url';
import path from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Test configuration
const TEST_CONFIG = {
  baseUrl: process.env.TEST_URL || 'http://localhost:5174',
  productionUrl: 'https://dashboard.legalscout.net',
  timeout: 10000
};

// Test results
let testResults = {
  passed: 0,
  failed: 0,
  total: 0,
  details: []
};

console.log('🌐 [CORS Test] Starting CORS preflight test suite...');
console.log(`Testing against: ${TEST_CONFIG.baseUrl}`);

async function runAllTests() {
  console.log('\n📋 Running comprehensive CORS tests...\n');
  
  // Test local API endpoints
  await testLocalEndpoints();
  
  // Test preflight requests
  await testPreflightRequests();
  
  // Test CORS headers
  await testCORSHeaders();
  
  // Print summary
  printSummary();
}

async function testLocalEndpoints() {
  console.log('🏠 Testing local API endpoints...');
  
  const endpoints = [
    '/api/health',
    '/api/env',
    '/api/call-logs',
    '/api/vapi/config',
    '/api/vapi-mcp-server'
  ];
  
  for (const endpoint of endpoints) {
    await testEndpoint('GET', endpoint);
  }
}

async function testPreflightRequests() {
  console.log('\n✈️ Testing preflight OPTIONS requests...');
  
  const endpoints = [
    '/api/health',
    '/api/env',
    '/api/call-logs',
    '/api/vapi-mcp-server'
  ];
  
  for (const endpoint of endpoints) {
    await testPreflight(endpoint);
  }
}

async function testCORSHeaders() {
  console.log('\n🔍 Testing CORS headers...');
  
  const endpoints = [
    '/api/health',
    '/api/env'
  ];
  
  for (const endpoint of endpoints) {
    await testCORSHeadersForEndpoint(endpoint);
  }
}

async function testEndpoint(method, endpoint) {
  const testName = `${method} ${endpoint}`;
  const url = `${TEST_CONFIG.baseUrl}${endpoint}`;
  
  try {
    console.log(`  Testing: ${testName}`);
    
    const response = await fetch(url, {
      method: method,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      timeout: TEST_CONFIG.timeout
    });
    
    const result = {
      test: testName,
      url: url,
      status: response.status,
      statusText: response.statusText,
      headers: Object.fromEntries(response.headers.entries()),
      success: response.ok
    };
    
    if (response.ok) {
      console.log(`    ✅ Success: ${response.status} ${response.statusText}`);
      testResults.passed++;
    } else {
      console.log(`    ❌ Failed: ${response.status} ${response.statusText}`);
      testResults.failed++;
    }
    
    testResults.details.push(result);
    testResults.total++;
    
  } catch (error) {
    console.log(`    ❌ Error: ${error.message}`);
    
    testResults.details.push({
      test: testName,
      url: url,
      error: error.message,
      success: false
    });
    
    testResults.failed++;
    testResults.total++;
  }
}

async function testPreflight(endpoint) {
  const testName = `OPTIONS ${endpoint} (preflight)`;
  const url = `${TEST_CONFIG.baseUrl}${endpoint}`;
  
  try {
    console.log(`  Testing: ${testName}`);
    
    const response = await fetch(url, {
      method: 'OPTIONS',
      headers: {
        'Access-Control-Request-Method': 'POST',
        'Access-Control-Request-Headers': 'Content-Type, Authorization',
        'Origin': 'http://localhost:5174'
      },
      timeout: TEST_CONFIG.timeout
    });
    
    const result = {
      test: testName,
      url: url,
      status: response.status,
      statusText: response.statusText,
      headers: Object.fromEntries(response.headers.entries()),
      success: response.ok
    };
    
    // Check for required CORS headers
    const corsHeaders = {
      'access-control-allow-origin': response.headers.get('access-control-allow-origin'),
      'access-control-allow-methods': response.headers.get('access-control-allow-methods'),
      'access-control-allow-headers': response.headers.get('access-control-allow-headers'),
      'access-control-max-age': response.headers.get('access-control-max-age')
    };
    
    result.corsHeaders = corsHeaders;
    
    if (response.ok) {
      console.log(`    ✅ Preflight Success: ${response.status}`);
      console.log(`    📋 CORS Headers:`, corsHeaders);
      testResults.passed++;
    } else {
      console.log(`    ❌ Preflight Failed: ${response.status} ${response.statusText}`);
      console.log(`    📋 CORS Headers:`, corsHeaders);
      testResults.failed++;
    }
    
    testResults.details.push(result);
    testResults.total++;
    
  } catch (error) {
    console.log(`    ❌ Preflight Error: ${error.message}`);
    
    testResults.details.push({
      test: testName,
      url: url,
      error: error.message,
      success: false
    });
    
    testResults.failed++;
    testResults.total++;
  }
}

async function testCORSHeadersForEndpoint(endpoint) {
  const testName = `CORS Headers ${endpoint}`;
  const url = `${TEST_CONFIG.baseUrl}${endpoint}`;
  
  try {
    console.log(`  Testing: ${testName}`);
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Origin': 'http://localhost:5174'
      },
      timeout: TEST_CONFIG.timeout
    });
    
    const corsHeaders = {
      'access-control-allow-origin': response.headers.get('access-control-allow-origin'),
      'access-control-allow-credentials': response.headers.get('access-control-allow-credentials'),
      'access-control-expose-headers': response.headers.get('access-control-expose-headers')
    };
    
    const result = {
      test: testName,
      url: url,
      status: response.status,
      corsHeaders: corsHeaders,
      success: response.ok && corsHeaders['access-control-allow-origin']
    };
    
    if (result.success) {
      console.log(`    ✅ CORS Headers Present`);
      console.log(`    📋 Headers:`, corsHeaders);
      testResults.passed++;
    } else {
      console.log(`    ❌ CORS Headers Missing or Invalid`);
      console.log(`    📋 Headers:`, corsHeaders);
      testResults.failed++;
    }
    
    testResults.details.push(result);
    testResults.total++;
    
  } catch (error) {
    console.log(`    ❌ CORS Headers Test Error: ${error.message}`);
    
    testResults.details.push({
      test: testName,
      url: url,
      error: error.message,
      success: false
    });
    
    testResults.failed++;
    testResults.total++;
  }
}

function printSummary() {
  console.log('\n' + '='.repeat(60));
  console.log('📊 CORS TEST SUMMARY');
  console.log('='.repeat(60));
  console.log(`Total Tests: ${testResults.total}`);
  console.log(`Passed: ${testResults.passed} ✅`);
  console.log(`Failed: ${testResults.failed} ❌`);
  console.log(`Success Rate: ${Math.round((testResults.passed / testResults.total) * 100)}%`);
  
  if (testResults.failed > 0) {
    console.log('\n❌ FAILED TESTS:');
    testResults.details
      .filter(result => !result.success)
      .forEach(result => {
        console.log(`  • ${result.test}: ${result.error || `${result.status} ${result.statusText}`}`);
      });
  }
  
  console.log('\n🔍 RECOMMENDATIONS:');
  
  const failedPreflights = testResults.details.filter(r => 
    r.test.includes('preflight') && !r.success
  );
  
  if (failedPreflights.length > 0) {
    console.log('  • Fix preflight OPTIONS request handling in API routes');
    console.log('  • Ensure all API endpoints return 200 status for OPTIONS requests');
    console.log('  • Verify CORS headers are set correctly for preflight responses');
  }
  
  const missingCorsHeaders = testResults.details.filter(r => 
    r.test.includes('CORS Headers') && !r.success
  );
  
  if (missingCorsHeaders.length > 0) {
    console.log('  • Add missing CORS headers to API responses');
    console.log('  • Ensure Access-Control-Allow-Origin is set correctly');
  }
  
  console.log('\n📝 DETAILED RESULTS:');
  console.log(JSON.stringify(testResults, null, 2));
}

// Run tests
runAllTests().catch(error => {
  console.error('❌ Test suite failed:', error);
  process.exit(1);
});

#!/usr/bin/env node
/**
 * Test script for Vapi MCP Server SDK
 *
 * This script tests the basic functionality of the Vapi MCP Server SDK.
 * It connects to the Vapi MCP Server and lists available tools.
 *
 * Usage:
 *   node scripts/test-vapi-mcp.js
 *
 * Environment variables:
 *   VAPI_TOKEN - Your Vapi API key
 */

import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { SSEClientTransport } from '@modelcontextprotocol/sdk/client/sse.js';
import dotenv from 'dotenv';

// Load environment variables from .env file
dotenv.config();

// Get API key from environment variables or use the provided one
let apiKey = process.env.VAPI_TOKEN;

// If not found in environment, use the hardcoded one for testing
if (!apiKey) {
  console.log('VAPI_TOKEN not found in environment, using provided API key');
  apiKey = '6734febc-fc65-4669-93b0-929b31ff6564';
}

console.log('Testing Vapi MCP Server SDK...');
console.log('API Key:', apiKey.substring(0, 4) + '...' + apiKey.substring(apiKey.length - 4));

async function testMcpServer() {
  try {
    // Initialize MCP client
    console.log('\nInitializing MCP client...');
    const mcpClient = new Client({
      name: 'legalscout-test',
      version: '1.0.0',
    });

    // Create SSE transport for connection to remote Vapi MCP server
    console.log('Creating SSE transport...');
    const sseUrl = new URL('https://mcp.vapi.ai/sse');
    console.log('SSE URL:', sseUrl.toString());

    const transport = new SSEClientTransport({
      url: sseUrl.toString(),
      headers: {
        'Authorization': `Bearer ${apiKey}`
      }
    });

    console.log('Connecting to Vapi MCP server via SSE...');
    await mcpClient.connect(transport);
    console.log('✅ Connected successfully');

    try {
      // List available tools
      console.log('\nListing available tools...');
      const toolsResult = await mcpClient.listTools();

      if (toolsResult.tools && toolsResult.tools.length > 0) {
        console.log(`✅ Found ${toolsResult.tools.length} tools:`);
        toolsResult.tools.forEach((tool) => {
          console.log(`  - ${tool.name}: ${tool.description || 'No description'}`);
        });
      } else {
        console.log('❌ No tools found');
      }
    } finally {
      console.log('\nDisconnecting from server...');
      await mcpClient.close();
      console.log('✅ Disconnected');
    }

    console.log('\n✅ Test completed successfully');
  } catch (error) {
    console.error('\n❌ Test failed with error:', error);
    process.exit(1);
  }
}

testMcpServer();

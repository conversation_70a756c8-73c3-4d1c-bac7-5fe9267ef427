/**
 * Disable Conflicting Scripts
 * 
 * This script disables all the conflicting fix scripts that are causing
 * authentication and other issues by interfering with each other.
 */

console.log('🧹 [DisableConflictingScripts] Starting cleanup of conflicting scripts...');

// List of global variables and functions created by conflicting scripts
const conflictingGlobals = [
  'ForceNewAssistant',
  'EmergencyApiKeyFix', 
  'CriticalProductionFix',
  'CleanAuthSolution',
  'RobustStateHandler',
  'UnifiedBannerFix',
  'ProductionCorsFix',
  'StandaloneAttorneyManager',
  'VapiDirectApiService',
  'resolveAttorneyState',
  'fixVapiAssistantConfig',
  'enhanceAttorneyManager',
  'controlledAssistantCreation'
];

// Disable conflicting global functions
conflictingGlobals.forEach(globalName => {
  if (typeof window[globalName] !== 'undefined') {
    console.log(`🧹 [DisableConflictingScripts] Disabling ${globalName}`);
    window[globalName] = function() {
      console.log(`🧹 [DisableConflictingScripts] ${globalName} has been disabled to prevent conflicts`);
      return Promise.resolve({ success: false, disabled: true, message: '<PERSON><PERSON><PERSON> disabled to prevent conflicts' });
    };
  }
});

// Disable fetch interceptors that might be interfering
if (window.originalFetch) {
  console.log('🧹 [DisableConflictingScripts] Restoring original fetch function');
  window.fetch = window.originalFetch;
}

// Clear any localStorage items that might be causing conflicts
const conflictingStorageKeys = [
  'force_new_assistant',
  'emergency_fix_applied',
  'critical_fix_applied',
  'clean_auth_applied',
  'robust_state_applied'
];

conflictingStorageKeys.forEach(key => {
  if (localStorage.getItem(key)) {
    console.log(`🧹 [DisableConflictingScripts] Clearing localStorage key: ${key}`);
    localStorage.removeItem(key);
  }
});

// Prevent new conflicting scripts from loading
const originalCreateElement = document.createElement;
document.createElement = function(tagName) {
  const element = originalCreateElement.call(this, tagName);
  
  if (tagName.toLowerCase() === 'script') {
    const originalSetAttribute = element.setAttribute;
    element.setAttribute = function(name, value) {
      if (name === 'src' && typeof value === 'string') {
        // Block known conflicting scripts
        const conflictingScripts = [
          'force-new-assistant.js',
          'emergency-api-key-fix.js',
          'critical-production-fix.js',
          'clean-auth-solution.js',
          'robust-state-handler.js',
          'unified-banner-fix.js',
          'production-cors-fix.js'
        ];
        
        if (conflictingScripts.some(script => value.includes(script))) {
          console.log(`🧹 [DisableConflictingScripts] Blocked loading of conflicting script: ${value}`);
          return;
        }
      }
      return originalSetAttribute.call(this, name, value);
    };
  }
  
  return element;
};

console.log('🧹 [DisableConflictingScripts] ✅ Conflicting scripts cleanup complete');

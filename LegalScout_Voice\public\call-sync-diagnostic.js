/**
 * Call Sync Diagnostic <PERSON>t
 * 
 * Run this script in the browser console to diagnose call sync issues.
 * Usage: Open browser console and run: window.diagnoseCallSync()
 */

window.diagnoseCallSync = async function() {
  console.log('🔍 CALL SYNC DIAGNOSTIC STARTING');
  console.log('='.repeat(50));
  
  const results = {
    database: { passed: 0, failed: 0, tests: [] },
    webhook: { passed: 0, failed: 0, tests: [] },
    vapi: { passed: 0, failed: 0, tests: [] },
    dataFlow: { passed: 0, failed: 0, tests: [] }
  };

  // Helper function to log test results
  function logTest(category, testName, passed, details = '') {
    const status = passed ? '✅' : '❌';
    const message = `${status} ${testName}${details ? ': ' + details : ''}`;
    console.log(message);
    
    results[category].tests.push({ name: testName, passed, details });
    if (passed) {
      results[category].passed++;
    } else {
      results[category].failed++;
    }
  }

  // 1. DATABASE TESTS
  console.log('\n📊 TESTING DATABASE STRUCTURE...');
  
  try {
    // Check if Supabase is available
    if (typeof window.supabase === 'undefined') {
      logTest('database', 'Supabase client availability', false, 'Supabase client not found');
    } else {
      logTest('database', 'Supabase client availability', true);
      
      // Test call_records table
      try {
        const { data: callRecords, error: callError } = await window.supabase
          .from('call_records')
          .select('count', { count: 'exact' })
          .limit(1);
        
        if (callError) {
          logTest('database', 'call_records table access', false, callError.message);
        } else {
          logTest('database', 'call_records table access', true, `Found ${callRecords.length} records`);
        }
      } catch (err) {
        logTest('database', 'call_records table access', false, err.message);
      }

      // Test consultations table
      try {
        const { data: consultations, error: consultError } = await window.supabase
          .from('consultations')
          .select('count', { count: 'exact' })
          .limit(1);
        
        if (consultError) {
          logTest('database', 'consultations table access', false, consultError.message);
        } else {
          logTest('database', 'consultations table access', true, `Found ${consultations.length} records`);
        }
      } catch (err) {
        logTest('database', 'consultations table access', false, err.message);
      }

      // Check attorney data
      try {
        const currentUser = window.supabase.auth.getUser ? await window.supabase.auth.getUser() : null;
        if (currentUser?.data?.user?.email) {
          const { data: attorney, error: attorneyError } = await window.supabase
            .from('attorneys')
            .select('id, assistant_id, email')
            .eq('email', currentUser.data.user.email)
            .single();
          
          if (attorneyError) {
            logTest('database', 'Current attorney lookup', false, attorneyError.message);
          } else {
            logTest('database', 'Current attorney lookup', true, `ID: ${attorney.id}, Assistant: ${attorney.assistant_id}`);
            window.diagnosticAttorney = attorney; // Store for later tests
          }
        } else {
          logTest('database', 'Current attorney lookup', false, 'No authenticated user');
        }
      } catch (err) {
        logTest('database', 'Current attorney lookup', false, err.message);
      }
    }
  } catch (err) {
    logTest('database', 'Database connection', false, err.message);
  }

  // 2. WEBHOOK TESTS
  console.log('\n🔗 TESTING WEBHOOK CONFIGURATION...');
  
  try {
    // Test webhook endpoint accessibility
    const webhookUrl = window.location.origin + '/api/webhook/vapi-call';
    
    try {
      const response = await fetch(webhookUrl, {
        method: 'GET' // Should return 405 Method Not Allowed
      });
      
      if (response.status === 405) {
        logTest('webhook', 'Webhook endpoint exists', true, 'Returns 405 for GET as expected');
      } else {
        logTest('webhook', 'Webhook endpoint exists', false, `Unexpected status: ${response.status}`);
      }
    } catch (err) {
      logTest('webhook', 'Webhook endpoint exists', false, err.message);
    }

    // Test webhook URL format
    const expectedPattern = /\/api\/webhook\/vapi-call$/;
    if (expectedPattern.test(webhookUrl)) {
      logTest('webhook', 'Webhook URL format', true, webhookUrl);
    } else {
      logTest('webhook', 'Webhook URL format', false, `URL: ${webhookUrl}`);
    }

  } catch (err) {
    logTest('webhook', 'Webhook configuration', false, err.message);
  }

  // 3. VAPI TESTS
  console.log('\n📞 TESTING VAPI INTEGRATION...');
  
  try {
    // Check if Vapi client is available
    if (typeof window.vapi === 'undefined') {
      logTest('vapi', 'Vapi client availability', false, 'Vapi client not found');
    } else {
      logTest('vapi', 'Vapi client availability', true);
    }

    // Check environment variables
    const vapiPublicKey = '310f0d43-27c2-47a5-a76d-e55171d024f7';
    if (vapiPublicKey && vapiPublicKey.length > 0) {
      logTest('vapi', 'Vapi public key configured', true);
    } else {
      logTest('vapi', 'Vapi public key configured', false, 'Public key not found');
    }

  } catch (err) {
    logTest('vapi', 'Vapi integration', false, err.message);
  }

  // 4. DATA FLOW TESTS
  console.log('\n🔄 TESTING DATA FLOW...');
  
  try {
    if (window.diagnosticAttorney && window.supabase) {
      // Check for call records for current attorney
      const { data: attorneyCallRecords, error: callRecordsError } = await window.supabase
        .from('call_records')
        .select('*')
        .eq('attorney_id', window.diagnosticAttorney.id)
        .order('created_at', { ascending: false })
        .limit(5);
      
      if (callRecordsError) {
        logTest('dataFlow', 'Attorney call records query', false, callRecordsError.message);
      } else {
        logTest('dataFlow', 'Attorney call records query', true, `Found ${attorneyCallRecords.length} call records`);
        
        if (attorneyCallRecords.length === 0) {
          logTest('dataFlow', 'Call records exist', false, 'No call records found for attorney');
        } else {
          logTest('dataFlow', 'Call records exist', true, `Latest: ${attorneyCallRecords[0].created_at}`);
        }
      }

      // Check for consultations for current attorney
      const { data: attorneyConsultations, error: consultationsError } = await window.supabase
        .from('consultations')
        .select('*')
        .eq('attorney_id', window.diagnosticAttorney.id)
        .order('created_at', { ascending: false })
        .limit(5);
      
      if (consultationsError) {
        logTest('dataFlow', 'Attorney consultations query', false, consultationsError.message);
      } else {
        logTest('dataFlow', 'Attorney consultations query', true, `Found ${attorneyConsultations.length} consultations`);
        
        if (attorneyConsultations.length > 0) {
          logTest('dataFlow', 'Consultations exist', true, `Latest: ${attorneyConsultations[0].created_at}`);
          
          // Check if consultations have corresponding call records
          const consultationsWithCallIds = attorneyConsultations.filter(c => 
            c.metadata && c.metadata.call_id
          );
          
          if (consultationsWithCallIds.length > 0) {
            logTest('dataFlow', 'Consultations linked to calls', true, `${consultationsWithCallIds.length} linked`);
          } else {
            logTest('dataFlow', 'Consultations linked to calls', false, 'No call_id metadata found');
          }
        } else {
          logTest('dataFlow', 'Consultations exist', false, 'No consultations found');
        }
      }
    } else {
      logTest('dataFlow', 'Attorney data available', false, 'No attorney data for testing');
    }
  } catch (err) {
    logTest('dataFlow', 'Data flow analysis', false, err.message);
  }

  // SUMMARY
  console.log('\n📋 DIAGNOSTIC SUMMARY');
  console.log('='.repeat(50));
  
  const totalPassed = Object.values(results).reduce((sum, cat) => sum + cat.passed, 0);
  const totalFailed = Object.values(results).reduce((sum, cat) => sum + cat.failed, 0);
  const totalTests = totalPassed + totalFailed;
  
  console.log(`Total Tests: ${totalTests}`);
  console.log(`Passed: ${totalPassed} ✅`);
  console.log(`Failed: ${totalFailed} ❌`);
  console.log(`Success Rate: ${Math.round((totalPassed / totalTests) * 100)}%`);
  
  Object.entries(results).forEach(([category, result]) => {
    const categoryTotal = result.passed + result.failed;
    const categoryRate = categoryTotal > 0 ? Math.round((result.passed / categoryTotal) * 100) : 0;
    console.log(`\n${category.toUpperCase()}: ${result.passed}/${categoryTotal} (${categoryRate}%)`);
    
    result.tests.forEach(test => {
      const status = test.passed ? '✅' : '❌';
      console.log(`  ${status} ${test.name}${test.details ? ': ' + test.details : ''}`);
    });
  });

  // RECOMMENDATIONS
  console.log('\n💡 RECOMMENDATIONS');
  console.log('='.repeat(50));
  
  if (results.database.failed > 0) {
    console.log('🔧 Database Issues Detected:');
    console.log('   - Check Supabase connection and table permissions');
    console.log('   - Verify RLS policies allow read/write access');
  }
  
  if (results.webhook.failed > 0) {
    console.log('🔧 Webhook Issues Detected:');
    console.log('   - Verify webhook endpoint is deployed and accessible');
    console.log('   - Check Vapi webhook configuration in dashboard');
  }
  
  if (results.dataFlow.failed > 0) {
    console.log('🔧 Data Flow Issues Detected:');
    console.log('   - Call records not being created from webhooks');
    console.log('   - Check webhook handler and Vapi configuration');
    console.log('   - Verify assistant_id mapping to attorney_id');
  }

  return results;
};

// Auto-run if in development
if (window.location.hostname === 'localhost') {
  console.log('🚀 Call Sync Diagnostic loaded. Run window.diagnoseCallSync() to start.');
}

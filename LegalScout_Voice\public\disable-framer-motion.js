/**
 * Complete Framer Motion Disabler
 *
 * This script completely disables Framer Motion by intercepting module imports
 * and replacing them with mock implementations.
 */

(function() {
  console.log('[Disabler] Setting up Framer Motion disabler');

  // CRITICAL: Immediately define React.createContext before any scripts load
  if (typeof window !== 'undefined') {
    // Create a global React object if it doesn't exist
    if (!window.React) {
      window.React = {};
    }

    // Force override createContext even if it exists
    window.React.createContext = function() {
      console.log('[Disabler] Using mock createContext');
      return {
        Provider: function(props) { return props.children || null; },
        Consumer: function(props) { return props.children ? props.children({}) : null; }
      };
    };
  }

  // Create a mock implementation of Framer Motion
  const mockFramerMotion = {
    motion: {
      div: 'div',
      span: 'span',
      button: 'button',
      a: 'a',
      ul: 'ul',
      li: 'li',
      p: 'p',
      h1: 'h1',
      h2: 'h2',
      h3: 'h3',
      img: 'img',
      section: 'section',
      article: 'article',
      nav: 'nav',
      header: 'header',
      footer: 'footer',
      main: 'main',
      aside: 'aside',
      form: 'form',
      input: 'input',
      textarea: 'textarea',
      select: 'select',
      option: 'option',
      label: 'label',
      svg: 'svg',
      path: 'path',
      circle: 'circle',
      rect: 'rect',
      polygon: 'polygon',
      g: 'g'
    },
    AnimatePresence: function(props) {
      return props.children || null;
    },
    useAnimation: function() {
      return {
        start: function() {},
        stop: function() {}
      };
    },
    useMotionValue: function(initial) {
      return {
        get: function() { return initial; },
        set: function() {},
        onChange: function() {}
      };
    },
    useTransform: function() {
      return {
        get: function() { return 0; }
      };
    },
    useSpring: function() {
      return {
        get: function() { return 0; }
      };
    },
    useViewportScroll: function() {
      return {
        scrollY: {
          get: function() { return 0; },
          onChange: function() {}
        },
        scrollYProgress: {
          get: function() { return 0; },
          onChange: function() {}
        }
      };
    },
    useScroll: function() {
      return {
        scrollY: {
          get: function() { return 0; },
          onChange: function() {}
        },
        scrollYProgress: {
          get: function() { return 0; },
          onChange: function() {}
        }
      };
    },
    useCycle: function() {
      return [0, function() {}];
    }
  };

  // Override the import function to intercept Framer Motion imports
  const originalImport = window.import;
  window.import = function(specifier) {
    if (specifier === 'framer-motion' || specifier.includes('framer-motion/')) {
      console.log('[Disabler] Intercepted Framer Motion import:', specifier);
      return Promise.resolve(mockFramerMotion);
    }
    return originalImport.apply(this, arguments);
  };

  // Create a global React object if it doesn't exist
  if (typeof window.React === 'undefined') {
    console.log('[Disabler] Creating React placeholder');
    window.React = {};
  }

  // Ensure createContext exists
  if (typeof window.React.createContext === 'undefined') {
    console.log('[Disabler] Adding createContext placeholder');
    window.React.createContext = function() {
      return {
        Provider: function() { return null; },
        Consumer: function() { return null; }
      };
    };
  }

  // Add other essential React methods
  const reactMethods = [
    'useState', 'useEffect', 'useLayoutEffect', 'useRef',
    'useCallback', 'useMemo', 'useContext', 'forwardRef',
    'createElement', 'cloneElement', 'createRef', 'Component',
    'PureComponent', 'Fragment', 'Children', 'isValidElement'
  ];

  reactMethods.forEach(method => {
    if (typeof window.React[method] === 'undefined') {
      console.log(`[Disabler] Adding ${method} placeholder`);
      window.React[method] = function() {
        return arguments[0] instanceof Function ? arguments[0]() : null;
      };
    }
  });

  // Override the require function to intercept Framer Motion requires
  if (typeof window.require === 'function') {
    const originalRequire = window.require;
    window.require = function(module) {
      if (module === 'framer-motion' || module.includes('framer-motion/')) {
        console.log('[Disabler] Intercepted Framer Motion require:', module);
        return mockFramerMotion;
      }
      return originalRequire.apply(this, arguments);
    };
  }

  // Intercept dynamic imports
  const originalCreateElement = document.createElement;
  document.createElement = function(tagName) {
    const element = originalCreateElement.apply(document, arguments);
    if (tagName.toLowerCase() === 'script') {
      const originalSetAttribute = element.setAttribute;
      element.setAttribute = function(name, value) {
        if (name === 'src' && value && (value.includes('framer-motion') || value.includes('LayoutGroupContext'))) {
          console.log('[Disabler] Blocking Framer Motion script:', value);
          return;
        }
        return originalSetAttribute.apply(this, arguments);
      };
    }
    return element;
  };

  console.log('[Disabler] Framer Motion completely disabled');
})();

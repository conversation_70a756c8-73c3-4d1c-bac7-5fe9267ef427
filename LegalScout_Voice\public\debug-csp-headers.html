<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Debug CSP Headers - LegalScout</title>
  
  <!-- NO CSP meta tag - let's see what the server is sending -->
  
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 20px;
      background: #f5f5f5;
    }
    .container {
      max-width: 1000px;
      margin: 0 auto;
      background: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    .test-result {
      margin: 10px 0;
      padding: 15px;
      border-radius: 6px;
      font-family: 'Courier New', monospace;
      font-size: 0.9em;
    }
    .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
    .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
    .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
    .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
    .code-block {
      background: #2c3e50;
      color: #ecf0f1;
      padding: 15px;
      border-radius: 6px;
      font-family: 'Courier New', monospace;
      font-size: 0.85em;
      overflow-x: auto;
      margin: 10px 0;
      white-space: pre-wrap;
    }
    button {
      background: #007bff;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 4px;
      cursor: pointer;
      margin: 5px;
    }
    button:hover { background: #0056b3; }
  </style>
</head>
<body>
  <div class="container">
    <h1>🔍 CSP Headers Debug Tool</h1>
    <p><strong>This page has NO CSP meta tag</strong> to see exactly what headers the server is sending.</p>
    
    <button onclick="checkAllHeaders()">🔍 Check All Headers</button>
    <button onclick="testEvalNow()">⚡ Test eval() Now</button>
    <button onclick="fetchMainPage()">📄 Fetch Main Page Headers</button>
    <button onclick="clearResults()">🧹 Clear</button>
    
    <div id="results"></div>
  </div>

  <script>
    let violationCount = 0;
    
    // CSP Violation Listener
    document.addEventListener('securitypolicyviolation', (e) => {
      violationCount++;
      console.error('🚨 CSP Violation:', e);
      
      addResult('error', `CSP Violation #${violationCount}`, `
Directive: ${e.violatedDirective}
Blocked URI: ${e.blockedURI}
Source: ${e.sourceFile}:${e.lineNumber}:${e.colno}
Sample: ${e.sample || 'N/A'}
Original Policy: ${e.originalPolicy}
Disposition: ${e.disposition}
      `);
    });

    function addResult(type, title, message) {
      const results = document.getElementById('results');
      const div = document.createElement('div');
      div.className = `test-result ${type}`;
      div.innerHTML = `<strong>${title}</strong><div class="code-block">${message}</div>`;
      results.appendChild(div);
    }

    async function checkAllHeaders() {
      addResult('info', 'Checking Current Page Headers', 'Fetching headers for this page...');
      
      try {
        const response = await fetch(window.location.href, {
          method: 'HEAD'
        });
        
        const headers = {};
        for (let [key, value] of response.headers.entries()) {
          headers[key] = value;
        }
        
        const cspHeader = response.headers.get('content-security-policy');
        const cspReportHeader = response.headers.get('content-security-policy-report-only');
        
        addResult('info', 'Response Headers Analysis', `
CSP Header: ${cspHeader || 'NOT SET'}
CSP Report-Only: ${cspReportHeader || 'NOT SET'}

All Response Headers:
${JSON.stringify(headers, null, 2)}
        `);
        
        // Check if CSP allows eval
        if (cspHeader) {
          const allowsEval = cspHeader.includes('unsafe-eval');
          addResult(allowsEval ? 'success' : 'error', 'CSP Eval Analysis', `
CSP Header Found: YES
Allows unsafe-eval: ${allowsEval ? 'YES' : 'NO'}

Full CSP Policy:
${cspHeader}
          `);
        } else {
          addResult('warning', 'CSP Eval Analysis', 'No CSP header found - eval should be allowed');
        }
        
      } catch (error) {
        addResult('error', 'Headers Check Failed', `Error: ${error.message}`);
      }
    }

    async function fetchMainPage() {
      addResult('info', 'Checking Main Page Headers', 'Fetching headers for main index.html...');
      
      try {
        const response = await fetch('/', {
          method: 'HEAD'
        });
        
        const headers = {};
        for (let [key, value] of response.headers.entries()) {
          headers[key] = value;
        }
        
        const cspHeader = response.headers.get('content-security-policy');
        const cspReportHeader = response.headers.get('content-security-policy-report-only');
        
        addResult('info', 'Main Page Headers', `
CSP Header: ${cspHeader || 'NOT SET'}
CSP Report-Only: ${cspReportHeader || 'NOT SET'}

All Headers:
${JSON.stringify(headers, null, 2)}
        `);
        
      } catch (error) {
        addResult('error', 'Main Page Check Failed', `Error: ${error.message}`);
      }
    }

    function testEvalNow() {
      addResult('info', 'Testing eval() Execution', 'Attempting to run eval() right now...');
      
      try {
        const result = eval('2 + 2');
        addResult('success', 'eval() Test Result', `
SUCCESS: eval() executed without errors
Result: ${result}
This means CSP is NOT blocking eval on this page
        `);
      } catch (error) {
        addResult('error', 'eval() Test Result', `
BLOCKED: eval() was blocked by CSP
Error: ${error.message}
This confirms CSP is actively blocking eval
        `);
      }
    }

    function clearResults() {
      document.getElementById('results').innerHTML = '';
      violationCount = 0;
    }

    // Auto-run tests
    document.addEventListener('DOMContentLoaded', function() {
      console.log('🔍 CSP Headers Debug Tool loaded');
      
      addResult('info', 'Debug Tool Ready', `
This page will help identify exactly what CSP headers are being sent.

Key things to check:
1. Are there CSP headers being sent by the server?
2. Do they include 'unsafe-eval' in script-src?
3. Is there a conflict between meta tag and server headers?

Auto-running tests in 2 seconds...
      `);
      
      setTimeout(() => {
        checkAllHeaders();
        testEvalNow();
      }, 2000);
    });
  </script>
</body>
</html>

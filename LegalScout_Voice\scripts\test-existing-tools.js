#!/usr/bin/env node
/**
 * Test Existing AI Meta MCP Tools
 *
 * This script tests the existing tools in the AI Meta MCP Server.
 * It doesn't try to register new tools, just uses the ones that are already there.
 *
 * Usage:
 *   node scripts/test-existing-tools.js
 */

import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Main function to test existing tools
const testExistingTools = async () => {
  try {
    console.log('Starting test of existing AI Meta MCP tools...');
    
    // Spawn a new AI Meta MCP Server process
    console.log('Starting AI Meta MCP Server...');
    const serverProcess = spawn('node', [
      path.resolve(__dirname, '..', 'ai-meta-mcp-server', 'build', 'index.js')
    ]);
    
    // Log server output
    serverProcess.stdout.on('data', (data) => {
      console.log(`[Server] ${data.toString().trim()}`);
    });
    
    serverProcess.stderr.on('data', (data) => {
      console.error(`[Server] ${data.toString().trim()}`);
    });
    
    // Wait for server to start
    console.log('Waiting for server to start...');
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Run a simple test script that uses the server's built-in tools
    console.log('\nTesting built-in tools...');
    const testProcess = spawn('node', ['-e', `
      const { spawn } = require('child_process');
      const { Client } = require('@modelcontextprotocol/sdk/client/index.js');
      const { StdioClientTransport } = require('@modelcontextprotocol/sdk/client/stdio.js');
      
      async function testTools() {
        try {
          // Create a client
          const client = new Client({
            name: 'test-client',
            version: '1.0.0'
          });
          
          // Connect to the server using stdio transport
          const transport = new StdioClientTransport({
            process: process.stdin,
            output: process.stdout
          });
          
          await client.connect(transport);
          console.log('Connected to server');
          
          // List available tools
          const tools = await client.listTools();
          console.log('Available tools:', tools);
          
          // Disconnect
          await client.disconnect();
          console.log('Disconnected from server');
        } catch (error) {
          console.error('Error:', error.message);
        }
      }
      
      testTools();
    `]);
    
    // Log test output
    testProcess.stdout.on('data', (data) => {
      console.log(`[Test] ${data.toString().trim()}`);
    });
    
    testProcess.stderr.on('data', (data) => {
      console.error(`[Test] ${data.toString().trim()}`);
    });
    
    // Wait for test to complete
    await new Promise((resolve) => {
      testProcess.on('close', (code) => {
        console.log(`Test process exited with code ${code}`);
        resolve();
      });
    });
    
    // Kill the server process
    serverProcess.kill();
    console.log('AI Meta MCP Server process terminated');
    
    console.log('Test completed');
  } catch (error) {
    console.error('Error testing existing tools:', error);
    process.exit(1);
  }
};

// Run the test
testExistingTools();

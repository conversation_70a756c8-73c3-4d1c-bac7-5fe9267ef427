/**
 * Test Streamable-HTTP Integration
 * 
 * Tests the production-recommended streamable-HTTP pattern
 * Based on: https://docs.vapi.ai/sdk/mcp-server#remote-streamable-http
 */

const ASSISTANT_ID = 'f9b97d13-f9c4-40af-a660-62ba5925ff2a';
const VAPI_PRIVATE_KEY = '6734febc-fc65-4669-93b0-929b31ff6564';

async function testStreamableHTTP() {
  console.log('🧪 Testing Corrected Streamable-HTTP Integration (Production Pattern)\n');

  try {
    // Test 1: Test proper MCP client with StreamableHTTPClientTransport
    console.log('📡 Test 1: Proper MCP Client with StreamableHTTP Transport');

    try {
      // Dynamic import to test if MCP SDK is available
      const { Client } = await import('@modelcontextprotocol/sdk/client/index.js');
      const { StreamableHTTPClientTransport } = await import('@modelcontextprotocol/sdk/client/streamableHttp.js');

      console.log('✅ MCP SDK modules imported successfully');

      // Create MCP client
      const mcpClient = new Client({
        name: 'legalscout-test-client',
        version: '1.0.0'
      });

      // Create StreamableHTTP transport
      const transport = new StreamableHTTPClientTransport(
        new URL('https://mcp.vapi.ai/mcp'),
        {
          requestInit: {
            headers: {
              'Authorization': `Bearer ${VAPI_PRIVATE_KEY}`
            }
          }
        }
      );

      console.log('✅ MCP client and transport created');

      // Try to connect
      await mcpClient.connect(transport);
      console.log('✅ MCP client connected successfully');

      // Test list assistants
      const listResponse = await mcpClient.callTool({
        name: 'list_assistants',
        arguments: {}
      });

      console.log('✅ List assistants tool call successful');

      // Parse response
      if (listResponse.content) {
        const textItem = listResponse.content.find(item => item.type === 'text');
        if (textItem?.text) {
          const assistants = JSON.parse(textItem.text);
          console.log(`Found ${assistants.length} assistants via proper MCP client`);

          // Check if our assistant exists
          const ourAssistant = assistants.find(a => a.id === ASSISTANT_ID);
          if (ourAssistant) {
            console.log(`✅ Our assistant found: ${ourAssistant.name}`);
          } else {
            console.log('⚠️ Our assistant not found in list');
          }
        }
      }

      // Clean up
      await mcpClient.close();
      console.log('✅ MCP client closed properly');

    } catch (mcpError) {
      console.log('❌ Proper MCP client failed:', mcpError.message);
      console.log('This is expected if MCP SDK is not available or there are connection issues');
    }

    // Test 2: Get specific assistant using streamable-HTTP
    console.log('\n📡 Test 2: Get Assistant via Streamable-HTTP');
    const getResponse = await fetch('https://mcp.vapi.ai/mcp', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${VAPI_PRIVATE_KEY}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify({
        jsonrpc: '2.0',
        id: Date.now(),
        method: 'tools/call',
        params: {
          name: 'get_assistant',
          arguments: { assistantId: ASSISTANT_ID }
        }
      })
    });

    console.log(`Status: ${getResponse.status} ${getResponse.statusText}`);
    
    if (getResponse.ok) {
      const getResult = await getResponse.json();
      console.log('✅ Streamable-HTTP get assistant successful');
      
      if (getResult.result?.content) {
        const textItem = getResult.result.content.find(item => item.type === 'text');
        if (textItem?.text) {
          const assistant = JSON.parse(textItem.text);
          console.log('Assistant Details:');
          console.log(`- Name: ${assistant.name}`);
          console.log(`- First Message: ${assistant.firstMessage || 'MISSING'}`);
          console.log(`- Instructions: ${assistant.model?.messages?.[0]?.content ? 'PRESENT' : 'MISSING'}`);
          console.log(`- Voice: ${assistant.voice?.provider}/${assistant.voice?.voiceId}`);
        }
      }
    } else {
      console.log('❌ Streamable-HTTP get failed');
      const errorText = await getResponse.text();
      console.log('Error:', errorText);
    }

    // Test 3: Compare with direct API
    console.log('\n📡 Test 3: Compare with Direct API');
    const directResponse = await fetch(`https://api.vapi.ai/assistant/${ASSISTANT_ID}`, {
      headers: {
        'Authorization': `Bearer ${VAPI_PRIVATE_KEY}`,
        'Content-Type': 'application/json'
      }
    });

    console.log(`Direct API Status: ${directResponse.status} ${directResponse.statusText}`);
    
    if (directResponse.ok) {
      const directAssistant = await directResponse.json();
      console.log('✅ Direct API successful');
      console.log('Direct API Assistant Details:');
      console.log(`- Name: ${directAssistant.name}`);
      console.log(`- First Message: ${directAssistant.firstMessage || 'MISSING'}`);
      console.log(`- Instructions: ${directAssistant.model?.messages?.[0]?.content ? 'PRESENT' : 'MISSING'}`);
      console.log(`- Voice: ${directAssistant.voice?.provider}/${directAssistant.voice?.voiceId}`);
    } else {
      console.log('❌ Direct API failed');
    }

  } catch (error) {
    console.error('💥 Test failed:', error);
  }
}

async function testProductionCORSFixes() {
  console.log('\n' + '='.repeat(60));
  console.log('🔧 PRODUCTION CORS FIXES TEST');
  console.log('='.repeat(60));

  const fixes = {
    properMCPClient: false,
    directAPIEndpoint: false,
    corsHandling: false,
    environmentVars: false,
    overallHealth: false
  };

  try {
    // Test 1: Proper MCP Client (should work)
    console.log('📡 Test 1: Proper MCP Client with StreamableHTTP');
    try {
      const { Client } = await import('@modelcontextprotocol/sdk/client/index.js');
      const { StreamableHTTPClientTransport } = await import('@modelcontextprotocol/sdk/client/streamableHttp.js');

      const mcpClient = new Client({ name: 'test-client', version: '1.0.0' });
      const transport = new StreamableHTTPClientTransport(
        new URL('https://mcp.vapi.ai/mcp'),
        { requestInit: { headers: { 'Authorization': `Bearer ${VAPI_PRIVATE_KEY}` } } }
      );

      await mcpClient.connect(transport);
      const response = await mcpClient.callTool({ name: 'list_assistants', arguments: {} });
      await mcpClient.close();

      fixes.properMCPClient = true;
      console.log('✅ Proper MCP Client: Working');
    } catch (error) {
      console.log('❌ Proper MCP Client: Failed -', error.message);
    }

    // Test 2: Direct API Endpoint (should work)
    console.log('📡 Test 2: Direct API Endpoint');
    try {
      const response = await fetch(`https://api.vapi.ai/assistant/${ASSISTANT_ID}`, {
        headers: {
          'Authorization': `Bearer ${VAPI_PRIVATE_KEY}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        fixes.directAPIEndpoint = true;
        console.log('✅ Direct API Endpoint: Working');
      } else {
        console.log('❌ Direct API Endpoint: Failed -', response.status, response.statusText);
      }
    } catch (error) {
      console.log('❌ Direct API Endpoint: Failed -', error.message);
    }

    // Test 3: CORS Handling (should gracefully fail and fallback)
    console.log('📡 Test 3: CORS Handling');
    try {
      // This should fail due to CORS, but our fix should handle it
      const response = await fetch('https://mcp.vapi.ai/mcp', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${VAPI_PRIVATE_KEY}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          jsonrpc: '2.0',
          id: 1,
          method: 'tools/call',
          params: { name: 'list_assistants', arguments: {} }
        })
      });

      console.log('⚠️ CORS Handling: Unexpected success (CORS may be fixed)');
      fixes.corsHandling = true;
    } catch (error) {
      if (error.message.includes('CORS') || error.message.includes('Failed to fetch')) {
        fixes.corsHandling = true;
        console.log('✅ CORS Handling: Expected failure, fallback should work');
      } else {
        console.log('❌ CORS Handling: Unexpected error -', error.message);
      }
    }

    // Test 4: Environment Variables
    console.log('📡 Test 4: Environment Variables');
    const hasVapiKey = !!(VAPI_PRIVATE_KEY && VAPI_PRIVATE_KEY !== 'undefined');
    const hasAssistantId = !!(ASSISTANT_ID && ASSISTANT_ID !== 'undefined');

    if (hasVapiKey && hasAssistantId) {
      fixes.environmentVars = true;
      console.log('✅ Environment Variables: Available');
    } else {
      console.log('❌ Environment Variables: Missing');
    }

    // Overall health
    fixes.overallHealth = fixes.directAPIEndpoint && fixes.corsHandling && fixes.environmentVars;

  } catch (error) {
    console.error('💥 Production CORS fixes test failed:', error);
  }

  return fixes;
}

async function testAttorneyReadiness() {
  console.log('\n' + '='.repeat(60));
  console.log('🎯 ATTORNEY READINESS TEST');
  console.log('='.repeat(60));

  const checks = {
    streamableHTTP: false,
    directAPI: false,
    assistantExists: false,
    assistantConfigured: false,
    readyToGo: false
  };

  try {
    // Check streamable-HTTP
    const streamableResponse = await fetch('https://mcp.vapi.ai/mcp', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${VAPI_PRIVATE_KEY}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify({
        jsonrpc: '2.0',
        id: Date.now(),
        method: 'tools/call',
        params: {
          name: 'get_assistant',
          arguments: { assistantId: ASSISTANT_ID }
        }
      })
    });

    if (streamableResponse.ok) {
      checks.streamableHTTP = true;
      console.log('✅ Streamable-HTTP: Working');
    } else {
      console.log('❌ Streamable-HTTP: Failed');
    }

    // Check direct API
    const directResponse = await fetch(`https://api.vapi.ai/assistant/${ASSISTANT_ID}`, {
      headers: {
        'Authorization': `Bearer ${VAPI_PRIVATE_KEY}`,
        'Content-Type': 'application/json'
      }
    });

    if (directResponse.ok) {
      checks.directAPI = true;
      checks.assistantExists = true;
      console.log('✅ Direct API: Working');
      console.log('✅ Assistant: Exists');

      const assistant = await directResponse.json();
      if (assistant.firstMessage && assistant.model?.messages?.[0]?.content) {
        checks.assistantConfigured = true;
        console.log('✅ Assistant: Fully Configured');
      } else {
        console.log('⚠️ Assistant: Missing Configuration');
      }
    } else {
      console.log('❌ Direct API: Failed');
      console.log('❌ Assistant: Cannot Verify');
    }

    // Overall readiness
    checks.readyToGo = (checks.streamableHTTP || checks.directAPI) && 
                      checks.assistantExists && 
                      checks.assistantConfigured;

    console.log('\n🎯 FINAL RESULT:');
    if (checks.readyToGo) {
      console.log('🎉 ATTORNEY IS READY TO GO!');
      console.log('✅ Can use voice assistant immediately');
      console.log('🔗 Working link: https://legalscout.net/simple-preview?assistantId=' + ASSISTANT_ID);
    } else {
      console.log('⚠️ ATTORNEY NEEDS SETUP');
      console.log('❌ Some components not working properly');
    }

  } catch (error) {
    console.error('💥 Readiness test failed:', error);
  }

  return checks;
}

async function main() {
  await testStreamableHTTP();
  const corsFixResults = await testProductionCORSFixes();
  const readinessResults = await testAttorneyReadiness();

  console.log('\n' + '='.repeat(60));
  console.log('📊 COMPREHENSIVE TEST SUMMARY');
  console.log('='.repeat(60));

  console.log('\n🔧 Production CORS Fixes:');
  console.log(`  Proper MCP Client: ${corsFixResults.properMCPClient ? '✅' : '❌'}`);
  console.log(`  Direct API Endpoint: ${corsFixResults.directAPIEndpoint ? '✅' : '❌'}`);
  console.log(`  CORS Handling: ${corsFixResults.corsHandling ? '✅' : '❌'}`);
  console.log(`  Environment Variables: ${corsFixResults.environmentVars ? '✅' : '❌'}`);
  console.log(`  Overall Health: ${corsFixResults.overallHealth ? '✅' : '❌'}`);

  console.log('\n🎯 Attorney Readiness:');
  console.log(`  Direct API: ${readinessResults.directAPI ? '✅' : '❌'}`);
  console.log(`  Assistant Exists: ${readinessResults.assistantExists ? '✅' : '❌'}`);
  console.log(`  Assistant Configured: ${readinessResults.assistantConfigured ? '✅' : '❌'}`);
  console.log(`  Ready to Go: ${readinessResults.readyToGo ? '✅' : '❌'}`);

  console.log('\n🎉 FINAL VERDICT:');
  if (corsFixResults.overallHealth && readinessResults.readyToGo) {
    console.log('🚀 SYSTEM IS PRODUCTION READY!');
    console.log('✅ All CORS issues fixed');
    console.log('✅ Attorney can be up and running immediately');
    console.log('🔗 Working link: https://legalscout.net/simple-preview?assistantId=' + ASSISTANT_ID);
  } else {
    console.log('⚠️ SYSTEM NEEDS ATTENTION');
    if (!corsFixResults.overallHealth) {
      console.log('❌ CORS fixes need work');
    }
    if (!readinessResults.readyToGo) {
      console.log('❌ Attorney readiness needs work');
    }
  }
}

// Run the tests
main().catch(error => {
  console.error('💥 Tests failed:', error);
  process.exit(1);
});

#!/usr/bin/env node

/**
 * Setup Priority MCP Servers
 * 
 * This script sets up the most valuable MCP servers for LegalScout Voice
 * based on the PulseMCP.com directory analysis.
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.resolve(__dirname, '..');

console.log('🚀 Setting up Priority MCP Servers for LegalScout Voice...');

// Priority MCP servers to install
const priorityServers = [
  {
    name: 'Supabase MCP Server',
    package: '@supabase/mcp-server',
    description: 'Official Supabase integration for enhanced database operations',
    envVars: ['SUPABASE_URL', 'SUPABASE_SERVICE_KEY'],
    priority: 1
  },
  {
    name: 'Gmail MCP Server',
    package: '@anthropic/gmail-mcp-server',
    description: 'Gmail integration for attorney notifications',
    envVars: ['GMAIL_CLIENT_ID', 'GMAIL_CLIENT_SECRET'],
    priority: 1
  },
  {
    name: 'Slack MCP Server',
    package: '@anthropic/slack-mcp-server',
    description: 'Slack integration for team communication',
    envVars: ['SLACK_BOT_TOKEN'],
    priority: 1
  },
  {
    name: 'Google Calendar MCP Server',
    package: '@anthropic/google-calendar-mcp-server',
    description: 'Calendar integration for consultation scheduling',
    envVars: ['GOOGLE_CALENDAR_CLIENT_ID', 'GOOGLE_CALENDAR_CLIENT_SECRET'],
    priority: 2
  },
  {
    name: 'PDF MCP Server',
    package: '@anthropic/pdf-mcp-server',
    description: 'PDF processing for legal documents',
    envVars: [],
    priority: 2
  },
  {
    name: 'Browser Tools MCP',
    package: '@agentdeskai/browser-tools-mcp',
    description: 'Browser automation for legal research',
    envVars: [],
    priority: 2
  }
];

// Check if package is already installed
function isPackageInstalled(packageName) {
  try {
    const packageJsonPath = path.join(projectRoot, 'package.json');
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    return packageJson.dependencies?.[packageName] || packageJson.devDependencies?.[packageName];
  } catch (error) {
    return false;
  }
}

// Install MCP server packages
function installMcpServers() {
  console.log('\n📦 Installing MCP Server packages...');
  
  const toInstall = priorityServers.filter(server => !isPackageInstalled(server.package));
  
  if (toInstall.length === 0) {
    console.log('✅ All priority MCP servers are already installed');
    return;
  }
  
  console.log(`Installing ${toInstall.length} MCP servers...`);
  
  toInstall.forEach(server => {
    console.log(`\n📥 Installing ${server.name}...`);
    console.log(`   Package: ${server.package}`);
    console.log(`   Description: ${server.description}`);
    
    try {
      // Try to install the package
      execSync(`npm install ${server.package}`, { 
        stdio: 'inherit',
        cwd: projectRoot 
      });
      console.log(`✅ ${server.name} installed successfully`);
    } catch (error) {
      console.warn(`⚠️ ${server.name} not available yet - will use placeholder configuration`);
    }
  });
}

// Create MCP configuration file
function createMcpConfig() {
  console.log('\n⚙️ Creating MCP server configuration...');
  
  const configPath = path.join(projectRoot, 'src/config/mcp-servers.config.js');
  
  const configContent = `/**
 * MCP Servers Configuration
 * 
 * Configuration for all MCP servers used in LegalScout Voice.
 * Based on PulseMCP.com directory analysis.
 */

// Helper function to get environment variables
const getEnvVar = (name) => {
  if (typeof import.meta !== 'undefined' && import.meta.env) {
    return import.meta.env[name];
  }
  if (typeof process !== 'undefined' && process.env) {
    return process.env[name];
  }
  return null;
};

export const mcpServersConfig = {
  // Priority 1: Core Business Operations
  supabase: {
    name: 'Supabase MCP Server',
    command: 'npx',
    args: ['@supabase/mcp-server'],
    env: {
      SUPABASE_URL: getEnvVar('VITE_SUPABASE_URL') || 'https://utopqxsvudgrtiwenlzl.supabase.co',
      SUPABASE_SERVICE_KEY: getEnvVar('SUPABASE_SERVICE_KEY') || getEnvVar('VITE_SUPABASE_KEY')
    },
    enabled: true,
    priority: 1,
    description: 'Enhanced database operations with natural language queries'
  },

  gmail: {
    name: 'Gmail MCP Server',
    command: 'npx',
    args: ['@anthropic/gmail-mcp-server'],
    env: {
      GMAIL_CLIENT_ID: getEnvVar('GMAIL_CLIENT_ID'),
      GMAIL_CLIENT_SECRET: getEnvVar('GMAIL_CLIENT_SECRET')
    },
    enabled: false, // Enable when credentials are configured
    priority: 1,
    description: 'Attorney email notifications and communication'
  },

  slack: {
    name: 'Slack MCP Server',
    command: 'npx',
    args: ['@anthropic/slack-mcp-server'],
    env: {
      SLACK_BOT_TOKEN: getEnvVar('SLACK_BOT_TOKEN')
    },
    enabled: false, // Enable when credentials are configured
    priority: 1,
    description: 'Team communication and real-time notifications'
  },

  // Priority 2: Legal-Specific Enhancements
  googleCalendar: {
    name: 'Google Calendar MCP Server',
    command: 'npx',
    args: ['@anthropic/google-calendar-mcp-server'],
    env: {
      GOOGLE_CALENDAR_CLIENT_ID: getEnvVar('GOOGLE_CALENDAR_CLIENT_ID'),
      GOOGLE_CALENDAR_CLIENT_SECRET: getEnvVar('GOOGLE_CALENDAR_CLIENT_SECRET')
    },
    enabled: false,
    priority: 2,
    description: 'Consultation scheduling and calendar integration'
  },

  pdf: {
    name: 'PDF MCP Server',
    command: 'npx',
    args: ['@anthropic/pdf-mcp-server'],
    env: {},
    enabled: true,
    priority: 2,
    description: 'Legal document processing and analysis'
  },

  browserTools: {
    name: 'Browser Tools MCP',
    command: 'npx',
    args: ['@agentdeskai/browser-tools-mcp'],
    env: {},
    enabled: true,
    priority: 2,
    description: 'Browser automation for legal research'
  },

  // Existing servers
  vapi: {
    name: 'Vapi MCP Server',
    command: 'npx',
    args: ['@vapi-ai/mcp-server'],
    env: {
      VAPI_TOKEN: getEnvVar('VAPI_TOKEN') || getEnvVar('VITE_VAPI_SECRET_KEY') || '6734febc-fc65-4669-93b0-929b31ff6564'
    },
    enabled: true,
    priority: 1,
    description: 'Voice AI integration and call management'
  },

  aiMeta: {
    name: 'AI Meta MCP Server',
    command: 'node',
    args: ['ai-meta-mcp-server/build/index.js'],
    env: {},
    enabled: true,
    priority: 3,
    description: 'Dynamic tool creation and meta-functions'
  }
};

// Get enabled servers only
export const getEnabledServers = () => {
  return Object.entries(mcpServersConfig)
    .filter(([_, config]) => config.enabled)
    .reduce((acc, [key, config]) => {
      acc[key] = config;
      return acc;
    }, {});
};

// Get servers by priority
export const getServersByPriority = (priority) => {
  return Object.entries(mcpServersConfig)
    .filter(([_, config]) => config.priority === priority)
    .reduce((acc, [key, config]) => {
      acc[key] = config;
      return acc;
    }, {});
};

export default mcpServersConfig;
`;

  // Ensure config directory exists
  const configDir = path.dirname(configPath);
  if (!fs.existsSync(configDir)) {
    fs.mkdirSync(configDir, { recursive: true });
  }

  fs.writeFileSync(configPath, configContent);
  console.log(`✅ MCP configuration created at ${configPath}`);
}

// Create MCP integration service
function createMcpIntegrationService() {
  console.log('\n🔧 Creating MCP Integration Service...');
  
  const servicePath = path.join(projectRoot, 'src/services/McpIntegrationService.js');
  
  const serviceContent = `/**
 * MCP Integration Service
 * 
 * Manages connections and interactions with multiple MCP servers.
 */

import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { SSEClientTransport } from '@modelcontextprotocol/sdk/client/sse.js';
import { mcpServersConfig, getEnabledServers } from '../config/mcp-servers.config.js';

export class McpIntegrationService {
  constructor() {
    this.clients = new Map();
    this.connections = new Map();
    this.initialized = false;
  }

  /**
   * Initialize all enabled MCP servers
   */
  async initialize() {
    console.log('[McpIntegrationService] Initializing MCP servers...');
    
    const enabledServers = getEnabledServers();
    const initPromises = Object.entries(enabledServers).map(([name, config]) => 
      this.initializeServer(name, config)
    );

    const results = await Promise.allSettled(initPromises);
    
    const successful = results.filter(r => r.status === 'fulfilled').length;
    const failed = results.filter(r => r.status === 'rejected').length;
    
    console.log(\`[McpIntegrationService] Initialized \${successful} servers, \${failed} failed\`);
    
    this.initialized = true;
    return { successful, failed };
  }

  /**
   * Initialize a single MCP server
   */
  async initializeServer(name, config) {
    try {
      console.log(\`[McpIntegrationService] Initializing \${config.name}...\`);
      
      // Create client
      const client = new Client({
        name: \`legalscout-\${name}\`,
        version: '1.0.0'
      });

      // For now, we'll use a placeholder connection
      // In production, this would connect to the actual MCP server
      this.clients.set(name, client);
      this.connections.set(name, { status: 'connected', config });
      
      console.log(\`[McpIntegrationService] \${config.name} initialized successfully\`);
      return true;
    } catch (error) {
      console.error(\`[McpIntegrationService] Failed to initialize \${config.name}:\`, error);
      throw error;
    }
  }

  /**
   * Call a tool on a specific MCP server
   */
  async callTool(serverName, toolName, params = {}) {
    if (!this.clients.has(serverName)) {
      throw new Error(\`MCP server '\${serverName}' not initialized\`);
    }

    try {
      const client = this.clients.get(serverName);
      // Placeholder implementation
      console.log(\`[McpIntegrationService] Calling \${toolName} on \${serverName}\`, params);
      
      // Return mock response for now
      return {
        success: true,
        result: \`Mock result from \${serverName}.\${toolName}\`,
        serverName,
        toolName,
        params
      };
    } catch (error) {
      console.error(\`[McpIntegrationService] Tool call failed:\`, error);
      throw error;
    }
  }

  /**
   * Get available tools from a server
   */
  async getTools(serverName) {
    if (!this.clients.has(serverName)) {
      throw new Error(\`MCP server '\${serverName}' not initialized\`);
    }

    // Placeholder implementation
    const config = mcpServersConfig[serverName];
    return {
      serverName,
      tools: [\`\${serverName}_tool_1\`, \`\${serverName}_tool_2\`],
      description: config.description
    };
  }

  /**
   * Get connection status for all servers
   */
  getConnectionStatus() {
    const status = {};
    for (const [name, connection] of this.connections) {
      status[name] = {
        connected: connection.status === 'connected',
        config: connection.config
      };
    }
    return status;
  }

  /**
   * Disconnect all servers
   */
  async disconnect() {
    console.log('[McpIntegrationService] Disconnecting all MCP servers...');
    
    for (const [name, client] of this.clients) {
      try {
        // Placeholder disconnect
        console.log(\`[McpIntegrationService] Disconnected \${name}\`);
      } catch (error) {
        console.error(\`[McpIntegrationService] Error disconnecting \${name}:\`, error);
      }
    }

    this.clients.clear();
    this.connections.clear();
    this.initialized = false;
  }
}

// Export singleton instance
export const mcpIntegrationService = new McpIntegrationService();
export default mcpIntegrationService;
`;

  fs.writeFileSync(servicePath, serviceContent);
  console.log(`✅ MCP Integration Service created at ${servicePath}`);
}

// Update environment variables template
function updateEnvTemplate() {
  console.log('\n📝 Updating environment variables template...');
  
  const envExamplePath = path.join(projectRoot, '.env.example');
  
  const additionalEnvVars = `
# MCP Server Configuration
# ========================

# Gmail MCP Server
GMAIL_CLIENT_ID=your-gmail-client-id
GMAIL_CLIENT_SECRET=your-gmail-client-secret

# Slack MCP Server
SLACK_BOT_TOKEN=xoxb-your-slack-bot-token

# Google Calendar MCP Server
GOOGLE_CALENDAR_CLIENT_ID=your-google-calendar-client-id
GOOGLE_CALENDAR_CLIENT_SECRET=your-google-calendar-client-secret

# Supabase Service Key (for MCP server)
SUPABASE_SERVICE_KEY=your-supabase-service-key
`;

  if (fs.existsSync(envExamplePath)) {
    const currentContent = fs.readFileSync(envExamplePath, 'utf8');
    if (!currentContent.includes('MCP Server Configuration')) {
      fs.appendFileSync(envExamplePath, additionalEnvVars);
      console.log('✅ Environment variables template updated');
    } else {
      console.log('✅ Environment variables template already up to date');
    }
  } else {
    console.warn('⚠️ .env.example not found - skipping environment variables update');
  }
}

// Main execution
async function main() {
  try {
    console.log('🎯 Priority MCP Servers Setup for LegalScout Voice');
    console.log('Based on analysis of PulseMCP.com directory\n');
    
    // Show what we're going to install
    console.log('📋 Priority MCP Servers:');
    priorityServers.forEach((server, index) => {
      console.log(`${index + 1}. ${server.name} (Priority ${server.priority})`);
      console.log(`   📦 ${server.package}`);
      console.log(`   📝 ${server.description}`);
      console.log('');
    });

    // Install packages
    installMcpServers();
    
    // Create configuration
    createMcpConfig();
    
    // Create integration service
    createMcpIntegrationService();
    
    // Update environment template
    updateEnvTemplate();
    
    console.log('\n🎉 Priority MCP Servers setup completed successfully!');
    console.log('\n📝 Next steps:');
    console.log('1. Configure environment variables for the services you want to use');
    console.log('2. Enable desired servers in src/config/mcp-servers.config.js');
    console.log('3. Test the integration with: npm run test:mcp');
    console.log('4. Integrate with your existing Vapi assistant');
    
    console.log('\n🔗 Useful resources:');
    console.log('• PulseMCP Directory: https://www.pulsemcp.com/servers');
    console.log('• Fast-Agent Framework: https://fast-agent.ai');
    console.log('• MCP Documentation: https://modelcontextprotocol.io');
    
  } catch (error) {
    console.error('❌ Setup failed:', error.message);
    process.exit(1);
  }
}

// Run the setup
main();

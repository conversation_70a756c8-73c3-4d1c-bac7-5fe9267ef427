/**
 * Simple Call Sync Diagnostic
 * 
 * A simplified diagnostic to identify why call records aren't syncing.
 */

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_KEY;

console.log('🔍 SIMPLE CALL SYNC DIAGNOSTIC');
console.log('='.repeat(50));

async function main() {
  // 1. Check environment
  console.log('\n🔧 Environment Check:');
  console.log(`Supabase URL: ${supabaseUrl ? '✅ Set' : '❌ Missing'}`);
  console.log(`Supabase Key: ${supabaseKey ? '✅ Set' : '❌ Missing'}`);
  
  if (!supabaseUrl || !supabaseKey) {
    console.log('❌ Missing Supabase configuration');
    return;
  }

  // 2. Test database connection
  console.log('\n🗄️  Database Connection:');
  try {
    const supabase = createClient(supabaseUrl, supabaseKey);
    
    // Test basic connection
    const { data: testData, error: testError } = await supabase
      .from('attorneys')
      .select('id')
      .limit(1);
    
    if (testError) {
      console.log('❌ Database connection failed:', testError.message);
      return;
    }
    
    console.log('✅ Database connection successful');
    
    // 3. Check table structures
    console.log('\n📊 Table Analysis:');
    
    // Check call_records
    const { data: callRecords, error: callError } = await supabase
      .from('call_records')
      .select('*')
      .limit(5);
    
    if (callError) {
      console.log('❌ call_records table error:', callError.message);
    } else {
      console.log(`✅ call_records table: ${callRecords.length} records found`);
      if (callRecords.length > 0) {
        console.log('   Latest record:', callRecords[0].created_at);
      }
    }
    
    // Check consultations
    const { data: consultations, error: consultError } = await supabase
      .from('consultations')
      .select('*')
      .limit(5);
    
    if (consultError) {
      console.log('❌ consultations table error:', consultError.message);
    } else {
      console.log(`✅ consultations table: ${consultations.length} records found`);
      if (consultations.length > 0) {
        console.log('   Latest record:', consultations[0].created_at);
      }
    }
    
    // 4. Check attorney configuration
    console.log('\n👨‍💼 Attorney Configuration:');
    
    const { data: attorneys, error: attorneyError } = await supabase
      .from('attorneys')
      .select('id, email, assistant_id, subdomain')
      .eq('email', '<EMAIL>');
    
    if (attorneyError) {
      console.log('❌ Attorney lookup failed:', attorneyError.message);
    } else if (attorneys.length === 0) {
      console.log('❌ No attorney found with email: <EMAIL>');
    } else {
      const attorney = attorneys[0];
      console.log('✅ Attorney found:');
      console.log(`   ID: ${attorney.id}`);
      console.log(`   Email: ${attorney.email}`);
      console.log(`   Assistant ID: ${attorney.assistant_id}`);
      console.log(`   Subdomain: ${attorney.subdomain}`);
      
      // Check for call records for this attorney
      const { data: attorneyCallRecords, error: attorneyCallError } = await supabase
        .from('call_records')
        .select('*')
        .eq('attorney_id', attorney.id);
      
      if (attorneyCallError) {
        console.log('❌ Attorney call records error:', attorneyCallError.message);
      } else {
        console.log(`   Call records: ${attorneyCallRecords.length}`);
      }
      
      // Check for consultations for this attorney
      const { data: attorneyConsultations, error: attorneyConsultError } = await supabase
        .from('consultations')
        .select('*')
        .eq('attorney_id', attorney.id);
      
      if (attorneyConsultError) {
        console.log('❌ Attorney consultations error:', attorneyConsultError.message);
      } else {
        console.log(`   Consultations: ${attorneyConsultations.length}`);
      }
    }
    
    // 5. Webhook endpoint check
    console.log('\n🔗 Webhook Configuration:');
    
    const webhookUrl = process.env.VERCEL_URL 
      ? `https://${process.env.VERCEL_URL}/api/webhook/vapi-call`
      : 'http://localhost:5175/api/webhook/vapi-call';
    
    console.log(`Expected webhook URL: ${webhookUrl}`);
    
    try {
      const response = await fetch(webhookUrl, { method: 'GET' });
      if (response.status === 405) {
        console.log('✅ Webhook endpoint accessible (405 for GET expected)');
      } else {
        console.log(`⚠️  Webhook returned status: ${response.status}`);
      }
    } catch (error) {
      console.log('❌ Webhook endpoint not accessible:', error.message);
    }
    
    // 6. Summary and recommendations
    console.log('\n📋 DIAGNOSTIC SUMMARY');
    console.log('='.repeat(50));
    
    const callRecordCount = callRecords?.length || 0;
    const consultationCount = consultations?.length || 0;
    
    if (callRecordCount === 0 && consultationCount > 0) {
      console.log('🔍 ROOT CAUSE IDENTIFIED:');
      console.log('   - Consultations exist but no call records');
      console.log('   - This indicates webhook is NOT receiving call events from Vapi');
      console.log('   - Consultations are being created by some other mechanism');
      
      console.log('\n🔧 REQUIRED FIXES:');
      console.log('1. Configure webhook in Vapi dashboard:');
      console.log(`   URL: ${webhookUrl}`);
      console.log('   Events: call.started, call.ended, call.completed');
      console.log('2. Verify assistant_id mapping in attorneys table');
      console.log('3. Test webhook with manual POST request');
      
    } else if (callRecordCount === 0 && consultationCount === 0) {
      console.log('🔍 ROOT CAUSE IDENTIFIED:');
      console.log('   - No call records AND no consultations');
      console.log('   - This indicates no calls are being processed at all');
      
      console.log('\n🔧 REQUIRED FIXES:');
      console.log('1. Verify Vapi integration is working');
      console.log('2. Check if calls are actually being made');
      console.log('3. Configure webhook in Vapi dashboard');
      
    } else if (callRecordCount > 0) {
      console.log('✅ Call records found - webhook appears to be working');
      console.log('   Check Briefs page data loading logic');
    }
    
  } catch (error) {
    console.log('❌ Diagnostic failed:', error.message);
  }
}

main();

/**
 * Clear Invalid Attorney Data
 * 
 * This script clears invalid attorney data from localStorage to fix authentication issues.
 */

(function() {
  console.log('[ClearInvalidAttorneyData] Starting cleanup...');
  
  try {
    // Get current attorney data
    const storedAttorney = localStorage.getItem('attorney');
    const storedAttorneyId = localStorage.getItem('attorney_id');
    
    console.log('[ClearInvalidAttorneyData] Current stored attorney:', storedAttorney);
    console.log('[ClearInvalidAttorneyData] Current stored attorney ID:', storedAttorneyId);
    
    // Check if we have invalid data
    let hasInvalidData = false;
    
    if (storedAttorney) {
      try {
        const attorneyData = JSON.parse(storedAttorney);
        if (attorneyData.id && typeof attorneyData.id === 'string' && attorneyData.id.startsWith('dev-') && !isValidUUID(attorneyData.id)) {
          console.log('[ClearInvalidAttorneyData] Found invalid attorney ID:', attorneyData.id);
          hasInvalidData = true;
        }
      } catch (e) {
        console.log('[ClearInvalidAttorneyData] Invalid attorney JSON data');
        hasInvalidData = true;
      }
    }
    
    if (storedAttorneyId && typeof storedAttorneyId === 'string' && storedAttorneyId.startsWith('dev-') && !isValidUUID(storedAttorneyId)) {
      console.log('[ClearInvalidAttorneyData] Found invalid attorney ID in storage:', storedAttorneyId);
      hasInvalidData = true;
    }
    
    if (hasInvalidData) {
      console.log('[ClearInvalidAttorneyData] Clearing invalid attorney data...');
      
      // Clear all attorney-related localStorage items
      const keysToRemove = [
        'attorney',
        'attorney_id',
        'currentAttorneyId',
        'attorney_version'
      ];
      
      keysToRemove.forEach(key => {
        if (localStorage.getItem(key)) {
          localStorage.removeItem(key);
          console.log('[ClearInvalidAttorneyData] Removed:', key);
        }
      });
      
      console.log('[ClearInvalidAttorneyData] Cleanup completed');
      
      // Reload the page to start fresh
      setTimeout(() => {
        console.log('[ClearInvalidAttorneyData] Reloading page to start fresh...');
        window.location.reload();
      }, 1000);
    } else {
      console.log('[ClearInvalidAttorneyData] No invalid data found, no cleanup needed');
    }
    
  } catch (error) {
    console.error('[ClearInvalidAttorneyData] Error during cleanup:', error);
  }
  
  // Helper function to validate UUID
  function isValidUUID(id) {
    if (!id) return false;
    
    // Allow proper development IDs (but not the invalid timestamp-based ones)
    if (typeof id === 'string' && id.startsWith('dev-')) {
      // Only allow specific development IDs, not timestamp-based ones
      const validDevIds = ['dev-attorney', 'dev-attorney-id', 'devmode'];
      return validDevIds.includes(id);
    }
    
    // Regular UUID validation
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(id);
  }
})();

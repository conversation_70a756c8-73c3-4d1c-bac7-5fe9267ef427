#!/usr/bin/env node
/**
 * Test Server
 *
 * This script starts a simple Express server to serve the API and test pages.
 *
 * Usage:
 *   node scripts/test-server.js
 */

import express from 'express';
import path from 'path';
import { fileURLToPath } from 'url';
import { createFunction, callFunction, listFunctions } from '../src/api/ai-meta-mcp.js';
import {
  syncAttorneyProfileHandler,
  manageAuthStateHandler,
  validateConfigurationHandler,
  checkPreviewConsistencyHandler
} from '../src/api/sync-tools.js';

// Get the directory name of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Create Express app
const app = express();
const PORT = 3001;

// Middleware
app.use(express.json());
app.use(express.static(path.join(__dirname, '..', 'public')));

// AI Meta MCP API routes
app.post('/api/ai-meta-mcp/create-function', createFunction);
app.post('/api/ai-meta-mcp/call-function', callFunction);
app.get('/api/ai-meta-mcp/list-functions', listFunctions);

// Sync Tools API routes
app.post('/api/sync-tools/sync-attorney-profile', syncAttorneyProfileHandler);
app.post('/api/sync-tools/manage-auth-state', manageAuthStateHandler);
app.post('/api/sync-tools/validate-configuration', validateConfigurationHandler);
app.post('/api/sync-tools/check-preview-consistency', checkPreviewConsistencyHandler);

// Start server
app.listen(PORT, () => {
  console.log(`Server running at http://localhost:${PORT}`);
  console.log(`AI Meta MCP Test page available at http://localhost:${PORT}/test-ai-meta-mcp.html`);
  console.log(`Sync Tools Test page available at http://localhost:${PORT}/sync-tools-test.html`);
});

/**
 * Fix Attorney State Manager
 * 
 * This script provides a more robust fix for the attorney state manager
 * by properly bridging between the standalone attorney manager and the
 * React-based attorney state manager.
 */

(function() {
  console.log('[FixAttorneyStateManager] Starting fix...');
  
  // Wait for the attorney state manager to be initialized
  const checkInterval = setInterval(() => {
    // Check if we have access to the attorney state manager
    const modules = Object.keys(window).filter(key => key.startsWith('__VITE_PRELOAD__'));
    let found = false;
    
    for (const moduleKey of modules) {
      try {
        const module = window[moduleKey];
        
        // Look for the attorney state manager in the module exports
        if (module && typeof module === 'object') {
          const keys = Object.keys(module);
          
          for (const key of keys) {
            const exportedValue = module[key];
            
            // Check if this is the attorney state manager
            if (exportedValue && 
                typeof exportedValue === 'object' && 
                exportedValue.syncWithVapi && 
                exportedValue.loadAttorney) {
              
              console.log('[FixAttorneyStateManager] Found attorney state manager, applying fix...');
              found = true;
              
              // Create a proxy for the attorney state manager
              const originalManager = exportedValue;
              
              // Override the initialize method
              const originalInitialize = originalManager.initialize;
              originalManager.initialize = async function() {
                console.log('[FixAttorneyStateManager] Initializing attorney state manager with fix...');
                
                try {
                  // Check if we have a standalone attorney manager
                  if (window.standaloneAttorneyManager && window.standaloneAttorneyManager.attorney) {
                    console.log('[FixAttorneyStateManager] Using standalone attorney manager');
                    
                    // Use the attorney from the standalone attorney manager
                    this.attorney = window.standaloneAttorneyManager.attorney;
                    
                    // Set up a subscription to keep them in sync
                    window.standaloneAttorneyManager.subscribe((attorney) => {
                      this.attorney = attorney;
                      this.notifySubscribers();
                    });
                    
                    // Mark as initialized
                    this.initialized = true;
                    console.log('[FixAttorneyStateManager] Initialization complete with standalone attorney');
                    
                    // Notify subscribers
                    this.notifySubscribers();
                    
                    return this.attorney;
                  }
                  
                  // Fall back to original initialization
                  console.log('[FixAttorneyStateManager] Falling back to original initialization');
                  return originalInitialize.apply(this);
                } catch (error) {
                  console.error('[FixAttorneyStateManager] Error in initialize:', error);
                  
                  // Try to recover by creating a default attorney
                  if (!this.attorney && window.standaloneAttorneyManager && window.standaloneAttorneyManager.attorney) {
                    this.attorney = window.standaloneAttorneyManager.attorney;
                    this.initialized = true;
                    this.notifySubscribers();
                    return this.attorney;
                  }
                  
                  return null;
                }
              };
              
              // Override the loadAttorney method
              const originalLoadAttorney = originalManager.loadAttorney;
              originalManager.loadAttorney = async function() {
                console.log('[FixAttorneyStateManager] Loading attorney with fix...');
                
                try {
                  // Check if we have a standalone attorney manager
                  if (window.standaloneAttorneyManager && window.standaloneAttorneyManager.attorney) {
                    console.log('[FixAttorneyStateManager] Using attorney from standalone attorney manager');
                    this.attorney = window.standaloneAttorneyManager.attorney;
                    this.notifySubscribers();
                    return this.attorney;
                  }
                  
                  // Fall back to original loadAttorney
                  console.log('[FixAttorneyStateManager] Falling back to original loadAttorney');
                  return originalLoadAttorney.apply(this);
                } catch (error) {
                  console.error('[FixAttorneyStateManager] Error in loadAttorney:', error);
                  
                  // Try to recover by creating a default attorney
                  if (!this.attorney && window.standaloneAttorneyManager && window.standaloneAttorneyManager.attorney) {
                    this.attorney = window.standaloneAttorneyManager.attorney;
                    this.notifySubscribers();
                    return this.attorney;
                  }
                  
                  return null;
                }
              };
              
              // Override the syncWithVapi method
              const originalSyncWithVapi = originalManager.syncWithVapi;
              originalManager.syncWithVapi = async function(options) {
                console.log('[FixAttorneyStateManager] Syncing with Vapi with fix...');
                
                try {
                  // Check if we have a standalone attorney manager
                  if (window.standaloneAttorneyManager && window.standaloneAttorneyManager.attorney) {
                    console.log('[FixAttorneyStateManager] Using standalone attorney manager for Vapi sync');
                    
                    // Use the standalone attorney manager to sync with Vapi
                    const result = await window.standaloneAttorneyManager.syncWithVapi(this.attorney);
                    
                    // Update our attorney with any changes
                    this.attorney = window.standaloneAttorneyManager.attorney;
                    this.notifySubscribers();
                    
                    return result;
                  }
                  
                  // Fall back to original syncWithVapi
                  console.log('[FixAttorneyStateManager] Falling back to original syncWithVapi');
                  return originalSyncWithVapi.apply(this, [options]);
                } catch (error) {
                  console.error('[FixAttorneyStateManager] Error in syncWithVapi:', error);
                  return { action: 'error', error: error.message };
                }
              };
              
              // Override the saveToLocalStorage method
              const originalSaveToLocalStorage = originalManager.saveToLocalStorage;
              originalManager.saveToLocalStorage = function(attorney) {
                console.log('[FixAttorneyStateManager] Saving to local storage with fix...');
                
                try {
                  // Validate the attorney
                  if (!attorney || !this.isValidUUID(attorney.id)) {
                    console.warn('[FixAttorneyStateManager] Cannot save invalid attorney to local storage');
                    
                    // Try to recover by using the standalone attorney manager
                    if (window.standaloneAttorneyManager && window.standaloneAttorneyManager.attorney) {
                      console.log('[FixAttorneyStateManager] Using attorney from standalone attorney manager');
                      attorney = window.standaloneAttorneyManager.attorney;
                    } else {
                      return;
                    }
                  }
                  
                  // Save using the standalone attorney manager if available
                  if (window.standaloneAttorneyManager) {
                    console.log('[FixAttorneyStateManager] Using standalone attorney manager for saving');
                    window.standaloneAttorneyManager.saveToLocalStorage(attorney);
                    return;
                  }
                  
                  // Fall back to original saveToLocalStorage
                  console.log('[FixAttorneyStateManager] Falling back to original saveToLocalStorage');
                  return originalSaveToLocalStorage.apply(this, [attorney]);
                } catch (error) {
                  console.error('[FixAttorneyStateManager] Error in saveToLocalStorage:', error);
                }
              };
              
              // Try to initialize immediately if not already initialized
              if (!originalManager.initialized) {
                console.log('[FixAttorneyStateManager] Initializing attorney state manager...');
                originalManager.initialize().catch(error => {
                  console.error('[FixAttorneyStateManager] Error initializing attorney state manager:', error);
                });
              }
              
              console.log('[FixAttorneyStateManager] Fix applied to attorney state manager');
              
              // Break out of the loops
              break;
            }
          }
          
          if (found) break;
        }
      } catch (error) {
        console.error('[FixAttorneyStateManager] Error checking module:', error);
      }
    }
    
    // If we found the attorney state manager, clear the interval
    if (found) {
      clearInterval(checkInterval);
      console.log('[FixAttorneyStateManager] Fix completed');
    }
  }, 100);
  
  // Clear interval after 10 seconds if attorney state manager is not found
  setTimeout(() => {
    clearInterval(checkInterval);
    console.warn('[FixAttorneyStateManager] Timed out waiting for attorney state manager');
    
    // Create a fallback if needed
    if (!window.attorneyStateManager) {
      console.log('[FixAttorneyStateManager] Creating fallback attorney state manager');
      
      // Create a minimal attorney state manager that uses the standalone attorney manager
      window.attorneyStateManager = {
        attorney: window.standaloneAttorneyManager ? window.standaloneAttorneyManager.attorney : null,
        isLoading: false,
        isSaving: false,
        isSyncing: false,
        lastError: null,
        subscribers: [],
        initialized: true,
        
        initialize: async function() {
          console.log('[FixAttorneyStateManager] Initializing fallback attorney state manager');
          
          if (window.standaloneAttorneyManager) {
            this.attorney = window.standaloneAttorneyManager.attorney;
            
            // Set up a subscription to keep them in sync
            window.standaloneAttorneyManager.subscribe((attorney) => {
              this.attorney = attorney;
              this.notifySubscribers();
            });
          }
          
          return this.attorney;
        },
        
        loadAttorney: async function() {
          console.log('[FixAttorneyStateManager] Loading attorney in fallback manager');
          
          if (window.standaloneAttorneyManager) {
            this.attorney = window.standaloneAttorneyManager.attorney;
          }
          
          return this.attorney;
        },
        
        syncWithVapi: async function() {
          console.log('[FixAttorneyStateManager] Syncing with Vapi in fallback manager');
          
          if (window.standaloneAttorneyManager) {
            return window.standaloneAttorneyManager.syncWithVapi();
          }
          
          return { action: 'none' };
        },
        
        saveToLocalStorage: function(attorney) {
          console.log('[FixAttorneyStateManager] Saving to local storage in fallback manager');
          
          if (window.standaloneAttorneyManager) {
            window.standaloneAttorneyManager.saveToLocalStorage(attorney);
          }
        },
        
        isValidUUID: function(uuid) {
          return /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(uuid);
        },
        
        subscribe: function(callback) {
          if (typeof callback !== 'function') {
            return () => {};
          }
          
          this.subscribers.push(callback);
          
          if (this.attorney) {
            callback(this.attorney);
          }
          
          return () => {
            this.subscribers = this.subscribers.filter(cb => cb !== callback);
          };
        },
        
        notifySubscribers: function() {
          if (!this.attorney) return;
          
          this.subscribers.forEach(callback => {
            try {
              callback(this.attorney);
            } catch (error) {
              console.error('[FixAttorneyStateManager] Error in subscriber callback:', error);
            }
          });
        }
      };
      
      console.log('[FixAttorneyStateManager] Fallback attorney state manager created');
    }
  }, 10000);
})();

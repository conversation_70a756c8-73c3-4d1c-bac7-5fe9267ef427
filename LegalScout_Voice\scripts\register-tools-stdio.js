#!/usr/bin/env node
/**
 * Register AI Meta MCP Tools using stdio transport
 *
 * This script registers the synchronization tools with the AI Meta MCP Server
 * using the stdio transport, which is more reliable in Node.js environments.
 *
 * Usage:
 *   node scripts/register-tools-stdio.js
 */

import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';
import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Function to register a tool with the AI Meta MCP Server
const registerTool = async (mcpClient, name, description, parametersSchema, implementationCode) => {
  try {
    console.log(`Registering tool: ${name}`);
    
    const result = await mcpClient.callTool({
      name: 'define_function',
      arguments: {
        name,
        description,
        parameters_schema: parametersSchema,
        implementation_code: implementationCode,
        execution_environment: 'javascript'
      },
    });
    
    console.log(`Successfully registered tool: ${name}`);
    return result;
  } catch (error) {
    console.error(`Error registering tool ${name}:`, error);
    throw error;
  }
};

// Main function to register all tools
const registerAllTools = async () => {
  try {
    console.log('Starting AI Meta MCP Server...');
    
    // Spawn the AI Meta MCP Server process
    const serverProcess = spawn('node', [
      path.resolve(__dirname, '..', 'ai-meta-mcp-server', 'build', 'index.js')
    ]);
    
    // Log server output
    serverProcess.stdout.on('data', (data) => {
      console.log(`[Server] ${data.toString().trim()}`);
    });
    
    serverProcess.stderr.on('data', (data) => {
      console.error(`[Server] ${data.toString().trim()}`);
    });
    
    // Wait for server to start
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    console.log('Connecting to AI Meta MCP Server...');
    
    // Get AI Meta MCP client
    const mcpClient = new Client({
      name: 'legalscout-tool-registrar',
      version: '1.0.0',
    });
    
    // Connect to the AI Meta MCP server using stdio transport
    const transport = new StdioClientTransport({
      process: serverProcess
    });
    
    await mcpClient.connect(transport);
    console.log('Connected to AI Meta MCP Server');
    
    // Register sync_attorney_profile tool
    await registerTool(
      mcpClient,
      'sync_attorney_profile',
      'Synchronize attorney profile data between Supabase and Vapi',
      {
        attorneyId: {
          type: 'string',
          description: 'ID of the attorney to synchronize'
        },
        forceUpdate: {
          type: 'boolean',
          description: 'Force update even if no discrepancies are found',
          default: false
        }
      },
      `
        const { attorneyId, forceUpdate = false } = params;
        
        console.log(\`Syncing attorney profile for \${attorneyId}, forceUpdate: \${forceUpdate}\`);
        
        // This is a placeholder implementation
        // In a real implementation, this would:
        // 1. Get data from Supabase
        // 2. Get data from Vapi
        // 3. Check for discrepancies
        // 4. Update Vapi if needed
        
        return {
          action: "simulated",
          attorneyId,
          message: "This is a simulated sync operation"
        };
      `
    );
    
    // Register manage_auth_state tool
    await registerTool(
      mcpClient,
      'manage_auth_state',
      'Manage authentication state across systems',
      {
        authData: {
          type: 'object',
          description: 'Authentication data'
        },
        action: {
          type: 'string',
          description: 'Authentication action (login, logout, refresh)',
          enum: ['login', 'logout', 'refresh']
        }
      },
      `
        const { authData, action } = params;
        
        console.log(\`Managing auth state for action: \${action}\`);
        
        // This is a placeholder implementation
        // In a real implementation, this would handle login, logout, and refresh
        
        return {
          action,
          success: true,
          message: \`Simulated auth state management for action: \${action}\`
        };
      `
    );
    
    // Register validate_configuration tool
    await registerTool(
      mcpClient,
      'validate_configuration',
      'Validate configuration before updates',
      {
        attorneyId: {
          type: 'string',
          description: 'ID of the attorney'
        },
        configData: {
          type: 'object',
          description: 'Configuration data to validate'
        }
      },
      `
        const { attorneyId, configData } = params;
        
        console.log(\`Validating configuration for attorney \${attorneyId}\`);
        console.log('Config data:', configData);
        
        // This is a placeholder implementation
        // In a real implementation, this would validate all required fields
        
        return {
          valid: true,
          message: "Configuration is valid (simulated)"
        };
      `
    );
    
    // Register check_preview_consistency tool
    await registerTool(
      mcpClient,
      'check_preview_consistency',
      'Ensure preview matches deployment',
      {
        attorneyId: {
          type: 'string',
          description: 'ID of the attorney'
        }
      },
      `
        const { attorneyId } = params;
        
        console.log(\`Checking preview consistency for attorney \${attorneyId}\`);
        
        // This is a placeholder implementation
        // In a real implementation, this would:
        // 1. Get attorney data from Supabase
        // 2. Get subdomain configuration
        // 3. Get Vapi assistant data
        // 4. Check for discrepancies
        // 5. Fix discrepancies if found
        
        return {
          consistent: true,
          message: "Preview is consistent with deployment (simulated)"
        };
      `
    );
    
    // List all registered functions
    console.log('\nListing all registered functions:');
    const listResult = await mcpClient.callTool({
      name: 'list_functions',
      arguments: {},
    });
    
    if (listResult.content && listResult.content[0]) {
      console.log(listResult.content[0].text);
    } else {
      console.log('No functions registered or unable to retrieve list');
    }
    
    // Test sync_attorney_profile
    console.log('\nTesting sync_attorney_profile:');
    const syncResult = await mcpClient.callTool({
      name: 'sync_attorney_profile',
      arguments: {
        attorneyId: 'test-attorney-id',
        forceUpdate: true
      },
    });
    
    if (syncResult.content && syncResult.content[0]) {
      console.log(syncResult.content[0].text);
    }
    
    // Disconnect from the MCP server
    await mcpClient.disconnect();
    console.log('\nDisconnected from AI Meta MCP Server');
    
    // Kill the server process
    serverProcess.kill();
    console.log('AI Meta MCP Server process terminated');
    
    console.log('All tools registered and tested successfully');
  } catch (error) {
    console.error('Error registering tools:', error);
    process.exit(1);
  }
};

// Run the registration
registerAllTools();

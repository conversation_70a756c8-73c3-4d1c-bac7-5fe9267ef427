<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>CSP Test - LegalScout</title>
  
  <!-- Updated CSP to allow Vapi SDK -->
  <meta http-equiv="Content-Security-Policy" content="
    default-src 'self';
    script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.vapi.ai https://vapi.ai https://*.vapi.ai;
    style-src 'self' 'unsafe-inline';
    img-src 'self' data: blob: https:;
    connect-src 'self' https: wss: ws:;
    font-src 'self' https://fonts.gstatic.com;
    object-src 'none';
    base-uri 'self';
    form-action 'self';
  ">

  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 20px;
      background: #f5f5f5;
    }
    .test-section {
      background: white;
      padding: 20px;
      margin: 10px 0;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .error {
      color: red;
      background: #ffe6e6;
      padding: 10px;
      border-radius: 4px;
      margin: 10px 0;
    }
    .success {
      color: green;
      background: #e6ffe6;
      padding: 10px;
      border-radius: 4px;
      margin: 10px 0;
    }
    .warning {
      color: orange;
      background: #fff3e6;
      padding: 10px;
      border-radius: 4px;
      margin: 10px 0;
    }
  </style>
</head>
<body>
  <h1>CSP Violation Test</h1>
  <p>This page tests which dependencies are causing CSP violations.</p>

  <div class="test-section">
    <h2>Test 1: Basic JavaScript</h2>
    <div id="basic-test">Testing...</div>
  </div>

  <div class="test-section">
    <h2>Test 2: Eval Usage Detection</h2>
    <div id="eval-test">Testing...</div>
  </div>

  <div class="test-section">
    <h2>Test 3: Vapi SDK Test</h2>
    <div id="vapi-test">Testing...</div>
  </div>

  <div class="test-section">
    <h2>Test 4: Framer Motion Test</h2>
    <div id="framer-test">Testing...</div>
  </div>

  <div class="test-section">
    <h2>CSP Violation Log</h2>
    <div id="csp-violations">No violations detected yet...</div>
  </div>

  <script>
    // Test 1: Basic JavaScript
    try {
      document.getElementById('basic-test').innerHTML = '<div class="success">✅ Basic JavaScript works</div>';
    } catch (error) {
      document.getElementById('basic-test').innerHTML = `<div class="error">❌ Basic JavaScript failed: ${error.message}</div>`;
    }

    // Test 2: Eval Usage Detection
    try {
      // This should fail with strict CSP
      eval('1 + 1');
      document.getElementById('eval-test').innerHTML = '<div class="warning">⚠️ Eval works (CSP allows unsafe-eval)</div>';
    } catch (error) {
      document.getElementById('eval-test').innerHTML = '<div class="success">✅ Eval blocked by CSP (good!)</div>';
    }

    // Test 3: Vapi SDK Test
    try {
      // Try to load Vapi SDK
      const script = document.createElement('script');
      script.src = 'https://cdn.vapi.ai/web/v2.3.1/index.js';
      script.onload = () => {
        document.getElementById('vapi-test').innerHTML = '<div class="success">✅ Vapi SDK loaded successfully</div>';
      };
      script.onerror = () => {
        document.getElementById('vapi-test').innerHTML = '<div class="error">❌ Vapi SDK failed to load</div>';
      };
      document.head.appendChild(script);
    } catch (error) {
      document.getElementById('vapi-test').innerHTML = `<div class="error">❌ Vapi SDK test failed: ${error.message}</div>`;
    }

    // Test 4: Framer Motion Test
    try {
      // Try to import framer-motion (this will likely fail due to module system)
      document.getElementById('framer-test').innerHTML = '<div class="warning">⚠️ Framer Motion test requires module system</div>';
    } catch (error) {
      document.getElementById('framer-test').innerHTML = `<div class="error">❌ Framer Motion test failed: ${error.message}</div>`;
    }

    // CSP Violation Listener
    const violations = [];
    document.addEventListener('securitypolicyviolation', (e) => {
      violations.push({
        directive: e.violatedDirective,
        blockedURI: e.blockedURI,
        lineNumber: e.lineNumber,
        columnNumber: e.columnNumber,
        sourceFile: e.sourceFile,
        sample: e.sample
      });
      
      const violationsDiv = document.getElementById('csp-violations');
      violationsDiv.innerHTML = violations.map((v, i) => `
        <div class="error">
          <strong>Violation ${i + 1}:</strong><br>
          Directive: ${v.directive}<br>
          Blocked URI: ${v.blockedURI}<br>
          Source: ${v.sourceFile}:${v.lineNumber}:${v.columnNumber}<br>
          Sample: ${v.sample || 'N/A'}
        </div>
      `).join('');
    });

    // Log all console errors
    const originalError = console.error;
    console.error = function(...args) {
      originalError.apply(console, args);
      
      // Check if it's a CSP-related error
      const message = args.join(' ');
      if (message.includes('Content Security Policy') || message.includes('eval')) {
        const violationsDiv = document.getElementById('csp-violations');
        violationsDiv.innerHTML += `<div class="error">Console Error: ${message}</div>`;
      }
    };
  </script>
</body>
</html>

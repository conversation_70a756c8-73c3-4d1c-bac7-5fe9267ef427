#!/usr/bin/env python
"""
Test AI Meta MCP Server

This script tests the AI Meta MCP Server by creating a simple function
and then calling it.

Usage:
  python scripts/test-ai-meta-mcp.py
"""

import subprocess
import time
import json
import os
import sys

def main():
    """Main function to test the AI Meta MCP Server."""
    print("Testing AI Meta MCP Server...")

    # Get the path to the AI Meta MCP Server
    script_dir = os.path.dirname(os.path.abspath(__file__))
    server_path = os.path.join(script_dir, "..", "ai-meta-mcp-server", "build", "index.js")
    server_path = os.path.normpath(server_path)

    print(f"Server path: {server_path}")

    # Start the AI Meta MCP Server
    print("Starting AI Meta MCP Server...")
    server_process = subprocess.Popen(
        ["node", server_path],
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True
    )

    # Wait for server to start
    print("Waiting for server to start...")
    time.sleep(2)

    # Check if server is running
    if server_process.poll() is not None:
        print("Error: Server failed to start")
        stdout, stderr = server_process.communicate()
        print(f"Server stdout: {stdout}")
        print(f"Server stderr: {stderr}")
        return

    print("Server started successfully")

    # Create a simple function
    print("\nCreating a simple function...")

    # Define the function
    function_name = "hello_world"
    function_description = "A simple hello world function"
    function_code = """
    const name = params.name || 'World';
    return `Hello, ${name}!`;
    """

    # Create a JSON file with the function definition
    function_def = {
        "name": function_name,
        "description": function_description,
        "parameters_schema": {
            "name": {
                "type": "string",
                "description": "Name to greet",
                "default": "World"
            }
        },
        "implementation_code": function_code,
        "execution_environment": "javascript"
    }

    function_def_file = os.path.join(script_dir, "function_def.json")
    with open(function_def_file, "w") as f:
        json.dump(function_def, f)

    # Create a simple Node.js script to create the function
    create_function_script = """
    import fs from 'fs';
    import { spawn } from 'child_process';
    import { fileURLToPath } from 'url';
    import path from 'path';

    // Read the function definition
    const functionDef = JSON.parse(fs.readFileSync(process.argv[2], 'utf-8'));

    // Create a simple protocol to communicate with the server
    const serverProcess = spawn('node', [process.argv[3]]);

    // Send the define_function command
    const command = {
        type: 'call_tool',
        id: '1',
        name: 'define_function',
        arguments: functionDef
    };

    serverProcess.stdin.write(JSON.stringify(command) + '\\n');

    // Handle server response
    let response = '';
    serverProcess.stdout.on('data', (data) => {
        response += data.toString();

        // Check if we have a complete response
        if (response.includes('\\n')) {
            const lines = response.split('\\n');
            for (const line of lines) {
                if (line.trim()) {
                    try {
                        const parsed = JSON.parse(line);
                        console.log(JSON.stringify(parsed));
                    } catch (error) {
                        console.error('Error parsing response:', line);
                    }
                }
            }

            // Exit after getting a response
            serverProcess.kill();
            process.exit(0);
        }
    });

    // Handle errors
    serverProcess.stderr.on('data', (data) => {
        console.error(data.toString());
    });

    // Handle server exit
    serverProcess.on('close', (code) => {
        console.log(`Server process exited with code ${code}`);
        process.exit(code);
    });
    """

    create_function_script_file = os.path.join(script_dir, "create_function.js")
    with open(create_function_script_file, "w") as f:
        f.write(create_function_script)

    # Run the script to create the function
    create_function_process = subprocess.Popen(
        ["node", create_function_script_file, function_def_file, server_path],
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True
    )

    # Wait for the function creation to complete
    stdout, stderr = create_function_process.communicate()

    if create_function_process.returncode != 0:
        print("Error creating function")
        print(f"stdout: {stdout}")
        print(f"stderr: {stderr}")
    else:
        print("Function created successfully")
        print(f"Response: {stdout}")

    # Clean up
    try:
        os.remove(function_def_file)
        os.remove(create_function_script_file)
    except:
        pass

    # Kill the server process
    server_process.kill()
    print("AI Meta MCP Server process terminated")

    print("Test completed")

if __name__ == "__main__":
    main()

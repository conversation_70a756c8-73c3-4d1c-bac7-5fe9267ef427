#!/usr/bin/env node

/**
 * Setup Assistant Assignments
 * Creates the assistant_assignments table and sets up initial assignments
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';

// Supabase configuration
const SUPABASE_URL = process.env.VITE_SUPABASE_URL || 'https://utopqxsvudgrtiwenlzl.supabase.co';
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!SUPABASE_SERVICE_KEY) {
  console.error('❌ SUPABASE_SERVICE_ROLE_KEY environment variable is required');
  process.exit(1);
}

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

async function setupAssistantAssignments() {
  console.log('🚀 Setting up Assistant Assignments System');
  console.log('==========================================');

  try {
    // 1. Read and execute the SQL file
    console.log('📋 Creating assistant_assignments table...');
    
    const sqlPath = path.join(process.cwd(), 'sql', 'create_assistant_assignments_table.sql');
    const sql = fs.readFileSync(sqlPath, 'utf8');
    
    const { error: sqlError } = await supabase.rpc('exec_sql', { sql_query: sql });
    
    if (sqlError) {
      console.error('❌ Error executing SQL:', sqlError);
      // Continue anyway, table might already exist
    } else {
      console.log('✅ Table created successfully');
    }

    // 2. Verify table exists
    console.log('\n🔍 Verifying table structure...');
    
    const { data: tableInfo, error: tableError } = await supabase
      .from('assistant_assignments')
      .select('*')
      .limit(1);

    if (tableError && tableError.code !== 'PGRST116') {
      console.error('❌ Error accessing table:', tableError);
      return false;
    }

    console.log('✅ Table is accessible');

    // 3. Get existing attorneys
    console.log('\n👥 Loading existing attorneys...');
    
    const { data: attorneys, error: attorneyError } = await supabase
      .from('attorneys')
      .select('id, oauth_email, vapi_assistant_id, firm_name, subdomain');

    if (attorneyError) {
      console.error('❌ Error loading attorneys:', attorneyError);
      return false;
    }

    console.log(`✅ Found ${attorneys.length} attorneys`);

    // 4. Set up assignments for each attorney
    console.log('\n🔗 Setting up assistant assignments...');

    const assignments = [];

    for (const attorney of attorneys) {
      if (!attorney.oauth_email) {
        console.log(`⚠️  Skipping attorney ${attorney.id} - no OAuth email`);
        continue;
      }

      console.log(`📋 Processing ${attorney.oauth_email}...`);

      // Add primary assistant assignment
      if (attorney.vapi_assistant_id) {
        assignments.push({
          oauth_email: attorney.oauth_email,
          assistant_id: attorney.vapi_assistant_id,
          assigned_by: attorney.id,
          notes: `Primary assistant for ${attorney.firm_name || 'attorney'}`
        });
      }

      // Add additional email variants for known users
      if (attorney.oauth_email === '<EMAIL>') {
        // Add <EMAIL> variant
        if (attorney.vapi_assistant_id) {
          assignments.push({
            oauth_email: '<EMAIL>',
            assistant_id: attorney.vapi_assistant_id,
            assigned_by: attorney.id,
            notes: 'Primary assistant for Damon (work email)'
          });
        }
      }
    }

    // 5. Insert assignments
    if (assignments.length > 0) {
      console.log(`📝 Inserting ${assignments.length} assignments...`);

      const { data: insertedAssignments, error: insertError } = await supabase
        .from('assistant_assignments')
        .upsert(assignments, { 
          onConflict: 'oauth_email,assistant_id',
          ignoreDuplicates: false 
        })
        .select();

      if (insertError) {
        console.error('❌ Error inserting assignments:', insertError);
        return false;
      }

      console.log(`✅ Successfully inserted ${insertedAssignments?.length || 0} assignments`);
    } else {
      console.log('⚠️  No assignments to insert');
    }

    // 6. Verify assignments
    console.log('\n🔍 Verifying assignments...');

    const { data: allAssignments, error: verifyError } = await supabase
      .from('assistant_assignments')
      .select('*')
      .eq('is_active', true);

    if (verifyError) {
      console.error('❌ Error verifying assignments:', verifyError);
      return false;
    }

    console.log(`✅ Found ${allAssignments.length} active assignments:`);
    
    allAssignments.forEach(assignment => {
      console.log(`   📧 ${assignment.oauth_email} → 🤖 ${assignment.assistant_id.substring(0, 8)}...`);
    });

    console.log('\n🎉 Assistant Assignments Setup Complete!');
    console.log('\n💡 Next Steps:');
    console.log('1. Test the assistant dropdown in the dashboard');
    console.log('2. Verify that only assigned assistants appear');
    console.log('3. Add more assignments as needed using the service');

    return true;

  } catch (error) {
    console.error('❌ Setup failed:', error);
    return false;
  }
}

async function testAssignmentService() {
  console.log('\n🧪 Testing Assignment Service');
  console.log('=============================');

  try {
    // Test getting assignments for a known user
    const testEmail = '<EMAIL>';
    
    const { data: assignments, error } = await supabase
      .from('assistant_assignments')
      .select('assistant_id')
      .eq('oauth_email', testEmail)
      .eq('is_active', true);

    if (error) {
      console.error('❌ Error testing service:', error);
      return false;
    }

    console.log(`✅ Found ${assignments.length} assignments for ${testEmail}`);
    assignments.forEach(assignment => {
      console.log(`   🤖 ${assignment.assistant_id}`);
    });

    return true;

  } catch (error) {
    console.error('❌ Test failed:', error);
    return false;
  }
}

async function main() {
  const setupSuccess = await setupAssistantAssignments();
  
  if (setupSuccess) {
    await testAssignmentService();
  }

  process.exit(setupSuccess ? 0 : 1);
}

// Run the setup
main().catch(error => {
  console.error('❌ Script failed:', error);
  process.exit(1);
});

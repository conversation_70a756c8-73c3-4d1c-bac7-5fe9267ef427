# LegalScout Technology Stack

## Core Technologies

### Frontend Framework
- **React 18.2.0**
  - Modern functional components
  - Hooks for state management
  - Context API for global state
  - React Router v7 for navigation

### Development Environment
- **Vite 4.4.5**
  - Fast development server
  - Hot Module Replacement (HMR)
  - Optimized production builds
  - Environment variable handling

### Styling and UI
- **TailwindCSS 3.3.3**
  - Utility-first CSS framework
  - Custom design system
  - Responsive design utilities
  - Dark mode support

### Maps and Visualization
- **Leaflet 1.9.4** & **React Leaflet 4.2.1**
  - Interactive map components
  - Custom markers and overlays
  - Geolocation features
  - Clustering support

### 3D Visualization
- **Three.js 0.174.0** & **Three-Globe 2.42.1**
  - 3D globe visualization
  - Custom geometry rendering
  - Animation effects
  - Interactive elements

### Model Context Protocol (MCP)
- **@modelcontextprotocol/sdk 1.12.1**
  - MCP client and server integration
  - Tool discovery and execution
  - Streamable HTTP and SSE transports
- **@vapi-ai/mcp-server 0.0.6**
  - Vapi-specific MCP server integration
  - Assistant management through MCP
  - Call creation and monitoring

## Voice Integration
- **Vapi.ai Web SDK 2.3.1**
  - Natural language processing
  - Voice recognition
  - Conversation management
  - Real-time responses
  - Model Context Protocol (MCP) integration
  - Assistant management and configuration

## Authentication
- **Supabase Auth with Google OAuth**
  - User authentication
  - OAuth integration
  - JWT handling
  - Row-level security policies
  - Profile-based access control

## Database
- **Supabase 2.49.1**
  - PostgreSQL database
  - Real-time subscriptions
  - Row Level Security (RLS)
  - Built-in authentication
  - File storage for logos and voice samples

## Development Tools

### Testing
- **Vitest 3.0.8**
  - Unit testing framework
  - Component testing
  - Integration testing
  - Test coverage reporting

### Code Quality
- **ESLint 8.45.0**
  - Code linting
  - Style enforcement
  - Best practices
  - Automatic fixing

### Type Checking
- **TypeScript Support**
  - Type definitions
  - Interface declarations
  - Type checking
  - IDE integration

### Build Tools
- **PostCSS 8.5.3**
  - CSS processing
  - Autoprefixer
  - CSS optimization
  - Modern CSS features

## Development Workflow

### Package Management
- **npm/yarn**
  - Dependency management
  - Script running
  - Lock file maintenance
  - Version control

### Version Control
- **Git**
  - Feature branching
  - Pull request workflow
  - Commit conventions
  - Release management

### Deployment
- **Vercel**
  - Continuous deployment
  - Edge functions
  - Environment management
  - Performance monitoring

## Performance Optimization

### Build Optimization
- Code splitting
- Tree shaking
- Lazy loading
- Asset optimization

### Runtime Optimization
- Caching strategies
- Memory management
- Network optimization
- Resource preloading

## Security Implementation

### Data Protection
- HTTPS enforcement
- XSS prevention
- CSRF protection
- Input sanitization

### Authentication Security
- JWT handling
- Session management
- Password policies
- 2FA support

## Monitoring and Analytics

### Performance Monitoring
- Lighthouse scores
- Core Web Vitals
- Error tracking
- User metrics

### Usage Analytics
- User behavior tracking
- Performance metrics
- Error reporting
- Feature usage statistics 
/**
 * Disable Automatic Assistant Creation
 * 
 * This script prevents automatic assistant creation to avoid Vapi API overrun.
 * It's now disabled since the orphaned assistant problem has been resolved.
 */

(function() {
  console.log('[DisableAutomaticAssistantCreation] Starting fix...');
  
  // Since the orphaned assistant problem is resolved, this script is now disabled
  // but kept for compatibility with existing references
  
  console.log('[DisableAutomaticAssistantCreation] DISABLED - Orphaned assistant problem resolved');
  console.log('[DisableAutomaticAssistantCreation] Using VapiDirectApiService for complete data loading');
  
  // Mark as loaded for any scripts that check for this
  window.disableAutomaticAssistantCreationLoaded = true;
  
  console.log('[DisableAutomaticAssistantCreation] Fix script loaded - automatic assistant creation management ready');
})();

/**
 * Agent Tab Debug Loop - Incisive Testing and Debugging
 * 
 * This script systematically tests the Agent tab and preview functionality
 * with focused debugging loops to identify and fix issues.
 */

(function() {
  'use strict';

  console.log('🔍 [AgentTabDebugLoop] Starting incisive debugging session...');

  // Debug state tracking
  const debugState = {
    testResults: [],
    currentTest: null,
    loopCount: 0,
    maxLoops: 10,
    issues: [],
    fixes: []
  };

  // Test suite for Agent tab functionality
  const testSuite = {
    // Test 1: Check if Agent tab is accessible
    async testAgentTabAccess() {
      console.log('🧪 [Test 1] Testing Agent tab accessibility...');
      
      const agentTab = document.querySelector('[data-tab="agent"], .tab-button:contains("Agent"), button:contains("Agent")');
      if (!agentTab) {
        return { success: false, error: 'Agent tab button not found', element: null };
      }

      // Click the Agent tab
      agentTab.click();
      await new Promise(resolve => setTimeout(resolve, 1000)); // Wait for tab switch

      const agentContent = document.querySelector('.agent-tab, [class*="agent"]');
      return {
        success: !!agentContent,
        error: agentContent ? null : 'Agent tab content not loaded',
        element: agentContent
      };
    },

    // Test 2: Check assistant dropdown functionality
    async testAssistantDropdown() {
      console.log('🧪 [Test 2] Testing assistant dropdown...');
      
      const dropdown = document.querySelector('#assistant-select, .assistant-select, select[id*="assistant"]');
      if (!dropdown) {
        return { success: false, error: 'Assistant dropdown not found', element: null };
      }

      const options = Array.from(dropdown.options);
      const selectedValue = dropdown.value;
      const hasValidOptions = options.length > 1; // Should have more than just "No Assistant"
      
      console.log('📋 Dropdown analysis:', {
        totalOptions: options.length,
        selectedValue: selectedValue,
        options: options.map(opt => ({ value: opt.value, text: opt.text }))
      });

      return {
        success: hasValidOptions && selectedValue !== '',
        error: hasValidOptions ? (selectedValue === '' ? 'No assistant selected' : null) : 'No valid assistant options',
        element: dropdown,
        data: { options, selectedValue, hasValidOptions }
      };
    },

    // Test 3: Check preview iframe functionality
    async testPreviewIframe() {
      console.log('🧪 [Test 3] Testing preview iframe...');
      
      const iframes = document.querySelectorAll('iframe[src*="preview"], iframe[src*="simple-preview"]');
      if (iframes.length === 0) {
        return { success: false, error: 'No preview iframes found', element: null };
      }

      const iframe = iframes[0];
      const isLoaded = iframe.contentDocument || iframe.contentWindow;
      
      return {
        success: !!isLoaded,
        error: isLoaded ? null : 'Preview iframe not loaded',
        element: iframe,
        data: { iframeCount: iframes.length, src: iframe.src }
      };
    },

    // Test 4: Check Vapi MCP service availability
    async testVapiMcpService() {
      console.log('🧪 [Test 4] Testing Vapi MCP service...');
      
      try {
        // Check if vapiMcpService is available globally
        if (window.vapiMcpService) {
          const assistants = await window.vapiMcpService.listAssistants();
          return {
            success: true,
            error: null,
            element: null,
            data: { assistants, serviceAvailable: true }
          };
        } else {
          return {
            success: false,
            error: 'Vapi MCP service not available globally',
            element: null,
            data: { serviceAvailable: false }
          };
        }
      } catch (error) {
        return {
          success: false,
          error: `Vapi MCP service error: ${error.message}`,
          element: null,
          data: { serviceAvailable: true, errorDetails: error }
        };
      }
    },

    // Test 5: Check attorney data availability
    async testAttorneyData() {
      console.log('🧪 [Test 5] Testing attorney data...');
      
      const attorneyData = {
        localStorage: localStorage.getItem('attorney'),
        windowState: window.currentAttorneyState,
        manager: window.StandaloneAttorneyManager?.attorney
      };

      let parsedAttorney = null;
      try {
        parsedAttorney = attorneyData.localStorage ? JSON.parse(attorneyData.localStorage) : null;
      } catch (e) {
        // Invalid JSON
      }

      const hasValidAttorney = parsedAttorney && parsedAttorney.id && parsedAttorney.vapi_assistant_id;

      return {
        success: hasValidAttorney,
        error: hasValidAttorney ? null : 'No valid attorney data found',
        element: null,
        data: {
          hasLocalStorage: !!attorneyData.localStorage,
          hasWindowState: !!attorneyData.windowState,
          hasManager: !!attorneyData.manager,
          parsedAttorney: parsedAttorney,
          assistantId: parsedAttorney?.vapi_assistant_id
        }
      };
    }
  };

  // Debug loop runner
  async function runDebugLoop() {
    debugState.loopCount++;
    console.log(`🔄 [DebugLoop ${debugState.loopCount}] Starting debug iteration...`);

    const testResults = {};

    // Run all tests
    for (const [testName, testFunc] of Object.entries(testSuite)) {
      try {
        debugState.currentTest = testName;
        const result = await testFunc();
        testResults[testName] = result;
        
        if (result.success) {
          console.log(`✅ [${testName}] PASSED`);
        } else {
          console.error(`❌ [${testName}] FAILED: ${result.error}`);
          debugState.issues.push({ test: testName, error: result.error, loop: debugState.loopCount });
        }
      } catch (error) {
        console.error(`💥 [${testName}] EXCEPTION: ${error.message}`);
        testResults[testName] = { success: false, error: error.message, exception: true };
      }
    }

    debugState.testResults.push({ loop: debugState.loopCount, results: testResults, timestamp: new Date() });

    // Analyze results and suggest fixes
    analyzeAndSuggestFixes(testResults);

    // Continue loop if issues remain and we haven't hit max loops
    if (debugState.loopCount < debugState.maxLoops && hasFailingTests(testResults)) {
      console.log(`🔄 [DebugLoop] Issues detected, continuing to loop ${debugState.loopCount + 1}...`);
      setTimeout(runDebugLoop, 2000); // Wait 2 seconds before next iteration
    } else {
      console.log('🏁 [DebugLoop] Debug session complete');
      generateDebugReport();
    }
  }

  // Check if any tests are failing
  function hasFailingTests(results) {
    return Object.values(results).some(result => !result.success);
  }

  // Analyze results and suggest fixes
  function analyzeAndSuggestFixes(results) {
    console.log('🔍 [Analysis] Analyzing test results...');

    // Assistant dropdown issues
    if (!results.testAssistantDropdown?.success) {
      if (results.testAttorneyData?.success && results.testAttorneyData?.data?.assistantId) {
        console.log('💡 [Fix Suggestion] Attorney has assistant ID but dropdown is empty - check loadAvailableAssistants function');
        suggestDropdownFix(results.testAttorneyData.data.assistantId);
      }
    }

    // Vapi MCP service issues
    if (!results.testVapiMcpService?.success) {
      console.log('💡 [Fix Suggestion] Vapi MCP service not working - check service initialization');
      suggestVapiMcpFix();
    }

    // Preview iframe issues
    if (!results.testPreviewIframe?.success) {
      console.log('💡 [Fix Suggestion] Preview iframe not loading - check iframe src and CORS');
      suggestPreviewFix();
    }
  }

  // Specific fix suggestions
  function suggestDropdownFix(assistantId) {
    console.log(`🔧 [Fix] Attempting to manually populate dropdown with assistant: ${assistantId}`);
    
    const dropdown = document.querySelector('#assistant-select, .assistant-select');
    if (dropdown && assistantId) {
      // Check if option already exists
      const existingOption = Array.from(dropdown.options).find(opt => opt.value === assistantId);
      
      if (!existingOption) {
        const option = document.createElement('option');
        option.value = assistantId;
        option.text = 'LegalScout Assistant';
        dropdown.appendChild(option);
        dropdown.value = assistantId;
        dropdown.dispatchEvent(new Event('change', { bubbles: true }));
        
        debugState.fixes.push({ type: 'dropdown', action: 'added_option', assistantId });
        console.log('✅ [Fix Applied] Added assistant option to dropdown');
      }
    }
  }

  function suggestVapiMcpFix() {
    console.log('🔧 [Fix] Checking Vapi MCP service initialization...');
    
    // Check if service exists in different locations
    const serviceLocations = [
      'window.vapiMcpService',
      'window.VapiMcpService',
      'window.vapiServiceManager?.mcpService'
    ];

    serviceLocations.forEach(location => {
      try {
        const service = eval(location);
        if (service) {
          console.log(`✅ [Fix] Found Vapi service at: ${location}`);
          window.vapiMcpService = service; // Make it globally available
        }
      } catch (e) {
        // Location doesn't exist
      }
    });
  }

  function suggestPreviewFix() {
    console.log('🔧 [Fix] Checking preview iframe issues...');
    
    const iframes = document.querySelectorAll('iframe');
    iframes.forEach((iframe, index) => {
      console.log(`📋 [Preview] Iframe ${index}: src=${iframe.src}, loaded=${!!iframe.contentWindow}`);
      
      // Try to reload iframe if it's not loaded
      if (!iframe.contentWindow) {
        iframe.src = iframe.src; // Force reload
        debugState.fixes.push({ type: 'preview', action: 'reload_iframe', index });
      }
    });
  }

  // Generate comprehensive debug report
  function generateDebugReport() {
    console.log('📊 [Report] Generating debug report...');
    
    const report = {
      summary: {
        totalLoops: debugState.loopCount,
        totalIssues: debugState.issues.length,
        totalFixes: debugState.fixes.length,
        timestamp: new Date()
      },
      issues: debugState.issues,
      fixes: debugState.fixes,
      testHistory: debugState.testResults
    };

    console.log('📊 [Final Report]', report);
    
    // Store report globally for inspection
    window.agentTabDebugReport = report;
    
    console.log('✅ [Report] Debug report stored in window.agentTabDebugReport');
  }

  // Expose debug functions globally
  window.agentTabDebugLoop = {
    runDebugLoop,
    testSuite,
    debugState,
    generateDebugReport
  };

  // Auto-start debug loop
  console.log('🚀 [AgentTabDebugLoop] Starting automatic debug loop...');
  setTimeout(runDebugLoop, 1000); // Start after 1 second

})();

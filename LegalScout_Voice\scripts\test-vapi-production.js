#!/usr/bin/env node

/**
 * Test Vapi API configuration in production
 * This script tests the Vapi API key configuration and phone numbers endpoint
 */

import fetch from 'node-fetch';

// Test configuration
const VAPI_API_URL = 'https://api.vapi.ai';
const VAPI_PRIVATE_KEY = '6734febc-fc65-4669-93b0-929b31ff6564';
const VAPI_PUBLIC_KEY = '310f0d43-27c2-47a5-a76d-e55171d024f7';

console.log('🧪 VAPI PRODUCTION TEST');
console.log('======================');
console.log('');

// Test phone numbers endpoint with private key
async function testPhoneNumbers() {
  console.log('📞 Testing Phone Numbers API...');
  console.log(`Using private key: ${VAPI_PRIVATE_KEY.substring(0, 8)}...`);
  
  try {
    const response = await fetch(`${VAPI_API_URL}/phone-number`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${VAPI_PRIVATE_KEY}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log(`Status: ${response.status} ${response.statusText}`);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.log('❌ Phone Numbers API Error:');
      console.log(errorText);
      return false;
    }
    
    const data = await response.json();
    console.log('✅ Phone Numbers API Success!');
    console.log(`Found ${Array.isArray(data) ? data.length : 'unknown'} phone numbers`);
    
    if (Array.isArray(data) && data.length > 0) {
      console.log('First phone number:', {
        id: data[0].id,
        number: data[0].phoneNumber || data[0].phone_number,
        name: data[0].name || data[0].friendly_name
      });
    }
    
    return true;
  } catch (error) {
    console.log('❌ Phone Numbers API Error:');
    console.log(error.message);
    return false;
  }
}

// Test assistants endpoint with private key
async function testAssistants() {
  console.log('');
  console.log('🤖 Testing Assistants API...');
  console.log(`Using private key: ${VAPI_PRIVATE_KEY.substring(0, 8)}...`);
  
  try {
    const response = await fetch(`${VAPI_API_URL}/assistant`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${VAPI_PRIVATE_KEY}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log(`Status: ${response.status} ${response.statusText}`);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.log('❌ Assistants API Error:');
      console.log(errorText);
      return false;
    }
    
    const data = await response.json();
    console.log('✅ Assistants API Success!');
    console.log(`Found ${Array.isArray(data) ? data.length : 'unknown'} assistants`);
    
    if (Array.isArray(data) && data.length > 0) {
      console.log('First assistant:', {
        id: data[0].id,
        name: data[0].name,
        model: data[0].model
      });
    }
    
    return true;
  } catch (error) {
    console.log('❌ Assistants API Error:');
    console.log(error.message);
    return false;
  }
}

// Test with public key (should fail for server operations)
async function testWithPublicKey() {
  console.log('');
  console.log('🔑 Testing with Public Key (should fail)...');
  console.log(`Using public key: ${VAPI_PUBLIC_KEY.substring(0, 8)}...`);
  
  try {
    const response = await fetch(`${VAPI_API_URL}/phone-number`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${VAPI_PUBLIC_KEY}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log(`Status: ${response.status} ${response.statusText}`);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.log('✅ Expected failure with public key:');
      console.log(errorText);
      return true; // This is expected to fail
    }
    
    console.log('⚠️ Unexpected success with public key');
    return false;
  } catch (error) {
    console.log('✅ Expected error with public key:');
    console.log(error.message);
    return true; // This is expected to fail
  }
}

// Run all tests
async function runTests() {
  console.log('Starting Vapi API tests...');
  console.log('');
  
  const phoneNumbersResult = await testPhoneNumbers();
  const assistantsResult = await testAssistants();
  const publicKeyResult = await testWithPublicKey();
  
  console.log('');
  console.log('📊 TEST RESULTS');
  console.log('===============');
  console.log(`Phone Numbers API: ${phoneNumbersResult ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Assistants API: ${assistantsResult ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Public Key Test: ${publicKeyResult ? '✅ PASS' : '❌ FAIL'}`);
  
  const allPassed = phoneNumbersResult && assistantsResult && publicKeyResult;
  console.log('');
  console.log(`Overall Result: ${allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);
  
  if (allPassed) {
    console.log('');
    console.log('🎉 Vapi API configuration is working correctly!');
    console.log('The private key is properly configured for server operations.');
  } else {
    console.log('');
    console.log('🔧 There are issues with the Vapi API configuration.');
    console.log('Check the error messages above for details.');
  }
  
  process.exit(allPassed ? 0 : 1);
}

// Run the tests
runTests().catch(error => {
  console.error('Fatal error running tests:', error);
  process.exit(1);
});

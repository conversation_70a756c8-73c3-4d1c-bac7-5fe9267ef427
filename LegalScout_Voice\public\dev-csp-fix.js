/**
 * Development CSP Fix
 * Allows localhost connections for development API server
 */

(function() {
    // Only run in development
    if (window.location.hostname !== 'localhost' && window.location.hostname !== '127.0.0.1') {
        return;
    }

    console.log('[DevCSPFix] 🔧 Applying development CSP fix for localhost API server...');

    // Override fetch to allow localhost:3001 connections
    const originalFetch = window.fetch;
    
    window.fetch = function(url, options = {}) {
        // Check if this is a localhost:3001 request
        if (typeof url === 'string' && url.includes('localhost:3001')) {
            console.log('[DevCSPFix] 🔓 Allowing localhost:3001 request:', url);
            
            // Use the original fetch without CSP restrictions
            return originalFetch.call(this, url, {
                ...options,
                mode: 'cors',
                headers: {
                    ...options.headers,
                    'Content-Type': 'application/json'
                }
            });
        }
        
        // For all other requests, use the original fetch
        return originalFetch.call(this, url, options);
    };

    console.log('[DevCSPFix] ✅ Development CSP fix applied - localhost:3001 connections allowed');
})();

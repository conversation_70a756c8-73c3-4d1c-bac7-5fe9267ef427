/**
 * Quick Assistant Data Integrity Test
 * 
 * Run this in the browser console to quickly check for assistant data issues
 */

console.log('🔍 Quick Assistant Data Integrity Check');

// Get attorney data
const attorney = JSON.parse(localStorage.getItem('attorney') || '{}');
console.log('👤 Attorney Data:');
console.log(`   ID: ${attorney.id}`);
console.log(`   Firm: ${attorney.firm_name}`);
console.log(`   Vapi Assistant ID: ${attorney.vapi_assistant_id}`);
console.log(`   Current Assistant ID: ${attorney.current_assistant_id}`);

// Check for critical issues
const issues = [];

if (attorney.vapi_assistant_id === attorney.id) {
  issues.push('🚨 CRITICAL: vapi_assistant_id matches attorney ID');
}

if (attorney.current_assistant_id === attorney.id) {
  issues.push('🚨 CRITICAL: current_assistant_id matches attorney ID');
}

if (attorney.vapi_assistant_id && attorney.vapi_assistant_id.includes('mock')) {
  issues.push('⚠️ WARNING: vapi_assistant_id contains "mock"');
}

if (issues.length > 0) {
  console.log('\n🚨 ISSUES FOUND:');
  issues.forEach(issue => console.log(issue));
} else {
  console.log('\n✅ No critical issues found in attorney data');
}

// Check assistant dropdown state
const dropdown = document.querySelector('.enhanced-assistant-dropdown');
if (dropdown) {
  console.log('\n🎯 Assistant Dropdown:');
  const currentSelection = dropdown.querySelector('.dropdown-trigger .assistant-name');
  console.log(`   Current: ${currentSelection?.textContent || 'None'}`);
  
  const items = dropdown.querySelectorAll('.dropdown-item');
  console.log(`   Options: ${items.length}`);
} else {
  console.log('\n⚠️ Assistant dropdown not found');
}

// Check URLs in share component
const shareUrls = document.querySelectorAll('[class*="share"] input[value*="legalscout.net"]');
if (shareUrls.length > 0) {
  console.log('\n🔗 Share URLs:');
  shareUrls.forEach((input, i) => {
    console.log(`   ${i + 1}. ${input.value}`);

    // Check for the problematic "select-assistant" URL
    if (input.value.includes('select-assistant.legalscout.net')) {
      console.log('      🚨 ISSUE: Still showing placeholder URL instead of assistant-specific URL');
    }
  });
} else {
  console.log('\n⚠️ No share URLs found');
}

// Check assistant context state
if (window.React && window.React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED) {
  console.log('\n🔍 Checking React context state...');
  // This is a simplified check - in practice you'd need React DevTools
}

// Check for assistant-aware components
const assistantAwareComponents = document.querySelectorAll('[data-assistant-aware], [class*="assistant-aware"]');
console.log(`\n🎯 Found ${assistantAwareComponents.length} assistant-aware components`);

// Check current assistant selection
const currentSelection = document.querySelector('.enhanced-assistant-dropdown .dropdown-trigger .assistant-name');
const currentSubdomain = document.querySelector('.enhanced-assistant-dropdown .dropdown-trigger .assistant-subdomain');

if (currentSelection && currentSubdomain) {
  console.log('\n📋 Current Selection:');
  console.log(`   Name: ${currentSelection.textContent}`);
  console.log(`   Subdomain: ${currentSubdomain.textContent}`);

  // Check if they match
  const nameText = currentSelection.textContent.trim();
  const subdomainText = currentSubdomain.textContent.trim();

  if (subdomainText.includes('select-assistant')) {
    console.log('   🚨 ISSUE: Subdomain still shows placeholder');
  }
}

console.log('\n✅ Quick check complete!');

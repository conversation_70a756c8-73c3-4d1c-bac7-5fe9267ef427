import { supabase, getRealSupabaseClient } from '../lib/supabase';

/**
 * Service for managing assistant UI configurations
 * Handles per-assistant UI settings storage and retrieval
 */
export class AssistantUIConfigService {

  /**
   * Upload image to Supabase Storage
   * @param {File} file - Image file to upload
   * @param {string} assistantId - Assistant ID for folder organization
   * @param {string} type - Image type ('logo' or 'assistant_image')
   * @returns {Promise<string>} Public URL of uploaded image
   */
  async uploadImage(file, assistantId, type = 'logo') {
    try {
      // Validate inputs
      if (!file || !assistantId) {
        throw new Error('File and assistantId are required for upload');
      }

      // Check authentication using real Supabase client
      let realSupabase;
      let session = null;

      try {
        realSupabase = await getRealSupabaseClient();
        const { data: { session: realSession }, error: authError } = await realSupabase.auth.getSession();

        if (authError) {
          console.warn('Auth check failed:', authError);
        } else {
          session = realSession;
        }
      } catch (realClientError) {
        console.warn('Failed to get real Supabase client:', realClientError);
        // Fall back to regular client
        const { data: { session: fallbackSession }, error: authError } = await supabase.auth.getSession();
        if (!authError) {
          session = fallbackSession;
        }
      }

      if (!session?.user) {
        console.warn('No authenticated user found');

        // Try emergency auth to get current user
        try {
          const { emergencyAuth } = await import('../lib/supabase.js');
          const { user, error } = await emergencyAuth.getCurrentUser();
          if (user && !error) {
            console.log('Found user via emergency auth:', user.email);
            // Create a mock session for the upload
            session = {
              user: user,
              access_token: JSON.parse(localStorage.getItem('supabase.auth.token') || '{}').access_token
            };
          }
        } catch (emergencyError) {
          console.warn('Emergency auth failed:', emergencyError);
        }
      } else {
        console.log('Authenticated user found:', session.user.id);
      }

      // Create unique filename with better pattern for RLS policies
      const fileExt = file.name.split('.').pop();
      const timestamp = Date.now();
      const fileName = `${assistantId}/${type}_${timestamp}.${fileExt}`;

      console.log('Uploading file:', fileName);

      // Use real Supabase client for upload if available
      const uploadClient = realSupabase || supabase;

      // Try direct upload first
      const { data, error } = await uploadClient.storage
        .from('legalscout_bucket1')
        .upload(fileName, file, {
          cacheControl: '3600',
          upsert: true,
          contentType: file.type
        });

      if (error) {
        console.error('Storage upload error:', error);

        // If RLS error, try alternative approaches
        if (error.message?.includes('row-level security') || error.statusCode === '403') {
          console.log('RLS error detected, trying alternative upload methods...');

          // Try 1: Simpler filename pattern
          const simpleFileName = `${type}_${assistantId}_${timestamp}.${fileExt}`;
          console.log('Trying simple filename:', simpleFileName);

          const { data: retryData, error: retryError } = await uploadClient.storage
            .from('legalscout_bucket1')
            .upload(simpleFileName, file, {
              cacheControl: '3600',
              upsert: true,
              contentType: file.type
            });

          if (retryError) {
            console.error('Simple filename upload failed:', retryError);

            // Try 2: Use API proxy for upload
            console.log('Trying upload via API proxy...');
            try {
              const formData = new FormData();
              formData.append('file', file);
              formData.append('fileName', simpleFileName);
              formData.append('bucket', 'legalscout_bucket1');

              const proxyResponse = await fetch('/api/storage-upload', {
                method: 'POST',
                body: formData,
                headers: {
                  'Authorization': session?.access_token ? `Bearer ${session.access_token}` : ''
                }
              });

              if (proxyResponse.ok) {
                const result = await proxyResponse.json();
                console.log('Proxy upload successful:', result);
                return result.publicUrl;
              } else {
                console.error('Proxy upload failed:', await proxyResponse.text());
              }
            } catch (proxyError) {
              console.error('Proxy upload error:', proxyError);
            }

            throw new Error(`Storage upload failed: ${retryError.message} (Status: ${retryError.statusCode})`);
          }

          // Get public URL for retry
          const { data: { publicUrl } } = uploadClient.storage
            .from('legalscout_bucket1')
            .getPublicUrl(simpleFileName);

          console.log('Alternative upload successful, public URL:', publicUrl);
          return publicUrl;
        }

        throw error;
      }

      // Get public URL
      const { data: { publicUrl } } = uploadClient.storage
        .from('legalscout_bucket1')
        .getPublicUrl(fileName);

      console.log('Upload successful, public URL:', publicUrl);
      return publicUrl;
    } catch (error) {
      console.error('Error uploading image:', error);
      throw new Error(`Failed to upload image: ${error.message}`);
    }
  }

  /**
   * Delete image from Supabase Storage
   * @param {string} imageUrl - URL of image to delete
   * @returns {Promise<boolean>} Success status
   */
  async deleteImage(imageUrl) {
    try {
      if (!imageUrl || !imageUrl.includes('legalscout_bucket1')) {
        return true; // Not a storage URL, nothing to delete
      }

      // Extract file path from URL
      const urlParts = imageUrl.split('/legalscout_bucket1/');
      if (urlParts.length < 2) return true;

      const filePath = urlParts[1].split('?')[0]; // Remove query params

      const { error } = await supabase.storage
        .from('legalscout_bucket1')
        .remove([filePath]);

      if (error) throw error;
      return true;
    } catch (error) {
      console.error('Error deleting image:', error);
      return false;
    }
  }
  /**
   * Get UI configuration for a specific assistant
   * @param {string} attorneyId - Attorney ID
   * @param {string} assistantId - Assistant ID
   * @returns {Promise<Object|null>} UI configuration or null if not found
   */
  async getAssistantConfig(attorneyId, assistantId) {
    try {
      const { data, error } = await supabase
        .from('assistant_ui_configs')
        .select('*')
        .eq('attorney_id', attorneyId)
        .eq('assistant_id', assistantId)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          // No config found - return null
          return null;
        }
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Error getting assistant config:', error);
      return null;
    }
  }

  /**
   * Get all UI configurations for an attorney
   * @param {string} attorneyId - Attorney ID
   * @returns {Promise<Array>} Array of UI configurations
   */
  async getAllAssistantConfigs(attorneyId) {
    try {
      const { data, error } = await supabase
        .from('assistant_ui_configs')
        .select('*')
        .eq('attorney_id', attorneyId)
        .order('updated_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error getting all assistant configs:', error);
      return [];
    }
  }

  /**
   * Save or update UI configuration for an assistant
   * @param {string} attorneyId - Attorney ID
   * @param {string} assistantId - Assistant ID
   * @param {Object} configData - UI configuration data
   * @returns {Promise<Object>} Saved configuration
   */
  async saveAssistantConfig(attorneyId, assistantId, configData) {
    try {
      // Create a copy of configData to avoid mutating the original
      const sanitizedData = { ...configData };

      // Check for base64 images and prevent database timeouts
      const base64Fields = ['logo_url', 'assistant_image_url', 'mascot_url'];
      for (const field of base64Fields) {
        if (sanitizedData[field] && sanitizedData[field].startsWith('data:')) {
          const base64Size = sanitizedData[field].length;
          console.warn(`⚠️ [AssistantUIConfig] Base64 image in ${field} is ${base64Size} bytes. Use uploadImage() instead.`);

          // ALWAYS remove data URLs to prevent statement timeouts
          // Data URLs should be uploaded to storage first, then the storage URL saved
          console.error(`❌ [AssistantUIConfig] Removing data URL in ${field} to prevent statement timeout. Use uploadImage() method instead.`);
          sanitizedData[field] = null;
        }
      }

      // FIXED: Proper upsert with explicit conflict resolution
      const { data, error } = await supabase
        .from('assistant_ui_configs')
        .upsert({
          attorney_id: attorneyId,
          assistant_id: assistantId,
          ...sanitizedData,
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'attorney_id,assistant_id',
          ignoreDuplicates: false
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error saving assistant config:', error);
      throw error;
    }
  }

  /**
   * Update UI configuration for an assistant (alias for saveAssistantConfig)
   * @param {string} attorneyId - Attorney ID
   * @param {string} assistantId - Assistant ID
   * @param {Object} configData - UI configuration data
   * @returns {Promise<Object>} Saved configuration
   */
  async updateAssistantConfig(attorneyId, assistantId, configData) {
    return await this.saveAssistantConfig(attorneyId, assistantId, configData);
  }

  /**
   * Create default UI configuration for an assistant
   * @param {string} attorneyId - Attorney ID
   * @param {string} assistantId - Assistant ID
   * @param {Object} baseData - Base data to use for defaults
   * @returns {Promise<Object>} Created configuration
   */
  async createDefaultConfig(attorneyId, assistantId, baseData = {}) {
    const defaultConfig = {
      firm_name: baseData.firm_name || 'LegalScout',
      logo_url: baseData.logo_url || null,
      assistant_image_url: baseData.assistant_image_url || null, // New field for assistant profile image
      primary_color: '#2563eb',
      secondary_color: '#1e40af',
      button_color: '#3b82f6',
      background_color: '#ffffff',
      background_opacity: 1.0,
      button_opacity: 1.0,
      practice_area_background_opacity: 0.1,
      text_background_color: '#ffffff',
      practice_description: baseData.practice_description || null,
      welcome_message: baseData.welcome_message || 'Hello, how can I help you today?',
      information_gathering: baseData.information_gathering || null,
      office_address: baseData.office_address || null,
      scheduling_link: baseData.scheduling_link || null,
      practice_areas: baseData.practice_areas || [],
      vapi_instructions: baseData.vapi_instructions || 'You are a legal assistant helping clients with their legal needs.',
      vapi_context: baseData.vapi_context || '',
      voice_provider: baseData.voice_provider || '11labs',
      voice_id: baseData.voice_id || 'sarah',
      ai_model: baseData.ai_model || 'gpt-4o',
      custom_fields: baseData.custom_fields || {}
    };

    return await this.saveAssistantConfig(attorneyId, assistantId, defaultConfig);
  }

  /**
   * Delete UI configuration for an assistant
   * @param {string} attorneyId - Attorney ID
   * @param {string} assistantId - Assistant ID
   * @returns {Promise<boolean>} Success status
   */
  async deleteAssistantConfig(attorneyId, assistantId) {
    try {
      const { error } = await supabase
        .from('assistant_ui_configs')
        .delete()
        .eq('attorney_id', attorneyId)
        .eq('assistant_id', assistantId);

      if (error) throw error;
      return true;
    } catch (error) {
      console.error('Error deleting assistant config:', error);
      return false;
    }
  }

  /**
   * Get current assistant ID for an attorney
   * @param {string} attorneyId - Attorney ID
   * @returns {Promise<string|null>} Current assistant ID
   */
  async getCurrentAssistantId(attorneyId) {
    try {
      const { data, error } = await supabase
        .from('attorneys')
        .select('current_assistant_id')
        .eq('id', attorneyId)
        .single();

      if (error) throw error;
      return data?.current_assistant_id || null;
    } catch (error) {
      console.error('Error getting current assistant ID:', error);
      return null;
    }
  }

  /**
   * Set current assistant ID for an attorney
   * @param {string} attorneyId - Attorney ID
   * @param {string} assistantId - Assistant ID to set as current
   * @returns {Promise<boolean>} Success status
   */
  async setCurrentAssistantId(attorneyId, assistantId) {
    try {
      const { error } = await supabase
        .from('attorneys')
        .update({ 
          current_assistant_id: assistantId,
          updated_at: new Date().toISOString()
        })
        .eq('id', attorneyId);

      if (error) throw error;
      return true;
    } catch (error) {
      console.error('Error setting current assistant ID:', error);
      return false;
    }
  }

  /**
   * Get UI configuration for the current assistant
   * @param {string} attorneyId - Attorney ID
   * @returns {Promise<Object|null>} Current assistant's UI configuration
   */
  async getCurrentAssistantConfig(attorneyId) {
    try {
      const currentAssistantId = await this.getCurrentAssistantId(attorneyId);
      if (!currentAssistantId) return null;

      return await this.getAssistantConfig(attorneyId, currentAssistantId);
    } catch (error) {
      console.error('Error getting current assistant config:', error);
      return null;
    }
  }

  /**
   * Switch to a different assistant and load its configuration
   * @param {string} attorneyId - Attorney ID
   * @param {string} assistantId - Assistant ID to switch to
   * @returns {Promise<Object>} The assistant's UI configuration
   */
  async switchToAssistant(attorneyId, assistantId) {
    try {
      console.log(`🔄 [AssistantUIConfig] Switching to assistant: ${assistantId}`);

      // Set as current assistant
      await this.setCurrentAssistantId(attorneyId, assistantId);

      // Get or create config for this assistant
      let config = await this.getAssistantConfig(attorneyId, assistantId);

      if (!config) {
        // Create default config if none exists
        console.log(`📝 [AssistantUIConfig] Creating default config for assistant: ${assistantId}`);
        config = await this.createDefaultConfig(attorneyId, assistantId);
      }

      console.log(`✅ [AssistantUIConfig] Successfully switched to assistant: ${assistantId}`);
      return config;
    } catch (error) {
      console.error('Error switching to assistant:', error);
      throw error;
    }
  }

  /**
   * Clear assistant-specific state to prevent data bleeding
   * @param {string} attorneyId - Attorney ID
   * @param {string} fromAssistantId - Previous assistant ID (for logging)
   * @param {string} toAssistantId - New assistant ID (for logging)
   */
  async clearAssistantState(attorneyId, fromAssistantId, toAssistantId) {
    console.log(`🧹 [AssistantUIConfig] Clearing state transition: ${fromAssistantId} → ${toAssistantId}`);

    // This method can be used by components to ensure clean state transitions
    // The actual state clearing should be handled by the components themselves
    // This is just a coordination point for logging and future enhancements

    return {
      cleared: true,
      fromAssistant: fromAssistantId,
      toAssistant: toAssistantId,
      timestamp: new Date().toISOString()
    };
  }
}

// Export singleton instance
export const assistantUIConfigService = new AssistantUIConfigService();
export default assistantUIConfigService;

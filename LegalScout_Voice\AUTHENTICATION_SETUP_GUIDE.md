# Authentication Setup Guide - UPDATED

## 🎯 **Overview**

This guide provides step-by-step instructions to configure authentication for both development and production environments using the NEW unified authentication system.

## 🔧 **Key Changes Made**

1. **Unified Authentication Service** - Consolidated all auth methods into a single, reliable service
2. **Environment-Aware Redirects** - Automatically detects dev vs production and uses appropriate URLs
3. **Enhanced Error Handling** - Better error messages and debugging capabilities
4. **Simplified Configuration** - Reduced complexity and eliminated conflicting auth systems

## 🔧 **1. Supabase Configuration**

### **A. Google OAuth Setup**

1. **Go to Supabase Dashboard**
   - Navigate to: https://supabase.com/dashboard/project/utopqxsvudgrtiwenlzl
   - Go to Authentication > Providers

2. **Configure Google Provider**
   - Enable Google provider
   - Add Google Client ID: `************-0b877bga5m3retigi7ntb3nufq6csolo.apps.googleusercontent.com`
   - Add Google Client Secret: `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`

3. **Set Redirect URLs**
   - Go to Authentication > URL Configuration
   - **Site URL**: `https://dashboard.legalscout.net` (production)
   - **Redirect URLs**: Add these URLs:
     ```
     http://localhost:5173/auth/callback
     http://localhost:3000/auth/callback
     http://127.0.0.1:5173/auth/callback
     https://dashboard.legalscout.net/auth/callback
     https://*.vercel.app/auth/callback
     ```

### **B. Google Cloud Console Setup**

1. **Go to Google Cloud Console**
   - Navigate to: https://console.cloud.google.com/
   - Select your project or create a new one

2. **Enable Google+ API**
   - Go to APIs & Services > Library
   - Search for "Google+ API" and enable it

3. **Configure OAuth Consent Screen**
   - Go to APIs & Services > OAuth consent screen
   - Add authorized domains:
     - `legalscout.net`
     - `vercel.app`
     - `localhost`

4. **Update OAuth 2.0 Client**
   - Go to APIs & Services > Credentials
   - Edit your OAuth 2.0 Client ID
   - **Authorized redirect URIs**:
     ```
     http://localhost:5173/auth/callback
     https://dashboard.legalscout.net/auth/callback
     https://utopqxsvudgrtiwenlzl.supabase.co/auth/v1/callback
     ```

## 🌍 **2. Environment Variables**

### **Development (.env)**
```bash
# Supabase Configuration
VITE_SUPABASE_URL=https://utopqxsvudgrtiwenlzl.supabase.co
VITE_SUPABASE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg5NDgwMDcsImV4cCI6MjA1NDUyNDAwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg5NDgwMDcsImV4cCI6MjA1NDUyNDAwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU

# Google OAuth
VITE_GOOGLE_CLIENT_ID=************-0b877bga5m3retigi7ntb3nufq6csolo.apps.googleusercontent.com
VITE_GOOGLE_CLIENT_SECRET=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
```

### **Production (Vercel)**
All environment variables are already configured in `vercel.json`.

## 🚀 **3. Testing Authentication**

### **Development Testing**
1. Start the development server: `npm run dev`
2. Navigate to: `http://localhost:5173`
3. Click "Sign in with Google"
4. Should redirect to Google OAuth
5. After authorization, should redirect to: `http://localhost:5173/auth/callback`
6. Should complete authentication and redirect to dashboard

### **Production Testing**
1. Deploy to Vercel
2. Navigate to: `https://dashboard.legalscout.net`
3. Click "Sign in with Google"
4. Should redirect to Google OAuth
5. After authorization, should redirect to: `https://dashboard.legalscout.net/auth/callback`
6. Should complete authentication and redirect to dashboard

## 🔍 **4. Debugging Authentication Issues**

### **Common Issues & Solutions**

1. **"Invalid redirect URI" Error**
   - Check Google Cloud Console OAuth client redirect URIs
   - Ensure exact URL match (including protocol and port)
   - Add both development and production URLs

2. **"Supabase not configured" Error**
   - Verify environment variables are loaded
   - Check browser console for environment variable values
   - Ensure VITE_ prefix for client-side variables

3. **"Authentication failed" Error**
   - Check Supabase dashboard for authentication logs
   - Verify Google OAuth provider is enabled
   - Check Google Cloud Console for API quotas

4. **Redirect Loop**
   - Clear browser cache and cookies
   - Check for conflicting authentication states
   - Verify redirect URLs don't have trailing slashes

### **Debug Tools**

1. **Browser Console Commands**
   ```javascript
   // Check unified auth service
   window.unifiedAuthService.getAuthState()
   
   // Get redirect URLs
   window.unifiedAuthService.getRedirectUrls()
   
   // Test authentication
   await window.unifiedAuthService.signInWithGoogle()
   ```

2. **Environment Check**
   ```javascript
   // Check environment variables
   console.log('Supabase URL:', import.meta.env.VITE_SUPABASE_URL)
   console.log('Google Client ID:', import.meta.env.VITE_GOOGLE_CLIENT_ID)
   ```

## ✅ **5. Verification Checklist**

### **Development Environment**
- [ ] Environment variables loaded correctly
- [ ] Google OAuth redirects to localhost callback
- [ ] Authentication completes successfully
- [ ] User redirected to dashboard
- [ ] Attorney profile created/loaded

### **Production Environment**
- [ ] Vercel environment variables configured
- [ ] Google OAuth redirects to production callback
- [ ] Authentication completes successfully
- [ ] User redirected to dashboard
- [ ] Attorney profile created/loaded

### **Cross-Environment**
- [ ] Same authentication flow works in both environments
- [ ] No hardcoded URLs or ports
- [ ] Proper error handling and user feedback
- [ ] Session persistence across page reloads

## 🔧 **6. Architecture Benefits**

The unified authentication system provides:

1. **Environment Agnostic**: Works in development and production
2. **Fallback Mechanisms**: Multiple authentication strategies
3. **Consistent API**: Single interface for all auth operations
4. **Better Error Handling**: Detailed error messages and recovery
5. **Session Management**: Proper session handling and persistence

## 📞 **7. Support**

If authentication issues persist:
1. Check browser console for detailed error logs
2. Verify all configuration steps above
3. Test with different browsers/incognito mode
4. Check Supabase dashboard authentication logs

import React, { useState, useEffect } from 'react';
import { getSupabaseClient, isSupabaseConfigured } from '../lib/supabase-fixed.js';

const SupabaseTest = () => {
  const [status, setStatus] = useState('Testing...');
  const [details, setDetails] = useState({});
  const [error, setError] = useState(null);

  useEffect(() => {
    const testSupabase = async () => {
      try {
        // Test 1: Check if Supabase is configured
        const configured = isSupabaseConfigured();
        setDetails(prev => ({ ...prev, configured }));

        if (!configured) {
          setStatus('❌ Supabase not configured');
          setError('Supabase configuration missing');
          return;
        }

        // Test 2: Get Supabase client
        setStatus('Initializing Supabase client...');
        const supabase = await getSupabaseClient();

        if (!supabase) {
          setStatus('❌ Supabase client not initialized');
          setError('Supabase client is null or undefined');
          return;
        }

        setDetails(prev => ({ ...prev, clientExists: true }));

        // Test 3: Test basic connection
        setStatus('Testing connection...');
        const { data, error: queryError } = await supabase
          .from('attorneys')
          .select('id, email, subdomain')
          .limit(5);

        if (queryError) {
          setStatus('❌ Connection failed');
          setError(queryError.message);
          setDetails(prev => ({ ...prev, queryError: queryError.message }));
          return;
        }

        // Test 4: Success
        setStatus('✅ Supabase working correctly');
        setDetails(prev => ({ 
          ...prev, 
          querySuccess: true,
          recordCount: data?.length || 0,
          sampleData: data?.slice(0, 2) || []
        }));

      } catch (err) {
        setStatus('❌ Unexpected error');
        setError(err.message);
        setDetails(prev => ({ ...prev, unexpectedError: err.message }));
      }
    };

    testSupabase();
  }, []);

  return (
    <div style={{ 
      padding: '20px', 
      fontFamily: 'monospace',
      backgroundColor: '#f5f5f5',
      minHeight: '100vh'
    }}>
      <h1>Supabase Connection Test</h1>
      
      <div style={{ 
        backgroundColor: 'white', 
        padding: '15px', 
        borderRadius: '8px',
        marginBottom: '20px',
        border: error ? '2px solid #ff4444' : '2px solid #44ff44'
      }}>
        <h2>Status: {status}</h2>
        {error && (
          <div style={{ color: '#ff4444', marginTop: '10px' }}>
            <strong>Error:</strong> {error}
          </div>
        )}
      </div>

      <div style={{ 
        backgroundColor: 'white', 
        padding: '15px', 
        borderRadius: '8px',
        marginBottom: '20px'
      }}>
        <h3>Test Details:</h3>
        <pre style={{ 
          backgroundColor: '#f8f8f8', 
          padding: '10px', 
          borderRadius: '4px',
          overflow: 'auto'
        }}>
          {JSON.stringify(details, null, 2)}
        </pre>
      </div>

      <div style={{ 
        backgroundColor: 'white', 
        padding: '15px', 
        borderRadius: '8px'
      }}>
        <h3>Environment Info:</h3>
        <ul>
          <li><strong>Mode:</strong> {import.meta.env.MODE}</li>
          <li><strong>Dev:</strong> {import.meta.env.DEV ? 'true' : 'false'}</li>
          <li><strong>VITE_SUPABASE_URL:</strong> {import.meta.env.VITE_SUPABASE_URL ? 'Set' : 'Missing'}</li>
          <li><strong>VITE_SUPABASE_KEY:</strong> {import.meta.env.VITE_SUPABASE_KEY ? 'Set' : 'Missing'}</li>
          <li><strong>VITE_SUPABASE_ANON_KEY:</strong> {import.meta.env.VITE_SUPABASE_ANON_KEY ? 'Set' : 'Missing'}</li>
        </ul>
      </div>

      <div style={{ marginTop: '20px' }}>
        <button 
          onClick={() => window.location.reload()}
          style={{
            padding: '10px 20px',
            backgroundColor: '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          Refresh Test
        </button>
        
        <button 
          onClick={() => window.location.href = '/dashboard'}
          style={{
            padding: '10px 20px',
            backgroundColor: '#28a745',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
            marginLeft: '10px'
          }}
        >
          Go to Dashboard
        </button>
      </div>
    </div>
  );
};

export default SupabaseTest;

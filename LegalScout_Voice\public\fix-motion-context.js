/**
 * Fix for Framer Motion Context Issues
 * 
 * This script fixes issues with MotionConfigContext and LayoutGroupContext
 * by ensuring they are properly defined before any other scripts load.
 */

(function() {
  console.log('[ContextFix] Setting up Motion Context fixes');

  // STEP 1: Ensure React is available globally
  if (typeof window.React === 'undefined') {
    console.log('[ContextFix] React not found, creating global object');
    window.React = {};
  }

  // STEP 2: Ensure createContext is available
  if (typeof window.React.createContext === 'undefined') {
    console.log('[ContextFix] createContext not found, creating polyfill');
    window.React.createContext = function(defaultValue) {
      console.log('[ContextFix] Using polyfill createContext');
      return {
        Provider: function(props) { return props.children || null; },
        Consumer: function(props) { return props.children ? props.children({}) : null; },
        displayName: 'MockContext',
        _currentValue: defaultValue,
        _currentValue2: defaultValue,
        _threadCount: 0,
        _defaultValue: defaultValue
      };
    };
  }

  // STEP 3: Define LayoutGroupContext globally
  window.LayoutGroupContext = {
    Provider: function(props) { return props.children || null; },
    Consumer: function(props) { return props.children ? props.children({}) : null; },
    displayName: 'LayoutGroupContext',
    _currentValue: {},
    _currentValue2: {},
    _threadCount: 0,
    _defaultValue: {}
  };

  // STEP 4: Define MotionConfigContext globally
  window.MotionConfigContext = {
    Provider: function(props) { return props.children || null; },
    Consumer: function(props) { return props.children ? props.children({}) : null; },
    displayName: 'MotionConfigContext',
    _currentValue: {},
    _currentValue2: {},
    _threadCount: 0,
    _defaultValue: {}
  };

  // STEP 5: Create mock modules for direct imports
  window.__framer_motion_LayoutGroupContext_mjs__ = {
    LayoutGroupContext: window.LayoutGroupContext,
    default: window.LayoutGroupContext
  };

  window.__framer_motion_MotionConfigContext_mjs__ = {
    MotionConfigContext: window.MotionConfigContext,
    default: window.MotionConfigContext
  };

  // STEP 6: Intercept dynamic imports
  if (typeof window.__vite__import === 'undefined') {
    window.__vite__import = function(moduleId) {
      if (moduleId.includes('LayoutGroupContext')) {
        console.log('[ContextFix] Intercepted import for LayoutGroupContext:', moduleId);
        return Promise.resolve(window.__framer_motion_LayoutGroupContext_mjs__);
      }
      
      if (moduleId.includes('MotionConfigContext')) {
        console.log('[ContextFix] Intercepted import for MotionConfigContext:', moduleId);
        return Promise.resolve(window.__framer_motion_MotionConfigContext_mjs__);
      }
      
      // For other imports, try to use the real import function
      if (typeof window.import === 'function') {
        return window.import(moduleId);
      }
      
      return Promise.reject(new Error(`[ContextFix] Cannot import module: ${moduleId}`));
    };
  }

  console.log('[ContextFix] Motion Context fixes applied successfully');
})();

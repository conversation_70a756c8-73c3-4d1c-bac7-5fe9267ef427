<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Test Authentication Fixes - LegalScout</title>
  
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 20px;
      background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
      color: #333;
      min-height: 100vh;
    }
    .container {
      max-width: 1000px;
      margin: 0 auto;
      background: white;
      padding: 30px;
      border-radius: 12px;
      box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    }
    .header {
      text-align: center;
      margin-bottom: 30px;
      color: #2c3e50;
    }
    .test-section {
      margin-bottom: 30px;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      overflow: hidden;
    }
    .test-header {
      background: #f8f9fa;
      padding: 15px 20px;
      border-bottom: 1px solid #e0e0e0;
      font-weight: 600;
      color: #2c3e50;
    }
    .test-content {
      padding: 20px;
    }
    .test-result {
      margin: 10px 0;
      padding: 15px;
      border-radius: 6px;
      font-family: 'Courier New', monospace;
      font-size: 0.9em;
    }
    .success { background: #d4edda; border: 2px solid #27ae60; color: #155724; }
    .error { background: #f8d7da; border: 2px solid #e74c3c; color: #721c24; }
    .info { background: #d1ecf1; border: 2px solid #3498db; color: #0c5460; }
    .warning { background: #fff3cd; border: 2px solid #f39c12; color: #856404; }
    
    button {
      background: #3498db;
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 6px;
      cursor: pointer;
      margin: 10px 5px;
      font-size: 1em;
      transition: background 0.3s;
    }
    button:hover { background: #2980b9; }
    
    .status-banner {
      background: #f8f9fa;
      border: 2px solid #dee2e6;
      border-radius: 8px;
      padding: 20px;
      margin: 20px 0;
      text-align: center;
    }
    
    .status-banner.success {
      background: #d4edda;
      border-color: #27ae60;
      color: #155724;
    }
    
    .status-banner.error {
      background: #f8d7da;
      border-color: #e74c3c;
      color: #721c24;
    }
    
    .code-block {
      background: #2c3e50;
      color: #ecf0f1;
      padding: 15px;
      border-radius: 6px;
      font-family: 'Courier New', monospace;
      font-size: 0.85em;
      overflow-x: auto;
      margin: 10px 0;
      white-space: pre-wrap;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>🔐 Authentication Fixes Test Suite</h1>
      <p>Comprehensive testing of all authentication fixes</p>
    </div>
    
    <div class="status-banner" id="status-banner">
      <h3>🔍 Ready to Test</h3>
      <p>Click "Run All Tests" to verify authentication fixes</p>
    </div>
    
    <div style="text-align: center; margin: 20px 0;">
      <button onclick="runAllTests()">🚀 Run All Tests</button>
      <button onclick="testSupabaseClient()">🔧 Test Supabase Client</button>
      <button onclick="testExports()">📦 Test Exports</button>
      <button onclick="testAuthFlow()">🔐 Test Auth Flow</button>
      <button onclick="clearResults()">🧹 Clear Results</button>
    </div>
    
    <!-- Supabase Client Tests -->
    <div class="test-section">
      <div class="test-header">🔧 Supabase Client Tests</div>
      <div class="test-content">
        <div id="supabase-tests-results"></div>
      </div>
    </div>

    <!-- Export Tests -->
    <div class="test-section">
      <div class="test-header">📦 Export Compatibility Tests</div>
      <div class="test-content">
        <div id="export-tests-results"></div>
      </div>
    </div>

    <!-- Authentication Flow Tests -->
    <div class="test-section">
      <div class="test-header">🔐 Authentication Flow Tests</div>
      <div class="test-content">
        <div id="auth-flow-results"></div>
      </div>
    </div>

    <!-- Error Monitoring -->
    <div class="test-section">
      <div class="test-header">🚨 Error Monitoring</div>
      <div class="test-content">
        <div id="error-monitoring-results"></div>
      </div>
    </div>
  </div>

  <script type="module">
    let testResults = {
      total: 0,
      passed: 0,
      failed: 0,
      errors: []
    };

    // Error monitoring
    window.addEventListener('error', (e) => {
      testResults.errors.push({
        type: 'JavaScript Error',
        message: e.message,
        filename: e.filename,
        lineno: e.lineno,
        timestamp: new Date().toISOString()
      });
      updateErrorMonitoring();
    });

    window.addEventListener('unhandledrejection', (e) => {
      testResults.errors.push({
        type: 'Unhandled Promise Rejection',
        message: e.reason?.message || e.reason,
        timestamp: new Date().toISOString()
      });
      updateErrorMonitoring();
    });

    function addResult(containerId, type, title, message) {
      const container = document.getElementById(containerId);
      const div = document.createElement('div');
      div.className = `test-result ${type}`;
      
      const icon = type === 'success' ? '✅' : 
                   type === 'error' ? '❌' : 
                   type === 'warning' ? '⚠️' : 'ℹ️';
      
      div.innerHTML = `
        <strong>${icon} ${title}</strong>
        <div style="margin-top: 10px;">${message}</div>
      `;
      container.appendChild(div);
    }

    function updateStatusBanner() {
      const banner = document.getElementById('status-banner');
      const successRate = testResults.total > 0 ? 
        Math.round((testResults.passed / testResults.total) * 100) : 0;
      
      if (testResults.errors.length > 0) {
        banner.className = 'status-banner error';
        banner.innerHTML = `
          <h3>❌ Tests Failed</h3>
          <p>JavaScript errors detected (${testResults.errors.length} errors)</p>
          <p><strong>Tests:</strong> ${testResults.passed}/${testResults.total} passed (${successRate}%)</p>
        `;
      } else if (testResults.failed > 0) {
        banner.className = 'status-banner error';
        banner.innerHTML = `
          <h3>⚠️ Some Tests Failed</h3>
          <p><strong>Tests:</strong> ${testResults.passed}/${testResults.total} passed (${successRate}%)</p>
        `;
      } else if (testResults.passed > 0) {
        banner.className = 'status-banner success';
        banner.innerHTML = `
          <h3>✅ All Tests Passed!</h3>
          <p><strong>Tests:</strong> ${testResults.passed}/${testResults.total} passed (${successRate}%)</p>
          <p>Authentication fixes are working correctly!</p>
        `;
      }
    }

    function updateErrorMonitoring() {
      const container = document.getElementById('error-monitoring-results');
      
      if (testResults.errors.length === 0) {
        container.innerHTML = `
          <div class="test-result success">
            <strong>✅ No Errors Detected</strong><br>
            No JavaScript errors or unhandled promise rejections detected.
          </div>
        `;
      } else {
        container.innerHTML = testResults.errors.map(error => `
          <div class="test-result error">
            <strong>❌ ${error.type}</strong><br>
            Message: ${error.message}<br>
            ${error.filename ? `File: ${error.filename}:${error.lineno}<br>` : ''}
            Time: ${new Date(error.timestamp).toLocaleTimeString()}
          </div>
        `).join('');
      }
    }

    async function runAllTests() {
      console.log('🚀 [AuthTest] Running all authentication tests...');
      clearResults();
      
      addResult('supabase-tests-results', 'info', 'Starting Tests', 'Running comprehensive authentication tests...');
      
      await testSupabaseClient();
      await testExports();
      await testAuthFlow();
      
      updateStatusBanner();
      console.log('✅ [AuthTest] All tests completed');
    }

    async function testSupabaseClient() {
      console.log('🔧 [AuthTest] Testing Supabase client...');
      
      testResults.total++;
      
      try {
        // Test dynamic import
        const supabaseModule = await import('/src/lib/supabase.js');
        
        addResult('supabase-tests-results', 'success', 'Module Import', 'Successfully imported Supabase module');
        
        // Test client initialization
        const client = await supabaseModule.getSupabaseClient();
        
        if (client) {
          addResult('supabase-tests-results', 'success', 'Client Initialization', 'Supabase client initialized successfully');
          testResults.passed++;
        } else {
          addResult('supabase-tests-results', 'error', 'Client Initialization', 'Failed to initialize Supabase client');
          testResults.failed++;
        }
        
      } catch (error) {
        addResult('supabase-tests-results', 'error', 'Supabase Client Test', `Error: ${error.message}`);
        testResults.failed++;
      }
      
      updateStatusBanner();
    }

    async function testExports() {
      console.log('📦 [AuthTest] Testing exports...');
      
      const expectedExports = [
        'getSupabaseClient',
        'getRealSupabaseClient',
        'signInWithGoogle',
        'getSession',
        'getCurrentUser',
        'signOut',
        'handleOAuthCallback',
        'getSupabaseUrl',
        'getSupabaseKey',
        'isSupabaseConfigured',
        'unifiedAuth',
        'emergencyAuth',
        'supabase'
      ];
      
      try {
        const supabaseModule = await import('/src/lib/supabase.js');
        
        let exportsPassed = 0;
        let exportsFailed = 0;
        
        for (const exportName of expectedExports) {
          testResults.total++;
          
          if (supabaseModule[exportName] !== undefined) {
            addResult('export-tests-results', 'success', `Export: ${exportName}`, `✅ Available (${typeof supabaseModule[exportName]})`);
            exportsPassed++;
            testResults.passed++;
          } else {
            addResult('export-tests-results', 'error', `Export: ${exportName}`, `❌ Missing export`);
            exportsFailed++;
            testResults.failed++;
          }
        }
        
        addResult('export-tests-results', 'info', 'Export Summary', `
          Total Exports Tested: ${expectedExports.length}
          Passed: ${exportsPassed}
          Failed: ${exportsFailed}
          Success Rate: ${Math.round((exportsPassed / expectedExports.length) * 100)}%
        `);
        
      } catch (error) {
        addResult('export-tests-results', 'error', 'Export Test Error', `Failed to test exports: ${error.message}`);
        testResults.failed++;
        testResults.total++;
      }
      
      updateStatusBanner();
    }

    async function testAuthFlow() {
      console.log('🔐 [AuthTest] Testing authentication flow...');
      
      try {
        const supabaseModule = await import('/src/lib/supabase.js');
        
        // Test configuration
        testResults.total++;
        const isConfigured = supabaseModule.isSupabaseConfigured();
        
        if (isConfigured) {
          addResult('auth-flow-results', 'success', 'Configuration Test', 'Supabase is properly configured');
          testResults.passed++;
        } else {
          addResult('auth-flow-results', 'warning', 'Configuration Test', 'Supabase configuration may be using defaults');
          testResults.failed++;
        }
        
        // Test session retrieval (should not throw errors)
        testResults.total++;
        try {
          const session = await supabaseModule.getSession();
          addResult('auth-flow-results', 'success', 'Session Test', `Session retrieval successful (${session ? 'has session' : 'no session'})`);
          testResults.passed++;
        } catch (error) {
          addResult('auth-flow-results', 'error', 'Session Test', `Session retrieval failed: ${error.message}`);
          testResults.failed++;
        }
        
        // Test user retrieval (should not throw errors)
        testResults.total++;
        try {
          const user = await supabaseModule.getCurrentUser();
          addResult('auth-flow-results', 'success', 'User Test', `User retrieval successful (${user ? 'has user' : 'no user'})`);
          testResults.passed++;
        } catch (error) {
          addResult('auth-flow-results', 'error', 'User Test', `User retrieval failed: ${error.message}`);
          testResults.failed++;
        }
        
        // Test global availability
        testResults.total++;
        if (window.emergencyAuth) {
          addResult('auth-flow-results', 'success', 'Global Auth Test', 'Emergency auth is globally available');
          testResults.passed++;
        } else {
          addResult('auth-flow-results', 'warning', 'Global Auth Test', 'Emergency auth not found globally');
          testResults.failed++;
        }
        
      } catch (error) {
        addResult('auth-flow-results', 'error', 'Auth Flow Test Error', `Failed to test auth flow: ${error.message}`);
        testResults.failed++;
        testResults.total++;
      }
      
      updateStatusBanner();
    }

    function clearResults() {
      testResults = { total: 0, passed: 0, failed: 0, errors: [] };
      
      document.getElementById('supabase-tests-results').innerHTML = '';
      document.getElementById('export-tests-results').innerHTML = '';
      document.getElementById('auth-flow-results').innerHTML = '';
      document.getElementById('error-monitoring-results').innerHTML = '';
      
      const banner = document.getElementById('status-banner');
      banner.className = 'status-banner';
      banner.innerHTML = `
        <h3>🔍 Ready to Test</h3>
        <p>Click "Run All Tests" to verify authentication fixes</p>
      `;
    }

    // Initialize error monitoring
    updateErrorMonitoring();

    // Make test functions globally available
    window.runAllTests = runAllTests;
    window.testSupabaseClient = testSupabaseClient;
    window.testExports = testExports;
    window.testAuthFlow = testAuthFlow;
    window.clearResults = clearResults;

    console.log('🔐 [AuthTest] Test suite loaded and ready');
  </script>
</body>
</html>

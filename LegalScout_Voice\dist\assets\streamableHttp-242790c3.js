import{A as E,U as f,B as p,C as g,F as I,G as O,J as y,H as P}from"./index-27efa71d.js";class R extends TransformStream{constructor({onError:e,onRetry:t,onComment:r}={}){let o;super({start(s){o=E({onEvent:i=>{s.enqueue(i)},onError(i){e==="terminate"?s.error(i):typeof e=="function"&&e(i)},onRetry:t,onComment:r})},transform(s){o.feed(s)}})}}const b={initialReconnectionDelay:1e3,maxReconnectionDelay:3e4,reconnectionDelayGrowFactor:1.5,maxRetries:2};class S extends Error{constructor(e,t){super(`Streamable HTTP error: ${t}`),this.code=e}}class A{constructor(e,t){var r;this._url=e,this._resourceMetadataUrl=void 0,this._requestInit=t?.requestInit,this._authProvider=t?.authProvider,this._sessionId=t?.sessionId,this._reconnectionOptions=(r=t?.reconnectionOptions)!==null&&r!==void 0?r:b}async _authThenStart(){var e;if(!this._authProvider)throw new f("No auth provider");let t;try{t=await p(this._authProvider,{serverUrl:this._url,resourceMetadataUrl:this._resourceMetadataUrl})}catch(r){throw(e=this.onerror)===null||e===void 0||e.call(this,r),r}if(t!=="AUTHORIZED")throw new f;return await this._startOrAuthSse({resumptionToken:void 0})}async _commonHeaders(){var e;const t={};if(this._authProvider){const r=await this._authProvider.tokens();r&&(t.Authorization=`Bearer ${r.access_token}`)}return this._sessionId&&(t["mcp-session-id"]=this._sessionId),new Headers({...t,...(e=this._requestInit)===null||e===void 0?void 0:e.headers})}async _startOrAuthSse(e){var t,r;const{resumptionToken:o}=e;try{const s=await this._commonHeaders();s.set("Accept","text/event-stream"),o&&s.set("last-event-id",o);const i=await fetch(this._url,{method:"GET",headers:s,signal:(t=this._abortController)===null||t===void 0?void 0:t.signal});if(!i.ok){if(i.status===401&&this._authProvider)return await this._authThenStart();if(i.status===405)return;throw new S(i.status,`Failed to open SSE stream: ${i.statusText}`)}this._handleSseStream(i.body,e)}catch(s){throw(r=this.onerror)===null||r===void 0||r.call(this,s),s}}_getNextReconnectionDelay(e){const t=this._reconnectionOptions.initialReconnectionDelay,r=this._reconnectionOptions.reconnectionDelayGrowFactor,o=this._reconnectionOptions.maxReconnectionDelay;return Math.min(t*Math.pow(r,e),o)}_scheduleReconnection(e,t=0){var r;const o=this._reconnectionOptions.maxRetries;if(o>0&&t>=o){(r=this.onerror)===null||r===void 0||r.call(this,new Error(`Maximum reconnection attempts (${o}) exceeded.`));return}const s=this._getNextReconnectionDelay(t);setTimeout(()=>{this._startOrAuthSse(e).catch(i=>{var d;(d=this.onerror)===null||d===void 0||d.call(this,new Error(`Failed to reconnect SSE stream: ${i instanceof Error?i.message:String(i)}`)),this._scheduleReconnection(e,t+1)})},s)}_handleSseStream(e,t){if(!e)return;const{onresumptiontoken:r,replayMessageId:o}=t;let s;(async()=>{var d,u,_,n;try{const v=e.pipeThrough(new TextDecoderStream).pipeThrough(new R).getReader();for(;;){const{value:c,done:m}=await v.read();if(m)break;if(c.id&&(s=c.id,r?.(c.id)),!c.event||c.event==="message")try{const a=y.parse(JSON.parse(c.data));o!==void 0&&P(a)&&(a.id=o),(d=this.onmessage)===null||d===void 0||d.call(this,a)}catch(a){(u=this.onerror)===null||u===void 0||u.call(this,a)}}}catch(v){if((_=this.onerror)===null||_===void 0||_.call(this,new Error(`SSE stream disconnected: ${v}`)),this._abortController&&!this._abortController.signal.aborted&&s!==void 0)try{this._scheduleReconnection({resumptionToken:s,onresumptiontoken:r,replayMessageId:o},0)}catch(c){(n=this.onerror)===null||n===void 0||n.call(this,new Error(`Failed to reconnect: ${c instanceof Error?c.message:String(c)}`))}}})()}async start(){if(this._abortController)throw new Error("StreamableHTTPClientTransport already started! If using Client class, note that connect() calls start() automatically.");this._abortController=new AbortController}async finishAuth(e){if(!this._authProvider)throw new f("No auth provider");if(await p(this._authProvider,{serverUrl:this._url,authorizationCode:e,resourceMetadataUrl:this._resourceMetadataUrl})!=="AUTHORIZED")throw new f("Failed to authorize")}async close(){var e,t;(e=this._abortController)===null||e===void 0||e.abort(),(t=this.onclose)===null||t===void 0||t.call(this)}async send(e,t){var r,o,s;try{const{resumptionToken:i,onresumptiontoken:d}=t||{};if(i){this._startOrAuthSse({resumptionToken:i,replayMessageId:g(e)?e.id:void 0}).catch(l=>{var h;return(h=this.onerror)===null||h===void 0?void 0:h.call(this,l)});return}const u=await this._commonHeaders();u.set("content-type","application/json"),u.set("accept","application/json, text/event-stream");const _={...this._requestInit,method:"POST",headers:u,body:JSON.stringify(e),signal:(r=this._abortController)===null||r===void 0?void 0:r.signal},n=await fetch(this._url,_),v=n.headers.get("mcp-session-id");if(v&&(this._sessionId=v),!n.ok){if(n.status===401&&this._authProvider){if(this._resourceMetadataUrl=I(n),await p(this._authProvider,{serverUrl:this._url,resourceMetadataUrl:this._resourceMetadataUrl})!=="AUTHORIZED")throw new f;return this.send(e)}const l=await n.text().catch(()=>null);throw new Error(`Error POSTing to endpoint (HTTP ${n.status}): ${l}`)}if(n.status===202){O(e)&&this._startOrAuthSse({resumptionToken:void 0}).catch(l=>{var h;return(h=this.onerror)===null||h===void 0?void 0:h.call(this,l)});return}const m=(Array.isArray(e)?e:[e]).filter(l=>"method"in l&&"id"in l&&l.id!==void 0).length>0,a=n.headers.get("content-type");if(m)if(a?.includes("text/event-stream"))this._handleSseStream(n.body,{onresumptiontoken:d});else if(a?.includes("application/json")){const l=await n.json(),h=Array.isArray(l)?l.map(w=>y.parse(w)):[y.parse(l)];for(const w of h)(o=this.onmessage)===null||o===void 0||o.call(this,w)}else throw new S(-1,`Unexpected content type: ${a}`)}catch(i){throw(s=this.onerror)===null||s===void 0||s.call(this,i),i}}get sessionId(){return this._sessionId}async terminateSession(){var e,t;if(this._sessionId)try{const r=await this._commonHeaders(),o={...this._requestInit,method:"DELETE",headers:r,signal:(e=this._abortController)===null||e===void 0?void 0:e.signal},s=await fetch(this._url,o);if(!s.ok&&s.status!==405)throw new S(s.status,`Failed to terminate session: ${s.statusText}`);this._sessionId=void 0}catch(r){throw(t=this.onerror)===null||t===void 0||t.call(this,r),r}}}export{A as StreamableHTTPClientTransport,S as StreamableHTTPError};

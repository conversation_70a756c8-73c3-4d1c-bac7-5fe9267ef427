#!/usr/bin/env node

/**
 * Test Assistant Images Integration
 * Verifies that assistant images work in the dropdown and update the dashboard header/preview
 */

console.log('🧪 Testing Assistant Images Integration');
console.log('=====================================');

// Mock the assistant assignment service behavior
class MockAssistantUIConfigService {
  constructor() {
    // Mock UI configs with assistant images
    this.configs = new Map([
      ['damon-attorney-id-cd0b44b7-397e-410d-8835-ce9c3ba584b2', {
        assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2',
        attorney_id: 'damon-attorney-id',
        firm_name: 'LegalScout',
        logo_url: '/PRIMARY CLEAR.png',
        assistant_image_url: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8Y2lyY2xlIGN4PSIxNiIgY3k9IjE2IiByPSIxNiIgZmlsbD0iIzRCNzRBQSIvPgogIDx0ZXh0IHg9IjE2IiB5PSIyMCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZmlsbD0id2hpdGUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxMiIgZm9udC13ZWlnaHQ9ImJvbGQiPkRMPC90ZXh0Pgo8L3N2Zz4K',
        primary_color: '#4B74AA',
        voice_provider: '11labs',
        voice_id: 'sarah'
      }],
      ['damon-attorney-id-assistant-2', {
        assistant_id: 'assistant-2',
        attorney_id: 'damon-attorney-id',
        firm_name: 'LegalScout',
        logo_url: '/PRIMARY CLEAR.png',
        assistant_image_url: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8Y2lyY2xlIGN4PSIxNiIgY3k9IjE2IiByPSIxNiIgZmlsbD0iIzEwYjk4MSIvPgogIDx0ZXh0IHg9IjE2IiB5PSIyMCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZmlsbD0id2hpdGUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxMiIgZm9udC13ZWlnaHQ9ImJvbGQiPkEyPC90ZXh0Pgo8L3N2Zz4K',
        primary_color: '#10b981',
        voice_provider: 'openai',
        voice_id: 'echo'
      }]
    ]);
  }

  async getAssistantConfig(attorneyId, assistantId) {
    const key = `${attorneyId}-${assistantId}`;
    const config = this.configs.get(key);
    console.log(`🔍 Getting config for ${key}:`, config ? 'Found' : 'Not found');
    return config || null;
  }

  async saveAssistantConfig(attorneyId, assistantId, config) {
    const key = `${attorneyId}-${assistantId}`;
    this.configs.set(key, config);
    console.log(`💾 Saved config for ${key}`);
    return config;
  }
}

// Mock Vapi service
class MockVapiService {
  constructor() {
    this.assistants = [
      { 
        id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2', 
        name: 'Damon Legal Assistant',
        toolIds: ['tool1', 'tool2']
      },
      { 
        id: 'assistant-2', 
        name: 'Damon Secondary Assistant',
        toolIds: ['tool3']
      }
    ];
  }

  async getAllAssistants() {
    return this.assistants;
  }
}

async function testAssistantImageGeneration() {
  console.log('\n🔍 Test 1: Assistant Image Generation');
  console.log('====================================');

  // Test the image generation function
  const getAssistantImageUrl = (assistant) => {
    // Check if assistant has a custom image in config
    if (assistant.configData?.assistant_image_url) {
      return assistant.configData.assistant_image_url;
    }
    
    // Generate a default avatar based on assistant name/ID
    const name = assistant.name || 'Assistant';
    const initials = name.split(' ').map(word => word[0]).join('').toUpperCase().slice(0, 2);
    
    // Create a simple colored avatar with initials
    return `data:image/svg+xml;base64,${Buffer.from(`
      <svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
        <circle cx="16" cy="16" r="16" fill="#4B74AA"/>
        <text x="16" y="20" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">${initials}</text>
      </svg>
    `).toString('base64')}`;
  };

  const testCases = [
    { name: 'Damon Legal Assistant', expected: 'DL' },
    { name: 'Robert Network Attorney', expected: 'RN' },
    { name: 'Assistant', expected: 'AS' },
    { name: '', expected: 'AS' }
  ];

  let allTestsPassed = true;

  for (const testCase of testCases) {
    console.log(`\n👤 Testing: "${testCase.name}"`);
    
    const assistant = { name: testCase.name };
    const imageUrl = getAssistantImageUrl(assistant);
    
    // Decode the SVG to check initials
    const base64Part = imageUrl.split('base64,')[1];
    const svgContent = Buffer.from(base64Part, 'base64').toString();
    
    const initialsMatch = svgContent.match(/>(.*?)<\/text>/);
    const actualInitials = initialsMatch ? initialsMatch[1] : 'NOT_FOUND';
    
    console.log(`   📊 Expected initials: ${testCase.expected}`);
    console.log(`   📊 Actual initials: ${actualInitials}`);
    
    if (actualInitials === testCase.expected) {
      console.log('   ✅ Image generation correct');
    } else {
      console.log('   ❌ Image generation failed');
      allTestsPassed = false;
    }
  }

  return allTestsPassed;
}

async function testConfigLoadingWithImages() {
  console.log('\n🔍 Test 2: Config Loading with Images');
  console.log('=====================================');

  const configService = new MockAssistantUIConfigService();
  const vapiService = new MockVapiService();

  // Simulate loading assistants with configs
  const assistants = await vapiService.getAllAssistants();
  
  const assistantsWithConfigs = await Promise.all(
    assistants.map(async (assistant) => {
      const config = await configService.getAssistantConfig(
        'damon-attorney-id', 
        assistant.id
      );
      return {
        ...assistant,
        hasConfig: !!config,
        configData: config
      };
    })
  );

  console.log(`\n📋 Loaded ${assistantsWithConfigs.length} assistants with configs:`);

  let allTestsPassed = true;

  assistantsWithConfigs.forEach(assistant => {
    console.log(`\n🤖 ${assistant.name} (${assistant.id.substring(0, 8)}...)`);
    console.log(`   📊 Has config: ${assistant.hasConfig}`);
    
    if (assistant.configData?.assistant_image_url) {
      console.log(`   🖼️  Has custom image: ✅`);
      console.log(`   🎨 Image type: ${assistant.configData.assistant_image_url.startsWith('data:') ? 'Data URI' : 'URL'}`);
    } else {
      console.log(`   🖼️  Has custom image: ❌ (will use generated)`);
    }
    
    if (assistant.configData?.primary_color) {
      console.log(`   🎨 Primary color: ${assistant.configData.primary_color}`);
    }
    
    if (assistant.configData?.voice_provider && assistant.configData?.voice_id) {
      console.log(`   🎤 Voice: ${assistant.configData.voice_provider}/${assistant.configData.voice_id}`);
    }
  });

  return allTestsPassed;
}

async function testImageUpdateFlow() {
  console.log('\n🔍 Test 3: Image Update Flow');
  console.log('============================');

  const configService = new MockAssistantUIConfigService();

  // Simulate uploading a new image
  const newImageData = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';
  
  console.log('\n📤 Simulating image upload...');
  
  // Save new image to config
  const updatedConfig = await configService.saveAssistantConfig(
    'damon-attorney-id',
    'cd0b44b7-397e-410d-8835-ce9c3ba584b2',
    {
      assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2',
      attorney_id: 'damon-attorney-id',
      assistant_image_url: newImageData,
      primary_color: '#4B74AA'
    }
  );

  console.log('✅ Image saved to config');

  // Verify the image was saved
  const retrievedConfig = await configService.getAssistantConfig(
    'damon-attorney-id',
    'cd0b44b7-397e-410d-8835-ce9c3ba584b2'
  );

  console.log('\n🔍 Verifying saved image...');
  
  if (retrievedConfig?.assistant_image_url === newImageData) {
    console.log('✅ Image correctly saved and retrieved');
    return true;
  } else {
    console.log('❌ Image save/retrieve failed');
    console.log(`   Expected: ${newImageData.substring(0, 50)}...`);
    console.log(`   Actual: ${retrievedConfig?.assistant_image_url?.substring(0, 50) || 'null'}...`);
    return false;
  }
}

async function testDropdownIntegration() {
  console.log('\n🔍 Test 4: Dropdown Integration');
  console.log('===============================');

  // Simulate the dropdown behavior
  const mockDropdownState = {
    availableAssistants: [
      {
        id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2',
        name: 'Damon Legal Assistant',
        hasConfig: true,
        configData: {
          assistant_image_url: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiPjwvc3ZnPg=='
        }
      },
      {
        id: 'assistant-2',
        name: 'Secondary Assistant',
        hasConfig: false,
        configData: null
      }
    ],
    currentAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2'
  };

  console.log('\n📋 Testing dropdown image display...');

  mockDropdownState.availableAssistants.forEach(assistant => {
    console.log(`\n🤖 ${assistant.name}`);
    
    // Test image URL generation (same logic as in component)
    let imageUrl;
    if (assistant.configData?.assistant_image_url) {
      imageUrl = assistant.configData.assistant_image_url;
      console.log('   🖼️  Using custom image from config');
    } else {
      // Generate default image
      const name = assistant.name || 'Assistant';
      const initials = name.split(' ').map(word => word[0]).join('').toUpperCase().slice(0, 2);
      imageUrl = `data:image/svg+xml;base64,${Buffer.from(`<svg>${initials}</svg>`).toString('base64')}`;
      console.log(`   🖼️  Generated default image with initials: ${initials}`);
    }
    
    console.log(`   📊 Image URL type: ${imageUrl.startsWith('data:') ? 'Data URI' : 'External URL'}`);
    console.log(`   📊 Image URL length: ${imageUrl.length} chars`);
  });

  console.log('\n✅ Dropdown integration test completed');
  return true;
}

async function runAllTests() {
  console.log('🚀 Starting Assistant Images Integration Tests...\n');

  const results = {
    imageGeneration: false,
    configLoading: false,
    imageUpdate: false,
    dropdownIntegration: false
  };

  // Test 1: Image Generation
  results.imageGeneration = await testAssistantImageGeneration();

  // Test 2: Config Loading
  results.configLoading = await testConfigLoadingWithImages();

  // Test 3: Image Update Flow
  results.imageUpdate = await testImageUpdateFlow();

  // Test 4: Dropdown Integration
  results.dropdownIntegration = await testDropdownIntegration();

  // Summary
  console.log('\n📊 Test Results Summary');
  console.log('======================');

  const tests = [
    { name: 'Image Generation', result: results.imageGeneration },
    { name: 'Config Loading', result: results.configLoading },
    { name: 'Image Update Flow', result: results.imageUpdate },
    { name: 'Dropdown Integration', result: results.dropdownIntegration }
  ];

  tests.forEach(test => {
    const status = test.result ? '✅ PASS' : '❌ FAIL';
    console.log(`${status} ${test.name}`);
  });

  const passCount = tests.filter(t => t.result).length;
  console.log(`\n🎯 Overall: ${passCount}/${tests.length} tests passed`);

  if (passCount === tests.length) {
    console.log('🎉 All tests passed! Assistant images integration is working correctly.');
    console.log('\n💡 Next Steps:');
    console.log('1. Test in the actual dashboard');
    console.log('2. Upload an assistant image in the Appearance tab');
    console.log('3. Switch between assistants to see images update');
    console.log('4. Verify images appear in dropdown and dashboard header');
  } else {
    console.log('⚠️  Some tests failed. Check the implementation.');
  }

  return passCount === tests.length;
}

// Run the tests
runAllTests().then(success => {
  process.exit(success ? 0 : 1);
}).catch(error => {
  console.error('❌ Test runner failed:', error);
  process.exit(1);
});

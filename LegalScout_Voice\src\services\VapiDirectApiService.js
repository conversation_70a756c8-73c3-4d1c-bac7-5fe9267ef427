/**
 * Vapi Direct API Service
 * 
 * This service provides direct API access to Vapi when MCP doesn't return complete data.
 * Used as a fallback to ensure we get all assistant configuration fields.
 */

class VapiDirectApiService {
  constructor() {
    this.apiUrl = 'https://api.vapi.ai';
    // FIXED: Use proper API key resolution with fallback chain
    this.apiKey = this.getApiKey();
  }

  /**
   * Get the correct API key for server operations
   * Priority: VITE_VAPI_SECRET_KEY > VITE_VAPI_PRIVATE_KEY > hardcoded fallback
   */
  getApiKey() {
    // Try environment variables in order of preference
    const secretKey = import.meta.env.VITE_VAPI_SECRET_KEY ||
                     import.meta.env.VITE_VAPI_PRIVATE_KEY ||
                     window.VITE_VAPI_SECRET_KEY ||
                     window.VITE_VAPI_PRIVATE_KEY;

    if (secretKey) {
      console.log('[VapiDirectApiService] Using environment API key:', secretKey.substring(0, 8) + '...');
      return secretKey;
    }

    // Fallback to hardcoded key
    const fallbackKey = '6734febc-fc65-4669-93b0-929b31ff6564';
    console.log('[VapiDirectApiService] Using fallback API key:', fallbackKey.substring(0, 8) + '...');
    return fallbackKey;
  }

  /**
   * Get complete assistant data using direct API
   * @param {string} assistantId - Assistant ID
   * @returns {Promise<Object>} Complete assistant data
   */
  async getAssistant(assistantId) {
    try {
      const response = await fetch(`${this.apiUrl}/assistant/${assistantId}`, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const assistant = await response.json();
      
      console.log('🔍 [VapiDirectApiService] Retrieved complete assistant data:', {
        id: assistant.id,
        name: assistant.name,
        hasFirstMessage: !!assistant.firstMessage,
        hasInstructions: !!assistant.model?.messages?.[0]?.content,
        voice: assistant.voice,
        model: assistant.model?.model
      });

      return assistant;
    } catch (error) {
      console.error('❌ [VapiDirectApiService] Error getting assistant:', error);
      throw error;
    }
  }

  /**
   * Update assistant using direct API
   * @param {string} assistantId - Assistant ID
   * @param {Object} updateData - Data to update
   * @returns {Promise<Object>} Updated assistant data
   */
  async updateAssistant(assistantId, updateData) {
    try {
      console.log('🔧 [VapiDirectApiService] Updating assistant:', {
        assistantId: assistantId.substring(0, 8) + '...',
        updateData
      });

      const response = await fetch(`${this.apiUrl}/assistant/${assistantId}`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(updateData)
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      const updatedAssistant = await response.json();
      
      console.log('✅ [VapiDirectApiService] Assistant updated successfully');
      return updatedAssistant;
    } catch (error) {
      console.error('❌ [VapiDirectApiService] Error updating assistant:', error);
      throw error;
    }
  }

  /**
   * Get assistant with fallback to MCP if direct API fails
   * @param {string} assistantId - Assistant ID
   * @param {Object} mcpService - MCP service instance for fallback
   * @returns {Promise<Object>} Assistant data
   */
  async getAssistantWithFallback(assistantId, mcpService = null) {
    try {
      // Try direct API first for complete data
      return await this.getAssistant(assistantId);
    } catch (error) {
      console.warn('🔄 [VapiDirectApiService] Direct API failed, trying MCP fallback:', error.message);
      
      if (mcpService) {
        try {
          return await mcpService.getAssistant(assistantId);
        } catch (mcpError) {
          console.error('❌ [VapiDirectApiService] Both direct API and MCP failed:', mcpError);
          throw new Error(`Both direct API and MCP failed: ${error.message}, ${mcpError.message}`);
        }
      } else {
        throw error;
      }
    }
  }

  /**
   * Merge MCP data with direct API data to get complete configuration
   * @param {string} assistantId - Assistant ID
   * @param {Object} mcpService - MCP service instance
   * @returns {Promise<Object>} Complete assistant data
   */
  async getCompleteAssistantData(assistantId, mcpService = null) {
    try {
      // Get data from both sources
      const [directData, mcpData] = await Promise.allSettled([
        this.getAssistant(assistantId),
        mcpService ? mcpService.getAssistant(assistantId) : Promise.resolve(null)
      ]);

      // Use direct API data as primary source (more complete)
      if (directData.status === 'fulfilled') {
        console.log('✅ [VapiDirectApiService] Using direct API data (complete)');
        return directData.value;
      }

      // Fallback to MCP data if direct API fails
      if (mcpData.status === 'fulfilled' && mcpData.value) {
        console.log('⚠️ [VapiDirectApiService] Using MCP data (may be incomplete)');
        return mcpData.value;
      }

      throw new Error('Both direct API and MCP failed to retrieve assistant data');
    } catch (error) {
      console.error('❌ [VapiDirectApiService] Error getting complete assistant data:', error);
      throw error;
    }
  }
}

// Create singleton instance
export const vapiDirectApiService = new VapiDirectApiService();
export default vapiDirectApiService;

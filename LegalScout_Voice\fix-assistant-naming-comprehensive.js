/**
 * Comprehensive Fix for Assistant Naming Issue
 * 
 * Problem Analysis:
 * 1. Wrong assistant config record exists with assistant_id = attorney_id (87756a2c-a398-43f2-889a-b8815684df71)
 * 2. Correct assistant (1d3471b7-8694-4844-b3ef-e05720693efc) has old name "LegalScout"
 * 3. System created new assistant instead of updating existing one
 * 4. Need to clean up wrong records and fix correct ones
 */

import { createClient } from '@supabase/supabase-js';

// Configuration
const SUPABASE_URL = 'https://utopqxsvudgrtiwenlzl.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg5NDgwMDcsImV4cCI6MjA1NDUyNDAwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU';
const VAPI_PRIVATE_KEY = '6734febc-fc65-4669-93b0-929b31ff6564';
const VAPI_API_URL = 'https://api.vapi.ai';

// Correct IDs
const CORRECT_ATTORNEY_ID = '87756a2c-a398-43f2-889a-b8815684df71';
const CORRECT_ASSISTANT_ID = '1d3471b7-8694-4844-b3ef-e05720693efc';
const WRONG_ASSISTANT_ID = '87756a2c-a398-43f2-889a-b8815684df71'; // This is actually the attorney ID!
const DESIRED_ASSISTANT_NAME = "Damon's Assistant";

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

async function main() {
  console.log('🔧 Starting Comprehensive Assistant Naming Fix...\n');

  try {
    // Step 1: Verify the correct assistant exists in Vapi
    console.log('📡 Step 1: Verifying Correct Assistant in Vapi');
    const vapiResponse = await fetch(`${VAPI_API_URL}/assistant/${CORRECT_ASSISTANT_ID}`, {
      headers: {
        'Authorization': `Bearer ${VAPI_PRIVATE_KEY}`,
        'Content-Type': 'application/json'
      }
    });

    if (!vapiResponse.ok) {
      throw new Error(`Vapi assistant not found: ${vapiResponse.status} ${vapiResponse.statusText}`);
    }

    const vapiAssistant = await vapiResponse.json();
    console.log('✅ Correct Vapi Assistant Found:', {
      id: vapiAssistant.id,
      name: vapiAssistant.name,
      updatedAt: vapiAssistant.updatedAt
    });

    // Step 2: Clean up wrong assistant config records
    console.log('\n🧹 Step 2: Cleaning Up Wrong Assistant Config Records');
    
    // Delete the wrong record where assistant_id = attorney_id
    const { error: deleteWrongError } = await supabase
      .from('assistant_ui_configs')
      .delete()
      .eq('assistant_id', WRONG_ASSISTANT_ID);

    if (deleteWrongError) {
      console.warn('⚠️ Error deleting wrong config:', deleteWrongError.message);
    } else {
      console.log('✅ Deleted wrong assistant config record');
    }

    // Step 3: Update the correct assistant name in Vapi
    console.log('\n🔄 Step 3: Updating Assistant Name in Vapi');
    
    if (vapiAssistant.name !== DESIRED_ASSISTANT_NAME) {
      console.log(`Updating Vapi assistant name from "${vapiAssistant.name}" to "${DESIRED_ASSISTANT_NAME}"`);
      
      const updateResponse = await fetch(`${VAPI_API_URL}/assistant/${CORRECT_ASSISTANT_ID}`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${VAPI_PRIVATE_KEY}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          name: DESIRED_ASSISTANT_NAME
        })
      });

      if (!updateResponse.ok) {
        throw new Error(`Failed to update Vapi assistant: ${updateResponse.status} ${updateResponse.statusText}`);
      }

      const updatedAssistant = await updateResponse.json();
      console.log('✅ Updated Vapi assistant name:', updatedAssistant.name);
    } else {
      console.log('✅ Vapi assistant name is already correct');
    }

    // Step 4: Update the correct assistant config in database
    console.log('\n💾 Step 4: Updating Correct Assistant Config in Database');
    
    const { data: existingConfig, error: fetchError } = await supabase
      .from('assistant_ui_configs')
      .select('*')
      .eq('assistant_id', CORRECT_ASSISTANT_ID)
      .eq('attorney_id', CORRECT_ATTORNEY_ID)
      .single();

    if (fetchError && fetchError.code !== 'PGRST116') {
      throw new Error(`Error fetching existing config: ${fetchError.message}`);
    }

    if (existingConfig) {
      // Update existing config
      const { error: updateError } = await supabase
        .from('assistant_ui_configs')
        .update({
          assistant_name: DESIRED_ASSISTANT_NAME,
          updated_at: new Date().toISOString()
        })
        .eq('assistant_id', CORRECT_ASSISTANT_ID)
        .eq('attorney_id', CORRECT_ATTORNEY_ID);

      if (updateError) {
        throw new Error(`Error updating config: ${updateError.message}`);
      }

      console.log('✅ Updated existing assistant config');
    } else {
      // Create new config if it doesn't exist
      const { error: createError } = await supabase
        .from('assistant_ui_configs')
        .insert({
          attorney_id: CORRECT_ATTORNEY_ID,
          assistant_id: CORRECT_ASSISTANT_ID,
          assistant_name: DESIRED_ASSISTANT_NAME,
          firm_name: 'LegalScout',
          primary_color: '#2563eb',
          secondary_color: '#1e40af',
          button_color: '#3b82f6',
          voice_provider: 'openai',
          voice_id: 'echo',
          ai_model: 'gpt-4o',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });

      if (createError) {
        throw new Error(`Error creating config: ${createError.message}`);
      }

      console.log('✅ Created new assistant config');
    }

    // Step 5: Verify attorney record is correct
    console.log('\n👨‍💼 Step 5: Verifying Attorney Record');
    
    const { data: attorney, error: attorneyError } = await supabase
      .from('attorneys')
      .select('*')
      .eq('id', CORRECT_ATTORNEY_ID)
      .single();

    if (attorneyError) {
      throw new Error(`Error fetching attorney: ${attorneyError.message}`);
    }

    console.log('✅ Attorney Record:', {
      id: attorney.id,
      email: attorney.email,
      firm_name: attorney.firm_name,
      vapi_assistant_id: attorney.vapi_assistant_id,
      current_assistant_id: attorney.current_assistant_id
    });

    // Update attorney record if needed
    if (attorney.current_assistant_id !== CORRECT_ASSISTANT_ID) {
      const { error: updateAttorneyError } = await supabase
        .from('attorneys')
        .update({
          current_assistant_id: CORRECT_ASSISTANT_ID,
          updated_at: new Date().toISOString()
        })
        .eq('id', CORRECT_ATTORNEY_ID);

      if (updateAttorneyError) {
        console.warn('⚠️ Error updating attorney:', updateAttorneyError.message);
      } else {
        console.log('✅ Updated attorney current_assistant_id');
      }
    }

    // Step 6: Final verification
    console.log('\n✅ Step 6: Final Verification');
    
    // Check Vapi again
    const verifyVapiResponse = await fetch(`${VAPI_API_URL}/assistant/${CORRECT_ASSISTANT_ID}`, {
      headers: {
        'Authorization': `Bearer ${VAPI_PRIVATE_KEY}`,
        'Content-Type': 'application/json'
      }
    });

    const verifiedVapiAssistant = await verifyVapiResponse.json();
    
    // Check database again
    const { data: verifiedConfig } = await supabase
      .from('assistant_ui_configs')
      .select('assistant_name, firm_name')
      .eq('attorney_id', CORRECT_ATTORNEY_ID)
      .eq('assistant_id', CORRECT_ASSISTANT_ID)
      .single();

    console.log('🎉 Final State:');
    console.log(`  Assistant ID: ${CORRECT_ASSISTANT_ID}`);
    console.log(`  Vapi Assistant Name: "${verifiedVapiAssistant.name}"`);
    console.log(`  Database Assistant Name: "${verifiedConfig?.assistant_name}"`);
    console.log(`  Database Firm Name: "${verifiedConfig?.firm_name}"`);

    if (verifiedVapiAssistant.name === verifiedConfig?.assistant_name && 
        verifiedConfig?.assistant_name === DESIRED_ASSISTANT_NAME) {
      console.log('✅ SUCCESS: Assistant naming is now fixed and synchronized!');
      
      // Step 7: Clean up any remaining wrong records
      console.log('\n🧹 Step 7: Final Cleanup');
      
      const { data: allConfigs } = await supabase
        .from('assistant_ui_configs')
        .select('assistant_id, assistant_name, attorney_id')
        .eq('attorney_id', CORRECT_ATTORNEY_ID);

      console.log('📋 All configs for this attorney:');
      allConfigs?.forEach(config => {
        console.log(`  - Assistant ID: ${config.assistant_id}, Name: "${config.assistant_name}"`);
      });

      // Delete any configs that don't use the correct assistant ID
      const wrongConfigs = allConfigs?.filter(config => config.assistant_id !== CORRECT_ASSISTANT_ID);
      if (wrongConfigs && wrongConfigs.length > 0) {
        console.log(`🗑️ Deleting ${wrongConfigs.length} wrong config(s)...`);
        
        for (const wrongConfig of wrongConfigs) {
          const { error: deleteError } = await supabase
            .from('assistant_ui_configs')
            .delete()
            .eq('assistant_id', wrongConfig.assistant_id)
            .eq('attorney_id', CORRECT_ATTORNEY_ID);

          if (deleteError) {
            console.warn(`⚠️ Error deleting config ${wrongConfig.assistant_id}:`, deleteError.message);
          } else {
            console.log(`✅ Deleted wrong config: ${wrongConfig.assistant_id}`);
          }
        }
      }

    } else {
      console.log('⚠️ WARNING: Names are still not synchronized');
      console.log('  Expected:', DESIRED_ASSISTANT_NAME);
      console.log('  Vapi:', verifiedVapiAssistant.name);
      console.log('  Database:', verifiedConfig?.assistant_name);
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
    process.exit(1);
  }
}

// Run the script
main().then(() => {
  console.log('\n🏁 Comprehensive assistant naming fix completed!');
  console.log('\n📝 Summary of changes:');
  console.log('  1. Deleted wrong assistant config record (assistant_id = attorney_id)');
  console.log('  2. Updated Vapi assistant name to "Damon\'s Assistant"');
  console.log('  3. Updated database assistant_name to "Damon\'s Assistant"');
  console.log('  4. Verified attorney record points to correct assistant');
  console.log('  5. Cleaned up any remaining wrong records');
  console.log('\n🎯 The assistant dropdown and header should now display "Damon\'s Assistant"');
  process.exit(0);
}).catch(error => {
  console.error('💥 Script failed:', error);
  process.exit(1);
});

# Row Level Security (RLS) Policies

This document describes all Row Level Security policies implemented in LegalScout's Supabase database to ensure proper data isolation and security.

## Overview

Row Level Security (RLS) is enabled on all tables containing sensitive data to ensure that:
- Users can only access data they own or are authorized to see
- Attorneys can only access their own profiles and associated data
- Call records and consultations are properly isolated
- Storage buckets have appropriate access controls

## Core Security Principles

1. **User-Based Isolation**: All data is tied to `auth.uid()` through the `attorneys` table
2. **Attorney-Centric Access**: Most data access is controlled through attorney ownership
3. **Assistant-Level Permissions**: UI configs and subdomains are scoped to specific assistants
4. **Public Read for Subdomains**: Subdomain lookup is publicly readable for routing

## Table-by-Table RLS Policies

### 1. attorneys Table

**RLS Status**: ✅ Enabled

```sql
-- Policy: Users can access their own attorney records
CREATE POLICY "Users can access their own attorney records" ON attorneys
  FOR ALL USING (user_id = auth.uid());
```

**Access Pattern**:
- Users can only see/modify attorney records where `user_id = auth.uid()`
- Supports full CRUD operations for owned records
- No public access to attorney data

### 2. assistant_ui_configs Table

**RLS Status**: ✅ Enabled

```sql
-- Policy: Users can access configs for their own attorney records
CREATE POLICY "Users can access their own assistant configs" ON assistant_ui_configs
  FOR ALL USING (
    attorney_id IN (
      SELECT id FROM attorneys WHERE user_id = auth.uid()
    )
  );
```

**Access Pattern**:
- Users can access UI configs for assistants they own
- Indirect ownership through `attorneys` table relationship
- Supports full CRUD operations for owned configs

### 3. assistant_subdomains Table

**RLS Status**: ✅ Enabled

```sql
-- Policy: Public read access for subdomain routing
CREATE POLICY "Public subdomain lookup" ON assistant_subdomains
  FOR SELECT USING (is_active = true);

-- Policy: Users can manage their own assistant subdomains
CREATE POLICY "Users can manage their own assistant subdomains" ON assistant_subdomains
  FOR INSERT, UPDATE, DELETE USING (
    attorney_id IN (
      SELECT id FROM attorneys WHERE user_id = auth.uid()
    )
  );
```

**Access Pattern**:
- Public read access for active subdomains (needed for routing)
- Users can only modify subdomains for their own assistants
- Separate policies for read vs write operations

### 4. call_records Table

**RLS Status**: ✅ Enabled

```sql
-- Policy: Users can access call records for their own attorneys
CREATE POLICY "Users can access their own call records" ON call_records
  FOR ALL USING (
    attorney_id IN (
      SELECT id FROM attorneys WHERE user_id = auth.uid()
    )
  );
```

**Access Pattern**:
- Users can only see call records for their own attorneys
- Supports full CRUD operations for owned records
- Call data is completely isolated between attorneys

### 5. consultations Table

**RLS Status**: ✅ Enabled

```sql
-- Policy: Users can access consultations for their own attorneys
CREATE POLICY "Users can access their own consultations" ON consultations
  FOR ALL USING (
    attorney_id IN (
      SELECT id FROM attorneys WHERE user_id = auth.uid()
    )
  );
```

**Access Pattern**:
- Users can only access consultations for their own attorneys
- Supports full CRUD operations for owned records
- Consultation data is completely isolated

### 6. custom_columns Table

**RLS Status**: ✅ Enabled

```sql
-- Policy: Users can access custom columns for their own attorneys
CREATE POLICY "Users can access their own custom columns" ON custom_columns
  FOR ALL USING (
    attorney_id IN (
      SELECT id FROM attorneys WHERE user_id = auth.uid()
    )
  );
```

**Access Pattern**:
- Users can only access custom field definitions they created
- Supports full CRUD operations for owned columns
- Field definitions are isolated between attorneys

### 7. forwarding_rules Table

**RLS Status**: ✅ Enabled

```sql
-- Policy: Users can access forwarding rules for their own attorneys
CREATE POLICY "Users can access their own forwarding rules" ON forwarding_rules
  FOR ALL USING (
    attorney_id IN (
      SELECT id FROM attorneys WHERE user_id = auth.uid()
    )
  );
```

**Access Pattern**:
- Users can only access forwarding rules they created
- Supports full CRUD operations for owned rules
- Call forwarding configuration is isolated

## Storage Bucket Policies

### attorney-logos Bucket

**RLS Status**: ✅ Enabled

```sql
-- Policy: Public read access for logos
CREATE POLICY "Public logo access" ON storage.objects
  FOR SELECT USING (bucket_id = 'attorney-logos');

-- Policy: Authenticated users can upload logos
CREATE POLICY "Authenticated logo upload" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'attorney-logos' AND 
    auth.role() = 'authenticated'
  );

-- Policy: Users can update their own logos
CREATE POLICY "Users can update own logos" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'attorney-logos' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

-- Policy: Users can delete their own logos
CREATE POLICY "Users can delete own logos" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'attorney-logos' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );
```

**Access Pattern**:
- Public read access (logos need to be publicly viewable)
- Authenticated users can upload logos
- Users can only modify/delete their own logos (organized by user ID folder)

### voice-samples Bucket

**RLS Status**: ✅ Enabled

```sql
-- Policy: Users can access their own voice samples
CREATE POLICY "Users can access own voice samples" ON storage.objects
  FOR ALL USING (
    bucket_id = 'voice-samples' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );
```

**Access Pattern**:
- Users can only access voice samples in their own folder
- Supports full CRUD operations for owned files
- Voice samples are completely private

## Views and Public Access

### v_subdomain_assistant_lookup View

**Access**: Public read access granted

```sql
-- Grant permissions for subdomain routing
GRANT SELECT ON v_subdomain_assistant_lookup TO anon, authenticated;
```

**Rationale**:
- Needed for subdomain routing to work for anonymous users
- Only exposes necessary data for routing (subdomain, assistant_id, attorney info)
- Does not expose sensitive configuration data

## Security Testing

### RLS Policy Testing

```sql
-- Test attorney isolation
SET ROLE authenticated;
SET request.jwt.claims TO '{"sub": "user1-uuid"}';

-- Should only return records for user1
SELECT * FROM attorneys;
SELECT * FROM call_records;
SELECT * FROM consultations;

-- Switch to different user
SET request.jwt.claims TO '{"sub": "user2-uuid"}';

-- Should only return records for user2 (different set)
SELECT * FROM attorneys;
SELECT * FROM call_records;
SELECT * FROM consultations;
```

### Storage Policy Testing

```sql
-- Test logo access
SELECT * FROM storage.objects WHERE bucket_id = 'attorney-logos';

-- Test voice sample isolation
SELECT * FROM storage.objects 
WHERE bucket_id = 'voice-samples' 
AND (storage.foldername(name))[1] = auth.uid()::text;
```

## Common RLS Patterns

### 1. Attorney Ownership Pattern

Most tables use this pattern for access control:

```sql
CREATE POLICY "policy_name" ON table_name
  FOR ALL USING (
    attorney_id IN (
      SELECT id FROM attorneys WHERE user_id = auth.uid()
    )
  );
```

### 2. Direct User Ownership Pattern

For tables directly linked to users:

```sql
CREATE POLICY "policy_name" ON table_name
  FOR ALL USING (user_id = auth.uid());
```

### 3. Public Read with Restricted Write

For data that needs public access but restricted modification:

```sql
-- Public read
CREATE POLICY "public_read" ON table_name
  FOR SELECT USING (condition);

-- Restricted write
CREATE POLICY "restricted_write" ON table_name
  FOR INSERT, UPDATE, DELETE USING (
    user_condition
  );
```

## Security Best Practices

### 1. Principle of Least Privilege
- Users only get access to data they absolutely need
- Public access is limited to routing and display data
- Sensitive data (call transcripts, client info) is strictly isolated

### 2. Defense in Depth
- RLS policies are the primary security layer
- Application-level checks provide additional validation
- API endpoints validate user permissions before database access

### 3. Audit and Monitoring
- All data access is logged through Supabase
- RLS policy violations are automatically blocked and logged
- Regular security audits verify policy effectiveness

## Troubleshooting RLS Issues

### Common Problems

1. **User Can't See Their Own Data**
   ```sql
   -- Check if user_id is properly set in attorneys table
   SELECT id, user_id, email FROM attorneys WHERE user_id = auth.uid();
   ```

2. **Public Subdomain Lookup Fails**
   ```sql
   -- Verify subdomain lookup view permissions
   SELECT * FROM v_subdomain_assistant_lookup WHERE subdomain = 'test';
   ```

3. **Storage Upload Fails**
   ```sql
   -- Check storage bucket policies
   SELECT * FROM storage.buckets WHERE id = 'attorney-logos';
   ```

### Debug Queries

```sql
-- Check current user context
SELECT auth.uid(), auth.role();

-- Test RLS policy for specific table
EXPLAIN (ANALYZE, BUFFERS) 
SELECT * FROM attorneys WHERE user_id = auth.uid();

-- Check policy definitions
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
FROM pg_policies 
WHERE tablename = 'attorneys';
```

This RLS implementation ensures that LegalScout maintains strict data isolation while providing the necessary public access for subdomain routing and logo display.

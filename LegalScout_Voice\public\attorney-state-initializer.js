/**
 * Attorney State Initializer
 * 
 * This script initializes the attorney state before the React application loads.
 * It ensures that attorney data is properly loaded from all available sources
 * and synchronized between Supabase, Vapi, and local storage.
 */

(function() {
  console.log('[AttorneyStateInitializer] Starting initialization...');
  
  // UUID validation regex
  const UUID_REGEX = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  
  // Storage keys
  const STORAGE_KEYS = {
    ATTORNEY: 'attorney',
    ATTORNEY_ID: 'attorney_id',
    ATTORNEY_VERSION: 'attorney_version',
    LAST_SYNC: 'attorney_last_sync'
  };
  
  /**
   * Validate a UUID
   * @param {string} uuid - The UUID to validate
   * @returns {boolean} Whether the UUID is valid
   */
  function isValidUUID(uuid) {
    if (!uuid || typeof uuid !== 'string') return false;
    return UUID_REGEX.test(uuid);
  }
  
  /**
   * Generate a fallback UUID
   * @returns {string} A valid UUID
   */
  function generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }
  
  /**
   * Create a default attorney object
   * @param {Object} overrides - Properties to override in the default attorney
   * @returns {Object} A default attorney object
   */
  function createDefaultAttorney(overrides = {}) {
    const defaultAttorney = {
      id: generateUUID(),
      subdomain: 'default',
      firm_name: 'Your Law Firm',
      name: 'Your Name',
      is_active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      voice_provider: '11labs',
      voice_id: 'sarah',
      welcome_message: 'Hello! I\'m your legal assistant. How can I help you today?',
      information_gathering_prompt: 'Tell me about your situation, and I\'ll help find the right solution for you.',
      vapi_instructions: 'You are a legal assistant helping potential clients understand their legal needs and connecting them with appropriate attorneys.'
    };
    
    return { ...defaultAttorney, ...overrides };
  }
  
  /**
   * Save attorney to local storage
   * @param {Object} attorney - The attorney to save
   */
  function saveToLocalStorage(attorney) {
    if (!attorney || !isValidUUID(attorney.id)) {
      console.warn('[AttorneyStateInitializer] Cannot save invalid attorney to local storage');
      return;
    }
    
    try {
      // Save full attorney object
      localStorage.setItem(STORAGE_KEYS.ATTORNEY, JSON.stringify(attorney));
      
      // Save ID separately for redundancy
      localStorage.setItem(STORAGE_KEYS.ATTORNEY_ID, attorney.id);
      
      // Save version and timestamp
      localStorage.setItem(STORAGE_KEYS.ATTORNEY_VERSION, Date.now().toString());
      
      console.log('[AttorneyStateInitializer] Saved attorney to local storage:', attorney.id);
    } catch (error) {
      console.error('[AttorneyStateInitializer] Error saving to local storage:', error);
    }
  }
  
  /**
   * Load attorney from local storage
   * @returns {Object|null} The attorney object or null if not found
   */
  function loadFromLocalStorage() {
    try {
      // Try to get from localStorage
      const storedAttorney = localStorage.getItem(STORAGE_KEYS.ATTORNEY);
      if (storedAttorney) {
        const parsedAttorney = JSON.parse(storedAttorney);
        
        // Validate the attorney data
        if (parsedAttorney && isValidUUID(parsedAttorney.id)) {
          console.log('[AttorneyStateInitializer] Loaded attorney from local storage:', parsedAttorney.id);
          return parsedAttorney;
        }
      }
      
      // If not found or invalid, try to get just the ID
      const storedAttorneyId = localStorage.getItem(STORAGE_KEYS.ATTORNEY_ID);
      if (storedAttorneyId && isValidUUID(storedAttorneyId)) {
        console.log('[AttorneyStateInitializer] Found attorney ID in local storage:', storedAttorneyId);
        return { id: storedAttorneyId };
      }
      
      return null;
    } catch (error) {
      console.error('[AttorneyStateInitializer] Error loading from local storage:', error);
      return null;
    }
  }
  
  /**
   * Initialize attorney state
   */
  function initializeAttorneyState() {
    // Check if we already have attorney data in local storage
    const localAttorney = loadFromLocalStorage();
    
    if (localAttorney) {
      console.log('[AttorneyStateInitializer] Found existing attorney in local storage:', localAttorney.id);
      
      // Ensure the attorney has all required fields
      const enhancedAttorney = {
        ...createDefaultAttorney(),
        ...localAttorney
      };
      
      // Save the enhanced attorney back to local storage
      saveToLocalStorage(enhancedAttorney);
    } else {
      console.log('[AttorneyStateInitializer] No attorney found in local storage, creating default');
      
      // Create a default attorney
      const defaultAttorney = createDefaultAttorney();
      
      // Save to local storage
      saveToLocalStorage(defaultAttorney);
    }
    
    // Add global helper function for emergency recovery
    window.recoverAttorneyState = function(attorneyId) {
      console.log('[AttorneyStateInitializer] Emergency recovery requested for attorney:', attorneyId);
      
      if (!isValidUUID(attorneyId)) {
        console.error('[AttorneyStateInitializer] Invalid attorney ID for recovery');
        return false;
      }
      
      try {
        // Create a recovery attorney with the provided ID
        const recoveryAttorney = createDefaultAttorney({ id: attorneyId });
        
        // Save to local storage
        saveToLocalStorage(recoveryAttorney);
        
        console.log('[AttorneyStateInitializer] Recovery successful for attorney:', attorneyId);
        return true;
      } catch (error) {
        console.error('[AttorneyStateInitializer] Recovery failed:', error);
        return false;
      }
    };
    
    // Add global helper function to check attorney state
    window.checkAttorneyState = function() {
      const attorney = loadFromLocalStorage();
      
      return {
        exists: !!attorney,
        isValid: attorney && isValidUUID(attorney.id),
        id: attorney?.id,
        version: localStorage.getItem(STORAGE_KEYS.ATTORNEY_VERSION),
        lastSync: localStorage.getItem(STORAGE_KEYS.LAST_SYNC)
      };
    };
    
    console.log('[AttorneyStateInitializer] Initialization complete');
  }
  
  // Initialize attorney state
  initializeAttorneyState();
})();

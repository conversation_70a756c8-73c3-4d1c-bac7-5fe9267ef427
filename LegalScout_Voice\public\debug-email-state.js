/**
 * Debug Email State
 * 
 * Diagnoses what email is being used where and why the assistant
 * creation/retrieval isn't working for dam<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com
 */

(function debugEmailState() {
  console.log('🔍 [DebugEmailState] Starting email state diagnosis...');
  
  // Add debug button
  const debugButton = document.createElement('button');
  debugButton.textContent = '🔍 Debug Email State';
  debugButton.style.cssText = `
    position: fixed;
    top: 110px;
    right: 10px;
    z-index: 10000;
    padding: 10px;
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 12px;
    max-width: 150px;
  `;
  
  debugButton.onclick = async () => {
    try {
      debugButton.textContent = '🔍 Debugging...';
      debugButton.disabled = true;
      
      await runEmailDiagnosis();
      
    } catch (error) {
      console.error('Debug error:', error);
    } finally {
      debugButton.textContent = '🔍 Debug Email State';
      debugButton.disabled = false;
    }
  };
  
  document.body.appendChild(debugButton);
  
  async function runEmailDiagnosis() {
    console.log('🔍 [DebugEmailState] Running comprehensive email diagnosis...');
    
    // 1. Check what email the UI thinks it's using
    console.log('📧 Step 1: Checking UI email state...');
    
    const uiEmails = {
      currentUser: window.currentUser?.email || 'Not set',
      currentAttorney: window.currentAttorney?.email || 'Not set',
      currentAttorneyState: window.currentAttorneyState?.attorney?.email || 'Not set',
      authUser: window.supabase?.auth?.user?.email || 'Not set'
    };
    
    console.log('📧 UI Email Sources:', uiEmails);
    
    // 2. Check what's in localStorage/sessionStorage
    console.log('💾 Step 2: Checking browser storage...');
    
    const storageEmails = {
      localStorage: localStorage.getItem('userEmail') || localStorage.getItem('attorney_email') || 'Not found',
      sessionStorage: sessionStorage.getItem('userEmail') || sessionStorage.getItem('attorney_email') || 'Not found'
    };
    
    console.log('💾 Storage Emails:', storageEmails);
    
    // 3. Check Supabase auth state
    console.log('🔐 Step 3: Checking Supabase auth state...');
    
    let authState = 'Not available';
    if (window.supabase) {
      try {
        const { data: { user } } = await window.supabase.auth.getUser();
        authState = user?.email || 'No user';
      } catch (error) {
        authState = `Error: ${error.message}`;
      }
    }
    
    console.log('🔐 Supabase Auth Email:', authState);
    
    // 4. Test database queries with both emails
    console.log('🗄️ Step 4: Testing database queries...');
    
    const testEmails = ['<EMAIL>', '<EMAIL>'];
    const dbResults = {};
    
    for (const email of testEmails) {
      try {
        const { data, error } = await window.supabase
          .from('attorneys')
          .select('*')
          .eq('email', email);
        
        dbResults[email] = {
          found: data && data.length > 0,
          count: data ? data.length : 0,
          hasAssistant: data && data[0] ? !!data[0].vapi_assistant_id : false,
          assistantId: data && data[0] ? data[0].vapi_assistant_id : null,
          error: error ? error.message : null
        };
      } catch (error) {
        dbResults[email] = { error: error.message };
      }
    }
    
    console.log('🗄️ Database Query Results:', dbResults);
    
    // 5. Check which functions are being called
    console.log('🔧 Step 5: Checking function availability...');
    
    const functionAvailability = {
      loadAttorney: typeof window.loadAttorney === 'function',
      loadAttorneyWithRobustHandling: typeof window.loadAttorneyWithRobustHandling === 'function',
      resolveAttorneyState: typeof window.resolveAttorneyState === 'function',
      createControlledAssistant: typeof window.createControlledAssistant === 'function',
      dashboardInterceptor: !!window.dashboardInterceptor
    };
    
    console.log('🔧 Function Availability:', functionAvailability);
    
    // 6. Test the robust state handler directly
    console.log('🧪 Step 6: Testing robust state handler...');
    
    if (window.resolveAttorneyState) {
      try {
        const testResult = await window.resolveAttorneyState('<EMAIL>');
        console.log('🧪 Robust Handler Test Result:', testResult);
      } catch (error) {
        console.log('🧪 Robust Handler Test Error:', error.message);
      }
    } else {
      console.log('🧪 Robust Handler Not Available');
    }
    
    // 7. Check interceptor status
    console.log('🚧 Step 7: Checking interceptor status...');
    
    const interceptorStatus = {
      exists: !!window.dashboardInterceptor,
      isActive: window.dashboardInterceptor ? window.dashboardInterceptor.isActive() : false
    };
    
    console.log('🚧 Interceptor Status:', interceptorStatus);
    
    // 8. Summary and recommendations
    console.log('📋 DIAGNOSIS SUMMARY:');
    console.log('==================');
    
    const diagnosis = {
      uiEmail: uiEmails.currentUser || uiEmails.currentAttorney || authState,
      dbEmailExists: dbResults['<EMAIL>']?.found || false,
      robustHandlerAvailable: functionAvailability.resolveAttorneyState,
      interceptorActive: interceptorStatus.isActive,
      recommendation: 'See console for detailed analysis'
    };
    
    if (!dbResults['<EMAIL>']?.found && functionAvailability.resolveAttorneyState) {
      diagnosis.recommendation = 'Run: window.resolveAttorneyState("<EMAIL>")';
    } else if (!functionAvailability.resolveAttorneyState) {
      diagnosis.recommendation = 'Robust state handler not loaded - check script loading order';
    } else if (dbResults['<EMAIL>']?.found && !dbResults['<EMAIL>']?.hasAssistant) {
      diagnosis.recommendation = 'Attorney exists but no assistant - run assistant creation';
    }
    
    console.log('📋 Final Diagnosis:', diagnosis);
    
    // Show visual summary
    showDiagnosisResult(diagnosis);
  }
  
  function showDiagnosisResult(diagnosis) {
    const result = document.createElement('div');
    result.style.cssText = `
      position: fixed;
      top: 170px;
      right: 10px;
      z-index: 10000;
      padding: 15px;
      border-radius: 5px;
      color: white;
      font-weight: 500;
      max-width: 300px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      background: #17a2b8;
      font-size: 12px;
    `;
    
    result.innerHTML = `
      <strong>Email Diagnosis:</strong><br>
      UI Email: ${diagnosis.uiEmail}<br>
      DB Record: ${diagnosis.dbEmailExists ? '✅' : '❌'}<br>
      Robust Handler: ${diagnosis.robustHandlerAvailable ? '✅' : '❌'}<br>
      Interceptor: ${diagnosis.interceptorActive ? '✅' : '❌'}<br>
      <br>
      <strong>Action:</strong><br>
      ${diagnosis.recommendation}
    `;
    
    document.body.appendChild(result);
    
    setTimeout(() => {
      if (result.parentNode) {
        result.parentNode.removeChild(result);
      }
    }, 15000);
  }
  
})();

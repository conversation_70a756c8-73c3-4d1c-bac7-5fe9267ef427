class s{constructor(){this.cache=new Map}async getSubdomainConfig(a){if(!a||a==="default")return null;const t=`subdomain_${a}`;if(this.cache.has(t))return this.cache.get(t);try{console.log(`🔍 [SimpleSubdomain] Loading config for: ${a}`);const o=await this.getAssistantIdFromSubdomain(a);if(!o)return console.log(`📭 [SimpleSubdomain] No assistant found for subdomain: ${a}`),null;const e=await this.getUIConfigFromSupabase(o),i=await this.getCallConfigFromVapi(o),n={id:o,subdomain:a,assistant_id:o,vapi_assistant_id:o,current_assistant_id:o,...e,email:e?.email||"<EMAIL>",name:e?.firmName||"LegalScout Assistant",vapiAssistant:i,loadedVia:"simple_subdomain_service",hasUIConfig:!!e,hasCallConfig:!!i,isActive:!0,vapiSyncStatus:"simple_service_loaded"};return this.cache.set(t,n),console.log(`✅ [SimpleSubdomain] Config loaded for ${a}:`,{firmName:n.firmName,assistant_id:o,hasUIConfig:!!e,hasCallConfig:!!i}),n}catch(o){return console.error(`❌ [SimpleSubdomain] Error loading config for ${a}:`,o),null}}async getAssistantIdFromSubdomain(a){try{console.log(`🔍 [SimpleSubdomain] Looking up assistant ID for: ${a}`);const t=await fetch(`${this.getSupabaseUrl()}/rest/v1/v_subdomain_assistant_lookup?subdomain=eq.${a}&is_active=eq.true`,{headers:{apikey:this.getSupabaseKey(),Authorization:`Bearer ${this.getSupabaseKey()}`,"Content-Type":"application/json"}});if(!t.ok)return console.warn(`⚠️ [SimpleSubdomain] Supabase query failed: ${t.status} ${t.statusText}`),a==="assistant1test"?(console.log(`🔄 [SimpleSubdomain] Using fallback assistant ID for ${a}`),"d1be86a2-49b3-4ff1-b39c-86f41c2b2f3d"):a==="damon"?(console.log(`🔄 [SimpleSubdomain] Using fallback assistant ID for ${a}`),"cd0b44b7-397e-410d-8835-ce9c3ba584b2"):null;const o=await t.json();return o&&o.length>0?(console.log(`✅ [SimpleSubdomain] Found assistant ID: ${o[0].assistant_id}`),o[0].assistant_id):(console.log(`📭 [SimpleSubdomain] No assistant found for subdomain: ${a}`),null)}catch(t){return console.error("❌ [SimpleSubdomain] Failed to get assistant ID:",t),a==="assistant1test"?(console.log(`🚨 [SimpleSubdomain] Emergency fallback for ${a}`),"d1be86a2-49b3-4ff1-b39c-86f41c2b2f3d"):a==="damon"?(console.log(`🚨 [SimpleSubdomain] Emergency fallback for ${a}`),"cd0b44b7-397e-410d-8835-ce9c3ba584b2"):null}}async getUIConfigFromSupabase(a){try{const t=await fetch(`${this.getSupabaseUrl()}/rest/v1/assistant_ui_configs?assistant_id=eq.${a}`,{headers:{apikey:this.getSupabaseKey(),Authorization:`Bearer ${this.getSupabaseKey()}`,"Content-Type":"application/json"}});if(!t.ok)return console.warn(`⚠️ [SimpleSubdomain] UI config query failed: ${t.status}`),this.getDefaultUIConfig();const o=await t.json();if(o&&o.length>0){const e=o[0];return{firmName:e.assistant_name||e.firm_name||"LegalScout",titleText:e.assistant_name||e.firm_name||"LegalScout",primaryColor:e.primary_color||"#3B82F6",secondaryColor:e.secondary_color||"#1e40af",buttonColor:e.button_color||"#3b82f6",backgroundColor:e.background_color||"#ffffff",backgroundOpacity:e.background_opacity||"1.00",buttonOpacity:e.button_opacity||"1.00",practiceAreaBackgroundOpacity:e.practice_area_background_opacity||"0.10",textBackgroundColor:e.text_background_color||"#ffffff",welcomeMessage:e.welcome_message||"Hello! How can I help you today?",practiceDescription:e.practice_description,informationGathering:e.information_gathering,vapiInstructions:e.vapi_instructions||"You are a helpful legal assistant.",vapiContext:e.vapi_context||"",voiceId:e.voice_id||"alloy",voiceProvider:e.voice_provider||"openai",aiModel:e.ai_model||"gpt-4o",logoUrl:e.logo_url,mascot:e.mascot_url,buttonImage:e.assistant_image_url,practiceAreas:e.practice_areas||[],officeAddress:e.office_address,schedulingLink:e.scheduling_link,actualFirmName:e.firm_name,agentName:e.assistant_name}}return console.log(`📭 [SimpleSubdomain] No UI config found for assistant: ${a}`),this.getDefaultUIConfig()}catch(t){return console.error("❌ [SimpleSubdomain] Failed to get UI config:",t),this.getDefaultUIConfig()}}async getCallConfigFromVapi(a){try{console.log(`🔍 [SimpleSubdomain] Getting Vapi config for: ${a}`);const t=await fetch(`https://api.vapi.ai/assistant/${a}`,{headers:{Authorization:`Bearer ${this.getVapiPrivateKey()}`,"Content-Type":"application/json"}});if(t.ok){const o=await t.json();return console.log("✅ [SimpleSubdomain] Vapi assistant loaded:",o.name),{id:o.id,name:o.name,voice:o.voice,llm:o.llm,transcriber:o.transcriber,tools:o.toolIds||o.tools||[]}}else console.warn(`⚠️ [SimpleSubdomain] Vapi API returned ${t.status} for: ${a}`)}catch(t){console.warn("⚠️ [SimpleSubdomain] Vapi API failed:",t.message)}return console.log(`🔄 [SimpleSubdomain] Using fallback config for: ${a}`),{id:a,name:"LegalScout Assistant",voice:{provider:"openai",voiceId:"alloy"},llm:{provider:"openai",model:"gpt-4o"},tools:[]}}getDefaultUIConfig(){return{firmName:"LegalScout",primaryColor:"#3B82F6",secondaryColor:"#1e40af",buttonColor:"#3b82f6",backgroundColor:"#ffffff",backgroundOpacity:"1.00",buttonOpacity:"1.00",practiceAreaBackgroundOpacity:"0.10",textBackgroundColor:"#ffffff",welcomeMessage:"Hello! How can I help you today?",vapiInstructions:"You are a helpful legal assistant.",voiceId:"alloy",voiceProvider:"openai",aiModel:"gpt-4o",practiceAreas:[]}}getSupabaseUrl(){return"https://utopqxsvudgrtiwenlzl.supabase.co"}getSupabaseKey(){return"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU"}getVapiPrivateKey(){return"6734febc-fc65-4669-93b0-929b31ff6564"}clearCache(a=null){a?this.cache.delete(`subdomain_${a}`):this.cache.clear()}}const c=new s;export{c as simpleSubdomainService};

import{r as S,j as a}from"./index-27efa71d.js";var we={},Ne={};/**
 * @license React
 * react-dom-server-legacy.browser.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Sr=S;function g(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var L=Object.prototype.hasOwnProperty,To=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,jn={},Tn={};function yr(e){return L.call(Tn,e)?!0:L.call(jn,e)?!1:To.test(e)?Tn[e]=!0:(jn[e]=!0,!1)}function U(e,t,n,r,i,o,l){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=i,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=o,this.removeEmptyString=l}var V={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){V[e]=new U(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];V[t]=new U(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){V[e]=new U(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){V[e]=new U(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){V[e]=new U(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){V[e]=new U(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){V[e]=new U(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){V[e]=new U(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){V[e]=new U(e,5,!1,e.toLowerCase(),null,!1,!1)});var Wt=/[\-:]([a-z])/g;function qt(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Wt,qt);V[t]=new U(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Wt,qt);V[t]=new U(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Wt,qt);V[t]=new U(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){V[e]=new U(e,1,!1,e.toLowerCase(),null,!1,!1)});V.xlinkHref=new U("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){V[e]=new U(e,1,!1,e.toLowerCase(),null,!0,!0)});var We={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Fo=["Webkit","ms","Moz","O"];Object.keys(We).forEach(function(e){Fo.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),We[t]=We[e]})});var Do=/["'&<>]/;function I(e){if(typeof e=="boolean"||typeof e=="number")return""+e;e=""+e;var t=Do.exec(e);if(t){var n="",r,i=0;for(r=t.index;r<e.length;r++){switch(e.charCodeAt(r)){case 34:t="&quot;";break;case 38:t="&amp;";break;case 39:t="&#x27;";break;case 60:t="&lt;";break;case 62:t="&gt;";break;default:continue}i!==r&&(n+=e.substring(i,r)),i=r+1,n+=t}e=i!==r?n+e.substring(i,r):n}return e}var zo=/([A-Z])/g,_o=/^ms-/,$t=Array.isArray;function re(e,t){return{insertionMode:e,selectedValue:t}}function Po(e,t,n){switch(t){case"select":return re(1,n.value!=null?n.value:n.defaultValue);case"svg":return re(2,null);case"math":return re(3,null);case"foreignObject":return re(1,null);case"table":return re(4,null);case"thead":case"tbody":case"tfoot":return re(5,null);case"colgroup":return re(7,null);case"tr":return re(6,null)}return 4<=e.insertionMode||e.insertionMode===0?re(1,null):e}var Fn=new Map;function wr(e,t,n){if(typeof n!="object")throw Error(g(62));t=!0;for(var r in n)if(L.call(n,r)){var i=n[r];if(i!=null&&typeof i!="boolean"&&i!==""){if(r.indexOf("--")===0){var o=I(r);i=I((""+i).trim())}else{o=r;var l=Fn.get(o);l!==void 0||(l=I(o.replace(zo,"-$1").toLowerCase().replace(_o,"-ms-")),Fn.set(o,l)),o=l,i=typeof i=="number"?i===0||L.call(We,r)?""+i:i+"px":I((""+i).trim())}t?(t=!1,e.push(' style="',o,":",i)):e.push(";",o,":",i)}}t||e.push('"')}function W(e,t,n,r){switch(n){case"style":wr(e,t,r);return;case"defaultValue":case"defaultChecked":case"innerHTML":case"suppressContentEditableWarning":case"suppressHydrationWarning":return}if(!(2<n.length)||n[0]!=="o"&&n[0]!=="O"||n[1]!=="n"&&n[1]!=="N"){if(t=V.hasOwnProperty(n)?V[n]:null,t!==null){switch(typeof r){case"function":case"symbol":return;case"boolean":if(!t.acceptsBooleans)return}switch(n=t.attributeName,t.type){case 3:r&&e.push(" ",n,'=""');break;case 4:r===!0?e.push(" ",n,'=""'):r!==!1&&e.push(" ",n,'="',I(r),'"');break;case 5:isNaN(r)||e.push(" ",n,'="',I(r),'"');break;case 6:!isNaN(r)&&1<=r&&e.push(" ",n,'="',I(r),'"');break;default:t.sanitizeURL&&(r=""+r),e.push(" ",n,'="',I(r),'"')}}else if(yr(n)){switch(typeof r){case"function":case"symbol":return;case"boolean":if(t=n.toLowerCase().slice(0,5),t!=="data-"&&t!=="aria-")return}e.push(" ",n,'="',I(r),'"')}}}function qe(e,t,n){if(t!=null){if(n!=null)throw Error(g(60));if(typeof t!="object"||!("__html"in t))throw Error(g(61));t=t.__html,t!=null&&e.push(""+t)}}function Vo(e){var t="";return Sr.Children.forEach(e,function(n){n!=null&&(t+=n)}),t}function Et(e,t,n,r){e.push(K(n));var i=n=null,o;for(o in t)if(L.call(t,o)){var l=t[o];if(l!=null)switch(o){case"children":n=l;break;case"dangerouslySetInnerHTML":i=l;break;default:W(e,r,o,l)}}return e.push(">"),qe(e,i,n),typeof n=="string"?(e.push(I(n)),null):n}var $o=/^[a-zA-Z][a-zA-Z:_\.\-\d]*$/,Dn=new Map;function K(e){var t=Dn.get(e);if(t===void 0){if(!$o.test(e))throw Error(g(65,e));t="<"+e,Dn.set(e,t)}return t}function Ro(e,t,n,r,i){switch(t){case"select":e.push(K("select"));var o=null,l=null;for(c in n)if(L.call(n,c)){var s=n[c];if(s!=null)switch(c){case"children":o=s;break;case"dangerouslySetInnerHTML":l=s;break;case"defaultValue":case"value":break;default:W(e,r,c,s)}}return e.push(">"),qe(e,l,o),o;case"option":l=i.selectedValue,e.push(K("option"));var u=s=null,d=null,c=null;for(o in n)if(L.call(n,o)){var f=n[o];if(f!=null)switch(o){case"children":s=f;break;case"selected":d=f;break;case"dangerouslySetInnerHTML":c=f;break;case"value":u=f;default:W(e,r,o,f)}}if(l!=null)if(n=u!==null?""+u:Vo(s),$t(l)){for(r=0;r<l.length;r++)if(""+l[r]===n){e.push(' selected=""');break}}else""+l===n&&e.push(' selected=""');else d&&e.push(' selected=""');return e.push(">"),qe(e,c,s),s;case"textarea":e.push(K("textarea")),c=l=o=null;for(s in n)if(L.call(n,s)&&(u=n[s],u!=null))switch(s){case"children":c=u;break;case"value":o=u;break;case"defaultValue":l=u;break;case"dangerouslySetInnerHTML":throw Error(g(91));default:W(e,r,s,u)}if(o===null&&l!==null&&(o=l),e.push(">"),c!=null){if(o!=null)throw Error(g(92));if($t(c)&&1<c.length)throw Error(g(93));o=""+c}return typeof o=="string"&&o[0]===`
`&&e.push(`
`),o!==null&&e.push(I(""+o)),null;case"input":e.push(K("input")),u=c=s=o=null;for(l in n)if(L.call(n,l)&&(d=n[l],d!=null))switch(l){case"children":case"dangerouslySetInnerHTML":throw Error(g(399,"input"));case"defaultChecked":u=d;break;case"defaultValue":s=d;break;case"checked":c=d;break;case"value":o=d;break;default:W(e,r,l,d)}return c!==null?W(e,r,"checked",c):u!==null&&W(e,r,"checked",u),o!==null?W(e,r,"value",o):s!==null&&W(e,r,"value",s),e.push("/>"),null;case"menuitem":e.push(K("menuitem"));for(var b in n)if(L.call(n,b)&&(o=n[b],o!=null))switch(b){case"children":case"dangerouslySetInnerHTML":throw Error(g(400));default:W(e,r,b,o)}return e.push(">"),null;case"title":e.push(K("title")),o=null;for(f in n)if(L.call(n,f)&&(l=n[f],l!=null))switch(f){case"children":o=l;break;case"dangerouslySetInnerHTML":throw Error(g(434));default:W(e,r,f,l)}return e.push(">"),o;case"listing":case"pre":e.push(K(t)),l=o=null;for(u in n)if(L.call(n,u)&&(s=n[u],s!=null))switch(u){case"children":o=s;break;case"dangerouslySetInnerHTML":l=s;break;default:W(e,r,u,s)}if(e.push(">"),l!=null){if(o!=null)throw Error(g(60));if(typeof l!="object"||!("__html"in l))throw Error(g(61));n=l.__html,n!=null&&(typeof n=="string"&&0<n.length&&n[0]===`
`?e.push(`
`,n):e.push(""+n))}return typeof o=="string"&&o[0]===`
`&&e.push(`
`),o;case"area":case"base":case"br":case"col":case"embed":case"hr":case"img":case"keygen":case"link":case"meta":case"param":case"source":case"track":case"wbr":e.push(K(t));for(var w in n)if(L.call(n,w)&&(o=n[w],o!=null))switch(w){case"children":case"dangerouslySetInnerHTML":throw Error(g(399,t));default:W(e,r,w,o)}return e.push("/>"),null;case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return Et(e,n,t,r);case"html":return i.insertionMode===0&&e.push("<!DOCTYPE html>"),Et(e,n,t,r);default:if(t.indexOf("-")===-1&&typeof n.is!="string")return Et(e,n,t,r);e.push(K(t)),l=o=null;for(d in n)if(L.call(n,d)&&(s=n[d],s!=null))switch(d){case"children":o=s;break;case"dangerouslySetInnerHTML":l=s;break;case"style":wr(e,r,s);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":break;default:yr(d)&&typeof s!="function"&&typeof s!="symbol"&&e.push(" ",d,'="',I(s),'"')}return e.push(">"),qe(e,l,o),o}}function zn(e,t,n){if(e.push('<!--$?--><template id="'),n===null)throw Error(g(395));return e.push(n),e.push('"></template>')}function Mo(e,t,n,r){switch(n.insertionMode){case 0:case 1:return e.push('<div hidden id="'),e.push(t.segmentPrefix),t=r.toString(16),e.push(t),e.push('">');case 2:return e.push('<svg aria-hidden="true" style="display:none" id="'),e.push(t.segmentPrefix),t=r.toString(16),e.push(t),e.push('">');case 3:return e.push('<math aria-hidden="true" style="display:none" id="'),e.push(t.segmentPrefix),t=r.toString(16),e.push(t),e.push('">');case 4:return e.push('<table hidden id="'),e.push(t.segmentPrefix),t=r.toString(16),e.push(t),e.push('">');case 5:return e.push('<table hidden><tbody id="'),e.push(t.segmentPrefix),t=r.toString(16),e.push(t),e.push('">');case 6:return e.push('<table hidden><tr id="'),e.push(t.segmentPrefix),t=r.toString(16),e.push(t),e.push('">');case 7:return e.push('<table hidden><colgroup id="'),e.push(t.segmentPrefix),t=r.toString(16),e.push(t),e.push('">');default:throw Error(g(397))}}function Io(e,t){switch(t.insertionMode){case 0:case 1:return e.push("</div>");case 2:return e.push("</svg>");case 3:return e.push("</math>");case 4:return e.push("</table>");case 5:return e.push("</tbody></table>");case 6:return e.push("</tr></table>");case 7:return e.push("</colgroup></table>");default:throw Error(g(397))}}var Uo=/[<\u2028\u2029]/g;function jt(e){return JSON.stringify(e).replace(Uo,function(t){switch(t){case"<":return"\\u003c";case"\u2028":return"\\u2028";case"\u2029":return"\\u2029";default:throw Error("escapeJSStringsForInstructionScripts encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React")}})}function Bo(e,t){return t=t===void 0?"":t,{bootstrapChunks:[],startInlineScript:"<script>",placeholderPrefix:t+"P:",segmentPrefix:t+"S:",boundaryPrefix:t+"B:",idPrefix:t,nextSuspenseID:0,sentCompleteSegmentFunction:!1,sentCompleteBoundaryFunction:!1,sentClientRenderFunction:!1,generateStaticMarkup:e}}function _n(e,t,n,r){return n.generateStaticMarkup?(e.push(I(t)),!1):(t===""?e=r:(r&&e.push("<!-- -->"),e.push(I(t)),e=!0),e)}var Ve=Object.assign,Lo=Symbol.for("react.element"),br=Symbol.for("react.portal"),Cr=Symbol.for("react.fragment"),Nr=Symbol.for("react.strict_mode"),kr=Symbol.for("react.profiler"),Er=Symbol.for("react.provider"),jr=Symbol.for("react.context"),Tr=Symbol.for("react.forward_ref"),Fr=Symbol.for("react.suspense"),Dr=Symbol.for("react.suspense_list"),zr=Symbol.for("react.memo"),Zt=Symbol.for("react.lazy"),Ao=Symbol.for("react.scope"),Ho=Symbol.for("react.debug_trace_mode"),Oo=Symbol.for("react.legacy_hidden"),Wo=Symbol.for("react.default_value"),Pn=Symbol.iterator;function Rt(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Cr:return"Fragment";case br:return"Portal";case kr:return"Profiler";case Nr:return"StrictMode";case Fr:return"Suspense";case Dr:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case jr:return(e.displayName||"Context")+".Consumer";case Er:return(e._context.displayName||"Context")+".Provider";case Tr:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case zr:return t=e.displayName||null,t!==null?t:Rt(e.type)||"Memo";case Zt:t=e._payload,e=e._init;try{return Rt(e(t))}catch{}}return null}var _r={};function Vn(e,t){if(e=e.contextTypes,!e)return _r;var n={},r;for(r in e)n[r]=t[r];return n}var Se=null;function dt(e,t){if(e!==t){e.context._currentValue2=e.parentValue,e=e.parent;var n=t.parent;if(e===null){if(n!==null)throw Error(g(401))}else{if(n===null)throw Error(g(401));dt(e,n)}t.context._currentValue2=t.value}}function Pr(e){e.context._currentValue2=e.parentValue,e=e.parent,e!==null&&Pr(e)}function Vr(e){var t=e.parent;t!==null&&Vr(t),e.context._currentValue2=e.value}function $r(e,t){if(e.context._currentValue2=e.parentValue,e=e.parent,e===null)throw Error(g(402));e.depth===t.depth?dt(e,t):$r(e,t)}function Rr(e,t){var n=t.parent;if(n===null)throw Error(g(402));e.depth===n.depth?dt(e,n):Rr(e,n),t.context._currentValue2=t.value}function tt(e){var t=Se;t!==e&&(t===null?Vr(e):e===null?Pr(t):t.depth===e.depth?dt(t,e):t.depth>e.depth?$r(t,e):Rr(t,e),Se=e)}var $n={isMounted:function(){return!1},enqueueSetState:function(e,t){e=e._reactInternals,e.queue!==null&&e.queue.push(t)},enqueueReplaceState:function(e,t){e=e._reactInternals,e.replace=!0,e.queue=[t]},enqueueForceUpdate:function(){}};function Rn(e,t,n,r){var i=e.state!==void 0?e.state:null;e.updater=$n,e.props=n,e.state=i;var o={queue:[],replace:!1};e._reactInternals=o;var l=t.contextType;if(e.context=typeof l=="object"&&l!==null?l._currentValue2:r,l=t.getDerivedStateFromProps,typeof l=="function"&&(l=l(n,i),i=l==null?i:Ve({},i,l),e.state=i),typeof t.getDerivedStateFromProps!="function"&&typeof e.getSnapshotBeforeUpdate!="function"&&(typeof e.UNSAFE_componentWillMount=="function"||typeof e.componentWillMount=="function"))if(t=e.state,typeof e.componentWillMount=="function"&&e.componentWillMount(),typeof e.UNSAFE_componentWillMount=="function"&&e.UNSAFE_componentWillMount(),t!==e.state&&$n.enqueueReplaceState(e,e.state,null),o.queue!==null&&0<o.queue.length)if(t=o.queue,l=o.replace,o.queue=null,o.replace=!1,l&&t.length===1)e.state=t[0];else{for(o=l?t[0]:e.state,i=!0,l=l?1:0;l<t.length;l++){var s=t[l];s=typeof s=="function"?s.call(e,o,n,r):s,s!=null&&(i?(i=!1,o=Ve({},o,s)):Ve(o,s))}e.state=o}else o.queue=null}var qo={id:1,overflow:""};function Mt(e,t,n){var r=e.id;e=e.overflow;var i=32-Ze(r)-1;r&=~(1<<i),n+=1;var o=32-Ze(t)+i;if(30<o){var l=i-i%5;return o=(r&(1<<l)-1).toString(32),r>>=l,i-=l,{id:1<<32-Ze(t)+i|n<<i|r,overflow:o+e}}return{id:1<<o|n<<i|r,overflow:e}}var Ze=Math.clz32?Math.clz32:Xo,Zo=Math.log,Go=Math.LN2;function Xo(e){return e>>>=0,e===0?32:31-(Zo(e)/Go|0)|0}function Yo(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Jo=typeof Object.is=="function"?Object.is:Yo,oe=null,Gt=null,Ge=null,N=null,De=!1,nt=!1,Re=0,de=null,mt=0;function ge(){if(oe===null)throw Error(g(321));return oe}function Mn(){if(0<mt)throw Error(g(312));return{memoizedState:null,queue:null,next:null}}function Xt(){return N===null?Ge===null?(De=!1,Ge=N=Mn()):(De=!0,N=Ge):N.next===null?(De=!1,N=N.next=Mn()):(De=!0,N=N.next),N}function Yt(){Gt=oe=null,nt=!1,Ge=null,mt=0,N=de=null}function Mr(e,t){return typeof t=="function"?t(e):t}function In(e,t,n){if(oe=ge(),N=Xt(),De){var r=N.queue;if(t=r.dispatch,de!==null&&(n=de.get(r),n!==void 0)){de.delete(r),r=N.memoizedState;do r=e(r,n.action),n=n.next;while(n!==null);return N.memoizedState=r,[r,t]}return[N.memoizedState,t]}return e=e===Mr?typeof t=="function"?t():t:n!==void 0?n(t):t,N.memoizedState=e,e=N.queue={last:null,dispatch:null},e=e.dispatch=Ko.bind(null,oe,e),[N.memoizedState,e]}function Un(e,t){if(oe=ge(),N=Xt(),t=t===void 0?null:t,N!==null){var n=N.memoizedState;if(n!==null&&t!==null){var r=n[1];e:if(r===null)r=!1;else{for(var i=0;i<r.length&&i<t.length;i++)if(!Jo(t[i],r[i])){r=!1;break e}r=!0}if(r)return n[0]}}return e=e(),N.memoizedState=[e,t],e}function Ko(e,t,n){if(25<=mt)throw Error(g(301));if(e===oe)if(nt=!0,e={action:n,next:null},de===null&&(de=new Map),n=de.get(t),n===void 0)de.set(t,e);else{for(t=n;t.next!==null;)t=t.next;t.next=e}}function Qo(){throw Error(g(394))}function Le(){}var Bn={readContext:function(e){return e._currentValue2},useContext:function(e){return ge(),e._currentValue2},useMemo:Un,useReducer:In,useRef:function(e){oe=ge(),N=Xt();var t=N.memoizedState;return t===null?(e={current:e},N.memoizedState=e):t},useState:function(e){return In(Mr,e)},useInsertionEffect:Le,useLayoutEffect:function(){},useCallback:function(e,t){return Un(function(){return e},t)},useImperativeHandle:Le,useEffect:Le,useDebugValue:Le,useDeferredValue:function(e){return ge(),e},useTransition:function(){return ge(),[!1,Qo]},useId:function(){var e=Gt.treeContext,t=e.overflow;e=e.id,e=(e&~(1<<32-Ze(e)-1)).toString(32)+t;var n=Xe;if(n===null)throw Error(g(404));return t=Re++,e=":"+n.idPrefix+"R"+e,0<t&&(e+="H"+t.toString(32)),e+":"},useMutableSource:function(e,t){return ge(),t(e._source)},useSyncExternalStore:function(e,t,n){if(n===void 0)throw Error(g(407));return n()}},Xe=null,Tt=Sr.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentDispatcher;function ei(e){return console.error(e),null}function ze(){}function ti(e,t,n,r,i,o,l,s,u){var d=[],c=new Set;return t={destination:null,responseState:t,progressiveChunkSize:r===void 0?12800:r,status:0,fatalError:null,nextSegmentId:0,allPendingTasks:0,pendingRootTasks:0,completedRootSegment:null,abortableTasks:c,pingedTasks:d,clientRenderedBoundaries:[],completedBoundaries:[],partialBoundaries:[],onError:i===void 0?ei:i,onAllReady:o===void 0?ze:o,onShellReady:l===void 0?ze:l,onShellError:s===void 0?ze:s,onFatalError:u===void 0?ze:u},n=rt(t,0,null,n,!1,!1),n.parentFlushed=!0,e=Jt(t,e,null,n,c,_r,null,qo),d.push(e),t}function Jt(e,t,n,r,i,o,l,s){e.allPendingTasks++,n===null?e.pendingRootTasks++:n.pendingTasks++;var u={node:t,ping:function(){var d=e.pingedTasks;d.push(u),d.length===1&&Br(e)},blockedBoundary:n,blockedSegment:r,abortSet:i,legacyContext:o,context:l,treeContext:s};return i.add(u),u}function rt(e,t,n,r,i,o){return{status:0,id:-1,index:t,parentFlushed:!1,chunks:[],children:[],formatContext:r,boundary:n,lastPushedText:i,textEmbedded:o}}function Me(e,t){if(e=e.onError(t),e!=null&&typeof e!="string")throw Error('onError returned something with a type other than "string". onError should return a string and may return null or undefined but must not return anything else. It received something of type "'+typeof e+'" instead');return e}function ot(e,t){var n=e.onShellError;n(t),n=e.onFatalError,n(t),e.destination!==null?(e.status=2,e.destination.destroy(t)):(e.status=1,e.fatalError=t)}function Ln(e,t,n,r,i){for(oe={},Gt=t,Re=0,e=n(r,i);nt;)nt=!1,Re=0,mt+=1,N=null,e=n(r,i);return Yt(),e}function An(e,t,n,r){var i=n.render(),o=r.childContextTypes;if(o!=null){var l=t.legacyContext;if(typeof n.getChildContext!="function")r=l;else{n=n.getChildContext();for(var s in n)if(!(s in o))throw Error(g(108,Rt(r)||"Unknown",s));r=Ve({},l,n)}t.legacyContext=r,Z(e,t,i),t.legacyContext=l}else Z(e,t,i)}function Hn(e,t){if(e&&e.defaultProps){t=Ve({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function It(e,t,n,r,i){if(typeof n=="function")if(n.prototype&&n.prototype.isReactComponent){i=Vn(n,t.legacyContext);var o=n.contextType;o=new n(r,typeof o=="object"&&o!==null?o._currentValue2:i),Rn(o,n,r,i),An(e,t,o,n)}else{o=Vn(n,t.legacyContext),i=Ln(e,t,n,r,o);var l=Re!==0;if(typeof i=="object"&&i!==null&&typeof i.render=="function"&&i.$$typeof===void 0)Rn(i,n,r,o),An(e,t,i,n);else if(l){r=t.treeContext,t.treeContext=Mt(r,1,0);try{Z(e,t,i)}finally{t.treeContext=r}}else Z(e,t,i)}else if(typeof n=="string"){switch(i=t.blockedSegment,o=Ro(i.chunks,n,r,e.responseState,i.formatContext),i.lastPushedText=!1,l=i.formatContext,i.formatContext=Po(l,n,r),Ut(e,t,o),i.formatContext=l,n){case"area":case"base":case"br":case"col":case"embed":case"hr":case"img":case"input":case"keygen":case"link":case"meta":case"param":case"source":case"track":case"wbr":break;default:i.chunks.push("</",n,">")}i.lastPushedText=!1}else{switch(n){case Oo:case Ho:case Nr:case kr:case Cr:Z(e,t,r.children);return;case Dr:Z(e,t,r.children);return;case Ao:throw Error(g(343));case Fr:e:{n=t.blockedBoundary,i=t.blockedSegment,o=r.fallback,r=r.children,l=new Set;var s={id:null,rootSegmentID:-1,parentFlushed:!1,pendingTasks:0,forceClientRender:!1,completedSegments:[],byteSize:0,fallbackAbortableTasks:l,errorDigest:null},u=rt(e,i.chunks.length,s,i.formatContext,!1,!1);i.children.push(u),i.lastPushedText=!1;var d=rt(e,0,null,i.formatContext,!1,!1);d.parentFlushed=!0,t.blockedBoundary=s,t.blockedSegment=d;try{if(Ut(e,t,r),e.responseState.generateStaticMarkup||d.lastPushedText&&d.textEmbedded&&d.chunks.push("<!-- -->"),d.status=1,it(s,d),s.pendingTasks===0)break e}catch(c){d.status=4,s.forceClientRender=!0,s.errorDigest=Me(e,c)}finally{t.blockedBoundary=n,t.blockedSegment=i}t=Jt(e,o,n,u,l,t.legacyContext,t.context,t.treeContext),e.pingedTasks.push(t)}return}if(typeof n=="object"&&n!==null)switch(n.$$typeof){case Tr:if(r=Ln(e,t,n.render,r,i),Re!==0){n=t.treeContext,t.treeContext=Mt(n,1,0);try{Z(e,t,r)}finally{t.treeContext=n}}else Z(e,t,r);return;case zr:n=n.type,r=Hn(n,r),It(e,t,n,r,i);return;case Er:if(i=r.children,n=n._context,r=r.value,o=n._currentValue2,n._currentValue2=r,l=Se,Se=r={parent:l,depth:l===null?0:l.depth+1,context:n,parentValue:o,value:r},t.context=r,Z(e,t,i),e=Se,e===null)throw Error(g(403));r=e.parentValue,e.context._currentValue2=r===Wo?e.context._defaultValue:r,e=Se=e.parent,t.context=e;return;case jr:r=r.children,r=r(n._currentValue2),Z(e,t,r);return;case Zt:i=n._init,n=i(n._payload),r=Hn(n,r),It(e,t,n,r,void 0);return}throw Error(g(130,n==null?n:typeof n,""))}}function Z(e,t,n){if(t.node=n,typeof n=="object"&&n!==null){switch(n.$$typeof){case Lo:It(e,t,n.type,n.props,n.ref);return;case br:throw Error(g(257));case Zt:var r=n._init;n=r(n._payload),Z(e,t,n);return}if($t(n)){On(e,t,n);return}if(n===null||typeof n!="object"?r=null:(r=Pn&&n[Pn]||n["@@iterator"],r=typeof r=="function"?r:null),r&&(r=r.call(n))){if(n=r.next(),!n.done){var i=[];do i.push(n.value),n=r.next();while(!n.done);On(e,t,i)}return}throw e=Object.prototype.toString.call(n),Error(g(31,e==="[object Object]"?"object with keys {"+Object.keys(n).join(", ")+"}":e))}typeof n=="string"?(r=t.blockedSegment,r.lastPushedText=_n(t.blockedSegment.chunks,n,e.responseState,r.lastPushedText)):typeof n=="number"&&(r=t.blockedSegment,r.lastPushedText=_n(t.blockedSegment.chunks,""+n,e.responseState,r.lastPushedText))}function On(e,t,n){for(var r=n.length,i=0;i<r;i++){var o=t.treeContext;t.treeContext=Mt(o,r,i);try{Ut(e,t,n[i])}finally{t.treeContext=o}}}function Ut(e,t,n){var r=t.blockedSegment.formatContext,i=t.legacyContext,o=t.context;try{return Z(e,t,n)}catch(u){if(Yt(),typeof u=="object"&&u!==null&&typeof u.then=="function"){n=u;var l=t.blockedSegment,s=rt(e,l.chunks.length,null,l.formatContext,l.lastPushedText,!0);l.children.push(s),l.lastPushedText=!1,e=Jt(e,t.node,t.blockedBoundary,s,t.abortSet,t.legacyContext,t.context,t.treeContext).ping,n.then(e,e),t.blockedSegment.formatContext=r,t.legacyContext=i,t.context=o,tt(o)}else throw t.blockedSegment.formatContext=r,t.legacyContext=i,t.context=o,tt(o),u}}function ni(e){var t=e.blockedBoundary;e=e.blockedSegment,e.status=3,Ur(this,t,e)}function Ir(e,t,n){var r=e.blockedBoundary;e.blockedSegment.status=3,r===null?(t.allPendingTasks--,t.status!==2&&(t.status=2,t.destination!==null&&t.destination.push(null))):(r.pendingTasks--,r.forceClientRender||(r.forceClientRender=!0,e=n===void 0?Error(g(432)):n,r.errorDigest=t.onError(e),r.parentFlushed&&t.clientRenderedBoundaries.push(r)),r.fallbackAbortableTasks.forEach(function(i){return Ir(i,t,n)}),r.fallbackAbortableTasks.clear(),t.allPendingTasks--,t.allPendingTasks===0&&(r=t.onAllReady,r()))}function it(e,t){if(t.chunks.length===0&&t.children.length===1&&t.children[0].boundary===null){var n=t.children[0];n.id=t.id,n.parentFlushed=!0,n.status===1&&it(e,n)}else e.completedSegments.push(t)}function Ur(e,t,n){if(t===null){if(n.parentFlushed){if(e.completedRootSegment!==null)throw Error(g(389));e.completedRootSegment=n}e.pendingRootTasks--,e.pendingRootTasks===0&&(e.onShellError=ze,t=e.onShellReady,t())}else t.pendingTasks--,t.forceClientRender||(t.pendingTasks===0?(n.parentFlushed&&n.status===1&&it(t,n),t.parentFlushed&&e.completedBoundaries.push(t),t.fallbackAbortableTasks.forEach(ni,e),t.fallbackAbortableTasks.clear()):n.parentFlushed&&n.status===1&&(it(t,n),t.completedSegments.length===1&&t.parentFlushed&&e.partialBoundaries.push(t)));e.allPendingTasks--,e.allPendingTasks===0&&(e=e.onAllReady,e())}function Br(e){if(e.status!==2){var t=Se,n=Tt.current;Tt.current=Bn;var r=Xe;Xe=e.responseState;try{var i=e.pingedTasks,o;for(o=0;o<i.length;o++){var l=i[o],s=e,u=l.blockedSegment;if(u.status===0){tt(l.context);try{Z(s,l,l.node),s.responseState.generateStaticMarkup||u.lastPushedText&&u.textEmbedded&&u.chunks.push("<!-- -->"),l.abortSet.delete(l),u.status=1,Ur(s,l.blockedBoundary,u)}catch(T){if(Yt(),typeof T=="object"&&T!==null&&typeof T.then=="function"){var d=l.ping;T.then(d,d)}else{l.abortSet.delete(l),u.status=4;var c=l.blockedBoundary,f=T,b=Me(s,f);if(c===null?ot(s,f):(c.pendingTasks--,c.forceClientRender||(c.forceClientRender=!0,c.errorDigest=b,c.parentFlushed&&s.clientRenderedBoundaries.push(c))),s.allPendingTasks--,s.allPendingTasks===0){var w=s.onAllReady;w()}}}finally{}}}i.splice(0,o),e.destination!==null&&Kt(e,e.destination)}catch(T){Me(e,T),ot(e,T)}finally{Xe=r,Tt.current=n,n===Bn&&tt(t)}}}function Ae(e,t,n){switch(n.parentFlushed=!0,n.status){case 0:var r=n.id=e.nextSegmentId++;return n.lastPushedText=!1,n.textEmbedded=!1,e=e.responseState,t.push('<template id="'),t.push(e.placeholderPrefix),e=r.toString(16),t.push(e),t.push('"></template>');case 1:n.status=2;var i=!0;r=n.chunks;var o=0;n=n.children;for(var l=0;l<n.length;l++){for(i=n[l];o<i.index;o++)t.push(r[o]);i=pt(e,t,i)}for(;o<r.length-1;o++)t.push(r[o]);return o<r.length&&(i=t.push(r[o])),i;default:throw Error(g(390))}}function pt(e,t,n){var r=n.boundary;if(r===null)return Ae(e,t,n);if(r.parentFlushed=!0,r.forceClientRender)return e.responseState.generateStaticMarkup||(r=r.errorDigest,t.push("<!--$!-->"),t.push("<template"),r&&(t.push(' data-dgst="'),r=I(r),t.push(r),t.push('"')),t.push("></template>")),Ae(e,t,n),e=e.responseState.generateStaticMarkup?!0:t.push("<!--/$-->"),e;if(0<r.pendingTasks){r.rootSegmentID=e.nextSegmentId++,0<r.completedSegments.length&&e.partialBoundaries.push(r);var i=e.responseState,o=i.nextSuspenseID++;return i=i.boundaryPrefix+o.toString(16),r=r.id=i,zn(t,e.responseState,r),Ae(e,t,n),t.push("<!--/$-->")}if(r.byteSize>e.progressiveChunkSize)return r.rootSegmentID=e.nextSegmentId++,e.completedBoundaries.push(r),zn(t,e.responseState,r.id),Ae(e,t,n),t.push("<!--/$-->");if(e.responseState.generateStaticMarkup||t.push("<!--$-->"),n=r.completedSegments,n.length!==1)throw Error(g(391));return pt(e,t,n[0]),e=e.responseState.generateStaticMarkup?!0:t.push("<!--/$-->"),e}function Wn(e,t,n){return Mo(t,e.responseState,n.formatContext,n.id),pt(e,t,n),Io(t,n.formatContext)}function qn(e,t,n){for(var r=n.completedSegments,i=0;i<r.length;i++)Lr(e,t,n,r[i]);if(r.length=0,e=e.responseState,r=n.id,n=n.rootSegmentID,t.push(e.startInlineScript),e.sentCompleteBoundaryFunction?t.push('$RC("'):(e.sentCompleteBoundaryFunction=!0,t.push('function $RC(a,b){a=document.getElementById(a);b=document.getElementById(b);b.parentNode.removeChild(b);if(a){a=a.previousSibling;var f=a.parentNode,c=a.nextSibling,e=0;do{if(c&&8===c.nodeType){var d=c.data;if("/$"===d)if(0===e)break;else e--;else"$"!==d&&"$?"!==d&&"$!"!==d||e++}d=c.nextSibling;f.removeChild(c);c=d}while(c);for(;b.firstChild;)f.insertBefore(b.firstChild,c);a.data="$";a._reactRetry&&a._reactRetry()}};$RC("')),r===null)throw Error(g(395));return n=n.toString(16),t.push(r),t.push('","'),t.push(e.segmentPrefix),t.push(n),t.push('")<\/script>')}function Lr(e,t,n,r){if(r.status===2)return!0;var i=r.id;if(i===-1){if((r.id=n.rootSegmentID)===-1)throw Error(g(392));return Wn(e,t,r)}return Wn(e,t,r),e=e.responseState,t.push(e.startInlineScript),e.sentCompleteSegmentFunction?t.push('$RS("'):(e.sentCompleteSegmentFunction=!0,t.push('function $RS(a,b){a=document.getElementById(a);b=document.getElementById(b);for(a.parentNode.removeChild(a);a.firstChild;)b.parentNode.insertBefore(a.firstChild,b);b.parentNode.removeChild(b)};$RS("')),t.push(e.segmentPrefix),i=i.toString(16),t.push(i),t.push('","'),t.push(e.placeholderPrefix),t.push(i),t.push('")<\/script>')}function Kt(e,t){try{var n=e.completedRootSegment;if(n!==null&&e.pendingRootTasks===0){pt(e,t,n),e.completedRootSegment=null;var r=e.responseState.bootstrapChunks;for(n=0;n<r.length-1;n++)t.push(r[n]);n<r.length&&t.push(r[n])}var i=e.clientRenderedBoundaries,o;for(o=0;o<i.length;o++){var l=i[o];r=t;var s=e.responseState,u=l.id,d=l.errorDigest,c=l.errorMessage,f=l.errorComponentStack;if(r.push(s.startInlineScript),s.sentClientRenderFunction?r.push('$RX("'):(s.sentClientRenderFunction=!0,r.push('function $RX(b,c,d,e){var a=document.getElementById(b);a&&(b=a.previousSibling,b.data="$!",a=a.dataset,c&&(a.dgst=c),d&&(a.msg=d),e&&(a.stck=e),b._reactRetry&&b._reactRetry())};$RX("')),u===null)throw Error(g(395));if(r.push(u),r.push('"'),d||c||f){r.push(",");var b=jt(d||"");r.push(b)}if(c||f){r.push(",");var w=jt(c||"");r.push(w)}if(f){r.push(",");var T=jt(f);r.push(T)}if(!r.push(")<\/script>")){e.destination=null,o++,i.splice(0,o);return}}i.splice(0,o);var E=e.completedBoundaries;for(o=0;o<E.length;o++)if(!qn(e,t,E[o])){e.destination=null,o++,E.splice(0,o);return}E.splice(0,o);var te=e.partialBoundaries;for(o=0;o<te.length;o++){var pe=te[o];e:{i=e,l=t;var Ee=pe.completedSegments;for(s=0;s<Ee.length;s++)if(!Lr(i,l,pe,Ee[s])){s++,Ee.splice(0,s);var vt=!1;break e}Ee.splice(0,s),vt=!0}if(!vt){e.destination=null,o++,te.splice(0,o);return}}te.splice(0,o);var je=e.completedBoundaries;for(o=0;o<je.length;o++)if(!qn(e,t,je[o])){e.destination=null,o++,je.splice(0,o);return}je.splice(0,o)}finally{e.allPendingTasks===0&&e.pingedTasks.length===0&&e.clientRenderedBoundaries.length===0&&e.completedBoundaries.length===0&&t.push(null)}}function ri(e,t){try{var n=e.abortableTasks;n.forEach(function(r){return Ir(r,e,t)}),n.clear(),e.destination!==null&&Kt(e,e.destination)}catch(r){Me(e,r),ot(e,r)}}function oi(){}function Ar(e,t,n,r){var i=!1,o=null,l="",s={push:function(d){return d!==null&&(l+=d),!0},destroy:function(d){i=!0,o=d}},u=!1;if(e=ti(e,Bo(n,t?t.identifierPrefix:void 0),{insertionMode:1,selectedValue:null},1/0,oi,void 0,function(){u=!0},void 0,void 0),Br(e),ri(e,r),e.status===1)e.status=2,s.destroy(e.fatalError);else if(e.status!==2&&e.destination===null){e.destination=s;try{Kt(e,s)}catch(d){Me(e,d),ot(e,d)}}if(i)throw o;if(!u)throw Error(g(426));return l}Ne.renderToNodeStream=function(){throw Error(g(207))};Ne.renderToStaticMarkup=function(e,t){return Ar(e,t,!0,'The server used "renderToStaticMarkup" which does not support Suspense. If you intended to have the server wait for the suspended component please switch to "renderToReadableStream" which supports Suspense on the server')};Ne.renderToStaticNodeStream=function(){throw Error(g(208))};Ne.renderToString=function(e,t){return Ar(e,t,!1,'The server used "renderToString" which does not support Suspense. If you intended for this Suspense boundary to render the fallback content on the server consider throwing an Error somewhere within the Suspense boundary. If you intended to have the server wait for the suspended component please switch to "renderToReadableStream" which supports Suspense on the server')};Ne.version="18.3.1";var Qt={};/**
 * @license React
 * react-dom-server.browser.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Hr=S;function v(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var G=null,X=0;function p(e,t){if(t.length!==0)if(512<t.length)0<X&&(e.enqueue(new Uint8Array(G.buffer,0,X)),G=new Uint8Array(512),X=0),e.enqueue(t);else{var n=G.length-X;n<t.length&&(n===0?e.enqueue(G):(G.set(t.subarray(0,n),X),e.enqueue(G),t=t.subarray(n)),G=new Uint8Array(512),X=0),G.set(t,X),X+=t.length}}function j(e,t){return p(e,t),!0}function Zn(e){G&&0<X&&(e.enqueue(new Uint8Array(G.buffer,0,X)),G=null,X=0)}var Or=new TextEncoder;function x(e){return Or.encode(e)}function m(e){return Or.encode(e)}function Wr(e,t){typeof e.error=="function"?e.error(t):e.close()}var A=Object.prototype.hasOwnProperty,ii=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Gn={},Xn={};function qr(e){return A.call(Xn,e)?!0:A.call(Gn,e)?!1:ii.test(e)?Xn[e]=!0:(Gn[e]=!0,!1)}function B(e,t,n,r,i,o,l){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=i,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=o,this.removeEmptyString=l}var $={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){$[e]=new B(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];$[t]=new B(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){$[e]=new B(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){$[e]=new B(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){$[e]=new B(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){$[e]=new B(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){$[e]=new B(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){$[e]=new B(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){$[e]=new B(e,5,!1,e.toLowerCase(),null,!1,!1)});var en=/[\-:]([a-z])/g;function tn(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(en,tn);$[t]=new B(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(en,tn);$[t]=new B(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(en,tn);$[t]=new B(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){$[e]=new B(e,1,!1,e.toLowerCase(),null,!1,!1)});$.xlinkHref=new B("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){$[e]=new B(e,1,!1,e.toLowerCase(),null,!0,!0)});var Ye={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},li=["Webkit","ms","Moz","O"];Object.keys(Ye).forEach(function(e){li.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Ye[t]=Ye[e]})});var si=/["'&<>]/;function P(e){if(typeof e=="boolean"||typeof e=="number")return""+e;e=""+e;var t=si.exec(e);if(t){var n="",r,i=0;for(r=t.index;r<e.length;r++){switch(e.charCodeAt(r)){case 34:t="&quot;";break;case 38:t="&amp;";break;case 39:t="&#x27;";break;case 60:t="&lt;";break;case 62:t="&gt;";break;default:continue}i!==r&&(n+=e.substring(i,r)),i=r+1,n+=t}e=i!==r?n+e.substring(i,r):n}return e}var ai=/([A-Z])/g,ui=/^ms-/,Bt=Array.isArray,ci=m("<script>"),di=m("<\/script>"),mi=m('<script src="'),pi=m('<script type="module" src="'),Yn=m('" async=""><\/script>'),fi=/(<\/|<)(s)(cript)/gi;function hi(e,t,n,r){return""+t+(n==="s"?"\\u0073":"\\u0053")+r}function gi(e,t,n,r,i){e=e===void 0?"":e,t=t===void 0?ci:m('<script nonce="'+P(t)+'">');var o=[];if(n!==void 0&&o.push(t,x((""+n).replace(fi,hi)),di),r!==void 0)for(n=0;n<r.length;n++)o.push(mi,x(P(r[n])),Yn);if(i!==void 0)for(r=0;r<i.length;r++)o.push(pi,x(P(i[r])),Yn);return{bootstrapChunks:o,startInlineScript:t,placeholderPrefix:m(e+"P:"),segmentPrefix:m(e+"S:"),boundaryPrefix:e+"B:",idPrefix:e,nextSuspenseID:0,sentCompleteSegmentFunction:!1,sentCompleteBoundaryFunction:!1,sentClientRenderFunction:!1}}function Q(e,t){return{insertionMode:e,selectedValue:t}}function vi(e){return Q(e==="http://www.w3.org/2000/svg"?2:e==="http://www.w3.org/1998/Math/MathML"?3:0,null)}function xi(e,t,n){switch(t){case"select":return Q(1,n.value!=null?n.value:n.defaultValue);case"svg":return Q(2,null);case"math":return Q(3,null);case"foreignObject":return Q(1,null);case"table":return Q(4,null);case"thead":case"tbody":case"tfoot":return Q(5,null);case"colgroup":return Q(7,null);case"tr":return Q(6,null)}return 4<=e.insertionMode||e.insertionMode===0?Q(1,null):e}var nn=m("<!-- -->");function Jn(e,t,n,r){return t===""?r:(r&&e.push(nn),e.push(x(P(t))),!0)}var Kn=new Map,Si=m(' style="'),Qn=m(":"),yi=m(";");function Zr(e,t,n){if(typeof n!="object")throw Error(v(62));t=!0;for(var r in n)if(A.call(n,r)){var i=n[r];if(i!=null&&typeof i!="boolean"&&i!==""){if(r.indexOf("--")===0){var o=x(P(r));i=x(P((""+i).trim()))}else{o=r;var l=Kn.get(o);l!==void 0||(l=m(P(o.replace(ai,"-$1").toLowerCase().replace(ui,"-ms-"))),Kn.set(o,l)),o=l,i=typeof i=="number"?i===0||A.call(Ye,r)?x(""+i):x(i+"px"):x(P((""+i).trim()))}t?(t=!1,e.push(Si,o,Qn,i)):e.push(yi,o,Qn,i)}}t||e.push(ve)}var ue=m(" "),Ce=m('="'),ve=m('"'),er=m('=""');function q(e,t,n,r){switch(n){case"style":Zr(e,t,r);return;case"defaultValue":case"defaultChecked":case"innerHTML":case"suppressContentEditableWarning":case"suppressHydrationWarning":return}if(!(2<n.length)||n[0]!=="o"&&n[0]!=="O"||n[1]!=="n"&&n[1]!=="N"){if(t=$.hasOwnProperty(n)?$[n]:null,t!==null){switch(typeof r){case"function":case"symbol":return;case"boolean":if(!t.acceptsBooleans)return}switch(n=x(t.attributeName),t.type){case 3:r&&e.push(ue,n,er);break;case 4:r===!0?e.push(ue,n,er):r!==!1&&e.push(ue,n,Ce,x(P(r)),ve);break;case 5:isNaN(r)||e.push(ue,n,Ce,x(P(r)),ve);break;case 6:!isNaN(r)&&1<=r&&e.push(ue,n,Ce,x(P(r)),ve);break;default:t.sanitizeURL&&(r=""+r),e.push(ue,n,Ce,x(P(r)),ve)}}else if(qr(n)){switch(typeof r){case"function":case"symbol":return;case"boolean":if(t=n.toLowerCase().slice(0,5),t!=="data-"&&t!=="aria-")return}e.push(ue,x(n),Ce,x(P(r)),ve)}}}var ce=m(">"),tr=m("/>");function Je(e,t,n){if(t!=null){if(n!=null)throw Error(v(60));if(typeof t!="object"||!("__html"in t))throw Error(v(61));t=t.__html,t!=null&&e.push(x(""+t))}}function wi(e){var t="";return Hr.Children.forEach(e,function(n){n!=null&&(t+=n)}),t}var Ft=m(' selected=""');function Dt(e,t,n,r){e.push(ee(n));var i=n=null,o;for(o in t)if(A.call(t,o)){var l=t[o];if(l!=null)switch(o){case"children":n=l;break;case"dangerouslySetInnerHTML":i=l;break;default:q(e,r,o,l)}}return e.push(ce),Je(e,i,n),typeof n=="string"?(e.push(x(P(n))),null):n}var zt=m(`
`),bi=/^[a-zA-Z][a-zA-Z:_\.\-\d]*$/,nr=new Map;function ee(e){var t=nr.get(e);if(t===void 0){if(!bi.test(e))throw Error(v(65,e));t=m("<"+e),nr.set(e,t)}return t}var Ci=m("<!DOCTYPE html>");function Ni(e,t,n,r,i){switch(t){case"select":e.push(ee("select"));var o=null,l=null;for(c in n)if(A.call(n,c)){var s=n[c];if(s!=null)switch(c){case"children":o=s;break;case"dangerouslySetInnerHTML":l=s;break;case"defaultValue":case"value":break;default:q(e,r,c,s)}}return e.push(ce),Je(e,l,o),o;case"option":l=i.selectedValue,e.push(ee("option"));var u=s=null,d=null,c=null;for(o in n)if(A.call(n,o)){var f=n[o];if(f!=null)switch(o){case"children":s=f;break;case"selected":d=f;break;case"dangerouslySetInnerHTML":c=f;break;case"value":u=f;default:q(e,r,o,f)}}if(l!=null)if(n=u!==null?""+u:wi(s),Bt(l)){for(r=0;r<l.length;r++)if(""+l[r]===n){e.push(Ft);break}}else""+l===n&&e.push(Ft);else d&&e.push(Ft);return e.push(ce),Je(e,c,s),s;case"textarea":e.push(ee("textarea")),c=l=o=null;for(s in n)if(A.call(n,s)&&(u=n[s],u!=null))switch(s){case"children":c=u;break;case"value":o=u;break;case"defaultValue":l=u;break;case"dangerouslySetInnerHTML":throw Error(v(91));default:q(e,r,s,u)}if(o===null&&l!==null&&(o=l),e.push(ce),c!=null){if(o!=null)throw Error(v(92));if(Bt(c)&&1<c.length)throw Error(v(93));o=""+c}return typeof o=="string"&&o[0]===`
`&&e.push(zt),o!==null&&e.push(x(P(""+o))),null;case"input":e.push(ee("input")),u=c=s=o=null;for(l in n)if(A.call(n,l)&&(d=n[l],d!=null))switch(l){case"children":case"dangerouslySetInnerHTML":throw Error(v(399,"input"));case"defaultChecked":u=d;break;case"defaultValue":s=d;break;case"checked":c=d;break;case"value":o=d;break;default:q(e,r,l,d)}return c!==null?q(e,r,"checked",c):u!==null&&q(e,r,"checked",u),o!==null?q(e,r,"value",o):s!==null&&q(e,r,"value",s),e.push(tr),null;case"menuitem":e.push(ee("menuitem"));for(var b in n)if(A.call(n,b)&&(o=n[b],o!=null))switch(b){case"children":case"dangerouslySetInnerHTML":throw Error(v(400));default:q(e,r,b,o)}return e.push(ce),null;case"title":e.push(ee("title")),o=null;for(f in n)if(A.call(n,f)&&(l=n[f],l!=null))switch(f){case"children":o=l;break;case"dangerouslySetInnerHTML":throw Error(v(434));default:q(e,r,f,l)}return e.push(ce),o;case"listing":case"pre":e.push(ee(t)),l=o=null;for(u in n)if(A.call(n,u)&&(s=n[u],s!=null))switch(u){case"children":o=s;break;case"dangerouslySetInnerHTML":l=s;break;default:q(e,r,u,s)}if(e.push(ce),l!=null){if(o!=null)throw Error(v(60));if(typeof l!="object"||!("__html"in l))throw Error(v(61));n=l.__html,n!=null&&(typeof n=="string"&&0<n.length&&n[0]===`
`?e.push(zt,x(n)):e.push(x(""+n)))}return typeof o=="string"&&o[0]===`
`&&e.push(zt),o;case"area":case"base":case"br":case"col":case"embed":case"hr":case"img":case"keygen":case"link":case"meta":case"param":case"source":case"track":case"wbr":e.push(ee(t));for(var w in n)if(A.call(n,w)&&(o=n[w],o!=null))switch(w){case"children":case"dangerouslySetInnerHTML":throw Error(v(399,t));default:q(e,r,w,o)}return e.push(tr),null;case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return Dt(e,n,t,r);case"html":return i.insertionMode===0&&e.push(Ci),Dt(e,n,t,r);default:if(t.indexOf("-")===-1&&typeof n.is!="string")return Dt(e,n,t,r);e.push(ee(t)),l=o=null;for(d in n)if(A.call(n,d)&&(s=n[d],s!=null))switch(d){case"children":o=s;break;case"dangerouslySetInnerHTML":l=s;break;case"style":Zr(e,r,s);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":break;default:qr(d)&&typeof s!="function"&&typeof s!="symbol"&&e.push(ue,x(d),Ce,x(P(s)),ve)}return e.push(ce),Je(e,l,o),o}}var ki=m("</"),Ei=m(">"),ji=m('<template id="'),Ti=m('"></template>'),Fi=m("<!--$-->"),Di=m('<!--$?--><template id="'),zi=m('"></template>'),_i=m("<!--$!-->"),Pi=m("<!--/$-->"),Vi=m("<template"),$i=m('"'),Ri=m(' data-dgst="');m(' data-msg="');m(' data-stck="');var Mi=m("></template>");function rr(e,t,n){if(p(e,Di),n===null)throw Error(v(395));return p(e,n),j(e,zi)}var Ii=m('<div hidden id="'),Ui=m('">'),Bi=m("</div>"),Li=m('<svg aria-hidden="true" style="display:none" id="'),Ai=m('">'),Hi=m("</svg>"),Oi=m('<math aria-hidden="true" style="display:none" id="'),Wi=m('">'),qi=m("</math>"),Zi=m('<table hidden id="'),Gi=m('">'),Xi=m("</table>"),Yi=m('<table hidden><tbody id="'),Ji=m('">'),Ki=m("</tbody></table>"),Qi=m('<table hidden><tr id="'),el=m('">'),tl=m("</tr></table>"),nl=m('<table hidden><colgroup id="'),rl=m('">'),ol=m("</colgroup></table>");function il(e,t,n,r){switch(n.insertionMode){case 0:case 1:return p(e,Ii),p(e,t.segmentPrefix),p(e,x(r.toString(16))),j(e,Ui);case 2:return p(e,Li),p(e,t.segmentPrefix),p(e,x(r.toString(16))),j(e,Ai);case 3:return p(e,Oi),p(e,t.segmentPrefix),p(e,x(r.toString(16))),j(e,Wi);case 4:return p(e,Zi),p(e,t.segmentPrefix),p(e,x(r.toString(16))),j(e,Gi);case 5:return p(e,Yi),p(e,t.segmentPrefix),p(e,x(r.toString(16))),j(e,Ji);case 6:return p(e,Qi),p(e,t.segmentPrefix),p(e,x(r.toString(16))),j(e,el);case 7:return p(e,nl),p(e,t.segmentPrefix),p(e,x(r.toString(16))),j(e,rl);default:throw Error(v(397))}}function ll(e,t){switch(t.insertionMode){case 0:case 1:return j(e,Bi);case 2:return j(e,Hi);case 3:return j(e,qi);case 4:return j(e,Xi);case 5:return j(e,Ki);case 6:return j(e,tl);case 7:return j(e,ol);default:throw Error(v(397))}}var sl=m('function $RS(a,b){a=document.getElementById(a);b=document.getElementById(b);for(a.parentNode.removeChild(a);a.firstChild;)b.parentNode.insertBefore(a.firstChild,b);b.parentNode.removeChild(b)};$RS("'),al=m('$RS("'),ul=m('","'),cl=m('")<\/script>'),dl=m('function $RC(a,b){a=document.getElementById(a);b=document.getElementById(b);b.parentNode.removeChild(b);if(a){a=a.previousSibling;var f=a.parentNode,c=a.nextSibling,e=0;do{if(c&&8===c.nodeType){var d=c.data;if("/$"===d)if(0===e)break;else e--;else"$"!==d&&"$?"!==d&&"$!"!==d||e++}d=c.nextSibling;f.removeChild(c);c=d}while(c);for(;b.firstChild;)f.insertBefore(b.firstChild,c);a.data="$";a._reactRetry&&a._reactRetry()}};$RC("'),ml=m('$RC("'),pl=m('","'),fl=m('")<\/script>'),hl=m('function $RX(b,c,d,e){var a=document.getElementById(b);a&&(b=a.previousSibling,b.data="$!",a=a.dataset,c&&(a.dgst=c),d&&(a.msg=d),e&&(a.stck=e),b._reactRetry&&b._reactRetry())};$RX("'),gl=m('$RX("'),vl=m('"'),xl=m(")<\/script>"),_t=m(","),Sl=/[<\u2028\u2029]/g;function Pt(e){return JSON.stringify(e).replace(Sl,function(t){switch(t){case"<":return"\\u003c";case"\u2028":return"\\u2028";case"\u2029":return"\\u2029";default:throw Error("escapeJSStringsForInstructionScripts encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React")}})}var $e=Object.assign,yl=Symbol.for("react.element"),Gr=Symbol.for("react.portal"),Xr=Symbol.for("react.fragment"),Yr=Symbol.for("react.strict_mode"),Jr=Symbol.for("react.profiler"),Kr=Symbol.for("react.provider"),Qr=Symbol.for("react.context"),eo=Symbol.for("react.forward_ref"),to=Symbol.for("react.suspense"),no=Symbol.for("react.suspense_list"),ro=Symbol.for("react.memo"),rn=Symbol.for("react.lazy"),wl=Symbol.for("react.scope"),bl=Symbol.for("react.debug_trace_mode"),Cl=Symbol.for("react.legacy_hidden"),Nl=Symbol.for("react.default_value"),or=Symbol.iterator;function Lt(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Xr:return"Fragment";case Gr:return"Portal";case Jr:return"Profiler";case Yr:return"StrictMode";case to:return"Suspense";case no:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Qr:return(e.displayName||"Context")+".Consumer";case Kr:return(e._context.displayName||"Context")+".Provider";case eo:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case ro:return t=e.displayName||null,t!==null?t:Lt(e.type)||"Memo";case rn:t=e._payload,e=e._init;try{return Lt(e(t))}catch{}}return null}var oo={};function ir(e,t){if(e=e.contextTypes,!e)return oo;var n={},r;for(r in e)n[r]=t[r];return n}var ye=null;function ft(e,t){if(e!==t){e.context._currentValue=e.parentValue,e=e.parent;var n=t.parent;if(e===null){if(n!==null)throw Error(v(401))}else{if(n===null)throw Error(v(401));ft(e,n)}t.context._currentValue=t.value}}function io(e){e.context._currentValue=e.parentValue,e=e.parent,e!==null&&io(e)}function lo(e){var t=e.parent;t!==null&&lo(t),e.context._currentValue=e.value}function so(e,t){if(e.context._currentValue=e.parentValue,e=e.parent,e===null)throw Error(v(402));e.depth===t.depth?ft(e,t):so(e,t)}function ao(e,t){var n=t.parent;if(n===null)throw Error(v(402));e.depth===n.depth?ft(e,n):ao(e,n),t.context._currentValue=t.value}function lt(e){var t=ye;t!==e&&(t===null?lo(e):e===null?io(t):t.depth===e.depth?ft(t,e):t.depth>e.depth?so(t,e):ao(t,e),ye=e)}var lr={isMounted:function(){return!1},enqueueSetState:function(e,t){e=e._reactInternals,e.queue!==null&&e.queue.push(t)},enqueueReplaceState:function(e,t){e=e._reactInternals,e.replace=!0,e.queue=[t]},enqueueForceUpdate:function(){}};function sr(e,t,n,r){var i=e.state!==void 0?e.state:null;e.updater=lr,e.props=n,e.state=i;var o={queue:[],replace:!1};e._reactInternals=o;var l=t.contextType;if(e.context=typeof l=="object"&&l!==null?l._currentValue:r,l=t.getDerivedStateFromProps,typeof l=="function"&&(l=l(n,i),i=l==null?i:$e({},i,l),e.state=i),typeof t.getDerivedStateFromProps!="function"&&typeof e.getSnapshotBeforeUpdate!="function"&&(typeof e.UNSAFE_componentWillMount=="function"||typeof e.componentWillMount=="function"))if(t=e.state,typeof e.componentWillMount=="function"&&e.componentWillMount(),typeof e.UNSAFE_componentWillMount=="function"&&e.UNSAFE_componentWillMount(),t!==e.state&&lr.enqueueReplaceState(e,e.state,null),o.queue!==null&&0<o.queue.length)if(t=o.queue,l=o.replace,o.queue=null,o.replace=!1,l&&t.length===1)e.state=t[0];else{for(o=l?t[0]:e.state,i=!0,l=l?1:0;l<t.length;l++){var s=t[l];s=typeof s=="function"?s.call(e,o,n,r):s,s!=null&&(i?(i=!1,o=$e({},o,s)):$e(o,s))}e.state=o}else o.queue=null}var kl={id:1,overflow:""};function At(e,t,n){var r=e.id;e=e.overflow;var i=32-Ke(r)-1;r&=~(1<<i),n+=1;var o=32-Ke(t)+i;if(30<o){var l=i-i%5;return o=(r&(1<<l)-1).toString(32),r>>=l,i-=l,{id:1<<32-Ke(t)+i|n<<i|r,overflow:o+e}}return{id:1<<o|n<<i|r,overflow:e}}var Ke=Math.clz32?Math.clz32:Tl,El=Math.log,jl=Math.LN2;function Tl(e){return e>>>=0,e===0?32:31-(El(e)/jl|0)|0}function Fl(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Dl=typeof Object.is=="function"?Object.is:Fl,ie=null,on=null,Qe=null,k=null,_e=!1,st=!1,Ie=0,me=null,ht=0;function xe(){if(ie===null)throw Error(v(321));return ie}function ar(){if(0<ht)throw Error(v(312));return{memoizedState:null,queue:null,next:null}}function ln(){return k===null?Qe===null?(_e=!1,Qe=k=ar()):(_e=!0,k=Qe):k.next===null?(_e=!1,k=k.next=ar()):(_e=!0,k=k.next),k}function sn(){on=ie=null,st=!1,Qe=null,ht=0,k=me=null}function uo(e,t){return typeof t=="function"?t(e):t}function ur(e,t,n){if(ie=xe(),k=ln(),_e){var r=k.queue;if(t=r.dispatch,me!==null&&(n=me.get(r),n!==void 0)){me.delete(r),r=k.memoizedState;do r=e(r,n.action),n=n.next;while(n!==null);return k.memoizedState=r,[r,t]}return[k.memoizedState,t]}return e=e===uo?typeof t=="function"?t():t:n!==void 0?n(t):t,k.memoizedState=e,e=k.queue={last:null,dispatch:null},e=e.dispatch=zl.bind(null,ie,e),[k.memoizedState,e]}function cr(e,t){if(ie=xe(),k=ln(),t=t===void 0?null:t,k!==null){var n=k.memoizedState;if(n!==null&&t!==null){var r=n[1];e:if(r===null)r=!1;else{for(var i=0;i<r.length&&i<t.length;i++)if(!Dl(t[i],r[i])){r=!1;break e}r=!0}if(r)return n[0]}}return e=e(),k.memoizedState=[e,t],e}function zl(e,t,n){if(25<=ht)throw Error(v(301));if(e===ie)if(st=!0,e={action:n,next:null},me===null&&(me=new Map),n=me.get(t),n===void 0)me.set(t,e);else{for(t=n;t.next!==null;)t=t.next;t.next=e}}function _l(){throw Error(v(394))}function He(){}var dr={readContext:function(e){return e._currentValue},useContext:function(e){return xe(),e._currentValue},useMemo:cr,useReducer:ur,useRef:function(e){ie=xe(),k=ln();var t=k.memoizedState;return t===null?(e={current:e},k.memoizedState=e):t},useState:function(e){return ur(uo,e)},useInsertionEffect:He,useLayoutEffect:function(){},useCallback:function(e,t){return cr(function(){return e},t)},useImperativeHandle:He,useEffect:He,useDebugValue:He,useDeferredValue:function(e){return xe(),e},useTransition:function(){return xe(),[!1,_l]},useId:function(){var e=on.treeContext,t=e.overflow;e=e.id,e=(e&~(1<<32-Ke(e)-1)).toString(32)+t;var n=et;if(n===null)throw Error(v(404));return t=Ie++,e=":"+n.idPrefix+"R"+e,0<t&&(e+="H"+t.toString(32)),e+":"},useMutableSource:function(e,t){return xe(),t(e._source)},useSyncExternalStore:function(e,t,n){if(n===void 0)throw Error(v(407));return n()}},et=null,Vt=Hr.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentDispatcher;function Pl(e){return console.error(e),null}function Pe(){}function Vl(e,t,n,r,i,o,l,s,u){var d=[],c=new Set;return t={destination:null,responseState:t,progressiveChunkSize:r===void 0?12800:r,status:0,fatalError:null,nextSegmentId:0,allPendingTasks:0,pendingRootTasks:0,completedRootSegment:null,abortableTasks:c,pingedTasks:d,clientRenderedBoundaries:[],completedBoundaries:[],partialBoundaries:[],onError:i===void 0?Pl:i,onAllReady:o===void 0?Pe:o,onShellReady:l===void 0?Pe:l,onShellError:s===void 0?Pe:s,onFatalError:u===void 0?Pe:u},n=at(t,0,null,n,!1,!1),n.parentFlushed=!0,e=an(t,e,null,n,c,oo,null,kl),d.push(e),t}function an(e,t,n,r,i,o,l,s){e.allPendingTasks++,n===null?e.pendingRootTasks++:n.pendingTasks++;var u={node:t,ping:function(){var d=e.pingedTasks;d.push(u),d.length===1&&po(e)},blockedBoundary:n,blockedSegment:r,abortSet:i,legacyContext:o,context:l,treeContext:s};return i.add(u),u}function at(e,t,n,r,i,o){return{status:0,id:-1,index:t,parentFlushed:!1,chunks:[],children:[],formatContext:r,boundary:n,lastPushedText:i,textEmbedded:o}}function Ue(e,t){if(e=e.onError(t),e!=null&&typeof e!="string")throw Error('onError returned something with a type other than "string". onError should return a string and may return null or undefined but must not return anything else. It received something of type "'+typeof e+'" instead');return e}function ut(e,t){var n=e.onShellError;n(t),n=e.onFatalError,n(t),e.destination!==null?(e.status=2,Wr(e.destination,t)):(e.status=1,e.fatalError=t)}function mr(e,t,n,r,i){for(ie={},on=t,Ie=0,e=n(r,i);st;)st=!1,Ie=0,ht+=1,k=null,e=n(r,i);return sn(),e}function pr(e,t,n,r){var i=n.render(),o=r.childContextTypes;if(o!=null){var l=t.legacyContext;if(typeof n.getChildContext!="function")r=l;else{n=n.getChildContext();for(var s in n)if(!(s in o))throw Error(v(108,Lt(r)||"Unknown",s));r=$e({},l,n)}t.legacyContext=r,Y(e,t,i),t.legacyContext=l}else Y(e,t,i)}function fr(e,t){if(e&&e.defaultProps){t=$e({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function Ht(e,t,n,r,i){if(typeof n=="function")if(n.prototype&&n.prototype.isReactComponent){i=ir(n,t.legacyContext);var o=n.contextType;o=new n(r,typeof o=="object"&&o!==null?o._currentValue:i),sr(o,n,r,i),pr(e,t,o,n)}else{o=ir(n,t.legacyContext),i=mr(e,t,n,r,o);var l=Ie!==0;if(typeof i=="object"&&i!==null&&typeof i.render=="function"&&i.$$typeof===void 0)sr(i,n,r,o),pr(e,t,i,n);else if(l){r=t.treeContext,t.treeContext=At(r,1,0);try{Y(e,t,i)}finally{t.treeContext=r}}else Y(e,t,i)}else if(typeof n=="string"){switch(i=t.blockedSegment,o=Ni(i.chunks,n,r,e.responseState,i.formatContext),i.lastPushedText=!1,l=i.formatContext,i.formatContext=xi(l,n,r),Ot(e,t,o),i.formatContext=l,n){case"area":case"base":case"br":case"col":case"embed":case"hr":case"img":case"input":case"keygen":case"link":case"meta":case"param":case"source":case"track":case"wbr":break;default:i.chunks.push(ki,x(n),Ei)}i.lastPushedText=!1}else{switch(n){case Cl:case bl:case Yr:case Jr:case Xr:Y(e,t,r.children);return;case no:Y(e,t,r.children);return;case wl:throw Error(v(343));case to:e:{n=t.blockedBoundary,i=t.blockedSegment,o=r.fallback,r=r.children,l=new Set;var s={id:null,rootSegmentID:-1,parentFlushed:!1,pendingTasks:0,forceClientRender:!1,completedSegments:[],byteSize:0,fallbackAbortableTasks:l,errorDigest:null},u=at(e,i.chunks.length,s,i.formatContext,!1,!1);i.children.push(u),i.lastPushedText=!1;var d=at(e,0,null,i.formatContext,!1,!1);d.parentFlushed=!0,t.blockedBoundary=s,t.blockedSegment=d;try{if(Ot(e,t,r),d.lastPushedText&&d.textEmbedded&&d.chunks.push(nn),d.status=1,ct(s,d),s.pendingTasks===0)break e}catch(c){d.status=4,s.forceClientRender=!0,s.errorDigest=Ue(e,c)}finally{t.blockedBoundary=n,t.blockedSegment=i}t=an(e,o,n,u,l,t.legacyContext,t.context,t.treeContext),e.pingedTasks.push(t)}return}if(typeof n=="object"&&n!==null)switch(n.$$typeof){case eo:if(r=mr(e,t,n.render,r,i),Ie!==0){n=t.treeContext,t.treeContext=At(n,1,0);try{Y(e,t,r)}finally{t.treeContext=n}}else Y(e,t,r);return;case ro:n=n.type,r=fr(n,r),Ht(e,t,n,r,i);return;case Kr:if(i=r.children,n=n._context,r=r.value,o=n._currentValue,n._currentValue=r,l=ye,ye=r={parent:l,depth:l===null?0:l.depth+1,context:n,parentValue:o,value:r},t.context=r,Y(e,t,i),e=ye,e===null)throw Error(v(403));r=e.parentValue,e.context._currentValue=r===Nl?e.context._defaultValue:r,e=ye=e.parent,t.context=e;return;case Qr:r=r.children,r=r(n._currentValue),Y(e,t,r);return;case rn:i=n._init,n=i(n._payload),r=fr(n,r),Ht(e,t,n,r,void 0);return}throw Error(v(130,n==null?n:typeof n,""))}}function Y(e,t,n){if(t.node=n,typeof n=="object"&&n!==null){switch(n.$$typeof){case yl:Ht(e,t,n.type,n.props,n.ref);return;case Gr:throw Error(v(257));case rn:var r=n._init;n=r(n._payload),Y(e,t,n);return}if(Bt(n)){hr(e,t,n);return}if(n===null||typeof n!="object"?r=null:(r=or&&n[or]||n["@@iterator"],r=typeof r=="function"?r:null),r&&(r=r.call(n))){if(n=r.next(),!n.done){var i=[];do i.push(n.value),n=r.next();while(!n.done);hr(e,t,i)}return}throw e=Object.prototype.toString.call(n),Error(v(31,e==="[object Object]"?"object with keys {"+Object.keys(n).join(", ")+"}":e))}typeof n=="string"?(r=t.blockedSegment,r.lastPushedText=Jn(t.blockedSegment.chunks,n,e.responseState,r.lastPushedText)):typeof n=="number"&&(r=t.blockedSegment,r.lastPushedText=Jn(t.blockedSegment.chunks,""+n,e.responseState,r.lastPushedText))}function hr(e,t,n){for(var r=n.length,i=0;i<r;i++){var o=t.treeContext;t.treeContext=At(o,r,i);try{Ot(e,t,n[i])}finally{t.treeContext=o}}}function Ot(e,t,n){var r=t.blockedSegment.formatContext,i=t.legacyContext,o=t.context;try{return Y(e,t,n)}catch(u){if(sn(),typeof u=="object"&&u!==null&&typeof u.then=="function"){n=u;var l=t.blockedSegment,s=at(e,l.chunks.length,null,l.formatContext,l.lastPushedText,!0);l.children.push(s),l.lastPushedText=!1,e=an(e,t.node,t.blockedBoundary,s,t.abortSet,t.legacyContext,t.context,t.treeContext).ping,n.then(e,e),t.blockedSegment.formatContext=r,t.legacyContext=i,t.context=o,lt(o)}else throw t.blockedSegment.formatContext=r,t.legacyContext=i,t.context=o,lt(o),u}}function $l(e){var t=e.blockedBoundary;e=e.blockedSegment,e.status=3,mo(this,t,e)}function co(e,t,n){var r=e.blockedBoundary;e.blockedSegment.status=3,r===null?(t.allPendingTasks--,t.status!==2&&(t.status=2,t.destination!==null&&t.destination.close())):(r.pendingTasks--,r.forceClientRender||(r.forceClientRender=!0,e=n===void 0?Error(v(432)):n,r.errorDigest=t.onError(e),r.parentFlushed&&t.clientRenderedBoundaries.push(r)),r.fallbackAbortableTasks.forEach(function(i){return co(i,t,n)}),r.fallbackAbortableTasks.clear(),t.allPendingTasks--,t.allPendingTasks===0&&(r=t.onAllReady,r()))}function ct(e,t){if(t.chunks.length===0&&t.children.length===1&&t.children[0].boundary===null){var n=t.children[0];n.id=t.id,n.parentFlushed=!0,n.status===1&&ct(e,n)}else e.completedSegments.push(t)}function mo(e,t,n){if(t===null){if(n.parentFlushed){if(e.completedRootSegment!==null)throw Error(v(389));e.completedRootSegment=n}e.pendingRootTasks--,e.pendingRootTasks===0&&(e.onShellError=Pe,t=e.onShellReady,t())}else t.pendingTasks--,t.forceClientRender||(t.pendingTasks===0?(n.parentFlushed&&n.status===1&&ct(t,n),t.parentFlushed&&e.completedBoundaries.push(t),t.fallbackAbortableTasks.forEach($l,e),t.fallbackAbortableTasks.clear()):n.parentFlushed&&n.status===1&&(ct(t,n),t.completedSegments.length===1&&t.parentFlushed&&e.partialBoundaries.push(t)));e.allPendingTasks--,e.allPendingTasks===0&&(e=e.onAllReady,e())}function po(e){if(e.status!==2){var t=ye,n=Vt.current;Vt.current=dr;var r=et;et=e.responseState;try{var i=e.pingedTasks,o;for(o=0;o<i.length;o++){var l=i[o],s=e,u=l.blockedSegment;if(u.status===0){lt(l.context);try{Y(s,l,l.node),u.lastPushedText&&u.textEmbedded&&u.chunks.push(nn),l.abortSet.delete(l),u.status=1,mo(s,l.blockedBoundary,u)}catch(T){if(sn(),typeof T=="object"&&T!==null&&typeof T.then=="function"){var d=l.ping;T.then(d,d)}else{l.abortSet.delete(l),u.status=4;var c=l.blockedBoundary,f=T,b=Ue(s,f);if(c===null?ut(s,f):(c.pendingTasks--,c.forceClientRender||(c.forceClientRender=!0,c.errorDigest=b,c.parentFlushed&&s.clientRenderedBoundaries.push(c))),s.allPendingTasks--,s.allPendingTasks===0){var w=s.onAllReady;w()}}}finally{}}}i.splice(0,o),e.destination!==null&&un(e,e.destination)}catch(T){Ue(e,T),ut(e,T)}finally{et=r,Vt.current=n,n===dr&&lt(t)}}}function Oe(e,t,n){switch(n.parentFlushed=!0,n.status){case 0:var r=n.id=e.nextSegmentId++;return n.lastPushedText=!1,n.textEmbedded=!1,e=e.responseState,p(t,ji),p(t,e.placeholderPrefix),e=x(r.toString(16)),p(t,e),j(t,Ti);case 1:n.status=2;var i=!0;r=n.chunks;var o=0;n=n.children;for(var l=0;l<n.length;l++){for(i=n[l];o<i.index;o++)p(t,r[o]);i=gt(e,t,i)}for(;o<r.length-1;o++)p(t,r[o]);return o<r.length&&(i=j(t,r[o])),i;default:throw Error(v(390))}}function gt(e,t,n){var r=n.boundary;if(r===null)return Oe(e,t,n);if(r.parentFlushed=!0,r.forceClientRender)r=r.errorDigest,j(t,_i),p(t,Vi),r&&(p(t,Ri),p(t,x(P(r))),p(t,$i)),j(t,Mi),Oe(e,t,n);else if(0<r.pendingTasks){r.rootSegmentID=e.nextSegmentId++,0<r.completedSegments.length&&e.partialBoundaries.push(r);var i=e.responseState,o=i.nextSuspenseID++;i=m(i.boundaryPrefix+o.toString(16)),r=r.id=i,rr(t,e.responseState,r),Oe(e,t,n)}else if(r.byteSize>e.progressiveChunkSize)r.rootSegmentID=e.nextSegmentId++,e.completedBoundaries.push(r),rr(t,e.responseState,r.id),Oe(e,t,n);else{if(j(t,Fi),n=r.completedSegments,n.length!==1)throw Error(v(391));gt(e,t,n[0])}return j(t,Pi)}function gr(e,t,n){return il(t,e.responseState,n.formatContext,n.id),gt(e,t,n),ll(t,n.formatContext)}function vr(e,t,n){for(var r=n.completedSegments,i=0;i<r.length;i++)fo(e,t,n,r[i]);if(r.length=0,e=e.responseState,r=n.id,n=n.rootSegmentID,p(t,e.startInlineScript),e.sentCompleteBoundaryFunction?p(t,ml):(e.sentCompleteBoundaryFunction=!0,p(t,dl)),r===null)throw Error(v(395));return n=x(n.toString(16)),p(t,r),p(t,pl),p(t,e.segmentPrefix),p(t,n),j(t,fl)}function fo(e,t,n,r){if(r.status===2)return!0;var i=r.id;if(i===-1){if((r.id=n.rootSegmentID)===-1)throw Error(v(392));return gr(e,t,r)}return gr(e,t,r),e=e.responseState,p(t,e.startInlineScript),e.sentCompleteSegmentFunction?p(t,al):(e.sentCompleteSegmentFunction=!0,p(t,sl)),p(t,e.segmentPrefix),i=x(i.toString(16)),p(t,i),p(t,ul),p(t,e.placeholderPrefix),p(t,i),j(t,cl)}function un(e,t){G=new Uint8Array(512),X=0;try{var n=e.completedRootSegment;if(n!==null&&e.pendingRootTasks===0){gt(e,t,n),e.completedRootSegment=null;var r=e.responseState.bootstrapChunks;for(n=0;n<r.length-1;n++)p(t,r[n]);n<r.length&&j(t,r[n])}var i=e.clientRenderedBoundaries,o;for(o=0;o<i.length;o++){var l=i[o];r=t;var s=e.responseState,u=l.id,d=l.errorDigest,c=l.errorMessage,f=l.errorComponentStack;if(p(r,s.startInlineScript),s.sentClientRenderFunction?p(r,gl):(s.sentClientRenderFunction=!0,p(r,hl)),u===null)throw Error(v(395));p(r,u),p(r,vl),(d||c||f)&&(p(r,_t),p(r,x(Pt(d||"")))),(c||f)&&(p(r,_t),p(r,x(Pt(c||"")))),f&&(p(r,_t),p(r,x(Pt(f)))),j(r,xl)}i.splice(0,o);var b=e.completedBoundaries;for(o=0;o<b.length;o++)vr(e,t,b[o]);b.splice(0,o),Zn(t),G=new Uint8Array(512),X=0;var w=e.partialBoundaries;for(o=0;o<w.length;o++){var T=w[o];e:{i=e,l=t;var E=T.completedSegments;for(s=0;s<E.length;s++)if(!fo(i,l,T,E[s])){s++,E.splice(0,s);var te=!1;break e}E.splice(0,s),te=!0}if(!te){e.destination=null,o++,w.splice(0,o);return}}w.splice(0,o);var pe=e.completedBoundaries;for(o=0;o<pe.length;o++)vr(e,t,pe[o]);pe.splice(0,o)}finally{Zn(t),e.allPendingTasks===0&&e.pingedTasks.length===0&&e.clientRenderedBoundaries.length===0&&e.completedBoundaries.length===0&&t.close()}}function xr(e,t){try{var n=e.abortableTasks;n.forEach(function(r){return co(r,e,t)}),n.clear(),e.destination!==null&&un(e,e.destination)}catch(r){Ue(e,r),ut(e,r)}}Qt.renderToReadableStream=function(e,t){return new Promise(function(n,r){var i,o,l=new Promise(function(c,f){o=c,i=f}),s=Vl(e,gi(t?t.identifierPrefix:void 0,t?t.nonce:void 0,t?t.bootstrapScriptContent:void 0,t?t.bootstrapScripts:void 0,t?t.bootstrapModules:void 0),vi(t?t.namespaceURI:void 0),t?t.progressiveChunkSize:void 0,t?t.onError:void 0,o,function(){var c=new ReadableStream({type:"bytes",pull:function(f){if(s.status===1)s.status=2,Wr(f,s.fatalError);else if(s.status!==2&&s.destination===null){s.destination=f;try{un(s,f)}catch(b){Ue(s,b),ut(s,b)}}},cancel:function(){xr(s)}},{highWaterMark:0});c.allReady=l,n(c)},function(c){l.catch(function(){}),r(c)},i);if(t&&t.signal){var u=t.signal,d=function(){xr(s,u.reason),u.removeEventListener("abort",d)};u.addEventListener("abort",d)}po(s)})};Qt.version="18.3.1";var ke,ho;ke=Ne,ho=Qt;we.version=ke.version;we.renderToString=ke.renderToString;we.renderToStaticMarkup=ke.renderToStaticMarkup;we.renderToNodeStream=ke.renderToNodeStream;we.renderToStaticNodeStream=ke.renderToStaticNodeStream;we.renderToReadableStream=ho.renderToReadableStream;const Rl=({children:e})=>a.jsxDEV(a.Fragment,{children:e},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/CreateAgentButton.jsx",lineNumber:10,columnNumber:10},globalThis),Ml=({primaryColor:e="#4B74AA",secondaryColor:t="#2C3E50",isDark:n=!0,isPreviewVisible:r=!1,logoUrl:i="/PRIMARY CLEAR.png",buttonOpacity:o=.15,onGetStarted:l=()=>{}})=>{const[s,u]=S.useState(!1),d=[{title:"Custom Landing Page + Domain",description:"Your own branded web presence with custom domain integration",icon:"🌐",details:["Personalized URL","Brand-matched design","SEO optimization"]},{title:"Embeddable Agent Widget",description:"Seamlessly integrate your AI assistant anywhere",icon:"🔌",details:["One-click installation","Customizable appearance","Mobile responsive"]},{title:"Lead Management & Analytics",description:"Comprehensive client interaction tracking",icon:"📊",details:["Real-time dashboard","Conversion tracking","Performance insights"]},{title:"Case Marketplace Access",description:"Connect with potential clients in your area",icon:"🤝",details:["Local client matching","Practice area filtering","Direct messaging"]}];return S.useEffect(()=>{if(r){const c=document.querySelector(".create-agent-overlay");c&&c.classList.add("visible")}},[r]),a.jsxDEV(Rl,{children:r&&a.jsxDEV("div",{className:"create-agent-overlay",style:{opacity:0,transform:"translateY(20px)",animation:"fadeIn 0.3s forwards"},children:a.jsxDEV("div",{className:"create-agent-button",onMouseEnter:()=>u(!0),onMouseLeave:()=>u(!1),style:{animation:"glow 3s ease-in-out infinite"},children:s?a.jsxDEV("div",{className:"content-container",style:{width:"100%",height:"100%",display:"flex",flexDirection:"column",gap:"24px",animation:"fadeIn 0.3s forwards"},children:[a.jsxDEV("div",{style:{display:"flex",alignItems:"center",gap:"16px",borderBottom:"1px solid rgba(216,87,34,0.4)",paddingBottom:"16px"},children:[a.jsxDEV("div",{className:"create-agent-icon",children:i&&i!=="/PRIMARY CLEAR.png"?a.jsxDEV("img",{src:i,alt:"Logo",style:{width:"100%",height:"100%",objectFit:"contain"},onError:c=>{console.error("Error loading logo:",c),c.target.style.display="none"}},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/CreateAgentButton.jsx",lineNumber:157,columnNumber:23},globalThis):a.jsxDEV("div",{style:{width:12,height:12,borderRadius:"50%",backgroundColor:"white",animation:"pulse 2s infinite ease-in-out"}},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/CreateAgentButton.jsx",lineNumber:171,columnNumber:23},globalThis)},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/CreateAgentButton.jsx",lineNumber:155,columnNumber:19},globalThis),a.jsxDEV("div",{children:[a.jsxDEV("h2",{className:"feature-title",style:{fontSize:"24px"},children:"Create My Agent"},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/CreateAgentButton.jsx",lineNumber:183,columnNumber:21},globalThis),a.jsxDEV("p",{className:"feature-description",style:{fontSize:"14px",marginTop:"4px"},children:"Get started with your AI legal assistant"},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/CreateAgentButton.jsx",lineNumber:186,columnNumber:21},globalThis)]},void 0,!0,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/CreateAgentButton.jsx",lineNumber:182,columnNumber:19},globalThis)]},void 0,!0,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/CreateAgentButton.jsx",lineNumber:146,columnNumber:17},globalThis),a.jsxDEV("div",{className:"feature-grid",children:d.map((c,f)=>a.jsxDEV("div",{className:"feature-card",style:{animation:`fadeInUp 0.3s forwards ${f*.05}s`},children:[a.jsxDEV("span",{className:"feature-icon",children:c.icon},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/CreateAgentButton.jsx",lineNumber:201,columnNumber:23},globalThis),a.jsxDEV("h3",{className:"feature-title",children:c.title},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/CreateAgentButton.jsx",lineNumber:202,columnNumber:23},globalThis),a.jsxDEV("p",{className:"feature-description",children:c.description},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/CreateAgentButton.jsx",lineNumber:203,columnNumber:23},globalThis),a.jsxDEV("div",{className:"feature-tags",children:c.details.map((b,w)=>a.jsxDEV("span",{className:"feature-tag",children:b},w,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/CreateAgentButton.jsx",lineNumber:206,columnNumber:27},globalThis))},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/CreateAgentButton.jsx",lineNumber:204,columnNumber:23},globalThis)]},c.title,!0,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/CreateAgentButton.jsx",lineNumber:194,columnNumber:21},globalThis))},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/CreateAgentButton.jsx",lineNumber:192,columnNumber:17},globalThis),a.jsxDEV("button",{className:"get-started-button",onClick:()=>{console.log("Create agent clicked"),typeof window.handleGetStarted=="function"?window.handleGetStarted():typeof l=="function"?l():window.location.href="/login"},children:["Get Started",a.jsxDEV("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:a.jsxDEV("path",{d:"M5 12h14M12 5l7 7-7 7",strokeLinecap:"round",strokeLinejoin:"round"},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/CreateAgentButton.jsx",lineNumber:232,columnNumber:21},globalThis)},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/CreateAgentButton.jsx",lineNumber:231,columnNumber:19},globalThis)]},void 0,!0,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/CreateAgentButton.jsx",lineNumber:215,columnNumber:17},globalThis)]},void 0,!0,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/CreateAgentButton.jsx",lineNumber:135,columnNumber:15},globalThis):a.jsxDEV(a.Fragment,{children:[a.jsxDEV("div",{className:"create-agent-icon",children:a.jsxDEV("div",{style:{width:8,height:8,borderRadius:"50%",backgroundColor:"#D85722",animation:"pulse 2s infinite ease-in-out"}},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/CreateAgentButton.jsx",lineNumber:122,columnNumber:19},globalThis)},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/CreateAgentButton.jsx",lineNumber:121,columnNumber:17},globalThis),a.jsxDEV("span",{className:"create-agent-text",children:"Create My Agent"},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/CreateAgentButton.jsx",lineNumber:132,columnNumber:17},globalThis)]},void 0,!0,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/CreateAgentButton.jsx",lineNumber:120,columnNumber:15},globalThis)},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/CreateAgentButton.jsx",lineNumber:111,columnNumber:11},globalThis)},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/CreateAgentButton.jsx",lineNumber:103,columnNumber:9},globalThis)},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/CreateAgentButton.jsx",lineNumber:101,columnNumber:5},globalThis)};const Il=`
  .config-tab:focus {
    outline: none !important;
    box-shadow: none !important;
    border-color: #D85722 !important;
  }
  .config-tab.active {
    border-bottom-color: #D85722 !important;
  }
  .config-tab {
    outline: none !important;
    -webkit-tap-highlight-color: transparent !important;
  }
  button.config-tab::-moz-focus-inner {
    border: 0 !important;
  }
  button:focus-visible {
    outline: none !important;
  }
  button.config-tab.active::after {
    display: none !important;
  }

  /* Consistent styling for all input elements */
  .config-section input[type="text"],
  .config-section input[type="tel"],
  .config-section input[type="url"],
  .config-section input[type="email"],
  .config-section textarea,
  .config-section select {
    padding: 10px 12px;
    border-radius: 8px;
    border: 1px solid rgba(0, 0, 0, 0.15);
    background-color: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(3px);
    font-size: 14px;
    width: 100%;
    transition: all 0.2s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  }

  .config-section input[type="text"]:focus,
  .config-section input[type="tel"]:focus,
  .config-section input[type="url"]:focus,
  .config-section input[type="email"]:focus,
  .config-section textarea:focus,
  .config-section select:focus {
    border-color: #D85722;
    box-shadow: 0 0 0 2px rgba(216, 87, 34, 0.2);
    background-color: rgba(255, 255, 255, 0.07);
    outline: none;
  }

  [data-theme="dark"] .config-section input[type="text"],
  [data-theme="dark"] .config-section input[type="tel"],
  [data-theme="dark"] .config-section input[type="url"],
  [data-theme="dark"] .config-section input[type="email"],
  [data-theme="dark"] .config-section textarea,
  [data-theme="dark"] .config-section select {
    background-color: rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(3px);
    border-color: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.9);
  }

  [data-theme="dark"] .config-section input[type="text"]:focus,
  [data-theme="dark"] .config-section input[type="tel"]:focus,
  [data-theme="dark"] .config-section input[type="url"]:focus,
  [data-theme="dark"] .config-section input[type="email"]:focus,
  [data-theme="dark"] .config-section textarea:focus,
  [data-theme="dark"] .config-section select:focus {
    border-color: #D85722;
    box-shadow: 0 0 0 2px rgba(216, 87, 34, 0.3);
    background-color: rgba(0, 0, 0, 0.2);
  }

  /* File input styling */
  .config-section input[type="file"] {
    padding: 8px;
    background-color: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(3px);
    border-radius: 8px;
    border: 1px solid rgba(0, 0, 0, 0.12);
    cursor: pointer;
  }

  [data-theme="dark"] .config-section input[type="file"] {
    background-color: rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(3px);
    border-color: rgba(255, 255, 255, 0.12);
    color: rgba(255, 255, 255, 0.8);
  }

  /* Color picker styling */
  .config-section .color-dot-button {
    border: 2px solid rgba(0, 0, 0, 0.1);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  [data-theme="dark"] .config-section .color-dot-button {
    border-color: rgba(255, 255, 255, 0.15);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  }
`,fs=({firmName:e,logoUrl:t,state:n,primaryColor:r,secondaryColor:i,backgroundColor:o,backgroundOpacity:l,welcomeMessage:s,informationGathering:u,practiceDescription:d,previewHeight:c,setPreviewHeight:f,attorneyName:b,selectedPracticeArea:w,handlePracticeAreaChange:T,showPreview:E,setShowPreview:te,handleLogoUpload:pe,handleRemoveLogo:Ee,practiceAreas:vt,activeConfigTab:je,setActiveConfigTab:Ul,goToPreview:go,setFirmName:Bl,setAttorneyName:Ll,setPracticeDescription:Al,setState:Hl,setWelcomeMessage:Ol,setInformationGathering:Wl,setPrimaryColor:ql,setSecondaryColor:Zl,setBackgroundColor:Gl,setBackgroundOpacity:Xl,iframeRef:ne,firmUrl:Yl,setFirmUrl:Jl,isLoading:Kl,handleUrlSubmit:Ql,isDarkTheme:fe,setLogoUrl:es,buttonText:cn,setButtonText:ts,buttonOpacity:Be,setButtonOpacity:ns,practiceAreaBackgroundOpacity:xt,setPracticeAreaBackgroundOpacity:rs,attorneyAddress:dn="",setAttorneyAddress:os=()=>{},attorneyPhone:mn="",setAttorneyPhone:is=()=>{},schedulingLink:pn="",setSchedulingLink:ls=()=>{},buttonColor:fn="#2C3E50",setButtonColor:ss=()=>{},handleGetStarted:hn=()=>{}})=>{const[as,vo]=S.useState("100%"),[St,xo]=S.useState("url");S.useState(!1);const[gn,us]=S.useState("fadeIn"),[yt,cs]=S.useState("#634C38");S.useState(0);const Te=S.useRef(null),vn=S.useRef(null),xn=S.useRef(null),[So,Sn]=S.useState(!1),[yn,wn]=S.useState(0),[Fe,wt]=S.useState(0),[yo,bn]=S.useState(!1),bt=S.useRef(null),[ds,wo]=S.useState(!1);S.useState(!1);const[Cn,bo]=S.useState(!1),Ct=[a.jsxDEV("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:a.jsxDEV("path",{d:"M21 10L12 5L3 10L12 15L21 10ZM12 18.84L5 14.5V18L12 22L19 18V14.5L12 18.84Z",fill:"currentColor"},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:195,columnNumber:7},globalThis)},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:194,columnNumber:5},globalThis),a.jsxDEV("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",width:"24",height:"24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:a.jsxDEV("path",{d:"M22 12h-4l-3 9L9 3l-3 9H2"},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:197,columnNumber:183},globalThis)},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:197,columnNumber:5},globalThis),a.jsxDEV("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",width:"24",height:"24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[a.jsxDEV("path",{d:"m21 15-9-9-9 9"},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:198,columnNumber:183},globalThis),a.jsxDEV("path",{d:"m21 21-9-9-9 9"},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:198,columnNumber:209},globalThis)]},void 0,!0,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:198,columnNumber:5},globalThis),a.jsxDEV("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",width:"24",height:"24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[a.jsxDEV("circle",{cx:"12",cy:"12",r:"10"},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:199,columnNumber:183},globalThis),a.jsxDEV("path",{d:"m8 12 3 3 6-6"},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:199,columnNumber:215},globalThis)]},void 0,!0,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:199,columnNumber:5},globalThis),a.jsxDEV("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",width:"24",height:"24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[a.jsxDEV("rect",{width:"18",height:"18",x:"3",y:"3",rx:"2"},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:200,columnNumber:183},globalThis),a.jsxDEV("path",{d:"M8 12h8M12 8v8"},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:200,columnNumber:232},globalThis)]},void 0,!0,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:200,columnNumber:5},globalThis),a.jsxDEV("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",width:"24",height:"24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:a.jsxDEV("path",{d:"M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5L17 3Z"},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:201,columnNumber:183},globalThis)},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:201,columnNumber:5},globalThis),a.jsxDEV("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",width:"24",height:"24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:a.jsxDEV("path",{d:"M3 6h18M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:202,columnNumber:183},globalThis)},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:202,columnNumber:5},globalThis),a.jsxDEV("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",width:"24",height:"24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[a.jsxDEV("rect",{width:"18",height:"11",x:"3",y:"11",rx:"2"},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:203,columnNumber:183},globalThis),a.jsxDEV("path",{d:"M7 11V7a5 5 0 0 1 10 0v4"},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:203,columnNumber:233},globalThis)]},void 0,!0,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:203,columnNumber:5},globalThis),a.jsxDEV("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",width:"24",height:"24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[a.jsxDEV("path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:204,columnNumber:183},globalThis),a.jsxDEV("polyline",{points:"14 2 14 8 20 8"},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:204,columnNumber:264},globalThis)]},void 0,!0,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:204,columnNumber:5},globalThis)],Nn=["Acquire Higher-Value Clients","Automate Client Intake Process","Qualify Leads 24/7","Improve Client Response Time","Reduce Administrative Costs","Focus On High-Value Work","Enhance Client Experience","Scale Your Practice Efficiently"],he=[{text:"24/7",emoji:a.jsxDEV("svg",{xmlns:"http://www.w3.org/2000/svg",width:"18",height:"18",viewBox:"0 0 24 24",fill:"currentColor",children:a.jsxDEV("path",{d:"M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10 10-4.5 10-10S17.5 2 12 2zm0 18c-4.4 0-8-3.6-8-8s3.6-8 8-8 8 3.6 8 8-3.6 8-8 8zm.5-13H11v6l5.2 3.2.8-1.3-4.5-2.7V7z"},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:236,columnNumber:131},globalThis)},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:236,columnNumber:28},globalThis)},{text:"Intelligent",emoji:a.jsxDEV("svg",{xmlns:"http://www.w3.org/2000/svg",width:"18",height:"18",viewBox:"0 0 24 24",fill:"currentColor",children:a.jsxDEV("path",{d:"M12 3C7.59 3 4 6.59 4 11c0 4.41 3.59 8 8 8 .28 0 .55-.04.81-.08C10.44 20.21 8 22.35 8 25h2c0-2.24 2.24-4.04 5-4.04s5 1.8 5 4.04h2c0-2.65-2.44-4.79-4.81-6.08.26.04.53.08.81.08 4.41 0 8-3.59 8-8 0-4.41-3.59-8-8-8zm0 14c-3.31 0-6-2.69-6-6s2.69-6 6-6 6 2.69 6 6-2.69 6-6 6z"},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:237,columnNumber:138},globalThis)},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:237,columnNumber:35},globalThis)},{text:"Efficient",emoji:a.jsxDEV("svg",{xmlns:"http://www.w3.org/2000/svg",width:"18",height:"18",viewBox:"0 0 24 24",fill:"currentColor",children:a.jsxDEV("path",{d:"M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8zm.5-13H11v6l5.25 3.15.75-1.23-4.5-2.67z"},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:238,columnNumber:136},globalThis)},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:238,columnNumber:33},globalThis)},{text:"Reliable",emoji:a.jsxDEV("svg",{xmlns:"http://www.w3.org/2000/svg",width:"18",height:"18",viewBox:"0 0 24 24",fill:"currentColor",children:[a.jsxDEV("path",{d:"M12 2L4 5v6.09c0 5.05 3.41 9.76 8 10.91 4.59-1.15 8-5.86 8-10.91V5l-8-3zm6 9.09c0 4-2.55 7.7-6 8.83-3.45-1.13-6-4.82-6-8.83v-4.7l6-2.25 6 2.25v4.7z"},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:239,columnNumber:135},globalThis),a.jsxDEV("path",{d:"M10.21 13.84L8.35 12 7 13.34l3.21 3.19 5.96-5.99L14.82 9 10.21 13.84z"},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:239,columnNumber:294},globalThis)]},void 0,!0,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:239,columnNumber:32},globalThis)},{text:"Caring",emoji:a.jsxDEV("svg",{xmlns:"http://www.w3.org/2000/svg",width:"18",height:"18",viewBox:"0 0 24 24",fill:"currentColor",children:a.jsxDEV("path",{d:"M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:240,columnNumber:133},globalThis)},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:240,columnNumber:30},globalThis)},{text:"Helpful",emoji:a.jsxDEV("svg",{xmlns:"http://www.w3.org/2000/svg",width:"18",height:"18",viewBox:"0 0 24 24",fill:"currentColor",children:a.jsxDEV("path",{d:"M16 13h-3V3h-2v10H8l4 4 4-4zM4 19v2h16v-2H4z"},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:241,columnNumber:134},globalThis)},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:241,columnNumber:31},globalThis)},{text:"Patient",emoji:a.jsxDEV("svg",{xmlns:"http://www.w3.org/2000/svg",width:"18",height:"18",viewBox:"0 0 24 24",fill:"currentColor",children:a.jsxDEV("path",{d:"M6 2v6h.01L6 8.01 10 12l-4 4 .01.01H6V22h12v-5.99h-.01L18 16l-4-4 4-3.99-.01-.01H18V2H6zm10 14.5V20H8v-3.5l4-4 4 4z"},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:242,columnNumber:134},globalThis)},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:242,columnNumber:31},globalThis)},{text:"Detailed",emoji:a.jsxDEV("svg",{xmlns:"http://www.w3.org/2000/svg",width:"18",height:"18",viewBox:"0 0 24 24",fill:"currentColor",children:a.jsxDEV("path",{d:"M3 13h2v-2H3v2zm0 4h2v-2H3v2zm0-8h2V7H3v2zm4 4h14v-2H7v2zm0 4h14v-2H7v2zM7 7v2h14V7H7z"},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:243,columnNumber:135},globalThis)},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:243,columnNumber:32},globalThis)},{text:"Balanced",emoji:a.jsxDEV("svg",{xmlns:"http://www.w3.org/2000/svg",width:"18",height:"18",viewBox:"0 0 24 24",fill:"currentColor",children:a.jsxDEV("path",{d:"M13 7h-2v2h2V7zm0 4h-2v2h2v-2zm4 0h-2v2h2v-2zm2-7h-1V2h-2v2H8V2H6v2H5c-1.11 0-1.99.9-1.99 2L3 20c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h16v13z"},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:244,columnNumber:135},globalThis)},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:244,columnNumber:32},globalThis)},{text:"Powerful",emoji:a.jsxDEV("svg",{xmlns:"http://www.w3.org/2000/svg",width:"18",height:"18",viewBox:"0 0 24 24",fill:"currentColor",children:a.jsxDEV("path",{d:"M19.77 7.23l.01-.01-3.72-3.72L15 4.56l2.11 2.11c-.94.36-1.61 1.26-1.61 2.33 0 1.38 1.12 2.5 2.5 2.5.36 0 .69-.08 1-.21v7.21c0 .55-.45 1-1 1s-1-.45-1-1V14c0-1.1-.9-2-2-2h-1V5c0-1.1-.9-2-2-2H6c-1.1 0-2 .9-2 2v16h10v-7.5h1.5v5c0 1.38 1.12 2.5 2.5 2.5s2.5-1.12 2.5-2.5V9c0-.69-.28-1.32-.73-1.77zM18 10c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1zM8 18v-4.5H6L10 6v5h2l-4 7z"},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:245,columnNumber:135},globalThis)},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:245,columnNumber:32},globalThis)}],Co=h=>h?["a","e","i","o","u"].includes(h.charAt(0).toLowerCase())?"An":"A":"An",No=h=>(h+1)%Ct.length,kn=h=>(h+1)%he.length;S.useEffect(()=>{if(St!=="url"||!Te.current)return;const h=["intake specialist","lead generator","client relations expert","legal consultant","case evaluator","case manager","scheduler","office assistant","LegalScout Agent"];try{const y=Te.current;if(!y)return;const D=y.querySelectorAll(".solari-flap");if(!D||D.length===0){console.warn("No flip elements found in the container");return}let H=0,O;const J=()=>{clearInterval(O),R(""),wn(0),O=setInterval(()=>{try{if(H=(H+1)%h.length,vn.current){const z=H===h.length-1?9:H+1;wn(z),Sn(!0),setTimeout(()=>{try{Sn(!1)}catch{}},300),setTimeout(()=>{try{Te.current&&le(h[H])}catch(F){console.warn("Error updating word in timeout:",F)}},50)}else Te.current&&le(h[H])}catch(z){console.warn("Error in animation interval:",z)}},5e3)},R=z=>{if(!D||D.length===0)return;const F=z||"";D.forEach((C,_)=>{try{if(!C)return;C.style.transform="rotateX(0deg)";const M=C.querySelector(".solari-front"),ae=C.querySelector(".solari-back");M&&(M.textContent=_<F.length?F[_].toLowerCase():" "),ae&&(ae.textContent=" "),z===""&&setTimeout(()=>{const jo=se(_<F.length?F[_].toLowerCase():" ");be(C,jo,0)},_*30)}catch(M){console.warn("Error resetting flap:",M)}})},be=(z,F,C)=>{if(!(!z||C>=F.length))try{const _=z.querySelector(".solari-back");_&&(_.textContent=F[C]),z.classList.add("flipping"),setTimeout(()=>{try{z.classList.remove("flipping");const M=z.querySelector(".solari-front");M&&(M.textContent=F[C]),z.style.transform="rotateX(0deg)",C<F.length-1&&setTimeout(()=>{be(z,F,C+1)},Math.random()*30+30)}catch(M){console.warn("Error in animateFlap:",M)}},150)}catch(_){console.warn("Error animating flap:",_)}},le=z=>{if(!(!D||D.length===0))try{const F=z.toLowerCase();for(let C=0;C<D.length;C++){const _=D[C];if(!_)continue;const M=C<F.length?F[C]:" ",ae=se(M);setTimeout(()=>{be(_,ae,0)},C*30)}}catch(F){console.warn("Error updating word:",F)}},se=z=>{const F="abcdefghijklmnopqrstuvwxyz0123456789 ",C=[],_=6+Math.floor(Math.random()*4);for(let M=0;M<_;M++){const ae=Math.floor(Math.random()*F.length);C.push(F[ae])}return C.push(z),C};return J(),()=>{clearInterval(O)}}catch(y){console.warn("Error in Solari board animation:",y)}},[St]),S.useEffect(()=>{const h=setInterval(()=>{wt(D=>(D+1)%Nn.length);const y=document.querySelector(".goal-text");y&&(y.style.animation="none",y.offsetWidth,y.style.animation="fadeChange 1.2s ease-in-out")},7e3);return()=>clearInterval(h)},[]),S.useEffect(()=>{if(bt.current)try{let h=0,y;const D=()=>{clearInterval(y),wt(0),H(0),y=setInterval(()=>{h=(h+1)%he.length,h===0&&(h=1),wt(h),bn(!0);const J=setTimeout(()=>{try{bn(!1)}catch(R){console.warn("Error resetting flip state:",R)}},600);return H(h),()=>clearTimeout(J)},7e3)},H=J=>{const R=bt.current;if(!R)return;const be=R.querySelector(".attr-front"),le=R.querySelector(".attr-back");if(be&&le){const se=he[J],z=document.createElement("div");z.className="emoji-circle";const F=se.emoji.type===void 0?se.emoji:we.renderToString(se.emoji);z.innerHTML=F;const C=document.createElement("span");C.className="attr-text",C.textContent=se.text,le.innerHTML="",le.appendChild(z),le.appendChild(C);const _=document.querySelector(".main-title-prefix");_&&(_.textContent=Co(se.text)),R.classList.add("flipping");const M=setTimeout(()=>{try{R.classList.remove("flipping"),be.innerHTML=le.innerHTML}catch(ae){console.warn("Error completing attribute flip:",ae)}},600);return()=>clearTimeout(M)}},O=setTimeout(()=>{D()},3e3);return()=>{clearInterval(y),clearTimeout(O)}}catch(h){console.warn("Error in attribute board animation:",h)}},[]),S.useEffect(()=>{const h=()=>{vo("100%")};return window.addEventListener("resize",h),()=>window.removeEventListener("resize",h)},[]);const ko=()=>{te(!1)},[ms,Nt]=S.useState({phase:"initial",direction:"none"});S.useEffect(()=>{if(E){Nt({phase:"animating",direction:"in"});const h=setTimeout(()=>{Nt({phase:"complete",direction:"in"})},500);return()=>clearTimeout(h)}else Nt({phase:"initial",direction:"none"})},[E]),S.useEffect(()=>(typeof window<"u"&&(window.triggerAutoConfigureButton=()=>{console.log("[SimpleDemoPage] Manual trigger of Auto-Configure button"),xn.current?(console.log("[SimpleDemoPage] Auto-Configure button found, clicking it"),xn.current.click()):console.error("[SimpleDemoPage] Auto-Configure button reference not available")}),()=>{typeof window<"u"&&delete window.triggerAutoConfigureButton}),[]),S.useEffect(()=>{if(!E)return;const h=y=>{if(y.data&&y.data.type==="iframeHeight"){const D=Math.max(500,y.data.height);f(D)}};return window.addEventListener("message",h),()=>{window.removeEventListener("message",h)}},[E,f]),S.useEffect(()=>{if(!E||!ne.current)return;const h=()=>{const y=window.innerHeight,D=60,H=80,O=40,J=y-D-H-O,R=Math.max(500,J);f(R)};return h(),window.addEventListener("resize",h),()=>{window.removeEventListener("resize",h)}},[E,ne,f]),S.useEffect(()=>{console.log("Modal state changed:",Cn),setTimeout(()=>{const h=document.querySelector(".try-it-button");h?(console.log("Try It Now button found in DOM after timeout"),h.addEventListener("click",()=>{console.log("Try It Now button clicked via direct event listener"),bo(!0)})):console.log("Try It Now button not found in DOM after timeout")},2e3)},[Cn]),S.useEffect(()=>{if(!ne.current)return;const y=ne.current.contentWindow,D=()=>{if(y){const O=R=>R?R.startsWith("#")?R:`#${R}`:"#000000",J={firmName:e,attorneyName:b,practiceAreas:w?[w]:[],state:n,practiceDescription:d,buttonText:cn,buttonOpacity:Be,attorneyAddress:dn,attorneyPhone:mn,schedulingLink:pn,primaryColor:O(r),secondaryColor:O(i),buttonColor:O(fn||i),backgroundColor:O(o),backgroundOpacity:l,practiceAreaBackgroundOpacity:xt,textBackgroundColor:O(yt),firmNameAnimation:gn,logoUrl:t||"",welcomeMessage:s,informationGathering:u,theme:fe?"dark":"light"};console.log("Sending customizations to iframe:",J),y.postMessage({type:"updateCustomizations",customizations:J},"*")}};D();const H=setInterval(D,300);return()=>clearInterval(H)},[e,b,w,n,d,cn,Be,dn,mn,pn,r,i,fn,o,l,xt,yt,gn,t,s,u,fe,ne]);const[kt,Eo]=S.useState(0),En=h=>{const y=Math.max(0,Math.min(h,3));Eo(y)};return S.useEffect(()=>{const h=y=>{y.key==="ArrowRight"?En(kt+1):y.key==="ArrowLeft"&&En(kt-1)};return document.addEventListener("keydown",h),()=>document.removeEventListener("keydown",h)},[kt]),S.useEffect(()=>{const h=()=>{const y=document.querySelector(".config-container");if(!y||E)return;const D=y.getBoundingClientRect();wo(D.top<window.innerHeight*.5)};return window.addEventListener("scroll",h,{passive:!0}),h(),()=>{window.removeEventListener("scroll",h)}},[E]),S.useEffect(()=>{w&&(xo("manual"),console.log("Practice area selected, setting configMode to manual:",w))},[w]),a.jsxDEV("div",{className:`demo-page-container ${E?"preview-active":""}`,children:[a.jsxDEV("style",{children:Il},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:1e3,columnNumber:7},globalThis),a.jsxDEV("style",{children:`
        /* Fix for scrollbar issues */
        .attribute-flipboard-wrapper {
          overflow: visible !important;
          max-width: 100% !important;
          box-sizing: border-box !important;
        }

        .attribute-flipboard {
          box-sizing: border-box !important;
          max-width: 100% !important;
        }

        /* Fix for mobile text size */
        @media (max-width: 480px) {
          .main-title-text {
            font-size: 1.2rem !important;
            line-height: 1.2 !important;
          }

          .main-title-container {
            flex-wrap: wrap !important;
            justify-content: center !important;
          }
        }
        `},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:1001,columnNumber:7},globalThis),!E&&a.jsxDEV(Ml,{primaryColor:r,secondaryColor:i,isDark:fe,isPreviewVisible:E,logoUrl:t,buttonOpacity:Be,onGetStarted:()=>{console.log("Get Started clicked in SimpleDemoPage"),typeof hn=="function"?hn():(console.warn("handleGetStarted is not a function"),go())}},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:1031,columnNumber:9},globalThis),E&&a.jsxDEV("div",{className:"background-overlay",style:{position:"fixed",top:0,left:0,right:0,bottom:0,zIndex:-1,backgroundColor:fe?"rgba(0, 0, 0, 0.4)":"rgba(255, 255, 255, 0.7)",backdropFilter:"blur(2px)",pointerEvents:"none"}},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:1054,columnNumber:9},globalThis),!E&&a.jsxDEV("div",{className:"hero-section","data-theme":fe?"dark":"light",children:a.jsxDEV("div",{className:"hero-content",children:[a.jsxDEV("div",{className:"main-title-container",style:{overflow:"hidden",maxWidth:"100%",boxSizing:"border-box",display:"flex",flexWrap:"wrap",justifyContent:"center",alignItems:"center",gap:"8px"},children:[a.jsxDEV("div",{className:"title-wrapper",style:{width:"100%",textAlign:"center"},children:a.jsxDEV("span",{className:`main-title-text ${fe?"main-title-white":""}`,style:{fontSize:"clamp(1.2rem, 5vw, 3.5rem)"},children:"Your Law Firm's"},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:1074,columnNumber:17},globalThis)},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:1073,columnNumber:15},globalThis),a.jsxDEV("div",{className:"attribute-flipboard-wrapper subtle-attribute",style:{overflow:"visible",maxWidth:"100%",boxSizing:"border-box"},children:a.jsxDEV("div",{ref:bt,className:`attribute-flipboard ${yo?"flipping":""}`,style:{boxSizing:"border-box",maxWidth:"100%"},children:[a.jsxDEV("div",{className:"attr-front",children:[a.jsxDEV("div",{className:"emoji-circle",children:he[Fe].emoji},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:1079,columnNumber:21},globalThis),a.jsxDEV("span",{className:"attr-text",children:he[Fe].text},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:1082,columnNumber:21},globalThis)]},void 0,!0,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:1078,columnNumber:19},globalThis),a.jsxDEV("div",{className:"attr-back",children:[a.jsxDEV("div",{className:"emoji-circle",children:he[kn(Fe)].emoji},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:1085,columnNumber:21},globalThis),a.jsxDEV("span",{className:"attr-text",children:he[kn(Fe)].text},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:1088,columnNumber:21},globalThis)]},void 0,!0,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:1084,columnNumber:19},globalThis)]},void 0,!0,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:1077,columnNumber:17},globalThis)},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:1076,columnNumber:15},globalThis)]},void 0,!0,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:1072,columnNumber:13},globalThis),a.jsxDEV("div",{className:"flipboard-container role-flipboard-container",children:[a.jsxDEV("div",{className:"icon-flip-container",children:a.jsxDEV("div",{ref:vn,className:`icon-flip ${So?"flipping":""}`,children:[a.jsxDEV("div",{className:"icon-flip-front",children:Ct[yn]},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:1098,columnNumber:19},globalThis),a.jsxDEV("div",{className:"icon-flip-back",children:Ct[No(yn)]},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:1101,columnNumber:19},globalThis)]},void 0,!0,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:1097,columnNumber:17},globalThis)},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:1096,columnNumber:15},globalThis),a.jsxDEV("div",{ref:Te,className:"solari-board",children:Array(20).fill().map((h,y)=>a.jsxDEV("div",{className:"solari-flap",children:[a.jsxDEV("div",{className:"solari-front",children:" "},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:1110,columnNumber:21},globalThis),a.jsxDEV("div",{className:"solari-back",children:" "},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:1111,columnNumber:21},globalThis)]},y,!0,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:1109,columnNumber:19},globalThis))},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:1107,columnNumber:15},globalThis)]},void 0,!0,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:1095,columnNumber:13},globalThis),a.jsxDEV("div",{className:"vertical-spacer"},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:1118,columnNumber:13},globalThis),a.jsxDEV("div",{className:"goal-text-transition",children:a.jsxDEV("h2",{className:"goal-text",children:Nn[Fe]},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:1122,columnNumber:15},globalThis)},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:1121,columnNumber:13},globalThis),a.jsxDEV("div",{className:"logo-agent-container",children:[a.jsxDEV("img",{src:"/organgelight clear.png",alt:"LegalScout Logo",className:"orange-logo"},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:1127,columnNumber:15},globalThis),a.jsxDEV("span",{className:"agent-text",children:"Agent"},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:1128,columnNumber:15},globalThis)]},void 0,!0,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:1126,columnNumber:13},globalThis),a.jsxDEV("div",{className:"vertical-spacer"},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:1132,columnNumber:13},globalThis)]},void 0,!0,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:1070,columnNumber:11},globalThis)},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:1069,columnNumber:9},globalThis),!1,console.log("Button conditions:",{configMode:St,showPreview:E}),a.jsxDEV("div",{className:"preview-container",children:E&&a.jsxDEV(a.Fragment,{children:[a.jsxDEV("div",{className:"preview-controls",children:[a.jsxDEV("button",{className:"icon-button",onClick:ko,title:"Minimize Preview",children:a.jsxDEV("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:a.jsxDEV("path",{d:"M19 13H5v-2h14v2z",fill:"currentColor"},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:1906,columnNumber:19},globalThis)},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:1905,columnNumber:17},globalThis)},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:1904,columnNumber:15},globalThis),a.jsxDEV("button",{className:"icon-button",onClick:()=>{ne.current&&(ne.current.src=ne.current.src)},title:"Refresh Preview",children:a.jsxDEV("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:a.jsxDEV("path",{d:"M17.65 6.35A7.958 7.958 0 0012 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08A5.99 5.99 0 0112 18c-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z",fill:"currentColor"},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:1919,columnNumber:19},globalThis)},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:1918,columnNumber:17},globalThis)},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:1909,columnNumber:15},globalThis)]},void 0,!0,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:1903,columnNumber:13},globalThis),a.jsxDEV("iframe",{ref:ne,className:"preview-iframe",src:`/preview?theme=${fe?"dark":"light"}&production=true&centered=true&fullWidth=true&primaryColor=${encodeURIComponent(r)}&secondaryColor=${encodeURIComponent(i)}&buttonOpacity=${Be}&practiceAreaBackgroundOpacity=${xt}&textBackgroundColor=${encodeURIComponent(yt)}&logoUrl=${encodeURIComponent(t||"")}`,style:{height:`${c}px`},title:"Agent Preview"},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:1923,columnNumber:13},globalThis)]},void 0,!0,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:1902,columnNumber:11},globalThis)},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:1900,columnNumber:7},globalThis)]},void 0,!0,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/SimpleDemoPage.jsx",lineNumber:982,columnNumber:5},globalThis)};export{fs as default};

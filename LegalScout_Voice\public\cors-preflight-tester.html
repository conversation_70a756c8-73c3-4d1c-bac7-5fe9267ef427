<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>CORS Preflight Tester - LegalScout</title>
  
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 20px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: #333;
      min-height: 100vh;
    }

    .container {
      max-width: 1000px;
      margin: 0 auto;
      background: white;
      border-radius: 12px;
      box-shadow: 0 10px 30px rgba(0,0,0,0.2);
      overflow: hidden;
    }

    .header {
      background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
      color: white;
      padding: 30px;
      text-align: center;
    }

    .content {
      padding: 30px;
    }

    .test-section {
      margin-bottom: 30px;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      overflow: hidden;
    }

    .test-header {
      background: #f8f9fa;
      padding: 15px 20px;
      border-bottom: 1px solid #e0e0e0;
      font-weight: 600;
      color: #2c3e50;
    }

    .test-content {
      padding: 20px;
    }

    .test-result {
      margin: 10px 0;
      padding: 15px;
      border-radius: 6px;
      font-family: 'Courier New', monospace;
      font-size: 0.9em;
      line-height: 1.4;
    }

    .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
    .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
    .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
    .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }

    .test-button {
      background: #e74c3c;
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 6px;
      cursor: pointer;
      font-size: 1em;
      margin: 10px 10px 10px 0;
      transition: background 0.3s;
    }

    .test-button:hover { background: #c0392b; }
    .test-button:disabled { background: #bdc3c7; cursor: not-allowed; }

    .code-block {
      background: #2c3e50;
      color: #ecf0f1;
      padding: 15px;
      border-radius: 6px;
      font-family: 'Courier New', monospace;
      font-size: 0.85em;
      overflow-x: auto;
      margin: 10px 0;
    }

    .grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 15px;
      margin: 20px 0;
    }

    .metric-card {
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 8px;
      padding: 15px;
      text-align: center;
    }

    .metric-value {
      font-size: 1.5em;
      font-weight: bold;
      color: #2c3e50;
      margin: 5px 0;
    }

    .metric-label {
      color: #6c757d;
      font-size: 0.8em;
      text-transform: uppercase;
      letter-spacing: 1px;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>🌐 CORS Preflight Tester</h1>
      <p>Detailed analysis of CORS preflight request failures</p>
    </div>

    <div class="content">
      <!-- Metrics -->
      <div class="grid">
        <div class="metric-card">
          <div class="metric-value" id="preflight-success">0</div>
          <div class="metric-label">Successful Preflights</div>
        </div>
        <div class="metric-card">
          <div class="metric-value" id="preflight-failed">0</div>
          <div class="metric-label">Failed Preflights</div>
        </div>
        <div class="metric-card">
          <div class="metric-value" id="cors-errors">0</div>
          <div class="metric-label">CORS Errors</div>
        </div>
        <div class="metric-card">
          <div class="metric-value" id="network-errors">0</div>
          <div class="metric-label">Network Errors</div>
        </div>
      </div>

      <!-- Test Controls -->
      <div class="test-section">
        <div class="test-header">🎮 Test Controls</div>
        <div class="test-content">
          <button class="test-button" onclick="runAllPreflightTests()">🚀 Run All Preflight Tests</button>
          <button class="test-button" onclick="testLocalAPI()">🏠 Test Local API</button>
          <button class="test-button" onclick="testExternalAPIs()">🌍 Test External APIs</button>
          <button class="test-button" onclick="clearResults()">🧹 Clear Results</button>
        </div>
      </div>

      <!-- Preflight Analysis -->
      <div class="test-section">
        <div class="test-header">🔍 Preflight Analysis</div>
        <div class="test-content">
          <div id="preflight-analysis-results"></div>
        </div>
      </div>

      <!-- Local API Tests -->
      <div class="test-section">
        <div class="test-header">🏠 Local API Tests</div>
        <div class="test-content">
          <div id="local-api-results"></div>
        </div>
      </div>

      <!-- External API Tests -->
      <div class="test-section">
        <div class="test-header">🌍 External API Tests</div>
        <div class="test-content">
          <div id="external-api-results"></div>
        </div>
      </div>

      <!-- Network Details -->
      <div class="test-section">
        <div class="test-header">📊 Network Request Details</div>
        <div class="test-content">
          <div id="network-details-results"></div>
        </div>
      </div>
    </div>
  </div>

  <script>
    let testMetrics = {
      preflightSuccess: 0,
      preflightFailed: 0,
      corsErrors: 0,
      networkErrors: 0
    };

    let requestLog = [];

    // Initialize
    document.addEventListener('DOMContentLoaded', function() {
      console.log('🌐 [CORS Tester] Initializing CORS preflight tester...');
      setupNetworkInterception();
      updateMetrics();
    });

    // Network interception
    function setupNetworkInterception() {
      const originalFetch = window.fetch;
      window.fetch = async function(...args) {
        const url = args[0];
        const options = args[1] || {};
        const startTime = performance.now();
        
        const logEntry = {
          timestamp: new Date().toISOString(),
          url: url,
          method: options.method || 'GET',
          headers: options.headers || {},
          startTime: startTime
        };
        
        try {
          const response = await originalFetch.apply(this, args);
          const endTime = performance.now();
          
          logEntry.endTime = endTime;
          logEntry.duration = Math.round(endTime - startTime);
          logEntry.status = response.status;
          logEntry.statusText = response.statusText;
          logEntry.responseHeaders = Object.fromEntries(response.headers.entries());
          logEntry.success = response.ok;
          
          if (!response.ok) {
            if (response.status >= 400 && response.status < 500) {
              testMetrics.corsErrors++;
            } else {
              testMetrics.networkErrors++;
            }
            updateMetrics();
          }
          
          requestLog.push(logEntry);
          updateNetworkDetails();
          
          return response;
        } catch (error) {
          const endTime = performance.now();
          
          logEntry.endTime = endTime;
          logEntry.duration = Math.round(endTime - startTime);
          logEntry.error = error.message;
          logEntry.success = false;
          
          testMetrics.networkErrors++;
          updateMetrics();
          
          requestLog.push(logEntry);
          updateNetworkDetails();
          
          throw error;
        }
      };
    }

    // Update metrics display
    function updateMetrics() {
      document.getElementById('preflight-success').textContent = testMetrics.preflightSuccess;
      document.getElementById('preflight-failed').textContent = testMetrics.preflightFailed;
      document.getElementById('cors-errors').textContent = testMetrics.corsErrors;
      document.getElementById('network-errors').textContent = testMetrics.networkErrors;
    }

    // Update network details
    function updateNetworkDetails() {
      const results = document.getElementById('network-details-results');
      const recentRequests = requestLog.slice(-10);
      
      results.innerHTML = recentRequests.map(req => `
        <div class="test-result ${req.success ? 'success' : 'error'}">
          <strong>${req.method} ${req.url}</strong><br>
          Status: ${req.status || 'N/A'} ${req.statusText || ''}<br>
          Duration: ${req.duration}ms<br>
          ${req.error ? `Error: ${req.error}<br>` : ''}
          Time: ${new Date(req.timestamp).toLocaleTimeString()}
        </div>
      `).join('');
    }

    // Run all preflight tests
    async function runAllPreflightTests() {
      console.log('🚀 [CORS] Running all preflight tests...');
      clearResults();
      
      await testLocalAPI();
      await testExternalAPIs();
      await analyzePreflightBehavior();
      
      console.log('✅ [CORS] All preflight tests completed');
    }

    // Test local API endpoints
    async function testLocalAPI() {
      console.log('🏠 [CORS] Testing local API endpoints...');
      const results = document.getElementById('local-api-results');
      
      const endpoints = [
        '/api/health',
        '/api/env',
        '/api/call-logs',
        '/api/vapi/config',
        '/api/vapi-mcp-server'
      ];
      
      for (const endpoint of endpoints) {
        await testPreflightForEndpoint(endpoint, results);
        await testActualRequest(endpoint, results);
      }
    }

    // Test external APIs
    async function testExternalAPIs() {
      console.log('🌍 [CORS] Testing external APIs...');
      const results = document.getElementById('external-api-results');
      
      const apis = [
        {
          name: 'Supabase',
          url: 'https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/',
          headers: { 'apikey': window.VITE_SUPABASE_KEY || '' }
        },
        {
          name: 'Vapi',
          url: 'https://api.vapi.ai/assistant',
          headers: { 'Authorization': `Bearer ${window.VITE_VAPI_SECRET_KEY || ''}` }
        }
      ];
      
      for (const api of apis) {
        await testExternalAPI(api, results);
      }
    }

    // Test preflight for specific endpoint
    async function testPreflightForEndpoint(endpoint, results) {
      try {
        const response = await fetch(endpoint, {
          method: 'OPTIONS',
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Request-Method': 'POST',
            'Access-Control-Request-Headers': 'Content-Type, Authorization'
          }
        });
        
        if (response.ok) {
          addTestResult(results, 'success', `✅ Preflight ${endpoint}`, 
            `OPTIONS successful: ${response.status}`);
          testMetrics.preflightSuccess++;
        } else {
          addTestResult(results, 'error', `❌ Preflight ${endpoint}`, 
            `OPTIONS failed: ${response.status} ${response.statusText}`);
          testMetrics.preflightFailed++;
        }
      } catch (error) {
        addTestResult(results, 'error', `❌ Preflight ${endpoint}`, 
          `OPTIONS error: ${error.message}`);
        testMetrics.preflightFailed++;
      }
      updateMetrics();
    }

    // Test actual request
    async function testActualRequest(endpoint, results) {
      try {
        const response = await fetch(endpoint, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json'
          }
        });
        
        if (response.ok) {
          addTestResult(results, 'success', `✅ Request ${endpoint}`, 
            `GET successful: ${response.status}`);
        } else {
          addTestResult(results, 'error', `❌ Request ${endpoint}`, 
            `GET failed: ${response.status} ${response.statusText}`);
        }
      } catch (error) {
        addTestResult(results, 'error', `❌ Request ${endpoint}`, 
          `GET error: ${error.message}`);
      }
    }

    // Test external API
    async function testExternalAPI(api, results) {
      try {
        const response = await fetch(api.url, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            ...api.headers
          }
        });
        
        if (response.ok) {
          addTestResult(results, 'success', `✅ ${api.name} API`, 
            `External API accessible: ${response.status}`);
        } else {
          addTestResult(results, 'error', `❌ ${api.name} API`, 
            `External API failed: ${response.status} ${response.statusText}`);
        }
      } catch (error) {
        addTestResult(results, 'error', `❌ ${api.name} API`, 
          `External API error: ${error.message}`);
      }
    }

    // Analyze preflight behavior
    async function analyzePreflightBehavior() {
      const results = document.getElementById('preflight-analysis-results');
      
      const analysis = {
        totalRequests: requestLog.length,
        successfulRequests: requestLog.filter(r => r.success).length,
        failedRequests: requestLog.filter(r => !r.success).length,
        optionsRequests: requestLog.filter(r => r.method === 'OPTIONS').length,
        corsErrors: requestLog.filter(r => r.status >= 400 && r.status < 500).length,
        averageResponseTime: requestLog.length > 0 ? 
          Math.round(requestLog.reduce((sum, r) => sum + (r.duration || 0), 0) / requestLog.length) : 0
      };
      
      results.innerHTML = `
        <div class="test-result info">
          <strong>📊 Preflight Analysis Summary</strong><br>
          Total Requests: ${analysis.totalRequests}<br>
          Successful: ${analysis.successfulRequests}<br>
          Failed: ${analysis.failedRequests}<br>
          OPTIONS Requests: ${analysis.optionsRequests}<br>
          CORS Errors: ${analysis.corsErrors}<br>
          Average Response Time: ${analysis.averageResponseTime}ms
        </div>
        <div class="code-block">
${JSON.stringify(analysis, null, 2)}
        </div>
      `;
    }

    // Helper function to add test results
    function addTestResult(container, type, title, message) {
      const resultDiv = document.createElement('div');
      resultDiv.className = `test-result ${type}`;
      resultDiv.innerHTML = `<strong>${title}</strong><br>${message}`;
      container.appendChild(resultDiv);
    }

    // Clear all results
    function clearResults() {
      testMetrics = { preflightSuccess: 0, preflightFailed: 0, corsErrors: 0, networkErrors: 0 };
      requestLog = [];
      updateMetrics();
      
      document.getElementById('preflight-analysis-results').innerHTML = '';
      document.getElementById('local-api-results').innerHTML = '';
      document.getElementById('external-api-results').innerHTML = '';
      document.getElementById('network-details-results').innerHTML = '';
    }
  </script>
</body>
</html>

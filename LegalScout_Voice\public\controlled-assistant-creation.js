/**
 * Controlled Assistant Creation
 * 
 * This script provides controlled assistant creation functionality
 * while preventing automatic mass creation. It allows manual creation
 * through the UI while maintaining safeguards.
 */

console.log('[ControlledAssistantCreation] Starting controlled assistant creation system...');

// Rate limiting configuration
const RATE_LIMITS = {
  CREATION_COOLDOWN_MS: 30000, // 30 seconds between creations
  MAX_ASSISTANTS_PER_HOUR: 3,
  MAX_ASSISTANTS_PER_DAY: 10
};

// Storage keys for rate limiting
const STORAGE_KEYS = {
  LAST_CREATION: 'last_assistant_creation',
  HOURLY_COUNT: 'hourly_assistant_count',
  DAILY_COUNT: 'daily_assistant_count',
  HOURLY_RESET: 'hourly_reset_time',
  DAILY_RESET: 'daily_reset_time'
};

// Function to check rate limits
function checkRateLimits() {
  const now = Date.now();
  
  // Check cooldown
  const lastCreation = localStorage.getItem(STORAGE_KEYS.LAST_CREATION);
  if (lastCreation && (now - parseInt(lastCreation)) < RATE_LIMITS.CREATION_COOLDOWN_MS) {
    const remainingTime = Math.ceil((RATE_LIMITS.CREATION_COOLDOWN_MS - (now - parseInt(lastCreation))) / 1000);
    throw new Error(`Please wait ${remainingTime} seconds before creating another assistant.`);
  }
  
  // Check hourly limit
  const hourlyReset = localStorage.getItem(STORAGE_KEYS.HOURLY_RESET);
  const hourlyCount = parseInt(localStorage.getItem(STORAGE_KEYS.HOURLY_COUNT) || '0');
  
  if (!hourlyReset || now > parseInt(hourlyReset)) {
    // Reset hourly counter
    localStorage.setItem(STORAGE_KEYS.HOURLY_COUNT, '0');
    localStorage.setItem(STORAGE_KEYS.HOURLY_RESET, (now + 3600000).toString()); // 1 hour from now
  } else if (hourlyCount >= RATE_LIMITS.MAX_ASSISTANTS_PER_HOUR) {
    throw new Error(`Hourly limit reached. You can create ${RATE_LIMITS.MAX_ASSISTANTS_PER_HOUR} assistants per hour.`);
  }
  
  // Check daily limit
  const dailyReset = localStorage.getItem(STORAGE_KEYS.DAILY_RESET);
  const dailyCount = parseInt(localStorage.getItem(STORAGE_KEYS.DAILY_COUNT) || '0');
  
  if (!dailyReset || now > parseInt(dailyReset)) {
    // Reset daily counter
    localStorage.setItem(STORAGE_KEYS.DAILY_COUNT, '0');
    localStorage.setItem(STORAGE_KEYS.DAILY_RESET, (now + 86400000).toString()); // 24 hours from now
  } else if (dailyCount >= RATE_LIMITS.MAX_ASSISTANTS_PER_DAY) {
    throw new Error(`Daily limit reached. You can create ${RATE_LIMITS.MAX_ASSISTANTS_PER_DAY} assistants per day.`);
  }
  
  return true;
}

// Function to update rate limit counters
function updateRateLimitCounters() {
  const now = Date.now();
  
  localStorage.setItem(STORAGE_KEYS.LAST_CREATION, now.toString());
  
  const hourlyCount = parseInt(localStorage.getItem(STORAGE_KEYS.HOURLY_COUNT) || '0');
  localStorage.setItem(STORAGE_KEYS.HOURLY_COUNT, (hourlyCount + 1).toString());
  
  const dailyCount = parseInt(localStorage.getItem(STORAGE_KEYS.DAILY_COUNT) || '0');
  localStorage.setItem(STORAGE_KEYS.DAILY_COUNT, (dailyCount + 1).toString());
}

// Function to create a new assistant with proper validation
async function createNewAssistant(assistantConfig) {
  try {
    // Check rate limits first
    checkRateLimits();
    
    // Validate required fields
    if (!assistantConfig.name || assistantConfig.name.trim().length === 0) {
      throw new Error('Assistant name is required');
    }
    
    if (!assistantConfig.instructions || assistantConfig.instructions.trim().length === 0) {
      throw new Error('Assistant instructions are required');
    }
    
    // Ensure we have a proper Vapi service
    if (!window.vapiMcpService && !window.enhancedVapiMcpService) {
      throw new Error('Vapi service not available. Please refresh the page and try again.');
    }
    
    const vapiService = window.enhancedVapiMcpService || window.vapiMcpService;
    
    // Create the assistant configuration
    const config = {
      name: assistantConfig.name.trim(),
      firstMessage: assistantConfig.firstMessage || `Hello, I'm ${assistantConfig.name}. How can I help you today?`,
      firstMessageMode: "assistant-speaks-first",
      model: {
        provider: assistantConfig.aiProvider || "openai",
        model: assistantConfig.aiModel || "gpt-4o",
        messages: [
          {
            role: "system",
            content: assistantConfig.instructions.trim()
          }
        ]
      },
      voice: {
        provider: assistantConfig.voiceProvider || "11labs",
        voiceId: assistantConfig.voiceId || "sarah"
      },
      transcriber: {
        provider: "deepgram",
        model: "nova-3"
      }
    };
    
    console.log('[ControlledAssistantCreation] Creating assistant with config:', config);
    
    // Create the assistant
    const assistant = await vapiService.createAssistant(config);
    
    if (!assistant || !assistant.id) {
      throw new Error('Failed to create assistant - no ID returned');
    }
    
    // Update rate limit counters
    updateRateLimitCounters();
    
    console.log('[ControlledAssistantCreation] Successfully created assistant:', assistant.id);
    
    return {
      success: true,
      assistant: assistant,
      message: `Successfully created assistant: ${assistant.name || assistant.id}`
    };
    
  } catch (error) {
    console.error('[ControlledAssistantCreation] Error creating assistant:', error);
    throw error;
  }
}

// Function to get rate limit status
function getRateLimitStatus() {
  const now = Date.now();
  
  const lastCreation = localStorage.getItem(STORAGE_KEYS.LAST_CREATION);
  const cooldownRemaining = lastCreation ? Math.max(0, RATE_LIMITS.CREATION_COOLDOWN_MS - (now - parseInt(lastCreation))) : 0;
  
  const hourlyCount = parseInt(localStorage.getItem(STORAGE_KEYS.HOURLY_COUNT) || '0');
  const dailyCount = parseInt(localStorage.getItem(STORAGE_KEYS.DAILY_COUNT) || '0');
  
  return {
    canCreate: cooldownRemaining === 0 && hourlyCount < RATE_LIMITS.MAX_ASSISTANTS_PER_HOUR && dailyCount < RATE_LIMITS.MAX_ASSISTANTS_PER_DAY,
    cooldownRemaining: Math.ceil(cooldownRemaining / 1000),
    hourlyCount: hourlyCount,
    hourlyLimit: RATE_LIMITS.MAX_ASSISTANTS_PER_HOUR,
    dailyCount: dailyCount,
    dailyLimit: RATE_LIMITS.MAX_ASSISTANTS_PER_DAY
  };
}

// Expose functions globally for UI components to use
window.controlledAssistantCreation = {
  createNewAssistant,
  getRateLimitStatus,
  checkRateLimits
};

console.log('[ControlledAssistantCreation] Controlled assistant creation system ready');

/**
 * Live Integration Test for Assistant Variable Propagation
 * Run this script to test the actual flow in your environment
 */

import { vapiAssistantService } from '../src/services/vapiAssistantService.js';
import { vapiMcpService } from '../src/services/vapiMcpService.js';

class AssistantPropagationTester {
  constructor() {
    this.results = [];
    this.errors = [];
  }

  log(message, type = 'info') {
    const timestamp = new Date().toISOString();
    const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
    console.log(logEntry);
    
    if (type === 'error') {
      this.errors.push(logEntry);
    } else {
      this.results.push(logEntry);
    }
  }

  async test1_LoadAssistants() {
    this.log('🔍 TEST 1: Loading all assistants from Vapi');
    
    try {
      const assistants = await vapiAssistantService.getAllAssistants();
      this.log(`✅ Loaded ${assistants.length} assistants`);
      
      assistants.forEach((assistant, index) => {
        this.log(`   ${index + 1}. ${assistant.name} (ID: ${assistant.id})`);
      });
      
      return assistants;
    } catch (error) {
      this.log(`❌ Failed to load assistants: ${error.message}`, 'error');
      return [];
    }
  }

  async test2_GetSpecificAssistant(assistantId) {
    this.log(`🔍 TEST 2: Getting specific assistant details for ${assistantId}`);
    
    try {
      const assistant = await vapiMcpService.getAssistant(assistantId);
      this.log(`✅ Retrieved assistant: ${assistant.name}`);
      this.log(`   Model: ${assistant.model?.provider} ${assistant.model?.model}`);
      this.log(`   Voice: ${assistant.voice?.provider} ${assistant.voice?.voiceId}`);
      
      return assistant;
    } catch (error) {
      this.log(`❌ Failed to get assistant: ${error.message}`, 'error');
      return null;
    }
  }

  async test3_SwitchAssistant(fromId, toId) {
    this.log(`🔍 TEST 3: Switching assistant from ${fromId} to ${toId}`);
    
    try {
      const result = await vapiAssistantService.switchAssistant(toId);
      
      if (result.success) {
        this.log(`✅ Successfully switched to assistant: ${result.assistant.name}`);
        return result.assistant;
      } else {
        this.log(`❌ Failed to switch assistant: ${result.error}`, 'error');
        return null;
      }
    } catch (error) {
      this.log(`❌ Error switching assistant: ${error.message}`, 'error');
      return null;
    }
  }

  async test4_CreateTestCall(assistantId) {
    this.log(`🔍 TEST 4: Creating test call with assistant ${assistantId}`);
    
    try {
      // Note: This creates a real call - use with caution
      const callData = {
        assistantId: assistantId,
        customer: {
          phoneNumber: '+1234567890' // Test number
        }
      };
      
      this.log('⚠️  WARNING: This would create a real call. Skipping actual call creation.');
      this.log(`   Would call with data: ${JSON.stringify(callData, null, 2)}`);
      
      // Uncomment below to actually test call creation
      // const call = await vapiMcpService.createCall(callData);
      // this.log(`✅ Created call: ${call.id}`);
      // return call;
      
      return { id: 'test-call-id', assistantId };
    } catch (error) {
      this.log(`❌ Failed to create call: ${error.message}`, 'error');
      return null;
    }
  }

  async test5_VerifyUIConfigPersistence(assistantId) {
    this.log(`🔍 TEST 5: Verifying UI config persistence for ${assistantId}`);
    
    try {
      // Test saving a config
      const testConfig = {
        theme: 'light',
        lastTab: 'agent',
        customSettings: { testValue: 'propagation-test' }
      };
      
      this.log(`   Saving test config: ${JSON.stringify(testConfig)}`);
      
      // Note: This would require importing supabaseService
      this.log('⚠️  UI Config persistence test requires Supabase service integration');
      this.log('   Manual verification needed in dashboard');
      
      return true;
    } catch (error) {
      this.log(`❌ Failed to test UI config: ${error.message}`, 'error');
      return false;
    }
  }

  async runAllTests() {
    this.log('🚀 Starting Assistant Propagation Tests');
    this.log('=====================================');
    
    // Test 1: Load all assistants
    const assistants = await this.test1_LoadAssistants();
    if (assistants.length === 0) {
      this.log('❌ Cannot continue tests - no assistants loaded', 'error');
      return this.generateReport();
    }
    
    // Use first two assistants for testing
    const assistant1 = assistants[0];
    const assistant2 = assistants[1] || assistant1;
    
    // Test 2: Get specific assistant details
    await this.test2_GetSpecificAssistant(assistant1.id);
    
    // Test 3: Switch between assistants
    if (assistant2.id !== assistant1.id) {
      await this.test3_SwitchAssistant(assistant1.id, assistant2.id);
      await this.test3_SwitchAssistant(assistant2.id, assistant1.id);
    }
    
    // Test 4: Create test call
    await this.test4_CreateTestCall(assistant1.id);
    
    // Test 5: UI config persistence
    await this.test5_VerifyUIConfigPersistence(assistant1.id);
    
    return this.generateReport();
  }

  generateReport() {
    this.log('=====================================');
    this.log('📊 TEST RESULTS SUMMARY');
    this.log('=====================================');
    
    this.log(`✅ Successful operations: ${this.results.length}`);
    this.log(`❌ Errors encountered: ${this.errors.length}`);
    
    if (this.errors.length > 0) {
      this.log('\n🔥 ERRORS:');
      this.errors.forEach(error => this.log(error));
    }
    
    this.log('\n📋 MANUAL VERIFICATION CHECKLIST:');
    this.log('1. Open dashboard and check assistant dropdown is populated');
    this.log('2. Select different assistants and verify UI updates');
    this.log('3. Check that assistant details appear in Agent tab');
    this.log('4. Verify voice/model settings reflect selected assistant');
    this.log('5. Test creating a call and confirm correct assistant is used');
    
    return {
      success: this.errors.length === 0,
      results: this.results,
      errors: this.errors
    };
  }
}

// Export for use in other scripts
export { AssistantPropagationTester };

// Run tests if script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const tester = new AssistantPropagationTester();
  tester.runAllTests().then(report => {
    process.exit(report.success ? 0 : 1);
  });
}

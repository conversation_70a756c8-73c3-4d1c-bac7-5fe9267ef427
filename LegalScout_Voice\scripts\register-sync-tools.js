#!/usr/bin/env node
/**
 * Register Synchronization Tools
 *
 * This script registers the synchronization tools with the AI Meta MCP Server.
 *
 * Usage:
 *   node scripts/register-sync-tools.js
 */

import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';
import fs from 'fs';

// Get the directory name of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Path to the AI Meta MCP Server
const serverPath = path.resolve(__dirname, '..', 'ai-meta-mcp-server', 'build', 'index.js');

// Path to the syncTools.js file
const syncToolsPath = path.resolve(__dirname, '..', 'src', 'services', 'syncTools.js');

// Main function to register the synchronization tools
const registerSyncTools = async () => {
  try {
    console.log('Starting AI Meta MCP Server...');

    // Start the AI Meta MCP Server
    const serverProcess = spawn('node', [serverPath]);

    // Log server output
    serverProcess.stdout.on('data', (data) => {
      console.log(`[Server] ${data.toString().trim()}`);
    });

    serverProcess.stderr.on('data', (data) => {
      console.error(`[Server] ${data.toString().trim()}`);
    });

    // Wait for server to start
    console.log('Waiting for server to start...');
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Read the syncTools.js file
    console.log(`Reading synchronization tools from ${syncToolsPath}...`);
    const syncToolsContent = fs.readFileSync(syncToolsPath, 'utf-8');

    // Extract the function implementations
    const syncAttorneyProfileImpl = extractFunctionImplementation(syncToolsContent, 'syncAttorneyProfile');
    const manageAuthStateImpl = extractFunctionImplementation(syncToolsContent, 'manageAuthState');
    const validateConfigurationImpl = extractFunctionImplementation(syncToolsContent, 'validateConfiguration');
    const checkPreviewConsistencyImpl = extractFunctionImplementation(syncToolsContent, 'checkPreviewConsistency');

    // Register the functions with the AI Meta MCP Server
    console.log('Registering synchronization tools...');

    // Create a temporary script to register the functions
    const registerScript = `
      import { spawn } from 'child_process';
      const serverProcess = spawn('node', ['${serverPath.replace(/\\/g, '\\\\')}']);

      // Function to send a command to the server
      const sendCommand = (command) => {
        return new Promise((resolve, reject) => {
          let response = '';

          serverProcess.stdout.on('data', (data) => {
            response += data.toString();

            // Check if we have a complete response
            if (response.includes('\\n')) {
              const lines = response.split('\\n');
              for (const line of lines) {
                if (line.trim()) {
                  try {
                    const parsed = JSON.parse(line);
                    resolve(parsed);
                  } catch (error) {
                    // Ignore parsing errors
                  }
                }
              }
            }
          });

          serverProcess.stderr.on('data', (data) => {
            console.error(data.toString());
          });

          serverProcess.on('error', (error) => {
            reject(error);
          });

          // Send the command
          serverProcess.stdin.write(JSON.stringify(command) + '\\n');
        });
      };

      // Register the syncAttorneyProfile function
      const registerSyncAttorneyProfile = async () => {
        const command = {
          type: 'call_tool',
          id: '1',
          name: 'define_function',
          arguments: {
            name: 'sync_attorney_profile',
            description: 'Synchronize attorney profile data between Supabase and Vapi',
            parameters_schema: {
              attorneyId: {
                type: 'string',
                description: 'ID of the attorney to synchronize'
              },
              forceUpdate: {
                type: 'boolean',
                description: 'Force update even if no discrepancies are found',
                default: false
              }
            },
            implementation_code: \`${syncAttorneyProfileImpl.replace(/`/g, '\\`')}\`,
            execution_environment: 'javascript'
          }
        };

        const result = await sendCommand(command);
        console.log('Registered sync_attorney_profile function');
        return result;
      };

      // Register the manageAuthState function
      const registerManageAuthState = async () => {
        const command = {
          type: 'call_tool',
          id: '2',
          name: 'define_function',
          arguments: {
            name: 'manage_auth_state',
            description: 'Manage authentication state across systems',
            parameters_schema: {
              authData: {
                type: 'object',
                description: 'Authentication data'
              },
              action: {
                type: 'string',
                description: 'Authentication action (login, logout, refresh)',
                enum: ['login', 'logout', 'refresh']
              }
            },
            implementation_code: \`${manageAuthStateImpl.replace(/`/g, '\\`')}\`,
            execution_environment: 'javascript'
          }
        };

        const result = await sendCommand(command);
        console.log('Registered manage_auth_state function');
        return result;
      };

      // Register the validateConfiguration function
      const registerValidateConfiguration = async () => {
        const command = {
          type: 'call_tool',
          id: '3',
          name: 'define_function',
          arguments: {
            name: 'validate_configuration',
            description: 'Validate configuration before updates',
            parameters_schema: {
              attorneyId: {
                type: 'string',
                description: 'ID of the attorney'
              },
              configData: {
                type: 'object',
                description: 'Configuration data to validate'
              }
            },
            implementation_code: \`${validateConfigurationImpl.replace(/`/g, '\\`')}\`,
            execution_environment: 'javascript'
          }
        };

        const result = await sendCommand(command);
        console.log('Registered validate_configuration function');
        return result;
      };

      // Register the checkPreviewConsistency function
      const registerCheckPreviewConsistency = async () => {
        const command = {
          type: 'call_tool',
          id: '4',
          name: 'define_function',
          arguments: {
            name: 'check_preview_consistency',
            description: 'Ensure preview matches deployment',
            parameters_schema: {
              attorneyId: {
                type: 'string',
                description: 'ID of the attorney'
              }
            },
            implementation_code: \`${checkPreviewConsistencyImpl.replace(/`/g, '\\`')}\`,
            execution_environment: 'javascript'
          }
        };

        const result = await sendCommand(command);
        console.log('Registered check_preview_consistency function');
        return result;
      };

      // List all registered functions
      const listFunctions = async () => {
        const command = {
          type: 'call_tool',
          id: '5',
          name: 'list_functions',
          arguments: {}
        };

        const result = await sendCommand(command);
        console.log('Registered functions:');
        console.log(JSON.stringify(result, null, 2));
        return result;
      };

      // Register all functions
      const registerAll = async () => {
        try {
          await registerSyncAttorneyProfile();
          await registerManageAuthState();
          await registerValidateConfiguration();
          await registerCheckPreviewConsistency();
          await listFunctions();

          // Kill the server process
          serverProcess.kill();
          console.log('All functions registered successfully');
        } catch (error) {
          console.error('Error registering functions:', error);
          serverProcess.kill();
          process.exit(1);
        }
      };

      // Run the registration
      registerAll();
    `;

    // Write the register script to a temporary file
    const registerScriptPath = path.resolve(__dirname, 'temp-register.js');
    fs.writeFileSync(registerScriptPath, registerScript);

    // Run the register script
    console.log('Running register script...');
    const registerProcess = spawn('node', [registerScriptPath]);

    // Log register script output
    registerProcess.stdout.on('data', (data) => {
      console.log(`[Register] ${data.toString().trim()}`);
    });

    registerProcess.stderr.on('data', (data) => {
      console.error(`[Register] ${data.toString().trim()}`);
    });

    // Wait for register script to complete
    await new Promise((resolve) => {
      registerProcess.on('close', (code) => {
        console.log(`Register script exited with code ${code}`);
        resolve();
      });
    });

    // Clean up
    fs.unlinkSync(registerScriptPath);

    // Kill the server process
    serverProcess.kill();
    console.log('AI Meta MCP Server process terminated');

    console.log('Synchronization tools registered successfully');
  } catch (error) {
    console.error('Error registering synchronization tools:', error);
    process.exit(1);
  }
};

// Function to extract a function implementation from the syncTools.js file
const extractFunctionImplementation = (content, functionName) => {
  const regex = new RegExp(`export const ${functionName} = async \\(params\\) => {[\\s\\S]*?};`, 'g');
  const match = regex.exec(content);

  if (!match) {
    throw new Error(`Function ${functionName} not found in syncTools.js`);
  }

  // Extract the function body
  const functionBody = match[0].replace(`export const ${functionName} = `, '');

  return functionBody;
};

// Run the registration
registerSyncTools();

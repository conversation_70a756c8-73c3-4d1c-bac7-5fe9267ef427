/**
 * Test Sync Checker
 * 
 * This script tests the new automatic sync checking functionality
 */

// Mock attorney data (same as what's in your Supabase)
const ATTORNEY_DATA = {
  id: "571390ac-5a83-46b2-ad3a-18b9cf39d701",
  subdomain: "damon",
  firm_name: "LegalScout",
  email: "<EMAIL>",
  vapi_assistant_id: "eb8533fa-902e-46be-8ce9-df20f5c550d7",
  vapi_instructions: "You will guide the user through jurrasic park",
  welcome_message: "Mr. <PERSON>, The phones are working."
};

async function testSyncChecker() {
  console.log('🧪 Testing Vapi Sync Checker\n');
  
  try {
    // Import the sync checker (this would normally be done in your app)
    const { checkAttorneySyncStatus, autoSyncAttorney } = await import('../src/utils/vapiSyncChecker.js');
    
    console.log('📋 Testing with attorney data:');
    console.log(`   ID: ${ATTORNEY_DATA.id}`);
    console.log(`   Firm: ${ATTORNEY_DATA.firm_name}`);
    console.log(`   Assistant ID: ${ATTORNEY_DATA.vapi_assistant_id}`);
    console.log(`   Instructions: "${ATTORNEY_DATA.vapi_instructions}"`);
    console.log(`   Welcome: "${ATTORNEY_DATA.welcome_message}"`);
    
    console.log('\n🔍 Step 1: Checking sync status...');
    const syncStatus = await checkAttorneySyncStatus(ATTORNEY_DATA);
    
    console.log('\n📊 Sync Status Result:');
    console.log(`   In Sync: ${syncStatus.inSync}`);
    console.log(`   Reason: ${syncStatus.reason}`);
    console.log(`   Action: ${syncStatus.action}`);
    
    if (syncStatus.differences && syncStatus.differences.length > 0) {
      console.log('\n🔍 Differences found:');
      syncStatus.differences.forEach((diff, index) => {
        console.log(`   ${index + 1}. ${diff.field}:`);
        console.log(`      Supabase: "${diff.supabase}"`);
        console.log(`      Vapi: "${diff.vapi}"`);
      });
    }
    
    if (!syncStatus.inSync && syncStatus.needsSync) {
      console.log('\n🔄 Step 2: Testing auto-sync...');
      const autoSyncResult = await autoSyncAttorney(ATTORNEY_DATA);
      
      console.log('\n📊 Auto-Sync Result:');
      console.log(`   Success: ${autoSyncResult.success}`);
      console.log(`   Action: ${autoSyncResult.action}`);
      console.log(`   Message: ${autoSyncResult.message}`);
      
      if (autoSyncResult.success) {
        console.log('\n✅ Auto-sync completed successfully!');
        
        // Verify the sync worked
        console.log('\n🔍 Step 3: Verifying sync...');
        const verifyStatus = await checkAttorneySyncStatus(ATTORNEY_DATA);
        console.log(`   Now in sync: ${verifyStatus.inSync}`);
        
        if (verifyStatus.inSync) {
          console.log('🎉 SUCCESS: Attorney data is now synchronized!');
        } else {
          console.log('⚠️ WARNING: Sync may not have completed properly');
        }
      }
    } else if (syncStatus.inSync) {
      console.log('\n✅ Attorney data is already in sync - no action needed!');
    }
    
  } catch (error) {
    console.error('\n❌ Test failed:', error);
    console.error('Stack trace:', error.stack);
  }
}

async function main() {
  console.log('🚀 Vapi Sync Checker Test\n');
  console.log('This test verifies that the automatic sync checking works correctly.');
  console.log('═'.repeat(70));
  
  await testSyncChecker();
  
  console.log('\n' + '═'.repeat(70));
  console.log('🏁 Test completed!');
  console.log('\nWhat this test does:');
  console.log('1. ✅ Checks if your attorney data matches your Vapi assistant');
  console.log('2. ✅ Identifies any differences between Supabase and Vapi');
  console.log('3. ✅ Automatically syncs if differences are found');
  console.log('4. ✅ Verifies that the sync worked correctly');
  console.log('\nThis same logic now runs automatically when you load your dashboard!');
}

// Handle fetch for Node.js
if (typeof fetch === 'undefined') {
  global.fetch = (await import('node-fetch')).default;
}

main().catch(error => {
  console.error('Script failed:', error);
  process.exit(1);
});

/**
 * Elegant Assistant Creation Handler
 * 
 * Implements the elegant flow:
 * 1. UI shows default values from configuration
 * 2. "No Assistant" dropdown with "Create New Assistant" option
 * 3. User clicks create → Uses populated default values
 * 4. Assistant created → Syncs to <PERSON>api with those values
 * 5. Assistant ID saved to Supabase (with fixed headers)
 */

(function elegantAssistantCreation() {
  console.log('🎯 [ElegantAssistantCreation] Initializing elegant assistant creation flow...');
  
  // Wait for DOM and services to be ready
  let checkInterval = setInterval(() => {
    if (document.readyState === 'complete' && window.supabase) {
      clearInterval(checkInterval);
      initializeElegantFlow();
    }
  }, 100);
  
  // Clear interval after 10 seconds if not ready
  setTimeout(() => {
    clearInterval(checkInterval);
    console.warn('[ElegantAssistantCreation] Timed out waiting for dependencies');
  }, 10000);
  
  function initializeElegantFlow() {
    console.log('🚀 [ElegantAssistantCreation] Starting elegant flow initialization...');
    
    // Enhanced assistant creation function
    window.createElegantAssistant = async function(attorneyData) {
      console.log('🎯 [ElegantAssistantCreation] Creating assistant with attorney data:', {
        firmName: attorneyData.firm_name,
        email: attorneyData.email,
        hasVoiceId: !!attorneyData.voice_id
      });
      
      try {
        // Step 1: Create assistant configuration using default values
        const assistantConfig = createAssistantConfiguration(attorneyData);
        
        // Step 2: Create assistant via Vapi (using MCP or direct API)
        const assistant = await createVapiAssistant(assistantConfig);
        
        // Step 3: Save assistant ID to Supabase (with fixed headers)
        await saveAssistantIdToSupabase(attorneyData.id, assistant.id);
        
        console.log('✅ [ElegantAssistantCreation] Assistant created successfully:', assistant.id);
        
        return {
          success: true,
          assistant,
          message: 'Assistant created successfully'
        };
        
      } catch (error) {
        console.error('❌ [ElegantAssistantCreation] Error creating assistant:', error);
        throw error;
      }
    };
    
    // Create assistant configuration with all default values
    function createAssistantConfiguration(attorneyData) {
      return {
        name: `${attorneyData.firm_name || 'LegalScout'} Assistant`,
        firstMessage: attorneyData.welcome_message || `Hello! I'm Scout from ${attorneyData.firm_name || 'LegalScout'}. How can I help you today?`,
        firstMessageMode: "assistant-speaks-first",
        
        model: {
          provider: "openai",
          model: attorneyData.ai_model || "gpt-4o",
          temperature: 0.7,
          messages: [
            {
              role: "system",
              content: attorneyData.vapi_instructions || `You are Scout, the AI legal assistant for ${attorneyData.firm_name || 'LegalScout'}. You help potential clients understand their legal needs and connect them with qualified attorneys. You guide users through initial legal consultations and collect relevant information.

[Identity]
You are Scout, an AI legal assistant representing ${attorneyData.firm_name || 'LegalScout'}. You are akin to a legal K9, navigating the complex legal terrain in the USA, and occasionally making light-hearted dog puns when appropriate.

[Primary Role]
Guide users through understanding their legal matters and gathering necessary details for creating a precise legal brief.

[Style Guidelines]
- Maintain a professional yet friendly tone
- Inject dog-themed humor when suitable without detracting from the seriousness
- Spell slowly when pronouncing letters for clarity

[Data Collection Process]
1. Begin by requesting the user's email address and confirm its accuracy by spelling it back letter by letter
2. Introduce yourself and your role, incorporating the user's name into a dog pun if inferred from their email
3. Use iterative questioning to understand the user's legal matter
4. Collect: client background, jurisdiction, statement of facts, legal issues, client objectives, practice area, name, phone number, street address, city, state, and zip code
5. Verify all information, particularly the user's state and email address

[Tool Usage]
You must initiate the tool "Live_Dossier" once the user provides their email and with every reply following that.

[Completion]
Conclude with: "Thank you! I have all the details I need to create your case file. ${attorneyData.firm_name || 'LegalScout'} is now working to find an attorney interested in your case. You'll receive an update soon."

Note: Always ensure to gather all necessary contact information before proceeding to inform the user about finding an attorney.`
            }
          ],
          toolIds: ["4a0d63cf-0b84-4eec-bddf-9c5869439d7e"] // Live Dossier tool
        },
        
        voice: {
          provider: attorneyData.voice_provider || "openai",
          voiceId: attorneyData.voice_id || "echo",
          speed: 1.0
        },
        
        transcriber: {
          provider: "deepgram",
          model: "nova-3",
          language: "en"
        },
        
        // Analysis configuration for data collection
        analysisPlan: {
          summaryPlan: {
            enabled: true,
            messages: [
              {
                role: "system",
                content: `Summarize the key points of this legal consultation for ${attorneyData.firm_name || 'LegalScout'}, including the client's legal issue, urgency level, jurisdiction, and next steps for attorney assignment.`
              }
            ]
          },
          
          structuredDataPlan: {
            enabled: true,
            messages: [
              {
                role: "system", 
                content: "Extract structured data from this legal consultation for CRM and case management."
              }
            ],
            schema: {
              type: "object",
              properties: {
                client: {
                  type: "object",
                  properties: {
                    full_name: { type: "string" },
                    email: { type: "string" },
                    phone: { type: "string" },
                    address: {
                      type: "object",
                      properties: {
                        street: { type: "string" },
                        city: { type: "string" },
                        state: { type: "string" },
                        zip: { type: "string" }
                      }
                    }
                  }
                },
                case: {
                  type: "object",
                  properties: {
                    legal_issue: { type: "string" },
                    practice_area: { 
                      type: "string", 
                      enum: ["Personal Injury", "Family Law", "Criminal Defense", "Business Law", "Real Estate", "Estate Planning", "Immigration", "Employment Law", "Other"]
                    },
                    urgency: { 
                      type: "string", 
                      enum: ["Low", "Medium", "High", "Critical"]
                    },
                    jurisdiction: { type: "string" },
                    case_summary: { type: "string" }
                  }
                }
              },
              required: ["client", "case"]
            }
          },
          
          successEvaluationPlan: {
            enabled: true,
            rubric: "NumericScale",
            messages: [
              {
                role: "system",
                content: "Evaluate this consultation's success (1-10) based on information gathering completeness, client engagement, lead quality, and process efficiency."
              }
            ]
          }
        },
        
        silenceTimeoutSeconds: 30,
        maxDurationSeconds: 900,
        backgroundSound: "office",
        endCallMessage: `Thank you for consulting with ${attorneyData.firm_name || 'LegalScout'}. You'll receive an update about your case soon. Have a great day!`,
        
        artifactPlan: {
          recordingEnabled: true,
          transcriptPlan: {
            enabled: true,
            assistantName: "Scout",
            userName: "Client"
          }
        }
      };
    }
    
    // Create assistant via Vapi API
    async function createVapiAssistant(config) {
      console.log('📡 [ElegantAssistantCreation] Creating Vapi assistant...');
      
      // Try MCP service first
      if (window.vapiMcpService) {
        try {
          await window.vapiMcpService.ensureConnection();
          const assistant = await window.vapiMcpService.createAssistant(config);
          console.log('✅ [ElegantAssistantCreation] Assistant created via MCP:', assistant.id);
          return assistant;
        } catch (mcpError) {
          console.warn('[ElegantAssistantCreation] MCP failed, trying direct API:', mcpError.message);
        }
      }
      
      // Fallback to direct API
      const apiKey = '6734febc-fc65-4669-93b0-929b31ff6564';
      
      const response = await fetch('https://api.vapi.ai/assistant', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(config)
      });
      
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Vapi API error: ${response.status} ${response.statusText} - ${errorText}`);
      }
      
      const assistant = await response.json();
      console.log('✅ [ElegantAssistantCreation] Assistant created via API:', assistant.id);
      return assistant;
    }
    
    // Save assistant ID to Supabase with fixed headers
    async function saveAssistantIdToSupabase(attorneyId, assistantId) {
      console.log('💾 [ElegantAssistantCreation] Saving assistant ID to Supabase...');
      
      // Validate inputs
      if (!attorneyId || !assistantId) {
        throw new Error('Attorney ID and Assistant ID are required');
      }
      
      // Validate assistant ID format (UUID)
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
      if (!uuidRegex.test(assistantId)) {
        throw new Error('Invalid assistant ID format');
      }
      
      try {
        const { data, error } = await window.supabase
          .from('attorneys')
          .update({
            vapi_assistant_id: assistantId,
            updated_at: new Date().toISOString()
          })
          .eq('id', attorneyId)
          .select()
          .single();
        
        if (error) {
          console.error('[ElegantAssistantCreation] Supabase error:', error);
          throw error;
        }
        
        console.log('✅ [ElegantAssistantCreation] Assistant ID saved to Supabase');
        return data;
        
      } catch (error) {
        console.error('❌ [ElegantAssistantCreation] Error saving to Supabase:', error);
        throw error;
      }
    }
    
    console.log('✅ [ElegantAssistantCreation] Elegant flow initialized successfully');
  }
})();

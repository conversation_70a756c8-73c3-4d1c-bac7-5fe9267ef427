# 🧪 Assistant Validation Testing System

## Quick Start

### 1. Terminal Tests (Recommended First)
```bash
# Run validation logic tests
npm run test:validation

# Run comprehensive test suite
npm run test:validation-all
```

### 2. Browser Console Tests
1. Start your app: `npm run dev`
2. Open browser Developer Tools (F12) → Console
3. Copy/paste: `scripts/browser-console-test-suite.js`
4. Run: `runAssistantValidationTests()`

### 3. Database Integrity Check
1. Open Supabase Dashboard → SQL Editor
2. Copy/paste: `scripts/database-integrity-check.sql`
3. Run each section to check for corruption

### 4. Live Monitoring (Optional)
1. In browser console, copy/paste: `scripts/live-monitoring-script.js`
2. Run: `quickMonitor(60)` for 60-second monitoring
3. Use app normally and watch for corruption alerts

## What These Tests Do

### ✅ Validation Logic Tests
- Verify attorney IDs are rejected as assistant IDs
- Test edge cases (null, undefined, mock IDs)
- Validate context resolution logic
- Check file system integrity

### 🌐 Browser Integration Tests
- Test real app state and localStorage
- Monitor console logs for corruption
- Check React component state
- Verify URL integrity

### 🗄️ Database Integrity Tests
- Find attorney IDs used as assistant IDs
- Check for missing columns
- Validate data consistency
- Verify schema migrations

### 🔍 Live Monitoring
- Real-time corruption detection
- Network request monitoring
- DOM change tracking
- Automatic reporting

## Expected Results

### ✅ All Tests Pass
- No attorney ID `87756a2c-a398-43f2-889a-b8815684df71` appears as assistant ID
- Validation system correctly rejects invalid IDs
- Database has proper schema and clean data
- App functions without corruption

### ⚠️ Tests Fail
**Common Issues & Fixes:**

1. **"Validation system not found"**
   ```bash
   # Check if files exist
   ls src/utils/assistantContextValidator.js
   ls src/components/AssistantSelectionPrompt.jsx
   ```

2. **"Database column does not exist"**
   ```sql
   -- Run in Supabase SQL Editor
   ALTER TABLE consultations ADD COLUMN assistant_id UUID;
   ```

3. **"Attorney ID detected as assistant ID"**
   ```bash
   # Run the corruption fix
   npm run fix:assistant-corruption
   ```

4. **"Corruption still detected"**
   ```javascript
   // Clear browser cache
   localStorage.clear();
   // Refresh page and re-test
   ```

## Files Overview

### Core Validation System
- `src/utils/assistantContextValidator.js` - Main validation logic
- `src/components/AssistantSelectionPrompt.jsx` - Fallback UI
- `src/contexts/AssistantAwareContext.jsx` - Updated context (modified)

### Test Scripts
- `scripts/terminal-test-runner.js` - Terminal validation tests
- `scripts/browser-console-test-suite.js` - Browser integration tests
- `scripts/live-monitoring-script.js` - Real-time monitoring
- `scripts/run-all-tests.js` - Master test coordinator

### Database Scripts
- `scripts/database-integrity-check.sql` - Integrity verification
- `scripts/add-assistant-id-to-consultations.sql` - Schema migration
- `scripts/fix-assistant-id-corruption.js` - Data cleanup

## Troubleshooting

### Schema Error: "Could not find 'mascot' column"
This suggests a schema cache issue. Try:
1. Refresh Supabase dashboard
2. Clear browser cache
3. Restart development server
4. Check if you're on the correct database/project

### Import Errors in Terminal Tests
```bash
# Ensure you're using Node.js 18+
node --version

# Check if files have correct extensions
ls scripts/*.js

# Try running with explicit module flag
node --experimental-modules scripts/terminal-test-runner.js
```

### Browser Console Errors
```javascript
// Check if validation system is loaded
console.log(window.AssistantContextValidator);

// Manually test validation
const validator = { /* paste validation code here */ };
```

## Success Criteria Checklist

### Phase 1: Immediate Fixes ✅
- [ ] No attorney ID in logs as assistant ID
- [ ] Validation system rejects attorney IDs
- [ ] Fallback UI appears when needed
- [ ] All terminal tests pass

### Phase 2: Database Integrity ✅
- [ ] `consultations.assistant_id` column exists
- [ ] No attorney IDs in assistant ID fields
- [ ] All assistant configs have valid IDs
- [ ] Database integrity check passes

### Phase 3: Live Validation ✅
- [ ] Browser tests pass
- [ ] Live monitoring shows no corruption
- [ ] App works without attorney ID fallbacks
- [ ] Assistant switching works correctly

## Support

If tests continue to fail:
1. Check console for specific error messages
2. Verify all files are in correct locations
3. Ensure database migrations have been run
4. Clear all browser cache and localStorage
5. Restart development server

The testing system is designed to be comprehensive yet simple to run. Each test provides clear feedback and actionable recommendations.

/**
 * Fix Label/Input ID Mismatches
 *
 * This script fixes accessibility issues where label 'for' attributes
 * don't match their corresponding input element IDs.
 *
 * Common issues this fixes:
 * - Labels with 'for' attributes pointing to non-existent IDs
 * - Form elements without proper label associations
 * - Mismatched label/input pairs due to typos or dynamic content
 */

(function() {
  // Browser-compatible development mode detection
  const isDevelopment = window.location.hostname === 'localhost' ||
                       window.location.hostname === '127.0.0.1' ||
                       window.location.hostname.includes('localhost');

  if (isDevelopment) {
    console.log('[LabelInputFix] Starting label/input ID mismatch fix...');
  }

  function fixLabelInputMismatches() {
    // Find all labels with 'for' attributes
    const labels = document.querySelectorAll('label[for]');
    const mismatches = [];

    if (isDevelopment) {
      console.log(`[LabelInputFix] Found ${labels.length} labels with 'for' attributes`);
    }

    labels.forEach((label, index) => {
      const forValue = label.getAttribute('for');
      const targetElement = document.getElementById(forValue);

      console.log(`[LabelInputFix] Label ${index + 1}: for="${forValue}", text="${label.textContent.trim()}", target found: ${!!targetElement}`);

      if (!targetElement) {
        // No element found with this ID
        console.warn(`[LabelInputFix] Missing target for label with for="${forValue}"`);
        mismatches.push({
          label: label,
          forValue: forValue,
          issue: 'missing-target',
          labelText: label.textContent.trim()
        });
      } else if (!['INPUT', 'TEXTAREA', 'SELECT', 'BUTTON'].includes(targetElement.tagName)) {
        // Target exists but is not a form element
        console.warn(`[LabelInputFix] Invalid target for label with for="${forValue}", target is ${targetElement.tagName}`);
        mismatches.push({
          label: label,
          forValue: forValue,
          issue: 'invalid-target',
          targetTag: targetElement.tagName,
          labelText: label.textContent.trim()
        });
      }
    });

    // Find form elements without proper labels
    const formElements = document.querySelectorAll('input, textarea, select');
    if (isDevelopment) {
      console.log(`[LabelInputFix] Found ${formElements.length} form elements`);
    }

    formElements.forEach((element, index) => {
      if (element.id) {
        const associatedLabel = document.querySelector(`label[for="${element.id}"]`);
        if (isDevelopment) {
          console.log(`[LabelInputFix] Form element ${index + 1}: id="${element.id}", type="${element.type || element.tagName}", has label: ${!!associatedLabel}`);
        }

        if (!associatedLabel) {
          // Check if the element is inside a label (implicit association)
          const parentLabel = element.closest('label');
          if (!parentLabel) {
            if (isDevelopment) {
              console.warn(`[LabelInputFix] Form element with id="${element.id}" has no associated label`);
            }
            mismatches.push({
              element: element,
              elementId: element.id,
              issue: 'missing-label',
              elementType: element.tagName,
              elementName: element.name || 'unnamed'
            });
          } else if (isDevelopment) {
            console.log(`[LabelInputFix] Form element with id="${element.id}" has implicit label association`);
          }
        }
      } else if (isDevelopment) {
        console.log(`[LabelInputFix] Form element ${index + 1} has no ID: ${element.tagName}${element.type ? `[type="${element.type}"]` : ''}`);
      }
    });

    // Report and attempt to fix mismatches
    if (mismatches.length > 0) {
      if (isDevelopment) {
        console.warn('[LabelInputFix] Found label/input mismatches:', mismatches);
      }

      mismatches.forEach(mismatch => {
        if (mismatch.issue === 'missing-target') {
          // Try to find a similar input element
          const possibleTargets = document.querySelectorAll('input, textarea, select');
          let bestMatch = null;
          let bestScore = 0;

          possibleTargets.forEach(target => {
            if (!target.id) return;

            // Calculate similarity score
            let score = 0;
            const targetId = target.id.toLowerCase();
            const forValue = mismatch.forValue.toLowerCase();
            const labelText = mismatch.labelText.toLowerCase();

            // Exact match
            if (targetId === forValue) {
              score = 100;
            }
            // Partial match
            else if (targetId.includes(forValue) || forValue.includes(targetId)) {
              score = 80;
            }
            // Label text similarity
            else if (labelText.includes(targetId) || targetId.includes(labelText.replace(/\s+/g, ''))) {
              score = 60;
            }
            // Name attribute similarity
            else if (target.name && (target.name.toLowerCase() === forValue || forValue.includes(target.name.toLowerCase()))) {
              score = 70;
            }

            if (score > bestScore) {
              bestScore = score;
              bestMatch = target;
            }
          });

          if (bestMatch && bestScore >= 60) {
            if (isDevelopment) {
              console.log(`[LabelInputFix] Fixing label for="${mismatch.forValue}" to point to element with id="${bestMatch.id}"`);
            }
            mismatch.label.setAttribute('for', bestMatch.id);
          } else if (isDevelopment) {
            console.warn(`[LabelInputFix] Could not find suitable target for label with for="${mismatch.forValue}"`);
          }
        }

        if (mismatch.issue === 'missing-label') {
          // Try to find a nearby label or create one
          const element = mismatch.element;

          // Look for a label that might be intended for this element
          const nearbyLabels = document.querySelectorAll('label:not([for])');
          let bestLabel = null;
          let bestDistance = Infinity;

          nearbyLabels.forEach(label => {
            const labelRect = label.getBoundingClientRect();
            const elementRect = element.getBoundingClientRect();

            // Calculate distance between label and element
            const distance = Math.sqrt(
              Math.pow(labelRect.left - elementRect.left, 2) +
              Math.pow(labelRect.top - elementRect.top, 2)
            );

            if (distance < bestDistance && distance < 200) { // Within 200px
              bestDistance = distance;
              bestLabel = label;
            }
          });

          if (bestLabel) {
            if (isDevelopment) {
              console.log(`[LabelInputFix] Associating nearby label "${bestLabel.textContent.trim()}" with element id="${element.id}"`);
            }
            bestLabel.setAttribute('for', element.id);
          } else if (isDevelopment) {
            console.warn(`[LabelInputFix] Element with id="${element.id}" has no associated label`);
          }
        }
      });
    } else if (isDevelopment) {
      console.log('[LabelInputFix] No label/input mismatches found');
    }
  }

  // Run the fix when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', fixLabelInputMismatches);
  } else {
    fixLabelInputMismatches();
  }

  // Run the fix multiple times to catch dynamic content
  setTimeout(fixLabelInputMismatches, 500);   // After 0.5 seconds
  setTimeout(fixLabelInputMismatches, 1000);  // After 1 second
  setTimeout(fixLabelInputMismatches, 2000);  // After 2 seconds

  // Also run when new content is added to the page
  const observer = new MutationObserver(function(mutations) {
    let shouldRecheck = false;

    mutations.forEach(function(mutation) {
      if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
        mutation.addedNodes.forEach(function(node) {
          if (node.nodeType === 1) { // Element node
            // Check if new labels or form elements were added
            if (node.tagName === 'LABEL' ||
                node.tagName === 'INPUT' ||
                node.tagName === 'TEXTAREA' ||
                node.tagName === 'SELECT' ||
                node.querySelector('label, input, textarea, select')) {
              shouldRecheck = true;
            }
          }
        });
      }
    });

    if (shouldRecheck) {
      if (isDevelopment) {
        console.log('[LabelInputFix] DOM mutation detected, rechecking labels...');
      }
      setTimeout(fixLabelInputMismatches, 100); // Small delay to let DOM settle
    }
  });

  observer.observe(document.documentElement, {
    childList: true,
    subtree: true
  });

  // Also run when React components update (listen for React events)
  window.addEventListener('react-component-update', function() {
    if (isDevelopment) {
      console.log('[LabelInputFix] React component update detected, rechecking labels...');
    }
    setTimeout(fixLabelInputMismatches, 50);
  });

  if (isDevelopment) {
    console.log('[LabelInputFix] Label/input mismatch fix initialized');
  }
})();

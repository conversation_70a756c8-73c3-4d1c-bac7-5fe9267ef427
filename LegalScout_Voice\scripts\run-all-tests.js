#!/usr/bin/env node

/**
 * Master Test Coordinator
 * 
 * Runs all available tests and provides a comprehensive report.
 * Usage: node scripts/run-all-tests.js
 */

import { runTests as runTerminalTests } from './terminal-test-runner.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

function log(message, type = 'info') {
  const timestamp = new Date().toISOString().split('T')[1].split('.')[0];
  const prefix = {
    info: '📋',
    success: '✅',
    error: '❌',
    warning: '⚠️',
    header: '🎯'
  }[type] || '📋';
  
  console.log(`${prefix} [${timestamp}] ${message}`);
}

function generateTestInstructions() {
  const instructions = `
# 🧪 Comprehensive Assistant Validation Testing Guide

## Quick Start (Choose One)

### Option 1: Terminal Tests Only
\`\`\`bash
node scripts/run-all-tests.js
\`\`\`

### Option 2: <PERSON>rowser Console Tests Only
1. Open your app in the browser
2. Open Developer Tools (F12)
3. Go to Console tab
4. Copy and paste the contents of \`scripts/browser-console-test-suite.js\`
5. Run: \`runAssistantValidationTests()\`

### Option 3: Live Monitoring
1. Open your app in the browser
2. Open Developer Tools (F12)
3. Copy and paste the contents of \`scripts/live-monitoring-script.js\`
4. Run: \`quickMonitor(60)\` for 60-second monitoring

## Complete Testing Workflow

### Step 1: Terminal Validation Tests
\`\`\`bash
# Run comprehensive terminal tests
node scripts/terminal-test-runner.js

# Expected output: All validation logic tests should pass
# If any fail, check file paths and dependencies
\`\`\`

### Step 2: Database Integrity Check
1. Open Supabase Dashboard
2. Go to SQL Editor
3. Copy and paste contents of \`scripts/database-integrity-check.sql\`
4. Run each section to check for corruption

**Expected Results:**
- No attorney IDs should appear as assistant IDs
- consultations table should have assistant_id column
- All assistant configs should have valid assistant IDs

### Step 3: Browser Integration Tests
1. Start your development server: \`npm run dev\`
2. Open browser and navigate to your app
3. Open Developer Tools Console
4. Run browser console test suite
5. Verify no corruption is detected

### Step 4: Live Monitoring (Optional)
1. With app running, start live monitoring
2. Navigate through different tabs
3. Switch between assistants
4. Check that no attorney IDs appear as assistant IDs

## Test Results Interpretation

### ✅ All Tests Pass
- Validation system is working correctly
- No corruption detected
- Safe to proceed with normal usage

### ⚠️ Some Tests Fail
- Check specific error messages
- Verify file paths are correct
- Ensure validation system is properly imported
- Run database fixes if needed

### 🚨 Corruption Detected
1. **Immediate Action**: Run database fix script
   \`\`\`bash
   node scripts/fix-assistant-id-corruption.js
   \`\`\`

2. **Verify Fix**: Re-run database integrity check

3. **Clear Cache**: Clear browser localStorage and refresh

4. **Re-test**: Run all tests again to verify fix

## Troubleshooting

### "Validation system not found"
- Ensure \`src/utils/assistantContextValidator.js\` exists
- Check import paths in your components
- Verify the validation system is properly exported

### "Database column does not exist"
- Run: \`scripts/add-assistant-id-to-consultations.sql\`
- Verify schema changes in Supabase dashboard

### "Attorney ID still appearing"
- Run: \`scripts/fix-assistant-id-corruption.js\`
- Clear localStorage: \`localStorage.clear()\`
- Refresh browser and re-test

## Success Criteria

### Immediate Success (Phase 1) ✅
- [ ] No attorney ID (87756a2c-a398-43f2-889a-b8815684df71) in logs as assistant ID
- [ ] Validation system rejects attorney IDs as assistant IDs
- [ ] Fallback UI appears when assistant context is invalid

### Database Success (Phase 2) ✅
- [ ] consultations.assistant_id column exists
- [ ] No attorney IDs in assistant ID fields
- [ ] All assistant configs have valid assistant IDs

### Integration Success (Phase 3) ✅
- [ ] All tabs show data for correct assistant
- [ ] Assistant switching works without corruption
- [ ] No fallback to attorney ID anywhere in the system

## Files Created/Modified

### New Files:
- \`src/utils/assistantContextValidator.js\` - Validation system
- \`src/components/AssistantSelectionPrompt.jsx\` - Fallback UI
- \`scripts/add-assistant-id-to-consultations.sql\` - Database migration
- \`scripts/fix-assistant-id-corruption.js\` - Data cleanup
- \`scripts/database-integrity-check.sql\` - Integrity verification

### Modified Files:
- \`src/contexts/AssistantAwareContext.jsx\` - Updated to use validation

## Support

If tests continue to fail:
1. Check the console for specific error messages
2. Verify all files are in the correct locations
3. Ensure database migrations have been run
4. Clear all browser cache and localStorage
5. Restart the development server

The testing system is designed to be comprehensive yet simple to run.
Each test provides clear feedback and actionable recommendations.
`;

  return instructions;
}

async function runAllTests() {
  log('Starting Comprehensive Assistant Validation Test Suite', 'header');
  
  const results = {
    terminal: null,
    fileChecks: null,
    instructions: null,
    summary: {
      totalTests: 0,
      passed: 0,
      failed: 0,
      warnings: []
    }
  };
  
  try {
    // 1. File existence checks
    log('Checking test files existence...');
    const testFiles = [
      'scripts/browser-console-test-suite.js',
      'scripts/terminal-test-runner.js',
      'scripts/database-integrity-check.sql',
      'scripts/live-monitoring-script.js',
      'src/utils/assistantContextValidator.js',
      'src/components/AssistantSelectionPrompt.jsx'
    ];
    
    const fileChecks = testFiles.map(file => {
      const fullPath = path.join(__dirname, '..', file);
      const exists = fs.existsSync(fullPath);
      return { file, exists, path: fullPath };
    });
    
    results.fileChecks = fileChecks;
    const missingFiles = fileChecks.filter(f => !f.exists);
    
    if (missingFiles.length > 0) {
      log(`Missing files detected: ${missingFiles.map(f => f.file).join(', ')}`, 'warning');
      results.summary.warnings.push(`Missing ${missingFiles.length} test files`);
    } else {
      log('All test files present', 'success');
    }
    
    // 2. Run terminal tests
    log('Running terminal validation tests...');
    try {
      results.terminal = await runTerminalTests();
      results.summary.totalTests += results.terminal.passed + results.terminal.failed;
      results.summary.passed += results.terminal.passed;
      results.summary.failed += results.terminal.failed;
      
      if (results.terminal.failed === 0) {
        log('Terminal tests completed successfully', 'success');
      } else {
        log(`Terminal tests completed with ${results.terminal.failed} failures`, 'warning');
      }
    } catch (error) {
      log(`Terminal tests failed: ${error.message}`, 'error');
      results.summary.failed++;
      results.summary.warnings.push('Terminal tests could not run');
    }
    
    // 3. Generate comprehensive instructions
    log('Generating test instructions...');
    results.instructions = generateTestInstructions();
    
    // 4. Generate summary report
    log('\n📊 Comprehensive Test Report', 'header');
    log(`📁 File Checks: ${fileChecks.filter(f => f.exists).length}/${fileChecks.length} files present`);
    
    if (results.terminal) {
      log(`🖥️ Terminal Tests: ${results.terminal.passed}/${results.terminal.passed + results.terminal.failed} passed`);
    }
    
    log(`📈 Overall Success Rate: ${Math.round((results.summary.passed / Math.max(results.summary.totalTests, 1)) * 100)}%`);
    
    // 5. Provide next steps
    log('\n📋 Next Steps:', 'header');
    
    if (missingFiles.length > 0) {
      log('🔧 Fix missing files first', 'warning');
      missingFiles.forEach(file => {
        log(`  - Create: ${file.file}`, 'warning');
      });
    }
    
    if (results.terminal && results.terminal.failed > 0) {
      log('🔧 Fix terminal test failures', 'warning');
      log('  - Check file paths and dependencies', 'info');
      log('  - Verify validation system is properly exported', 'info');
    }
    
    log('🌐 Run browser console tests:', 'info');
    log('  1. Start your app: npm run dev', 'info');
    log('  2. Open browser console', 'info');
    log('  3. Paste browser-console-test-suite.js', 'info');
    log('  4. Run: runAssistantValidationTests()', 'info');
    
    log('🗄️ Run database integrity check:', 'info');
    log('  1. Open Supabase SQL Editor', 'info');
    log('  2. Run database-integrity-check.sql', 'info');
    log('  3. Fix any corruption found', 'info');
    
    log('🔍 Optional: Run live monitoring:', 'info');
    log('  1. Paste live-monitoring-script.js in console', 'info');
    log('  2. Run: quickMonitor(60)', 'info');
    log('  3. Use app normally and check for detections', 'info');
    
    // 6. Save detailed report
    const reportPath = path.join(__dirname, 'test-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(results, null, 2));
    log(`📄 Detailed report saved: ${reportPath}`, 'success');
    
    // 7. Save instructions
    const instructionsPath = path.join(__dirname, 'TESTING_INSTRUCTIONS.md');
    fs.writeFileSync(instructionsPath, results.instructions);
    log(`📖 Testing instructions saved: ${instructionsPath}`, 'success');
    
    return results;
    
  } catch (error) {
    log(`Test coordinator error: ${error.message}`, 'error');
    throw error;
  }
}

// Run if executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runAllTests().catch(console.error);
}

export { runAllTests };

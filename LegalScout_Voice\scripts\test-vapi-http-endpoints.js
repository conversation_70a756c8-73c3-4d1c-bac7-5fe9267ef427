/**
 * Test Vapi HTTP Endpoints
 *
 * This script tests the Vapi HTTP endpoints by making direct API calls.
 */

import { config } from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, resolve } from 'path';
import fetch from 'node-fetch';

// Get current file path
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables from .env file
config({ path: resolve(__dirname, '../.env') });

// Get Vapi API keys from environment variables
const VAPI_PUBLIC_KEY = process.env.VITE_VAPI_PUBLIC_KEY || process.env.VAPI_PUBLIC_KEY || '6734febc-fc65-4669-93b0-929b31ff6564';
const VAPI_PRIVATE_KEY = process.env.VAPI_TOKEN || process.env.VAPI_SECRET_KEY || '6734febc-fc65-4669-93b0-929b31ff6564';

console.log(`🔑 Using Vapi Public API key: ${VAPI_PUBLIC_KEY.substring(0, 5)}...`);
console.log(`🔑 Using Vapi Private API key: ${VAPI_PRIVATE_KEY.substring(0, 5)}...`);

// Test Vapi API endpoints
async function testVapiApi() {
  console.log('🧪 Testing Vapi API endpoints...');

  // Define endpoints to test
  const endpoints = [
    { name: 'Assistants', url: 'https://api.vapi.ai/assistant' },
    { name: 'Calls', url: 'https://api.vapi.ai/call' },
    { name: 'Phone Numbers', url: 'https://api.vapi.ai/phone-number' },
    { name: 'Tools', url: 'https://api.vapi.ai/tool' }
  ];

  // Test with private key
  console.log('\n🔍 Testing with Private API Key:');
  await testEndpoints(endpoints, VAPI_PRIVATE_KEY);
}

// Test local MCP server endpoints
async function testLocalMcpServer() {
  console.log('\n🧪 Testing local MCP server endpoints...');

  // Define MCP server endpoints to test
  const endpoints = [
    { name: 'MCP Server', url: 'http://localhost:5175/vapi-mcp-server' },
    { name: 'MCP Server SSE', url: 'http://localhost:5175/vapi-mcp-server/sse' },
    { name: 'API MCP Server', url: 'http://localhost:5175/api/vapi-mcp-server' },
    { name: 'API MCP Server SSE', url: 'http://localhost:5175/api/vapi-mcp-server/sse' }
  ];

  // Test each endpoint
  for (const endpoint of endpoints) {
    console.log(`\n🔍 Testing ${endpoint.name} endpoint: ${endpoint.url}`);

    try {
      const response = await fetch(endpoint.url, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${VAPI_PRIVATE_KEY}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.text();
        console.log(`✅ ${endpoint.name} endpoint test successful`);
        console.log(`📊 Response: ${data.substring(0, 100)}...`);
      } else {
        console.error(`❌ ${endpoint.name} endpoint test failed: ${response.status} ${response.statusText}`);

        // Try to get more details from the response
        try {
          const errorData = await response.text();
          console.error(`Error details: ${errorData.substring(0, 200)}...`);
        } catch (e) {
          // Ignore if we can't parse the response
        }
      }
    } catch (error) {
      console.error(`❌ ${endpoint.name} endpoint test error: ${error.message}`);
    }
  }
}

// Test MCP server tools
async function testMcpServerTools() {
  console.log('\n🧪 Testing MCP server tools...');

  // Define MCP server tools to test
  const tools = [
    {
      name: 'List Assistants',
      url: 'http://localhost:5175/vapi-mcp-server',
      method: 'POST',
      body: {
        name: 'list_assistants_vapi-mcp-server',
        arguments: {}
      }
    },
    {
      name: 'List Calls',
      url: 'http://localhost:5175/vapi-mcp-server',
      method: 'POST',
      body: {
        name: 'list_calls_vapi-mcp-server',
        arguments: {}
      }
    },
    {
      name: 'List Phone Numbers',
      url: 'http://localhost:5175/vapi-mcp-server',
      method: 'POST',
      body: {
        name: 'list_phone_numbers_vapi-mcp-server',
        arguments: {}
      }
    },
    {
      name: 'List Tools',
      url: 'http://localhost:5175/vapi-mcp-server',
      method: 'POST',
      body: {
        name: 'list_tools_vapi-mcp-server',
        arguments: {}
      }
    }
  ];

  // Test each tool
  for (const tool of tools) {
    console.log(`\n🔍 Testing ${tool.name} tool: ${tool.url}`);

    try {
      const response = await fetch(tool.url, {
        method: tool.method,
        headers: {
          'Authorization': `Bearer ${VAPI_PRIVATE_KEY}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(tool.body)
      });

      if (response.ok) {
        const data = await response.json();
        console.log(`✅ ${tool.name} tool test successful`);
        console.log(`📊 Found ${Array.isArray(data) ? data.length : 1} items`);
      } else {
        console.error(`❌ ${tool.name} tool test failed: ${response.status} ${response.statusText}`);

        // Try to get more details from the response
        try {
          const errorData = await response.text();
          console.error(`Error details: ${errorData.substring(0, 200)}...`);
        } catch (e) {
          // Ignore if we can't parse the response
        }
      }
    } catch (error) {
      console.error(`❌ ${tool.name} tool test error: ${error.message}`);
    }
  }
}

// Test endpoints with a specific API key
async function testEndpoints(endpoints, apiKey) {
  // Test each endpoint
  for (const endpoint of endpoints) {
    console.log(`\n🔍 Testing ${endpoint.name} endpoint: ${endpoint.url}`);

    try {
      const response = await fetch(endpoint.url, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        console.log(`✅ ${endpoint.name} endpoint test successful`);
        console.log(`📊 Found ${Array.isArray(data) ? data.length : 1} items`);
      } else {
        console.error(`❌ ${endpoint.name} endpoint test failed: ${response.status} ${response.statusText}`);

        // Try to get more details from the response
        try {
          const errorData = await response.text();
          console.error(`Error details: ${errorData}`);
        } catch (e) {
          // Ignore if we can't parse the response
        }
      }
    } catch (error) {
      console.error(`❌ ${endpoint.name} endpoint test error: ${error.message}`);
    }
  }
}

// Run the tests
async function runTests() {
  // Test Vapi API endpoints
  await testVapiApi();

  // Test local MCP server endpoints
  await testLocalMcpServer();

  // Test MCP server tools
  await testMcpServerTools();

  console.log('\n📋 Test Summary:');
  console.log('All tests completed. Check the logs for details.');
}

// Run the tests
runTests().catch(error => {
  console.error(`❌ Error running tests: ${error.message}`);
  process.exit(1);
});

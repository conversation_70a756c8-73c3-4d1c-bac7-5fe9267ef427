/**
 * <PERSON><PERSON><PERSON> to sync webhook URLs for all attorneys with Vapi assistants
 * This ensures all assistants have the correct subdomain-specific webhook URLs
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const vapiPrivateKey = process.env.VAPI_PRIVATE_KEY;
const webhookSecret = process.env.VAPI_WEBHOOK_SECRET || 'legalscout-webhook-secret';
const baseDomain = process.env.VITE_BASE_DOMAIN || 'legalscout.net';

if (!supabaseUrl || !supabaseKey || !vapiPrivateKey) {
  console.error('❌ Missing required environment variables:');
  console.error('   - VITE_SUPABASE_URL');
  console.error('   - SUPABASE_SERVICE_ROLE_KEY');
  console.error('   - VAPI_PRIVATE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * Generate webhook URL for attorney subdomain
 */
function generateWebhookUrl(subdomain) {
  if (!subdomain) {
    return `https://${baseDomain}/api/vapi-webhook-direct`;
  }
  return `https://${subdomain}.${baseDomain}/api/vapi-webhook-direct`;
}

/**
 * Update Vapi assistant webhook URL
 */
async function updateVapiAssistantWebhook(assistantId, webhookUrl, attorneyInfo) {
  try {
    const response = await fetch(`https://api.vapi.ai/assistant/${assistantId}`, {
      method: 'PATCH',
      headers: {
        'Authorization': `Bearer ${vapiPrivateKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        serverUrl: webhookUrl,
        serverUrlSecret: webhookSecret
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Vapi API error: ${response.status} - ${errorText}`);
    }

    const result = await response.json();
    console.log(`✅ Updated ${attorneyInfo.firm_name} (${assistantId}): ${webhookUrl}`);
    return result;

  } catch (error) {
    console.error(`❌ Failed to update ${attorneyInfo.firm_name} (${assistantId}):`, error.message);
    throw error;
  }
}

/**
 * Main sync function
 */
async function syncAllWebhookUrls() {
  try {
    console.log('🔍 Fetching all attorneys with Vapi assistants...\n');

    // Get all attorneys with assistant IDs
    const { data: attorneys, error } = await supabase
      .from('attorneys')
      .select('id, firm_name, subdomain, vapi_assistant_id, email')
      .not('vapi_assistant_id', 'is', null)
      .order('firm_name');

    if (error) {
      throw new Error(`Failed to fetch attorneys: ${error.message}`);
    }

    if (!attorneys || attorneys.length === 0) {
      console.log('ℹ️  No attorneys with Vapi assistants found.');
      return;
    }

    console.log(`📋 Found ${attorneys.length} attorneys with Vapi assistants:\n`);

    // Display all attorneys that will be updated
    attorneys.forEach((attorney, index) => {
      const webhookUrl = generateWebhookUrl(attorney.subdomain);
      console.log(`${index + 1}. ${attorney.firm_name}`);
      console.log(`   Email: ${attorney.email}`);
      console.log(`   Subdomain: ${attorney.subdomain || 'none'}`);
      console.log(`   Assistant ID: ${attorney.vapi_assistant_id}`);
      console.log(`   Webhook URL: ${webhookUrl}`);
      console.log('');
    });

    console.log('🔄 Starting webhook URL sync...\n');

    // Update each attorney's assistant
    let successCount = 0;
    let errorCount = 0;
    const errors = [];

    for (const attorney of attorneys) {
      try {
        const webhookUrl = generateWebhookUrl(attorney.subdomain);
        await updateVapiAssistantWebhook(attorney.vapi_assistant_id, webhookUrl, attorney);
        
        // Update the attorney record with sync timestamp
        await supabase
          .from('attorneys')
          .update({ 
            webhook_last_synced: new Date().toISOString(),
            webhook_url: webhookUrl
          })
          .eq('id', attorney.id);

        successCount++;
      } catch (error) {
        errorCount++;
        errors.push({
          attorney: attorney.firm_name,
          assistantId: attorney.vapi_assistant_id,
          error: error.message
        });
      }
    }

    console.log('\n📊 Sync Results:');
    console.log(`✅ Successfully updated: ${successCount}`);
    console.log(`❌ Failed to update: ${errorCount}`);

    if (errors.length > 0) {
      console.log('\n❌ Errors encountered:');
      errors.forEach(({ attorney, assistantId, error }) => {
        console.log(`   - ${attorney} (${assistantId}): ${error}`);
      });
    }

    if (successCount > 0) {
      console.log('\n🎉 Webhook URL sync completed successfully!');
      console.log('   All assistants now have correct subdomain-specific webhook URLs.');
    }

  } catch (error) {
    console.error('❌ Error during webhook URL sync:', error);
    process.exit(1);
  }
}

// Run the sync
console.log('🚀 Starting webhook URL sync for all attorneys...\n');
syncAllWebhookUrls();

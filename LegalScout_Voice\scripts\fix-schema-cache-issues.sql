-- Fix Schema Cache Issues
-- Run this in Supabase SQL Editor to fix the "missing key" errors

-- =============================================================================
-- 1. ADD MISSING COLUMNS TO ATTORNEYS TABLE
-- =============================================================================

-- Add mascot column (if it doesn't exist)
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'attorneys' AND column_name = 'mascot'
    ) THEN
        ALTER TABLE attorneys ADD COLUMN mascot TEXT;
        RAISE NOTICE 'Added mascot column to attorneys table';
    ELSE
        RAISE NOTICE 'mascot column already exists';
    END IF;
END $$;

-- Add information_gathering_prompt column (if it doesn't exist)
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'attorneys' AND column_name = 'information_gathering_prompt'
    ) THEN
        ALTER TABLE attorneys ADD COLUMN information_gathering_prompt TEXT;
        RAISE NOTICE 'Added information_gathering_prompt column to attorneys table';
    ELSE
        RAISE NOTICE 'information_gathering_prompt column already exists';
    END IF;
END $$;

-- =============================================================================
-- 2. ADD MISSING ASSISTANT_ID COLUMN TO CONSULTATIONS TABLE
-- =============================================================================

-- Add assistant_id column to consultations (if it doesn't exist)
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'consultations' AND column_name = 'assistant_id'
    ) THEN
        ALTER TABLE consultations ADD COLUMN assistant_id UUID;
        RAISE NOTICE 'Added assistant_id column to consultations table';
        
        -- Create index for performance
        CREATE INDEX IF NOT EXISTS idx_consultations_assistant_id 
        ON consultations(assistant_id);
        RAISE NOTICE 'Created index on consultations.assistant_id';
        
    ELSE
        RAISE NOTICE 'assistant_id column already exists in consultations table';
    END IF;
END $$;

-- =============================================================================
-- 3. MIGRATE EXISTING CONSULTATIONS DATA
-- =============================================================================

-- Associate existing consultations with attorney's primary assistant
DO $$ 
BEGIN
    -- Only run if there are consultations without assistant_id
    IF EXISTS (
        SELECT 1 FROM consultations 
        WHERE assistant_id IS NULL AND attorney_id IS NOT NULL
    ) THEN
        UPDATE consultations 
        SET assistant_id = (
            SELECT vapi_assistant_id 
            FROM attorneys 
            WHERE attorneys.id = consultations.attorney_id
            AND attorneys.vapi_assistant_id IS NOT NULL
            AND attorneys.vapi_assistant_id != attorneys.id  -- Prevent attorney ID corruption
        )
        WHERE assistant_id IS NULL 
        AND attorney_id IS NOT NULL;
        
        RAISE NOTICE 'Migrated existing consultations to use assistant_id';
    ELSE
        RAISE NOTICE 'No consultations need migration';
    END IF;
END $$;

-- =============================================================================
-- 4. REFRESH SCHEMA CACHE
-- =============================================================================

-- Force Supabase to refresh its schema cache
NOTIFY pgrst, 'reload schema';

-- =============================================================================
-- 5. VERIFY THE FIXES
-- =============================================================================

-- Check that all required columns exist
SELECT 
    table_name,
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns 
WHERE table_name IN ('attorneys', 'consultations')
    AND column_name IN ('mascot', 'information_gathering_prompt', 'assistant_id')
ORDER BY table_name, column_name;

-- Check for any remaining corruption
SELECT 
    'attorneys_self_reference' as issue_type,
    COUNT(*) as count
FROM attorneys 
WHERE vapi_assistant_id = id OR current_assistant_id = id

UNION ALL

SELECT 
    'consultations_without_assistant_id' as issue_type,
    COUNT(*) as count
FROM consultations 
WHERE assistant_id IS NULL AND attorney_id IS NOT NULL

UNION ALL

SELECT 
    'consultations_total' as issue_type,
    COUNT(*) as count
FROM consultations;

-- =============================================================================
-- 6. SUCCESS MESSAGE
-- =============================================================================

DO $$ 
BEGIN
    RAISE NOTICE '✅ Schema cache fix completed!';
    RAISE NOTICE '📋 Next steps:';
    RAISE NOTICE '  1. Refresh your browser to clear client-side cache';
    RAISE NOTICE '  2. The "missing key" errors should be resolved';
    RAISE NOTICE '  3. Run the browser console tests to verify';
END $$;

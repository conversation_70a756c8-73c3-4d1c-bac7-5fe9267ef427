/**
 * Test Voice Sync
 * 
 * This script tests the voice synchronization between dashboard and Vapi
 */

const VAPI_API_URL = 'https://api.vapi.ai';
const VAPI_PRIVATE_KEY = '6734febc-fc65-4669-93b0-929b31ff6564';
const ASSISTANT_ID = 'eb8533fa-902e-46be-8ce9-df20f5c550d7';

async function getVapiAssistantVoice() {
  try {
    console.log('🔍 Fetching current voice settings from Vapi...');
    
    const response = await fetch(`${VAPI_API_URL}/assistant/${ASSISTANT_ID}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${VAPI_PRIVATE_KEY}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (!response.ok) {
      throw new Error(`API request failed: ${response.status} ${response.statusText}`);
    }
    
    const assistant = await response.json();
    
    console.log('📊 Current Vapi Assistant Voice Settings:');
    console.log(`   Provider: ${assistant.voice?.provider || 'Not set'}`);
    console.log(`   Voice ID: ${assistant.voice?.voiceId || 'Not set'}`);
    console.log(`   Model: ${assistant.voice?.model || 'Not set'}`);
    console.log(`   Updated: ${assistant.updatedAt}`);
    
    return assistant.voice;
    
  } catch (error) {
    console.error('❌ Error fetching Vapi assistant voice:', error);
    throw error;
  }
}

async function updateVapiAssistantVoice(voiceId, provider = '11labs') {
  try {
    console.log(`🔄 Updating Vapi assistant voice to: ${voiceId} (${provider})`);
    
    const updateConfig = {
      voice: {
        provider: provider,
        voiceId: voiceId
      }
    };
    
    const response = await fetch(`${VAPI_API_URL}/assistant/${ASSISTANT_ID}`, {
      method: 'PATCH',
      headers: {
        'Authorization': `Bearer ${VAPI_PRIVATE_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(updateConfig)
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`API request failed: ${response.status} ${response.statusText} - ${errorText}`);
    }
    
    const updatedAssistant = await response.json();
    
    console.log('✅ Successfully updated Vapi assistant voice:');
    console.log(`   Provider: ${updatedAssistant.voice?.provider}`);
    console.log(`   Voice ID: ${updatedAssistant.voice?.voiceId}`);
    console.log(`   Updated: ${updatedAssistant.updatedAt}`);
    
    return updatedAssistant.voice;
    
  } catch (error) {
    console.error('❌ Error updating Vapi assistant voice:', error);
    throw error;
  }
}

async function testVoiceSync() {
  console.log('🧪 Testing Voice Sync Between Dashboard and Vapi\n');
  
  try {
    // Step 1: Get current voice settings
    console.log('📋 Step 1: Check current voice settings');
    const currentVoice = await getVapiAssistantVoice();
    
    // Step 2: Test changing to a different voice
    console.log('\n🔄 Step 2: Test changing voice to "adam"');
    const newVoice = await updateVapiAssistantVoice('adam', '11labs');
    
    // Step 3: Verify the change
    console.log('\n🔍 Step 3: Verify the change took effect');
    const verifyVoice = await getVapiAssistantVoice();
    
    if (verifyVoice.voiceId === 'adam' && verifyVoice.provider === '11labs') {
      console.log('✅ SUCCESS: Voice change verified!');
    } else {
      console.log('❌ FAILED: Voice change not reflected');
    }
    
    // Step 4: Change back to original voice
    if (currentVoice && currentVoice.voiceId) {
      console.log(`\n🔄 Step 4: Changing back to original voice: ${currentVoice.voiceId}`);
      await updateVapiAssistantVoice(currentVoice.voiceId, currentVoice.provider);
      
      // Verify restoration
      const finalVoice = await getVapiAssistantVoice();
      if (finalVoice.voiceId === currentVoice.voiceId) {
        console.log('✅ SUCCESS: Original voice restored!');
      } else {
        console.log('⚠️ WARNING: Could not restore original voice');
      }
    }
    
  } catch (error) {
    console.error('💥 Test failed:', error);
  }
}

async function demonstrateVoiceOptions() {
  console.log('\n📋 Available Voice Options:');
  console.log('\n🎵 11labs Voices:');
  console.log('   - sarah (Female)');
  console.log('   - adam (Male)');
  console.log('   - josh (Male)');
  console.log('   - rachel (Female)');
  console.log('   - daniel (Male)');
  
  console.log('\n🎵 OpenAI Voices:');
  console.log('   - alloy (Neutral)');
  console.log('   - echo (Male)');
  console.log('   - fable (Female)');
  console.log('   - onyx (Male)');
  console.log('   - nova (Female)');
  console.log('   - shimmer (Female)');
  
  console.log('\n💡 To test a specific voice, you can run:');
  console.log('   node scripts/test-voice-sync.js --voice=echo --provider=openai');
}

async function main() {
  console.log('🚀 Voice Sync Test Tool\n');
  console.log('This tool tests voice synchronization between your dashboard and Vapi.');
  console.log('═'.repeat(70));
  
  // Check for command line arguments
  const args = process.argv.slice(2);
  const voiceArg = args.find(arg => arg.startsWith('--voice='));
  const providerArg = args.find(arg => arg.startsWith('--provider='));
  
  if (voiceArg) {
    const voiceId = voiceArg.split('=')[1];
    const provider = providerArg ? providerArg.split('=')[1] : '11labs';
    
    console.log(`🎯 Testing specific voice: ${voiceId} (${provider})`);
    
    try {
      await getVapiAssistantVoice();
      await updateVapiAssistantVoice(voiceId, provider);
      await getVapiAssistantVoice();
    } catch (error) {
      console.error('Test failed:', error);
    }
  } else {
    // Run full test suite
    await testVoiceSync();
    await demonstrateVoiceOptions();
  }
  
  console.log('\n' + '═'.repeat(70));
  console.log('🏁 Test completed!');
  console.log('\nWhat this test shows:');
  console.log('1. ✅ Current voice settings in your Vapi assistant');
  console.log('2. ✅ How to change voice settings directly in Vapi');
  console.log('3. ✅ Verification that changes take effect immediately');
  console.log('\nYour dashboard should now load and save voices directly from/to Vapi!');
}

// Handle fetch for Node.js
if (typeof fetch === 'undefined') {
  global.fetch = (await import('node-fetch')).default;
}

main().catch(error => {
  console.error('Script failed:', error);
  process.exit(1);
});

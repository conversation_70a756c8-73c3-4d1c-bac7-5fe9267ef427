-- Database Integrity Check for Assistant ID Corruption
-- Run these queries in your Supabase SQL editor to verify data integrity

-- =============================================================================
-- 1. CHECK FOR ATTORNEY IDs USED AS ASSISTANT IDs
-- =============================================================================

-- Check attorneys table for self-referencing assistant IDs
SELECT 
  'attorneys.vapi_assistant_id' as issue_type,
  id as attorney_id,
  firm_name,
  vapi_assistant_id as problematic_value,
  'Attorney ID used as vapi_assistant_id' as description
FROM attorneys 
WHERE vapi_assistant_id = id
UNION ALL
SELECT 
  'attorneys.current_assistant_id' as issue_type,
  id as attorney_id,
  firm_name,
  current_assistant_id as problematic_value,
  'Attorney ID used as current_assistant_id' as description
FROM attorneys 
WHERE current_assistant_id = id;

-- =============================================================================
-- 2. CHECK FOR SPECIFIC PROBLEMATIC ATTORNEY ID
-- =============================================================================

-- Check for the specific attorney ID that's been causing issues
WITH problematic_id AS (
  SELECT '87756a2c-a398-43f2-889a-b8815684df71'::uuid as id
)
SELECT 
  'assistant_ui_configs' as table_name,
  assistant_id,
  assistant_name,
  attorney_id,
  'Problematic attorney ID used as assistant ID' as issue
FROM assistant_ui_configs, problematic_id
WHERE assistant_id = problematic_id.id
UNION ALL
SELECT 
  'assistant_subdomains' as table_name,
  assistant_id::text,
  subdomain as assistant_name,
  attorney_id,
  'Problematic attorney ID used as assistant ID' as issue
FROM assistant_subdomains, problematic_id
WHERE assistant_id = problematic_id.id
UNION ALL
SELECT 
  'attorneys' as table_name,
  vapi_assistant_id::text,
  firm_name as assistant_name,
  id as attorney_id,
  'Problematic attorney ID used as vapi_assistant_id' as issue
FROM attorneys, problematic_id
WHERE vapi_assistant_id = problematic_id.id
UNION ALL
SELECT 
  'attorneys' as table_name,
  current_assistant_id::text,
  firm_name as assistant_name,
  id as attorney_id,
  'Problematic attorney ID used as current_assistant_id' as issue
FROM attorneys, problematic_id
WHERE current_assistant_id = problematic_id.id;

-- =============================================================================
-- 3. VERIFY CONSULTATIONS TABLE HAS ASSISTANT_ID COLUMN
-- =============================================================================

-- Check if assistant_id column exists in consultations table
SELECT 
  column_name,
  data_type,
  is_nullable,
  column_default
FROM information_schema.columns 
WHERE table_name = 'consultations' 
  AND column_name = 'assistant_id';

-- If the above returns no rows, the column doesn't exist and needs to be added

-- =============================================================================
-- 4. CHECK DATA CONSISTENCY
-- =============================================================================

-- Check for orphaned assistant configs (assistant_id not in any attorney record)
SELECT 
  ac.assistant_id,
  ac.assistant_name,
  ac.attorney_id,
  'Orphaned assistant config' as issue
FROM assistant_ui_configs ac
LEFT JOIN attorneys a ON (
  ac.assistant_id = a.vapi_assistant_id OR 
  ac.assistant_id = a.current_assistant_id
)
WHERE a.id IS NULL;

-- Check for missing assistant configs (attorneys without any assistant configs)
SELECT 
  a.id as attorney_id,
  a.firm_name,
  a.vapi_assistant_id,
  a.current_assistant_id,
  'Attorney without assistant config' as issue
FROM attorneys a
LEFT JOIN assistant_ui_configs ac ON ac.attorney_id = a.id
WHERE ac.attorney_id IS NULL;

-- =============================================================================
-- 5. VALIDATE ASSISTANT ID FORMATS
-- =============================================================================

-- Check for assistant IDs that don't look like valid UUIDs
SELECT 
  'assistant_ui_configs' as table_name,
  assistant_id,
  assistant_name,
  'Invalid UUID format' as issue
FROM assistant_ui_configs
WHERE assistant_id !~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'
UNION ALL
SELECT 
  'assistant_subdomains' as table_name,
  assistant_id::text,
  subdomain,
  'Invalid UUID format' as issue
FROM assistant_subdomains
WHERE assistant_id::text !~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$';

-- =============================================================================
-- 6. SUMMARY STATISTICS
-- =============================================================================

-- Get overall statistics
SELECT 
  'Total attorneys' as metric,
  COUNT(*)::text as value
FROM attorneys
UNION ALL
SELECT 
  'Attorneys with vapi_assistant_id' as metric,
  COUNT(*)::text as value
FROM attorneys 
WHERE vapi_assistant_id IS NOT NULL
UNION ALL
SELECT 
  'Attorneys with current_assistant_id' as metric,
  COUNT(*)::text as value
FROM attorneys 
WHERE current_assistant_id IS NOT NULL
UNION ALL
SELECT 
  'Total assistant configs' as metric,
  COUNT(*)::text as value
FROM assistant_ui_configs
UNION ALL
SELECT 
  'Total assistant subdomains' as metric,
  COUNT(*)::text as value
FROM assistant_subdomains
UNION ALL
SELECT 
  'Consultations with assistant_id' as metric,
  CASE 
    WHEN EXISTS (
      SELECT 1 FROM information_schema.columns 
      WHERE table_name = 'consultations' AND column_name = 'assistant_id'
    ) THEN (
      SELECT COUNT(*)::text FROM consultations WHERE assistant_id IS NOT NULL
    )
    ELSE 'Column does not exist'
  END as value
FROM (SELECT 1) as dummy;

-- =============================================================================
-- 7. RECOMMENDED ACTIONS
-- =============================================================================

-- This query shows what actions should be taken based on the findings
SELECT 
  CASE 
    WHEN EXISTS (
      SELECT 1 FROM attorneys WHERE vapi_assistant_id = id OR current_assistant_id = id
    ) THEN 'CRITICAL: Run fix-assistant-id-corruption.js script'
    ELSE 'OK: No attorney IDs used as assistant IDs'
  END as attorney_id_corruption,
  
  CASE 
    WHEN EXISTS (
      SELECT 1 FROM information_schema.columns 
      WHERE table_name = 'consultations' AND column_name = 'assistant_id'
    ) THEN 'OK: consultations.assistant_id column exists'
    ELSE 'ACTION NEEDED: Run add-assistant-id-to-consultations.sql'
  END as consultations_schema,
  
  CASE 
    WHEN EXISTS (
      SELECT 1 FROM assistant_ui_configs 
      WHERE assistant_id = '87756a2c-a398-43f2-889a-b8815684df71'
    ) THEN 'CRITICAL: Problematic attorney ID found in assistant configs'
    ELSE 'OK: No problematic attorney ID in assistant configs'
  END as specific_corruption_check;

-- =============================================================================
-- 8. QUICK FIX VERIFICATION
-- =============================================================================

-- After running fixes, use this query to verify everything is clean
SELECT 
  COUNT(*) as corruption_count,
  CASE 
    WHEN COUNT(*) = 0 THEN '✅ All clean - no corruption detected'
    ELSE '❌ Corruption still exists - check individual queries above'
  END as status
FROM (
  -- Check for attorney IDs used as assistant IDs
  SELECT id FROM attorneys WHERE vapi_assistant_id = id OR current_assistant_id = id
  UNION ALL
  -- Check for specific problematic ID
  SELECT assistant_id::text as id FROM assistant_ui_configs 
  WHERE assistant_id = '87756a2c-a398-43f2-889a-b8815684df71'::uuid
  UNION ALL
  SELECT assistant_id::text as id FROM assistant_subdomains 
  WHERE assistant_id = '87756a2c-a398-43f2-889a-b8815684df71'::uuid
) as corruption_check;

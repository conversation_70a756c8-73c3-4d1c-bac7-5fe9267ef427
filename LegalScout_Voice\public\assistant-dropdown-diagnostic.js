/**
 * Assistant Dropdown Diagnostic Script
 * 
 * This script diagnoses issues with the assistant dropdown not showing the correct assistant ID.
 * It checks all the key components and data flow.
 */

(function() {
  'use strict';

  console.log('🔍 [AssistantDropdownDiagnostic] Starting diagnostic...');

  // Function to check assistant dropdown state
  function checkAssistantDropdown() {
    const dropdown = document.querySelector('select[id*="assistant"], select.assistant-select');
    
    if (!dropdown) {
      console.error('❌ [AssistantDropdownDiagnostic] Assistant dropdown not found');
      return null;
    }

    console.log('✅ [AssistantDropdownDiagnostic] Assistant dropdown found:', dropdown);
    console.log('📋 [AssistantDropdownDiagnostic] Dropdown value:', dropdown.value);
    console.log('📋 [AssistantDropdownDiagnostic] Dropdown options:', Array.from(dropdown.options).map(opt => ({
      value: opt.value,
      text: opt.text
    })));

    return dropdown;
  }

  // Function to check attorney data
  function checkAttorneyData() {
    console.log('🔍 [AssistantDropdownDiagnostic] Checking attorney data sources...');

    // Check localStorage
    try {
      const storedAttorney = localStorage.getItem('attorney');
      if (storedAttorney) {
        const attorney = JSON.parse(storedAttorney);
        console.log('✅ [AssistantDropdownDiagnostic] Attorney in localStorage:', {
          id: attorney.id,
          firm_name: attorney.firm_name,
          vapi_assistant_id: attorney.vapi_assistant_id
        });
      } else {
        console.warn('⚠️ [AssistantDropdownDiagnostic] No attorney in localStorage');
      }
    } catch (error) {
      console.error('❌ [AssistantDropdownDiagnostic] Error reading localStorage:', error);
    }

    // Check window.currentAttorneyState
    if (window.currentAttorneyState) {
      console.log('✅ [AssistantDropdownDiagnostic] Window attorney state:', window.currentAttorneyState);
    } else {
      console.warn('⚠️ [AssistantDropdownDiagnostic] No window.currentAttorneyState');
    }

    // Check if StandaloneAttorneyManager exists
    if (window.StandaloneAttorneyManager) {
      console.log('✅ [AssistantDropdownDiagnostic] StandaloneAttorneyManager found');
      try {
        const manager = window.StandaloneAttorneyManager;
        if (manager.attorney) {
          console.log('✅ [AssistantDropdownDiagnostic] Manager attorney:', {
            id: manager.attorney.id,
            firm_name: manager.attorney.firm_name,
            vapi_assistant_id: manager.attorney.vapi_assistant_id
          });
        } else {
          console.warn('⚠️ [AssistantDropdownDiagnostic] Manager has no attorney');
        }
      } catch (error) {
        console.error('❌ [AssistantDropdownDiagnostic] Error accessing manager:', error);
      }
    } else {
      console.warn('⚠️ [AssistantDropdownDiagnostic] No StandaloneAttorneyManager');
    }
  }

  // Function to check Supabase data
  async function checkSupabaseData() {
    console.log('🔍 [AssistantDropdownDiagnostic] Checking Supabase data...');

    if (!window.supabase) {
      console.error('❌ [AssistantDropdownDiagnostic] Supabase not available');
      return;
    }

    try {
      // Check attorney_assistants table
      const { data: assistantMappings, error } = await window.supabase
        .from('attorney_assistants')
        .select('*')
        .limit(10);

      if (error) {
        console.error('❌ [AssistantDropdownDiagnostic] Supabase query error:', error);
      } else {
        console.log('✅ [AssistantDropdownDiagnostic] Assistant mappings:', assistantMappings);
      }
    } catch (error) {
      console.error('❌ [AssistantDropdownDiagnostic] Supabase exception:', error);
    }
  }

  // Function to test Vapi API
  async function checkVapiAPI() {
    console.log('🔍 [AssistantDropdownDiagnostic] Testing Vapi API...');

    const assistantId = 'f9b97d13-f9c4-40af-a660-62ba5925ff2a';
    
    try {
      const response = await fetch(`/api/vapi-proxy/assistant/${assistantId}`);
      
      if (response.ok) {
        const data = await response.json();
        console.log('✅ [AssistantDropdownDiagnostic] Vapi API working:', {
          id: data.id,
          name: data.name,
          status: response.status
        });
      } else {
        console.error('❌ [AssistantDropdownDiagnostic] Vapi API error:', response.status, response.statusText);
      }
    } catch (error) {
      console.error('❌ [AssistantDropdownDiagnostic] Vapi API exception:', error);
    }
  }

  // Function to run all diagnostics
  async function runDiagnostics() {
    console.log('🚀 [AssistantDropdownDiagnostic] Running full diagnostic suite...');
    
    checkAttorneyData();
    checkAssistantDropdown();
    await checkSupabaseData();
    await checkVapiAPI();
    
    console.log('✅ [AssistantDropdownDiagnostic] Diagnostic complete');
  }

  // Function to fix the dropdown manually
  function fixDropdown() {
    console.log('🔧 [AssistantDropdownDiagnostic] Attempting to fix dropdown...');
    
    const dropdown = checkAssistantDropdown();
    if (!dropdown) return;

    const expectedAssistantId = 'f9b97d13-f9c4-40af-a660-62ba5925ff2a';
    
    // Check if the option exists
    const option = Array.from(dropdown.options).find(opt => opt.value === expectedAssistantId);
    
    if (option) {
      console.log('✅ [AssistantDropdownDiagnostic] Option exists, setting value');
      dropdown.value = expectedAssistantId;
      dropdown.dispatchEvent(new Event('change', { bubbles: true }));
    } else {
      console.log('🔧 [AssistantDropdownDiagnostic] Option missing, adding it');
      const newOption = document.createElement('option');
      newOption.value = expectedAssistantId;
      newOption.text = 'LegalScout Assistant';
      dropdown.appendChild(newOption);
      dropdown.value = expectedAssistantId;
      dropdown.dispatchEvent(new Event('change', { bubbles: true }));
    }
  }

  // Expose functions globally for manual testing
  window.assistantDropdownDiagnostic = {
    runDiagnostics,
    checkAssistantDropdown,
    checkAttorneyData,
    checkSupabaseData,
    checkVapiAPI,
    fixDropdown
  };

  // Auto-run diagnostics when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      setTimeout(runDiagnostics, 2000); // Wait for other scripts to load
    });
  } else {
    setTimeout(runDiagnostics, 2000);
  }

  console.log('✅ [AssistantDropdownDiagnostic] Diagnostic script loaded. Use window.assistantDropdownDiagnostic.runDiagnostics() to run manually.');

})();

#!/usr/bin/env node

/**
 * Test Dropdown Error Handling
 * Verifies that the assistant dropdown handles undefined/invalid data gracefully
 */

console.log('🧪 Testing Dropdown Error Handling');
console.log('==================================');

// Test the helper functions that were causing the error
function testGetAssistantImageUrl() {
  console.log('\n🔍 Test 1: getAssistantImageUrl with various inputs');
  console.log('==================================================');

  const getDefaultAvatarImage = (initials) => {
    try {
      const svgContent = `
        <svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
          <circle cx="16" cy="16" r="16" fill="#4B74AA"/>
          <text x="16" y="20" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">${initials}</text>
        </svg>
      `;
      return `data:image/svg+xml;base64,${Buffer.from(svgContent).toString('base64')}`;
    } catch (error) {
      console.error('Error generating default avatar:', error);
      return `data:image/svg+xml;base64,${Buffer.from(`
        <svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
          <circle cx="16" cy="16" r="16" fill="#4B74AA"/>
        </svg>
      `).toString('base64')}`;
    }
  };

  const getAssistantImageUrl = (assistant) => {
    // Safety check for assistant object
    if (!assistant) {
      return getDefaultAvatarImage('AS');
    }

    // Check if assistant has a custom image in config
    if (assistant.configData?.assistant_image_url) {
      return assistant.configData.assistant_image_url;
    }
    
    // Generate a default avatar based on assistant name/ID
    const name = assistant.name || 'Assistant';
    const initials = name.split(' ').map(word => word[0]).join('').toUpperCase().slice(0, 2);
    
    return getDefaultAvatarImage(initials);
  };

  const testCases = [
    { name: 'undefined assistant', input: undefined, expected: 'AS' },
    { name: 'null assistant', input: null, expected: 'AS' },
    { name: 'empty object', input: {}, expected: 'AS' },
    { name: 'assistant with name', input: { name: 'Damon Legal' }, expected: 'DL' },
    { name: 'assistant with custom image', input: { 
      name: 'Test Assistant',
      configData: { assistant_image_url: 'custom-image-url' }
    }, expected: 'custom-image-url' },
    { name: 'assistant with no name', input: { id: 'test-123' }, expected: 'AS' }
  ];

  let allTestsPassed = true;

  testCases.forEach(testCase => {
    console.log(`\n👤 Testing: ${testCase.name}`);
    
    try {
      const result = getAssistantImageUrl(testCase.input);
      
      if (testCase.expected === 'custom-image-url') {
        if (result === 'custom-image-url') {
          console.log('   ✅ Custom image URL returned correctly');
        } else {
          console.log('   ❌ Custom image URL not returned');
          allTestsPassed = false;
        }
      } else {
        // Check if it's a data URI with expected initials
        if (result.startsWith('data:image/svg+xml;base64,')) {
          const svgContent = Buffer.from(result.split('base64,')[1], 'base64').toString();
          const initialsMatch = svgContent.match(/>(.*?)<\/text>/);
          const actualInitials = initialsMatch ? initialsMatch[1] : 'NOT_FOUND';
          
          console.log(`   📊 Expected initials: ${testCase.expected}`);
          console.log(`   📊 Actual initials: ${actualInitials}`);
          
          if (actualInitials === testCase.expected) {
            console.log('   ✅ Image generation correct');
          } else {
            console.log('   ❌ Image generation failed');
            allTestsPassed = false;
          }
        } else {
          console.log('   ❌ Invalid data URI format');
          allTestsPassed = false;
        }
      }
    } catch (error) {
      console.log(`   ❌ Error: ${error.message}`);
      allTestsPassed = false;
    }
  });

  return allTestsPassed;
}

function testGetAssistantDisplayName() {
  console.log('\n🔍 Test 2: getAssistantDisplayName with various inputs');
  console.log('====================================================');

  const getAssistantDisplayName = (assistant) => {
    if (!assistant) return 'Unknown Assistant';
    
    const name = assistant.name || `Assistant ${assistant.id?.slice(0, 8) || 'Unknown'}`;
    const toolCount = assistant.toolIds?.length || 0;
    const hasConfig = assistant.hasConfig;
    
    let suffix = '';
    if (toolCount > 0) suffix += ` (${toolCount} tools)`;
    if (hasConfig) suffix += ' ✓';
    
    return name + suffix;
  };

  const testCases = [
    { name: 'undefined assistant', input: undefined, expected: 'Unknown Assistant' },
    { name: 'null assistant', input: null, expected: 'Unknown Assistant' },
    { name: 'empty object', input: {}, expected: 'Assistant Unknown' },
    { name: 'assistant with name', input: { name: 'Damon Legal' }, expected: 'Damon Legal' },
    { name: 'assistant with name and tools', input: { 
      name: 'Test Assistant',
      toolIds: ['tool1', 'tool2']
    }, expected: 'Test Assistant (2 tools)' },
    { name: 'assistant with config', input: { 
      name: 'Config Assistant',
      hasConfig: true
    }, expected: 'Config Assistant ✓' },
    { name: 'assistant with everything', input: { 
      name: 'Full Assistant',
      toolIds: ['tool1'],
      hasConfig: true
    }, expected: 'Full Assistant (1 tools) ✓' }
  ];

  let allTestsPassed = true;

  testCases.forEach(testCase => {
    console.log(`\n👤 Testing: ${testCase.name}`);
    
    try {
      const result = getAssistantDisplayName(testCase.input);
      
      console.log(`   📊 Expected: "${testCase.expected}"`);
      console.log(`   📊 Actual: "${result}"`);
      
      if (result === testCase.expected) {
        console.log('   ✅ Display name correct');
      } else {
        console.log('   ❌ Display name mismatch');
        allTestsPassed = false;
      }
    } catch (error) {
      console.log(`   ❌ Error: ${error.message}`);
      allTestsPassed = false;
    }
  });

  return allTestsPassed;
}

function testAssistantFiltering() {
  console.log('\n🔍 Test 3: Assistant Filtering with invalid data');
  console.log('===============================================');

  // Simulate the filtering logic from the component
  const mockAssistants = [
    { id: 'valid-1', name: 'Valid Assistant 1' },
    null, // Invalid
    undefined, // Invalid
    { name: 'No ID Assistant' }, // Invalid - no ID
    { id: 'valid-2', name: 'Valid Assistant 2' },
    { id: '', name: 'Empty ID' }, // Invalid - empty ID
    { id: 'valid-3', name: 'Valid Assistant 3' }
  ];

  const assignedIds = ['valid-1', 'valid-2', 'valid-3'];

  console.log('\n📋 Original assistants:', mockAssistants.length);
  console.log('📋 Assigned IDs:', assignedIds);

  // Apply the filtering logic from the component
  const relevantAssistants = mockAssistants.filter(assistant =>
    assistant && assistant.id && assignedIds.includes(assistant.id)
  );

  console.log('\n✅ Filtered assistants:', relevantAssistants.length);
  console.log('📋 Valid assistants:');
  relevantAssistants.forEach(assistant => {
    console.log(`   🤖 ${assistant.name} (${assistant.id})`);
  });

  const expectedCount = 3;
  if (relevantAssistants.length === expectedCount) {
    console.log(`\n✅ Filtering correct: ${relevantAssistants.length}/${expectedCount} valid assistants`);
    return true;
  } else {
    console.log(`\n❌ Filtering failed: expected ${expectedCount}, got ${relevantAssistants.length}`);
    return false;
  }
}

async function runAllTests() {
  console.log('🚀 Starting Dropdown Error Handling Tests...\n');

  const results = {
    imageUrl: false,
    displayName: false,
    filtering: false
  };

  // Test 1: Image URL generation
  results.imageUrl = testGetAssistantImageUrl();

  // Test 2: Display name generation
  results.displayName = testGetAssistantDisplayName();

  // Test 3: Assistant filtering
  results.filtering = testAssistantFiltering();

  // Summary
  console.log('\n📊 Test Results Summary');
  console.log('======================');

  const tests = [
    { name: 'Image URL Generation', result: results.imageUrl },
    { name: 'Display Name Generation', result: results.displayName },
    { name: 'Assistant Filtering', result: results.filtering }
  ];

  tests.forEach(test => {
    const status = test.result ? '✅ PASS' : '❌ FAIL';
    console.log(`${status} ${test.name}`);
  });

  const passCount = tests.filter(t => t.result).length;
  console.log(`\n🎯 Overall: ${passCount}/${tests.length} tests passed`);

  if (passCount === tests.length) {
    console.log('🎉 All tests passed! Error handling is working correctly.');
    console.log('\n💡 The dropdown should now handle:');
    console.log('- Undefined/null assistants gracefully');
    console.log('- Missing configData properties');
    console.log('- Invalid assistant objects');
    console.log('- Network errors during config loading');
  } else {
    console.log('⚠️  Some tests failed. Check the implementation.');
  }

  return passCount === tests.length;
}

// Run the tests
runAllTests().then(success => {
  process.exit(success ? 0 : 1);
}).catch(error => {
  console.error('❌ Test runner failed:', error);
  process.exit(1);
});

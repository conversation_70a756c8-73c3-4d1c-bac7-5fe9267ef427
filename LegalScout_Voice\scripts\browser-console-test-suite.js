/**
 * <PERSON><PERSON>er Console Test Suite
 * 
 * Copy and paste this entire function into your browser console while the app is running.
 * It will test the assistant context validation system with real app data.
 * 
 * Usage: Paste this code, then run: runAssistantValidationTests()
 */

window.runAssistantValidationTests = function() {
  console.log('🧪 Starting Browser Console Test Suite for Assistant Validation\n');
  
  const results = {
    passed: 0,
    failed: 0,
    tests: []
  };
  
  function test(name, testFn) {
    try {
      const result = testFn();
      if (result.success) {
        console.log(`✅ ${name}: PASS - ${result.message}`);
        results.passed++;
      } else {
        console.log(`❌ ${name}: FAIL - ${result.message}`);
        results.failed++;
      }
      results.tests.push({ name, success: result.success, message: result.message });
    } catch (error) {
      console.log(`❌ ${name}: ERROR - ${error.message}`);
      results.failed++;
      results.tests.push({ name, success: false, message: error.message });
    }
  }
  
  // Test 1: Check if validation system is loaded
  test('Validation System Loaded', () => {
    const hasValidator = window.AssistantContextValidator || 
                        (window.React && window.React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED);
    return {
      success: !!hasValidator,
      message: hasValidator ? 'Validation system is available' : 'Validation system not found'
    };
  });
  
  // Test 2: Check current app state
  test('App State Check', () => {
    const hasReact = !!window.React;
    const hasLocalStorage = !!window.localStorage;
    const hasAttorneyData = !!localStorage.getItem('attorney');
    
    return {
      success: hasReact && hasLocalStorage,
      message: `React: ${hasReact}, localStorage: ${hasLocalStorage}, attorney data: ${hasAttorneyData}`
    };
  });
  
  // Test 3: Check for attorney ID in localStorage
  test('localStorage Attorney Data', () => {
    const attorneyData = localStorage.getItem('attorney');
    if (!attorneyData) {
      return { success: true, message: 'No attorney data in localStorage' };
    }
    
    try {
      const attorney = JSON.parse(attorneyData);
      const problematicId = '87756a2c-a398-43f2-889a-b8815684df71';
      const hasCorruption = attorney.vapi_assistant_id === attorney.id || 
                           attorney.current_assistant_id === attorney.id ||
                           attorney.vapi_assistant_id === problematicId ||
                           attorney.current_assistant_id === problematicId;
      
      return {
        success: !hasCorruption,
        message: hasCorruption ? 
          `Corruption detected: vapi_assistant_id=${attorney.vapi_assistant_id}, current_assistant_id=${attorney.current_assistant_id}` :
          'localStorage attorney data is clean'
      };
    } catch (e) {
      return { success: false, message: 'Could not parse attorney data' };
    }
  });
  
  // Test 4: Monitor console logs for attorney ID
  test('Console Log Monitoring Setup', () => {
    const problematicId = '87756a2c-a398-43f2-889a-b8815684df71';
    let detectedCorruption = false;
    
    // Override console.log temporarily to monitor for corruption
    const originalLog = console.log;
    const monitoringLog = function(...args) {
      const message = args.join(' ');
      if (message.includes(problematicId) && message.includes('currentAssistant')) {
        detectedCorruption = true;
        console.warn('🚨 CORRUPTION DETECTED IN LOGS:', message);
      }
      return originalLog.apply(console, args);
    };
    
    // Set up monitoring
    console.log = monitoringLog;
    
    // Restore after a short delay
    setTimeout(() => {
      console.log = originalLog;
    }, 5000);
    
    return {
      success: true,
      message: 'Console monitoring active for 5 seconds'
    };
  });
  
  // Test 5: Test validation logic (if available)
  test('Validation Logic Test', () => {
    // Try to access the validation system through various paths
    let validator = null;
    
    // Check if it's available globally
    if (window.AssistantContextValidator) {
      validator = window.AssistantContextValidator;
    }
    
    if (!validator) {
      return { success: false, message: 'Validation system not accessible' };
    }
    
    const problematicId = '87756a2c-a398-43f2-889a-b8815684df71';
    const validId = 'cd0b44b7-397e-410d-8835-ce9c3ba584b2';
    
    try {
      const invalidResult = validator.validateAssistantId(problematicId, problematicId);
      const validResult = validator.validateAssistantId(validId, problematicId);
      
      const success = !invalidResult.valid && validResult.valid;
      return {
        success,
        message: success ? 
          'Validation correctly rejects attorney ID and accepts valid ID' :
          `Validation failed: invalid=${invalidResult.valid}, valid=${validResult.valid}`
      };
    } catch (e) {
      return { success: false, message: `Validation test error: ${e.message}` };
    }
  });
  
  // Test 6: Check React component state (if accessible)
  test('React Component State', () => {
    try {
      // Try to access React DevTools
      if (window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
        const hook = window.__REACT_DEVTOOLS_GLOBAL_HOOK__;
        const problematicId = '87756a2c-a398-43f2-889a-b8815684df71';
        
        // This is a basic check - in a real scenario you'd need to traverse the React tree
        return {
          success: true,
          message: 'React DevTools available - manual inspection recommended'
        };
      } else {
        return {
          success: false,
          message: 'React DevTools not available'
        };
      }
    } catch (e) {
      return { success: false, message: `React state check error: ${e.message}` };
    }
  });
  
  // Test 7: Network request monitoring
  test('Network Request Monitoring', () => {
    const problematicId = '87756a2c-a398-43f2-889a-b8815684df71';
    let corruptionDetected = false;
    
    // Monitor fetch requests
    const originalFetch = window.fetch;
    window.fetch = function(...args) {
      const url = args[0];
      const options = args[1];
      
      if (url && url.includes && url.includes(problematicId)) {
        corruptionDetected = true;
        console.warn('🚨 CORRUPTION DETECTED IN NETWORK REQUEST:', url);
      }
      
      if (options && options.body) {
        const body = typeof options.body === 'string' ? options.body : JSON.stringify(options.body);
        if (body.includes(problematicId)) {
          corruptionDetected = true;
          console.warn('🚨 CORRUPTION DETECTED IN REQUEST BODY:', body);
        }
      }
      
      return originalFetch.apply(window, args);
    };
    
    // Restore after monitoring period
    setTimeout(() => {
      window.fetch = originalFetch;
    }, 10000);
    
    return {
      success: true,
      message: 'Network monitoring active for 10 seconds'
    };
  });
  
  // Test 8: URL and routing check
  test('URL and Routing Check', () => {
    const problematicId = '87756a2c-a398-43f2-889a-b8815684df71';
    const currentUrl = window.location.href;
    const hasCorruption = currentUrl.includes(problematicId);
    
    return {
      success: !hasCorruption,
      message: hasCorruption ? 
        `Attorney ID found in URL: ${currentUrl}` :
        'URL is clean'
    };
  });
  
  // Summary
  console.log('\n📊 Test Results Summary:');
  console.log(`✅ Passed: ${results.passed}`);
  console.log(`❌ Failed: ${results.failed}`);
  console.log(`📈 Success Rate: ${Math.round((results.passed / (results.passed + results.failed)) * 100)}%`);
  
  if (results.failed > 0) {
    console.log('\n🔧 Failed Tests:');
    results.tests.filter(t => !t.success).forEach(test => {
      console.log(`  - ${test.name}: ${test.message}`);
    });
  }
  
  console.log('\n📝 Recommendations:');
  if (results.failed === 0) {
    console.log('  🎉 All tests passed! The validation system appears to be working correctly.');
  } else {
    console.log('  🔧 Some tests failed. Check the failed tests above for specific issues.');
    console.log('  📋 Run the database integrity check to verify backend data.');
    console.log('  🔄 Try refreshing the page and running tests again.');
  }
  
  return results;
};

// Auto-run instructions
console.log('🧪 Browser Console Test Suite Loaded!');
console.log('📋 To run tests, execute: runAssistantValidationTests()');
console.log('⚡ Tests will monitor for corruption in real-time');
console.log('📊 Results will show pass/fail status and recommendations');

#!/usr/bin/env node
/**
 * Register AI Meta MCP Tools
 *
 * This script registers the synchronization tools with the AI Meta MCP Server.
 *
 * Usage:
 *   node scripts/register-ai-meta-mcp-tools.js
 */

import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { SSEClientTransport } from '@modelcontextprotocol/sdk/client/sse.js';
import {
  syncAttorneyProfile,
  manageAuthState,
  validateConfiguration,
  checkPreviewConsistency
} from '../src/services/syncTools.js';

// Function to register a tool with the AI Meta MCP Server
const registerTool = async (mcpClient, name, description, parametersSchema, implementationCode) => {
  try {
    console.log(`Registering tool: ${name}`);

    const result = await mcpClient.callTool({
      name: 'define_function',
      arguments: {
        name,
        description,
        parameters_schema: parametersSchema,
        implementation_code: implementationCode.toString(),
        execution_environment: 'javascript'
      },
    });

    console.log(`Successfully registered tool: ${name}`);
    return result;
  } catch (error) {
    console.error(`Error registering tool ${name}:`, error);
    throw error;
  }
};

// Main function to register all tools
const registerAllTools = async () => {
  try {
    console.log('Connecting to AI Meta MCP Server...');

    // Get AI Meta MCP client
    const mcpClient = new Client({
      name: 'legalscout-tool-registrar',
      version: '1.0.0',
    });

    // Connect to the AI Meta MCP server
    const transport = new SSEClientTransport({
      url: 'http://localhost:8080/sse', // Adjust port if needed
    });

    await mcpClient.connect(transport);
    console.log('Connected to AI Meta MCP Server');

    // Register all tools
    await registerTool(
      mcpClient,
      'sync_attorney_profile',
      'Synchronize attorney profile data between Supabase and Vapi',
      {
        attorneyId: {
          type: 'string',
          description: 'ID of the attorney to synchronize'
        },
        forceUpdate: {
          type: 'boolean',
          description: 'Force update even if no discrepancies are found',
          default: false
        }
      },
      syncAttorneyProfile
    );

    await registerTool(
      mcpClient,
      'manage_auth_state',
      'Manage authentication state across systems',
      {
        authData: {
          type: 'object',
          description: 'Authentication data'
        },
        action: {
          type: 'string',
          description: 'Authentication action (login, logout, refresh)',
          enum: ['login', 'logout', 'refresh']
        }
      },
      manageAuthState
    );

    await registerTool(
      mcpClient,
      'validate_configuration',
      'Validate configuration before updates',
      {
        attorneyId: {
          type: 'string',
          description: 'ID of the attorney'
        },
        configData: {
          type: 'object',
          description: 'Configuration data to validate'
        }
      },
      validateConfiguration
    );

    await registerTool(
      mcpClient,
      'check_preview_consistency',
      'Ensure preview matches deployment',
      {
        attorneyId: {
          type: 'string',
          description: 'ID of the attorney'
        }
      },
      checkPreviewConsistency
    );

    // List all registered functions
    const listResult = await mcpClient.callTool({
      name: 'list_functions',
      arguments: {},
    });

    console.log('Registered functions:');
    if (listResult.content && listResult.content[0]) {
      console.log(listResult.content[0].text);
    } else {
      console.log('No functions registered or unable to retrieve list');
    }

    // Disconnect from the MCP server
    await mcpClient.disconnect();
    console.log('Disconnected from AI Meta MCP Server');

    console.log('All tools registered successfully');
  } catch (error) {
    console.error('Error registering tools:', error);
    process.exit(1);
  }
};

// Run the registration
registerAllTools();

/**
 * Fix for ProfileTab component
 * 
 * This script patches the ProfileTab component to handle missing attorney ID
 * more gracefully and bypass Supabase RLS errors.
 */

(function() {
  console.log('[ProfileTabFix] Starting ProfileTab fix');
  
  // Wait for React to be available
  const waitForReact = setInterval(() => {
    if (window.React) {
      clearInterval(waitForReact);
      applyFix();
    }
  }, 100);
  
  function applyFix() {
    console.log('[ProfileTabFix] React found, applying fix');
    
    // Monitor for ProfileTab component rendering
    const originalCreateElement = React.createElement;
    React.createElement = function(type, props, ...children) {
      // Check if this is the ProfileTab component
      if (type && typeof type === 'function' && type.name === 'ProfileTab') {
        console.log('[ProfileTabFix] ProfileTab component detected, applying patch');
        
        // Wrap the original component
        const WrappedProfileTab = function(props) {
          // Add error handling
          try {
            return type(props);
          } catch (error) {
            console.error('[ProfileTabFix] Error in ProfileTab:', error);
            
            // Return a simplified version of the component
            return originalCreateElement('div', { className: 'profile-tab' },
              originalCreateElement('h2', null, 'Profile Information'),
              originalCreateElement('p', { className: 'tab-description' }, 
                'Update your profile information to customize your LegalScout experience.'),
              originalCreateElement('div', { className: 'alert alert-info' },
                'Development mode active. Profile updates will be stored locally.')
            );
          }
        };
        
        // Copy properties from original component
        WrappedProfileTab.displayName = 'ProfileTab';
        
        // Use the wrapped component instead
        return originalCreateElement(WrappedProfileTab, props, ...children);
      }
      
      // Otherwise, use the original createElement
      return originalCreateElement(type, props, ...children);
    };
    
    console.log('[ProfileTabFix] ProfileTab fix applied');
  }
})();

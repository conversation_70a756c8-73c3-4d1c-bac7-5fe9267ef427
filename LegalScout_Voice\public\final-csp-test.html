<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Final CSP Test - LegalScout</title>
  
  <!-- NO CSP meta tag - relying on server headers only -->
  
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 20px;
      background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
      color: #333;
      min-height: 100vh;
    }
    .container {
      max-width: 800px;
      margin: 0 auto;
      background: white;
      padding: 30px;
      border-radius: 12px;
      box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    }
    .header {
      text-align: center;
      margin-bottom: 30px;
      color: #e74c3c;
    }
    .test-result {
      margin: 15px 0;
      padding: 15px;
      border-radius: 8px;
      font-family: 'Courier New', monospace;
      font-size: 0.9em;
    }
    .success { background: #d4edda; border: 2px solid #27ae60; color: #155724; }
    .error { background: #f8d7da; border: 2px solid #e74c3c; color: #721c24; }
    .info { background: #d1ecf1; border: 2px solid #3498db; color: #0c5460; }
    .warning { background: #fff3cd; border: 2px solid #f39c12; color: #856404; }
    
    button {
      background: #e74c3c;
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 6px;
      cursor: pointer;
      margin: 10px 5px;
      font-size: 1em;
      transition: background 0.3s;
    }
    button:hover { background: #c0392b; }
    
    .status-banner {
      background: #f8f9fa;
      border: 2px solid #dee2e6;
      border-radius: 8px;
      padding: 20px;
      margin: 20px 0;
      text-align: center;
    }
    
    .status-banner.success {
      background: #d4edda;
      border-color: #27ae60;
      color: #155724;
    }
    
    .status-banner.error {
      background: #f8d7da;
      border-color: #e74c3c;
      color: #721c24;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>🔥 Final CSP Test</h1>
      <p>Testing CSP with server headers only (no meta tag conflicts)</p>
    </div>
    
    <div class="status-banner" id="status-banner">
      <h3>🔍 Testing in Progress...</h3>
      <p>Checking if CSP allows eval() usage</p>
    </div>
    
    <div style="text-align: center; margin: 20px 0;">
      <button onclick="runFinalTest()">🚀 Run Final Test</button>
      <button onclick="checkHeaders()">📋 Check Headers</button>
      <button onclick="testVapi()">📞 Test Vapi</button>
      <button onclick="clearResults()">🧹 Clear</button>
    </div>
    
    <div id="results"></div>
  </div>

  <script>
    let violationCount = 0;
    let testsPassed = 0;
    let testsTotal = 0;

    // CSP Violation Listener
    document.addEventListener('securitypolicyviolation', (e) => {
      violationCount++;
      console.error('🚨 CSP Violation:', e);
      
      addResult('error', `🚨 CSP Violation #${violationCount}`, `
        STILL BLOCKED: ${e.violatedDirective}
        URI: ${e.blockedURI}
        Source: ${e.sourceFile}:${e.lineNumber}
        
        This means the CSP fix is not working yet.
      `);
      
      updateStatusBanner();
    });

    function addResult(type, title, message) {
      const results = document.getElementById('results');
      const div = document.createElement('div');
      div.className = `test-result ${type}`;
      
      const icon = type === 'success' ? '✅' : 
                   type === 'error' ? '❌' : 
                   type === 'warning' ? '⚠️' : 'ℹ️';
      
      div.innerHTML = `
        <strong>${icon} ${title}</strong>
        <div style="margin-top: 10px;">${message}</div>
      `;
      results.appendChild(div);
    }

    function updateStatusBanner() {
      const banner = document.getElementById('status-banner');
      
      if (violationCount > 0) {
        banner.className = 'status-banner error';
        banner.innerHTML = `
          <h3>❌ CSP Fix Failed</h3>
          <p>eval() is still being blocked by CSP (${violationCount} violations)</p>
          <p><strong>The server headers are not working correctly</strong></p>
        `;
      } else if (testsPassed > 0) {
        banner.className = 'status-banner success';
        banner.innerHTML = `
          <h3>✅ CSP Fix Successful!</h3>
          <p>eval() is now allowed - no CSP violations detected</p>
          <p><strong>The server headers are working correctly</strong></p>
        `;
      }
    }

    async function runFinalTest() {
      addResult('info', '🚀 Starting Final CSP Test', 'Testing if eval() works without CSP violations...');
      
      testsTotal++;
      
      try {
        // Test eval()
        const result = eval('2 + 2');
        
        // If we get here without a CSP violation, it worked
        setTimeout(() => {
          if (violationCount === 0) {
            addResult('success', '✅ eval() Test PASSED', `
              SUCCESS: eval() executed without CSP violations!
              Result: ${result}
              
              This confirms the CSP fix is working correctly.
            `);
            testsPassed++;
            updateStatusBanner();
          }
        }, 500); // Wait a bit for any violations to be reported
        
      } catch (error) {
        addResult('error', '❌ eval() Test FAILED', `
          ERROR: eval() failed to execute
          Error: ${error.message}
          
          This indicates a JavaScript error, not necessarily CSP.
        `);
      }
    }

    async function checkHeaders() {
      addResult('info', '📋 Checking Server Headers', 'Fetching CSP headers from server...');
      
      try {
        const response = await fetch(window.location.href, {
          method: 'HEAD'
        });
        
        const cspHeader = response.headers.get('content-security-policy');
        const cspReportHeader = response.headers.get('content-security-policy-report-only');
        
        if (cspHeader) {
          const allowsEval = cspHeader.includes('unsafe-eval');
          addResult(allowsEval ? 'success' : 'error', 'CSP Header Analysis', `
            CSP Header Found: YES
            Allows unsafe-eval: ${allowsEval ? 'YES' : 'NO'}
            
            Full Policy: ${cspHeader}
          `);
        } else {
          addResult('warning', 'CSP Header Analysis', `
            CSP Header: NOT FOUND
            CSP Report-Only: ${cspReportHeader || 'NOT FOUND'}
            
            If no CSP header is found, eval() should be allowed by default.
          `);
        }
        
      } catch (error) {
        addResult('error', 'Header Check Failed', `Error: ${error.message}`);
      }
    }

    async function testVapi() {
      addResult('info', '📞 Testing Vapi SDK', 'Loading Vapi SDK with 2025 method...');
      
      try {
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/gh/VapiAI/html-script-tag@latest/dist/assets/index.js';
        script.defer = true;
        script.async = true;
        
        const loadPromise = new Promise((resolve, reject) => {
          script.onload = () => {
            if (typeof window.vapiSDK !== 'undefined') {
              resolve('Vapi SDK loaded successfully');
            } else {
              reject(new Error('Vapi SDK not available'));
            }
          };
          script.onerror = () => reject(new Error('Vapi SDK failed to load'));
          setTimeout(() => reject(new Error('Vapi SDK timeout')), 15000);
        });
        
        document.head.appendChild(script);
        const result = await loadPromise;
        
        addResult('success', '✅ Vapi SDK Test PASSED', `
          ${result}
          
          Available: window.vapiSDK = ${typeof window.vapiSDK}
          Run method: window.vapiSDK.run = ${typeof window.vapiSDK?.run}
        `);
        
      } catch (error) {
        addResult('error', '❌ Vapi SDK Test FAILED', error.message);
      }
    }

    function clearResults() {
      document.getElementById('results').innerHTML = '';
      violationCount = 0;
      testsPassed = 0;
      testsTotal = 0;
      
      const banner = document.getElementById('status-banner');
      banner.className = 'status-banner';
      banner.innerHTML = `
        <h3>🔍 Ready to Test</h3>
        <p>Click "Run Final Test" to check if CSP allows eval()</p>
      `;
    }

    // Auto-run test on load
    document.addEventListener('DOMContentLoaded', function() {
      console.log('🔥 Final CSP Test loaded');
      
      addResult('info', 'Final CSP Test Ready', `
        This page has NO CSP meta tag to avoid conflicts.
        It relies entirely on server headers from vercel.json.
        
        If the fix worked, eval() should execute without violations.
      `);
      
      // Auto-run the test after a short delay
      setTimeout(() => {
        runFinalTest();
      }, 2000);
    });
  </script>
</body>
</html>

#!/usr/bin/env node
/**
 * Simple HTTP Server
 * 
 * This script creates a simple HTTP server to serve static files.
 * 
 * Usage:
 *   node scripts/simple-http-server.js [port]
 * 
 * Default port is 8080.
 */

import http from 'http';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Default port
const PORT = process.argv[2] || 8080;

// MIME types
const MIME_TYPES = {
  '.html': 'text/html',
  '.css': 'text/css',
  '.js': 'text/javascript',
  '.json': 'application/json',
  '.png': 'image/png',
  '.jpg': 'image/jpeg',
  '.gif': 'image/gif',
  '.svg': 'image/svg+xml',
  '.ico': 'image/x-icon',
  '.txt': 'text/plain',
};

// Create the server
const server = http.createServer((req, res) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
  
  // Parse URL
  let url = req.url;
  
  // Default to index.html for root path
  if (url === '/') {
    url = '/vapi-mcp-test.html';
  }
  
  // Resolve file path
  const filePath = path.join(__dirname, '..', 'public', url);
  
  // Get file extension
  const extname = path.extname(filePath);
  
  // Set content type
  const contentType = MIME_TYPES[extname] || 'application/octet-stream';
  
  // Read file
  fs.readFile(filePath, (err, content) => {
    if (err) {
      if (err.code === 'ENOENT') {
        // File not found
        fs.readFile(path.join(__dirname, '..', 'public', '404.html'), (err, content) => {
          res.writeHead(404, { 'Content-Type': 'text/html' });
          res.end(content || '404 Not Found', 'utf-8');
        });
      } else {
        // Server error
        res.writeHead(500);
        res.end(`Server Error: ${err.code}`);
      }
    } else {
      // Success
      res.writeHead(200, { 'Content-Type': contentType });
      res.end(content, 'utf-8');
    }
  });
});

// Start the server
server.listen(PORT, () => {
  console.log(`Server running at http://localhost:${PORT}/`);
  console.log(`Vapi MCP Test page: http://localhost:${PORT}/vapi-mcp-test.html`);
  console.log('Press Ctrl+C to stop the server');
});

/**
 * Auth Context Fix
 * 
 * This script specifically fixes issues with the AuthContext.jsx file
 * by ensuring React.createContext is available before the file is loaded.
 */

(function() {
  console.log('[AuthContextFix] Starting fix...');
  
  // Create a global React object if it doesn't exist
  if (typeof window.React === 'undefined') {
    window.React = {};
    console.log('[AuthContextFix] Created global React object');
  }
  
  // Ensure createContext is available
  if (typeof window.React.createContext === 'undefined') {
    window.React.createContext = function createContext(defaultValue) {
      console.log('[AuthContextFix] Creating context with default value:', defaultValue);
      
      const context = {
        Provider: function Provider(props) {
          context._currentValue = props.value !== undefined ? props.value : defaultValue;
          return props.children;
        },
        Consumer: function Consumer(props) {
          return props.children(context._currentValue);
        },
        _currentValue: defaultValue,
        $$typeof: Symbol.for('react.context'),
        _currentRenderer: null,
        _defaultValue: defaultValue
      };
      
      return context;
    };
    
    console.log('[AuthContextFix] Added createContext polyfill');
  }
  
  // Patch the import system to ensure AuthContext.jsx gets the correct React
  const originalImport = window.import;
  if (originalImport) {
    window.import = function(specifier) {
      console.log('[AuthContextFix] Import called for:', specifier);
      
      // If this is the AuthContext.jsx file, ensure React.createContext exists
      if (specifier.includes('AuthContext.jsx') || specifier.includes('contexts/AuthContext')) {
        console.log('[AuthContextFix] AuthContext import detected, ensuring React.createContext exists');
        
        if (typeof window.React.createContext === 'undefined') {
          window.React.createContext = function createContext(defaultValue) {
            const context = {
              Provider: function Provider(props) {
                context._currentValue = props.value !== undefined ? props.value : defaultValue;
                return props.children;
              },
              Consumer: function Consumer(props) {
                return props.children(context._currentValue);
              },
              _currentValue: defaultValue,
              $$typeof: Symbol.for('react.context'),
              _currentRenderer: null,
              _defaultValue: defaultValue
            };
            return context;
          };
          
          console.log('[AuthContextFix] Re-added createContext polyfill before AuthContext import');
        }
      }
      
      // Call the original import
      return originalImport.apply(this, arguments);
    };
  }
  
  // Create a global AuthContext as a fallback
  if (typeof window.AuthContext === 'undefined') {
    window.AuthContext = window.React.createContext(null);
    console.log('[AuthContextFix] Created global AuthContext fallback');
  }
  
  console.log('[AuthContextFix] Fix completed');
})();

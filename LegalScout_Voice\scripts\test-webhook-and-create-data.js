/**
 * Webhook Test and Data Creation Script
 * 
 * This script tests the webhook functionality and creates test consultation data
 * to verify the Briefs page is working correctly.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase configuration');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function testWebhookAndCreateData() {
  console.log('🧪 Testing Webhook and Creating Test Data...\n');

  try {
    // 1. Get your attorney records
    console.log('1️⃣ Getting Attorney Records:');
    const { data: attorneys, error: attorneyError } = await supabase
      .from('attorneys')
      .select('*')
      .in('email', ['<EMAIL>', '<EMAIL>']);

    if (attorneyError) {
      console.error('❌ Error fetching attorneys:', attorneyError);
      return;
    }

    console.log(`Found ${attorneys.length} attorney records:`);
    attorneys.forEach(attorney => {
      console.log(`  - ${attorney.name} (${attorney.email}) - Assistant: ${attorney.vapi_assistant_id}`);
    });

    // 2. Create test call records
    console.log('\n2️⃣ Creating Test Call Records:');
    
    for (const attorney of attorneys) {
      if (attorney.vapi_assistant_id) {
        const testCallRecord = {
          call_id: `test-call-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          assistant_id: attorney.vapi_assistant_id,
          attorney_id: attorney.id,
          customer_phone: '+1234567890',
          status: 'completed',
          duration: 300, // 5 minutes
          start_time: new Date(Date.now() - 300000).toISOString(), // 5 minutes ago
          end_time: new Date().toISOString(),
          transcripts: [
            {
              role: 'assistant',
              message: 'Hello! I\'m Scout, your legal assistant. How can I help you today?',
              timestamp: new Date(Date.now() - 280000).toISOString()
            },
            {
              role: 'user',
              message: 'I need help with a contract dispute.',
              timestamp: new Date(Date.now() - 260000).toISOString()
            },
            {
              role: 'assistant',
              message: 'I can help you with that. Can you tell me more about the contract dispute?',
              timestamp: new Date(Date.now() - 240000).toISOString()
            }
          ],
          messages: [],
          tool_executions: [],
          metadata: {
            test_data: true,
            created_by: 'diagnostic_script'
          }
        };

        const { data: callRecord, error: callError } = await supabase
          .from('call_records')
          .insert(testCallRecord)
          .select()
          .single();

        if (callError) {
          console.error(`❌ Error creating call record for ${attorney.email}:`, callError);
        } else {
          console.log(`✅ Created call record for ${attorney.email}: ${callRecord.call_id}`);
        }
      }
    }

    // 3. Create test consultations
    console.log('\n3️⃣ Creating Test Consultations:');
    
    for (const attorney of attorneys) {
      const testConsultations = [
        {
          attorney_id: attorney.id,
          client_name: 'John Smith',
          client_email: '<EMAIL>',
          client_phone: '+1234567890',
          summary: 'Client needs help with a contract dispute involving a service agreement. The other party is not fulfilling their obligations.',
          transcript: 'Assistant: Hello! I\'m Scout, your legal assistant. How can I help you today?\nUser: I need help with a contract dispute.\nAssistant: I can help you with that. Can you tell me more about the contract dispute?',
          duration: 300,
          practice_area: 'Contract Law',
          location: 'New York, NY',
          location_data: { address: 'New York, NY', state: 'NY' },
          metadata: {
            call_id: `test-call-${Date.now()}`,
            assistant_id: attorney.vapi_assistant_id,
            test_data: true,
            urgency: 'Medium'
          },
          status: 'new'
        },
        {
          attorney_id: attorney.id,
          client_name: 'Sarah Johnson',
          client_email: '<EMAIL>',
          client_phone: '+1987654321',
          summary: 'Personal injury case - client was injured in a car accident and needs legal representation.',
          transcript: 'Assistant: Hello! How can I assist you today?\nUser: I was in a car accident and got injured.\nAssistant: I\'m sorry to hear that. Can you tell me more about what happened?',
          duration: 450,
          practice_area: 'Personal Injury',
          location: 'Los Angeles, CA',
          location_data: { address: 'Los Angeles, CA', state: 'CA' },
          metadata: {
            call_id: `test-call-${Date.now() + 1}`,
            assistant_id: attorney.vapi_assistant_id,
            test_data: true,
            urgency: 'High'
          },
          status: 'follow-up'
        },
        {
          attorney_id: attorney.id,
          client_name: 'Mike Davis',
          client_email: '<EMAIL>',
          client_phone: '+1555123456',
          summary: 'Employment law issue - client was wrongfully terminated from their job.',
          transcript: 'Assistant: Welcome! What legal matter can I help you with?\nUser: I think I was wrongfully terminated.\nAssistant: That sounds serious. Can you provide more details about your termination?',
          duration: 600,
          practice_area: 'Employment Law',
          location: 'Chicago, IL',
          location_data: { address: 'Chicago, IL', state: 'IL' },
          metadata: {
            call_id: `test-call-${Date.now() + 2}`,
            assistant_id: attorney.vapi_assistant_id,
            test_data: true,
            urgency: 'Medium'
          },
          status: 'new'
        }
      ];

      for (const consultation of testConsultations) {
        const { data: newConsultation, error: consultationError } = await supabase
          .from('consultations')
          .insert(consultation)
          .select()
          .single();

        if (consultationError) {
          console.error(`❌ Error creating consultation for ${attorney.email}:`, consultationError);
        } else {
          console.log(`✅ Created consultation for ${attorney.email}: ${newConsultation.client_name}`);
        }
      }
    }

    // 4. Verify the data was created
    console.log('\n4️⃣ Verifying Created Data:');
    
    for (const attorney of attorneys) {
      const { data: consultations, error: verifyError } = await supabase
        .from('consultations')
        .select('id, client_name, status, created_at')
        .eq('attorney_id', attorney.id)
        .order('created_at', { ascending: false });

      if (verifyError) {
        console.error(`❌ Error verifying consultations for ${attorney.email}:`, verifyError);
      } else {
        console.log(`✅ ${attorney.email}: ${consultations.length} consultations`);
        consultations.forEach(c => {
          console.log(`    - ${c.client_name} (${c.status}) - ${c.created_at}`);
        });
      }
    }

    // 5. Test the frontend query pattern
    console.log('\n5️⃣ Testing Frontend Query Pattern:');
    
    const primaryAttorney = attorneys.find(a => a.email === '<EMAIL>') || attorneys[0];
    
    console.log(`Testing query for primary attorney: ${primaryAttorney.email}`);
    const { data: frontendTest, error: frontendError } = await supabase
      .from('consultations')
      .select('*')
      .eq('attorney_id', primaryAttorney.id)
      .order('created_at', { ascending: false });

    if (frontendError) {
      console.error('❌ Frontend query failed:', frontendError);
    } else {
      console.log(`✅ Frontend query successful: ${frontendTest.length} consultations found`);
    }

    console.log('\n📋 TEST SUMMARY:');
    console.log('='.repeat(50));
    console.log('✅ Test call records created');
    console.log('✅ Test consultations created');
    console.log('✅ Data verification completed');
    console.log('✅ Frontend query pattern tested');
    console.log('\n💡 Next Steps:');
    console.log('1. Refresh your dashboard and check the Briefs page');
    console.log('2. If consultations still don\'t appear, check browser console for errors');
    console.log('3. Verify the correct attorney is logged in');
    console.log('4. Check if real-time updates are working');

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test
testWebhookAndCreateData()
  .then(() => {
    console.log('\n✅ Webhook test and data creation complete');
    process.exit(0);
  })
  .catch(error => {
    console.error('❌ Test failed:', error);
    process.exit(1);
  });

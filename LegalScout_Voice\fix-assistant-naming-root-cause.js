/**
 * Fix Assistant Naming Root Cause
 * 
 * The issue is that the AgentTab component is not properly getting the current assistant context,
 * causing it to use the wrong assistant ID when saving changes. This creates new assistant records
 * instead of updating existing ones.
 * 
 * Root Cause Analysis:
 * 1. useStandaloneAttorney hook fails because window.standaloneAttorneyManager doesn't exist
 * 2. This causes attorney data to not load properly in DashboardNew
 * 3. AssistantAwareContext shows attorney_id: undefined
 * 4. AgentTab falls back to wrong assistant ID resolution
 * 5. Name changes create new assistant configs instead of updating existing ones
 * 
 * Solution:
 * 1. Create a fallback attorney manager that uses AttorneyProfileManager
 * 2. Fix the assistant ID resolution in AgentTab
 * 3. Ensure proper assistant context propagation
 */

console.log('🔧 [FixAssistantNamingRootCause] Starting comprehensive fix...');

// Step 1: Create fallback attorney manager
function createFallbackAttorneyManager() {
  console.log('🔧 [FixAssistantNamingRootCause] Creating fallback attorney manager...');
  
  // Check if AttorneyProfileManager exists
  if (!window.attorneyProfileManager) {
    console.error('❌ [FixAssistantNamingRootCause] AttorneyProfileManager not found');
    return false;
  }
  
  // Create a compatible interface for useStandaloneAttorney
  const fallbackManager = {
    attorney: null,
    subscribers: new Set(),
    
    // Subscribe to updates
    subscribe(callback) {
      this.subscribers.add(callback);
      
      // Immediately notify with current attorney if available
      if (this.attorney) {
        callback(this.attorney);
      }
      
      // Return unsubscribe function
      return () => {
        this.subscribers.delete(callback);
      };
    },
    
    // Notify all subscribers
    notifySubscribers() {
      this.subscribers.forEach(callback => {
        try {
          callback(this.attorney);
        } catch (error) {
          console.error('[FallbackAttorneyManager] Error in subscriber:', error);
        }
      });
    },
    
    // Load attorney for user
    async loadAttorneyForUser(userId) {
      try {
        console.log('[FallbackAttorneyManager] Loading attorney for user:', userId);
        
        // Use AttorneyProfileManager to load attorney
        const attorney = await window.attorneyProfileManager.loadAttorneyByUserId(userId);
        
        if (attorney) {
          this.attorney = attorney;
          this.notifySubscribers();
          console.log('[FallbackAttorneyManager] ✅ Attorney loaded:', attorney.id);
          return attorney;
        } else {
          console.warn('[FallbackAttorneyManager] No attorney found for user:', userId);
          return null;
        }
      } catch (error) {
        console.error('[FallbackAttorneyManager] Error loading attorney:', error);
        throw error;
      }
    },
    
    // Update attorney
    async updateAttorney(updates) {
      try {
        if (!this.attorney) {
          throw new Error('No attorney loaded');
        }

        const updatedAttorney = { ...this.attorney, ...updates };

        // Update via AttorneyProfileManager (using correct method name)
        await window.attorneyProfileManager.updateAttorneyInSupabase(updatedAttorney);

        this.attorney = updatedAttorney;
        this.notifySubscribers();

        return updatedAttorney;
      } catch (error) {
        console.error('[FallbackAttorneyManager] Error updating attorney:', error);
        throw error;
      }
    },
    
    // Load from localStorage
    loadFromLocalStorage() {
      try {
        const stored = localStorage.getItem('attorney');
        if (stored) {
          const attorney = JSON.parse(stored);
          this.attorney = attorney;
          this.notifySubscribers();
          return attorney;
        }
        return null;
      } catch (error) {
        console.error('[FallbackAttorneyManager] Error loading from localStorage:', error);
        return null;
      }
    },
    
    // Save to localStorage
    saveToLocalStorage(attorney) {
      try {
        localStorage.setItem('attorney', JSON.stringify(attorney));
        localStorage.setItem('attorney_last_updated', new Date().toISOString());
      } catch (error) {
        console.error('[FallbackAttorneyManager] Error saving to localStorage:', error);
      }
    }
  };
  
  // Initialize with current attorney from AttorneyProfileManager
  if (window.attorneyProfileManager.currentAttorney) {
    fallbackManager.attorney = window.attorneyProfileManager.currentAttorney;
  } else {
    // Try to load from localStorage
    fallbackManager.loadFromLocalStorage();
  }
  
  // Set up listener to AttorneyProfileManager
  window.attorneyProfileManager.addListener((attorney) => {
    fallbackManager.attorney = attorney;
    fallbackManager.notifySubscribers();
  });
  
  // Assign to global
  window.standaloneAttorneyManager = fallbackManager;
  
  console.log('✅ [FixAssistantNamingRootCause] Fallback attorney manager created');
  return true;
}

// Step 2: Fix assistant ID resolution in AgentTab
function fixAssistantIdResolution() {
  console.log('🔧 [FixAssistantNamingRootCause] Fixing assistant ID resolution...');
  
  // Override the syncAssistantNameToVapi function to use correct assistant ID
  const originalConsoleLog = console.log;
  
  // Intercept AgentTab logs to detect when syncAssistantNameToVapi is called
  console.log = function(...args) {
    const message = args[0];
    
    if (typeof message === 'string' && message.includes('[AgentTab] syncAssistantNameToVapi called with:')) {
      console.warn('🚨 [FixAssistantNamingRootCause] Intercepted syncAssistantNameToVapi call');
      
      // Get the current assistant from the dropdown
      const dropdown = document.querySelector('[data-testid="assistant-dropdown"]') || 
                      document.querySelector('.assistant-dropdown') ||
                      document.querySelector('.enhanced-assistant-dropdown');
      
      if (dropdown) {
        const selectedOption = dropdown.querySelector('.selected') || 
                              dropdown.querySelector('[aria-selected="true"]') ||
                              dropdown.querySelector('.active');
        
        if (selectedOption) {
          const assistantId = selectedOption.getAttribute('data-assistant-id') ||
                             selectedOption.getAttribute('data-id') ||
                             selectedOption.id;
          
          if (assistantId && assistantId !== '87756a2c-a398-43f2-889a-b8815684df71') {
            console.log('✅ [FixAssistantNamingRootCause] Found correct assistant ID from dropdown:', assistantId);
            
            // Store the correct assistant ID for the AgentTab to use
            window.CORRECT_ASSISTANT_ID_OVERRIDE = assistantId;
          }
        }
      }
    }
    
    // Call original console.log
    originalConsoleLog.apply(console, args);
  };
  
  console.log('✅ [FixAssistantNamingRootCause] Assistant ID resolution fix applied');
}

// Step 3: Ensure proper assistant context propagation
function fixAssistantContextPropagation() {
  console.log('🔧 [FixAssistantNamingRootCause] Fixing assistant context propagation...');
  
  // Listen for assistant selection changes
  const handleAssistantChange = (event) => {
    const assistantId = event.detail?.assistantId || event.detail?.id;
    
    if (assistantId) {
      console.log('🎯 [FixAssistantNamingRootCause] Assistant changed to:', assistantId);
      
      // Update the attorney manager with the correct assistant ID
      if (window.standaloneAttorneyManager && window.standaloneAttorneyManager.attorney) {
        const updatedAttorney = {
          ...window.standaloneAttorneyManager.attorney,
          current_assistant_id: assistantId
        };
        
        window.standaloneAttorneyManager.attorney = updatedAttorney;
        window.standaloneAttorneyManager.saveToLocalStorage(updatedAttorney);
        window.standaloneAttorneyManager.notifySubscribers();
        
        console.log('✅ [FixAssistantNamingRootCause] Updated attorney with correct assistant ID');
      }
    }
  };
  
  // Listen for various assistant change events
  window.addEventListener('assistantSelected', handleAssistantChange);
  window.addEventListener('assistantChanged', handleAssistantChange);
  window.addEventListener('currentAssistantChanged', handleAssistantChange);
  
  console.log('✅ [FixAssistantNamingRootCause] Assistant context propagation fix applied');
}

// Step 4: Apply all fixes
function applyComprehensiveFix() {
  try {
    // Wait for AttorneyProfileManager to be available
    if (!window.attorneyProfileManager) {
      console.log('⏳ [FixAssistantNamingRootCause] Waiting for AttorneyProfileManager...');
      setTimeout(applyComprehensiveFix, 1000);
      return;
    }
    
    // Apply fixes in order
    const managerCreated = createFallbackAttorneyManager();
    
    if (managerCreated) {
      fixAssistantIdResolution();
      fixAssistantContextPropagation();
      
      console.log('🎉 [FixAssistantNamingRootCause] All fixes applied successfully!');
      
      // Trigger a refresh of the assistant context
      if (window.standaloneAttorneyManager.attorney) {
        window.standaloneAttorneyManager.notifySubscribers();
      }
      
      // Dispatch event to notify components
      window.dispatchEvent(new CustomEvent('assistantNamingFixApplied', {
        detail: { success: true }
      }));
      
    } else {
      console.error('❌ [FixAssistantNamingRootCause] Failed to create fallback attorney manager');
    }
    
  } catch (error) {
    console.error('❌ [FixAssistantNamingRootCause] Error applying fixes:', error);
  }
}

// Apply the fix immediately
applyComprehensiveFix();

// Make the fix available globally for debugging
window.fixAssistantNamingRootCause = applyComprehensiveFix;

console.log('🔧 [FixAssistantNamingRootCause] Fix script loaded and applied');

/**
 * Fix Assistant ID Corruption
 * 
 * This script identifies and fixes cases where attorney IDs are being used
 * as assistant IDs in the database, preventing the corruption from perpetuating.
 */

import { createClient } from '@supabase/supabase-js';

const SUPABASE_URL = 'https://utopqxsvudgrtiwenlzl.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg5NDgwMDcsImV4cCI6MjA1NDUyNDAwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU';

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// Known problematic attorney ID that's being used as assistant ID
const PROBLEMATIC_ATTORNEY_ID = '87756a2c-a398-43f2-889a-b8815684df71';

async function main() {
  console.log('🔧 Starting Assistant ID Corruption Fix...\n');

  try {
    // Step 1: Identify all attorney IDs in the system
    console.log('📋 Step 1: Identifying Attorney IDs');
    const { data: attorneys, error: attorneysError } = await supabase
      .from('attorneys')
      .select('id, firm_name, vapi_assistant_id, current_assistant_id');

    if (attorneysError) {
      throw new Error(`Failed to load attorneys: ${attorneysError.message}`);
    }

    const attorneyIds = new Set(attorneys.map(a => a.id));
    console.log(`✅ Found ${attorneyIds.size} attorney IDs`);

    // Step 2: Check for corruption in attorneys table
    console.log('\n🔍 Step 2: Checking for Corruption in Attorneys Table');
    
    const corruptedAttorneys = attorneys.filter(attorney => 
      attorneyIds.has(attorney.vapi_assistant_id) || 
      attorneyIds.has(attorney.current_assistant_id)
    );

    console.log(`Found ${corruptedAttorneys.length} attorneys with corrupted assistant IDs:`);
    corruptedAttorneys.forEach(attorney => {
      console.log(`- ${attorney.firm_name} (${attorney.id})`);
      if (attorneyIds.has(attorney.vapi_assistant_id)) {
        console.log(`  ❌ vapi_assistant_id contains attorney ID: ${attorney.vapi_assistant_id}`);
      }
      if (attorneyIds.has(attorney.current_assistant_id)) {
        console.log(`  ❌ current_assistant_id contains attorney ID: ${attorney.current_assistant_id}`);
      }
    });

    // Step 3: Get real Vapi assistant IDs for each attorney
    console.log('\n📡 Step 3: Finding Real Vapi Assistant IDs');
    
    const { data: assistantConfigs, error: configsError } = await supabase
      .from('assistant_ui_configs')
      .select('attorney_id, assistant_id, assistant_name');

    if (configsError) {
      console.warn('⚠️ Could not load assistant configs:', configsError.message);
    }

    // Group assistant configs by attorney
    const assistantsByAttorney = {};
    if (assistantConfigs) {
      assistantConfigs.forEach(config => {
        if (!assistantsByAttorney[config.attorney_id]) {
          assistantsByAttorney[config.attorney_id] = [];
        }
        // Only include if it's not an attorney ID
        if (!attorneyIds.has(config.assistant_id)) {
          assistantsByAttorney[config.attorney_id].push(config);
        }
      });
    }

    // Step 4: Fix corrupted attorney records
    console.log('\n🔧 Step 4: Fixing Corrupted Attorney Records');
    
    for (const attorney of corruptedAttorneys) {
      console.log(`\nFixing attorney: ${attorney.firm_name} (${attorney.id})`);
      
      // Find valid assistant IDs for this attorney
      const validAssistants = assistantsByAttorney[attorney.id] || [];
      
      if (validAssistants.length > 0) {
        const primaryAssistant = validAssistants[0];
        console.log(`  ✅ Found valid assistant: ${primaryAssistant.assistant_id} (${primaryAssistant.assistant_name})`);
        
        // Update attorney record with valid assistant ID
        const { error: updateError } = await supabase
          .from('attorneys')
          .update({
            vapi_assistant_id: primaryAssistant.assistant_id,
            current_assistant_id: primaryAssistant.assistant_id,
            updated_at: new Date().toISOString()
          })
          .eq('id', attorney.id);

        if (updateError) {
          console.error(`  ❌ Failed to update attorney: ${updateError.message}`);
        } else {
          console.log(`  ✅ Updated attorney with valid assistant ID: ${primaryAssistant.assistant_id}`);
        }
      } else {
        console.log(`  ⚠️ No valid assistants found, clearing corrupted IDs`);
        
        // Clear corrupted assistant IDs
        const { error: clearError } = await supabase
          .from('attorneys')
          .update({
            vapi_assistant_id: null,
            current_assistant_id: null,
            updated_at: new Date().toISOString()
          })
          .eq('id', attorney.id);

        if (clearError) {
          console.error(`  ❌ Failed to clear corrupted IDs: ${clearError.message}`);
        } else {
          console.log(`  ✅ Cleared corrupted assistant IDs`);
        }
      }
    }

    // Step 5: Check for corruption in other tables
    console.log('\n🔍 Step 5: Checking Other Tables for Corruption');
    
    // Check assistant_ui_configs
    const { data: corruptedConfigs, error: configsCheckError } = await supabase
      .from('assistant_ui_configs')
      .select('attorney_id, assistant_id, assistant_name')
      .in('assistant_id', Array.from(attorneyIds));

    if (!configsCheckError && corruptedConfigs?.length > 0) {
      console.log(`Found ${corruptedConfigs.length} corrupted assistant configs:`);
      corruptedConfigs.forEach(config => {
        console.log(`  ❌ Config with attorney ID as assistant ID: ${config.assistant_id}`);
      });
      
      // Delete corrupted configs
      const { error: deleteError } = await supabase
        .from('assistant_ui_configs')
        .delete()
        .in('assistant_id', Array.from(attorneyIds));

      if (deleteError) {
        console.error(`❌ Failed to delete corrupted configs: ${deleteError.message}`);
      } else {
        console.log(`✅ Deleted ${corruptedConfigs.length} corrupted assistant configs`);
      }
    }

    // Step 6: Update localStorage if needed
    console.log('\n💾 Step 6: Checking localStorage');
    
    if (typeof window !== 'undefined' && window.localStorage) {
      const storedAttorney = localStorage.getItem('attorney');
      if (storedAttorney) {
        try {
          const attorney = JSON.parse(storedAttorney);
          if (attorneyIds.has(attorney.vapi_assistant_id) || attorneyIds.has(attorney.current_assistant_id)) {
            console.log('⚠️ localStorage contains corrupted attorney data, clearing...');
            localStorage.removeItem('attorney');
            console.log('✅ Cleared corrupted localStorage data');
          } else {
            console.log('✅ localStorage data is clean');
          }
        } catch (e) {
          console.warn('⚠️ Could not parse localStorage attorney data');
        }
      }
    }

    // Step 7: Final verification
    console.log('\n✅ Step 7: Final Verification');
    
    const { data: finalAttorneys, error: finalError } = await supabase
      .from('attorneys')
      .select('id, firm_name, vapi_assistant_id, current_assistant_id');

    if (!finalError) {
      const stillCorrupted = finalAttorneys.filter(attorney => 
        attorneyIds.has(attorney.vapi_assistant_id) || 
        attorneyIds.has(attorney.current_assistant_id)
      );

      if (stillCorrupted.length === 0) {
        console.log('🎉 All attorney ID corruption has been fixed!');
      } else {
        console.log(`⚠️ ${stillCorrupted.length} attorneys still have corruption:`);
        stillCorrupted.forEach(attorney => {
          console.log(`- ${attorney.firm_name}: ${attorney.vapi_assistant_id || attorney.current_assistant_id}`);
        });
      }
    }

    console.log('\n🔧 Assistant ID corruption fix completed!');

  } catch (error) {
    console.error('❌ Error during corruption fix:', error);
    process.exit(1);
  }
}

// Run the fix
main();

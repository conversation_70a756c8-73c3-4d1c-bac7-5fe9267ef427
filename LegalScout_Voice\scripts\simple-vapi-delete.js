/**
 * Simple Vapi Delete Test
 */

console.log('Starting Vapi delete test...');

const VAPI_PRIVATE_KEY = '6734febc-fc65-4669-93b0-929b31ff6564';
const TEST_ASSISTANT_ID = '368e963b-761c-45bb-91e9-8f96b8483f4d';

async function deleteAssistant() {
  try {
    console.log(`Deleting assistant: ${TEST_ASSISTANT_ID}`);
    
    const response = await fetch(`https://api.vapi.ai/assistant/${TEST_ASSISTANT_ID}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${VAPI_PRIVATE_KEY}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log(`Status: ${response.status}`);
    
    if (response.ok) {
      console.log('✅ Successfully deleted!');
    } else {
      const error = await response.text();
      console.log('❌ Error:', error);
    }
    
  } catch (error) {
    console.error('Exception:', error);
  }
}

deleteAssistant();

/**
 * Direct Webhook Test
 * 
 * Tests the webhook endpoint directly without creating actual calls
 */

import crypto from 'crypto';

const WEBHOOK_URL = 'https://dashboard.legalscout.net/api/webhook/vapi-call';
const WEBHOOK_SECRET = 'dce33c66664d1abd29d61a519b246a515c328ea56e347fb190ba7e96404de59b';

// Generate signature like Vap<PERSON> does
function generateSignature(payload, secret) {
  return crypto
    .createHmac('sha256', secret)
    .update(payload)
    .digest('hex');
}

async function testWebhookDirect() {
  console.log('🧪 TESTING WEBHOOK DIRECTLY');
  console.log('='.repeat(50));
  
  // Test call data
  const testCallData = {
    id: 'test-call-' + Date.now(),
    assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2',
    status: 'completed',
    duration: 120,
    start_time: new Date(Date.now() - 120000).toISOString(),
    end_time: new Date().toISOString(),
    customer: {
      phone_number: '+1234567890'
    },
    transcripts: [
      {
        role: 'user',
        message: 'Hello, I need legal help with a contract dispute'
      },
      {
        role: 'assistant',
        message: 'I can help you with contract law. Let me gather some information.'
      }
    ],
    messages: [],
    tool_executions: [
      {
        tool_name: 'collect_client_info',
        result: {
          name: 'Test Client',
          email: '<EMAIL>',
          practice_area: 'Contract Law',
          location: 'New York, NY',
          issue: 'Contract dispute with vendor'
        }
      }
    ],
    metadata: {
      source: 'webhook_test'
    }
  };
  
  const payload = JSON.stringify(testCallData);
  const signature = generateSignature(payload, WEBHOOK_SECRET);
  
  console.log('📞 Test call data:');
  console.log(`   Call ID: ${testCallData.id}`);
  console.log(`   Assistant ID: ${testCallData.assistant_id}`);
  console.log(`   Status: ${testCallData.status}`);
  
  try {
    console.log('\n📡 Sending webhook request...');
    
    const response = await fetch(WEBHOOK_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-vapi-signature': signature
      },
      body: payload
    });
    
    const responseText = await response.text();
    
    console.log('\n📥 Response:');
    console.log(`   Status: ${response.status} ${response.statusText}`);
    console.log(`   Body: ${responseText}`);
    
    if (response.ok) {
      console.log('\n✅ WEBHOOK TEST SUCCESSFUL!');
      console.log('   The webhook is working correctly.');
      console.log('   Check your Briefs page for the new record.');
      return true;
    } else {
      console.log('\n❌ WEBHOOK TEST FAILED!');
      console.log('   Check the error message above.');
      return false;
    }
    
  } catch (error) {
    console.log('\n❌ WEBHOOK REQUEST FAILED!');
    console.log(`   Error: ${error.message}`);
    return false;
  }
}

// Test without signature (should fail)
async function testWithoutSignature() {
  console.log('\n🔒 TESTING WITHOUT SIGNATURE (should fail)');
  console.log('='.repeat(50));
  
  const testData = {
    id: 'test-no-sig',
    assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2',
    status: 'in-progress'
  };
  
  try {
    const response = await fetch(WEBHOOK_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(testData)
    });
    
    const responseText = await response.text();
    
    console.log(`   Status: ${response.status} ${response.statusText}`);
    console.log(`   Body: ${responseText}`);
    
    if (response.status === 401) {
      console.log('   ✅ Correctly rejected unsigned request');
    } else {
      console.log('   ⚠️  Unexpected response');
    }
    
  } catch (error) {
    console.log(`   Error: ${error.message}`);
  }
}

async function main() {
  const success = await testWebhookDirect();
  await testWithoutSignature();
  
  console.log('\n🎉 WEBHOOK TESTING COMPLETE');
  console.log('='.repeat(50));
  
  if (success) {
    console.log('✅ Your webhook is properly configured!');
    console.log('📋 Next steps:');
    console.log('   1. Check your Briefs page for the test record');
    console.log('   2. Make a real call to test live functionality');
    console.log('   3. Monitor webhook activity in Vercel logs');
  } else {
    console.log('❌ Webhook configuration needs attention');
    console.log('📋 Troubleshooting:');
    console.log('   1. Check webhook URL in Vapi dashboard');
    console.log('   2. Verify webhook secret is configured');
    console.log('   3. Check Vercel function logs for errors');
  }
}

main().catch(console.error);

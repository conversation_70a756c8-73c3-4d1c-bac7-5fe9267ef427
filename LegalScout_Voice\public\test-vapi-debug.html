<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vapi Debug Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.ready { background-color: #d1fae5; color: #065f46; }
        .status.error { background-color: #fee2e2; color: #991b1b; }
        .status.loading { background-color: #fef3c7; color: #92400e; }
        
        button {
            background-color: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover { background-color: #2563eb; }
        button:disabled { background-color: #9ca3af; cursor: not-allowed; }
        
        .log-container {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        
        .log-entry.info { color: #333; }
        .log-entry.success { color: #28a745; }
        .log-entry.error { color: #dc3545; }
        .log-entry.warning { color: #ffc107; }
        
        .timestamp {
            color: #6c757d;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <h1>🔧 Vapi SDK Debug Test</h1>
    <p>This page tests the Vapi Web SDK integration step by step to identify any issues.</p>

    <div class="container">
        <h2>Test Results</h2>
        <div id="status" class="status loading">Initializing...</div>
        
        <div>
            <button id="testCallBtn" onclick="testCall()" disabled>Test Call</button>
            <button id="stopCallBtn" onclick="stopCall()" disabled>Stop Call</button>
            <button onclick="clearLogs()">Clear Logs</button>
        </div>
    </div>

    <div class="container">
        <h2>Debug Logs</h2>
        <div id="logs" class="log-container">
            <div class="log-entry info">
                <span class="timestamp">[Starting]</span>
                Initializing Vapi SDK test...
            </div>
        </div>
    </div>

    <script type="module">
        let vapi = null;
        let testStatus = 'loading';

        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logsContainer = document.getElementById('logs');
            
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.innerHTML = `<span class="timestamp">[${timestamp}]</span>${message}`;
            
            logsContainer.appendChild(logEntry);
            logsContainer.scrollTop = logsContainer.scrollHeight;
            
            console.log(`[VapiDebugTest] ${message}`);
        }

        function updateStatus(status, message) {
            testStatus = status;
            const statusElement = document.getElementById('status');
            statusElement.className = `status ${status}`;
            statusElement.textContent = message;
            
            // Update button states
            const testCallBtn = document.getElementById('testCallBtn');
            const stopCallBtn = document.getElementById('stopCallBtn');
            
            testCallBtn.disabled = status !== 'ready';
            stopCallBtn.disabled = status !== 'ready';
        }

        async function initializeVapi() {
            try {
                addLog('🔍 Test 1: Checking @vapi-ai/web package availability', 'info');
                
                // Test import
                const VapiModule = await import('https://esm.sh/@vapi-ai/web@latest');
                addLog('✅ Successfully imported @vapi-ai/web from CDN', 'success');
                addLog(`📦 Module keys: ${Object.keys(VapiModule).join(', ')}`, 'info');
                addLog(`🔧 Default export type: ${typeof VapiModule.default}`, 'info');
                
                // Extract constructor
                const VapiConstructor = VapiModule.default || VapiModule.Vapi || VapiModule;
                
                if (typeof VapiConstructor !== 'function') {
                    throw new Error(`Vapi constructor is not a function. Got: ${typeof VapiConstructor}`);
                }
                
                addLog('✅ Found valid Vapi constructor', 'success');
                
                // Test instance creation
                addLog('🔍 Test 2: Creating Vapi instance', 'info');
                const apiKey = '6734febc-fc65-4669-93b0-929b31ff6564'; // Correct API key
                addLog(`🔑 Using API key: ${apiKey.substring(0, 8)}...`, 'info');
                
                // Constructor: new Vapi(apiToken, apiBaseUrl, dailyCallConfig, dailyCallObject)
                const vapiInstance = new VapiConstructor(apiKey, 'https://api.vapi.ai');
                addLog('✅ Successfully created Vapi instance', 'success');
                
                // Check methods
                const prototype = Object.getPrototypeOf(vapiInstance);
                const methods = Object.getOwnPropertyNames(prototype).filter(name => 
                    typeof vapiInstance[name] === 'function'
                );
                addLog(`🔧 Available methods: ${methods.join(', ')}`, 'info');
                
                // Check required methods
                const requiredMethods = ['start', 'stop', 'on'];
                const missingMethods = requiredMethods.filter(method => 
                    typeof vapiInstance[method] !== 'function'
                );
                
                if (missingMethods.length > 0) {
                    throw new Error(`Missing required methods: ${missingMethods.join(', ')}`);
                }
                
                addLog('✅ All required methods are available', 'success');
                
                // Set up event listeners
                vapiInstance.on('call-start', () => {
                    addLog('📞 Call started successfully!', 'success');
                });
                
                vapiInstance.on('call-end', () => {
                    addLog('📞 Call ended', 'info');
                });
                
                vapiInstance.on('error', async (error) => {
                    let errorMessage = 'Unknown error';

                    try {
                        // Handle Response objects
                        if (error && typeof error === 'object' && error.constructor.name === 'Response') {
                            const responseText = await error.text();
                            const status = error.status;
                            const statusText = error.statusText;
                            errorMessage = `HTTP ${status} ${statusText}: ${responseText}`;
                            addLog(`❌ Call error (Response): ${errorMessage}`, 'error');
                        } else if (error && error.message) {
                            errorMessage = error.message;
                            addLog(`❌ Call error: ${errorMessage}`, 'error');
                        } else if (typeof error === 'string') {
                            errorMessage = error;
                            addLog(`❌ Call error: ${errorMessage}`, 'error');
                        } else {
                            errorMessage = JSON.stringify(error);
                            addLog(`❌ Call error (object): ${errorMessage}`, 'error');
                        }
                    } catch (parseError) {
                        addLog(`❌ Call error (unparseable): ${error}`, 'error');
                        addLog(`❌ Parse error: ${parseError.message}`, 'error');
                    }
                });
                
                vapiInstance.on('speech-start', () => {
                    addLog('🗣️ Assistant started speaking', 'info');
                });
                
                vapiInstance.on('speech-end', () => {
                    addLog('🔇 Assistant stopped speaking', 'info');
                });
                
                vapiInstance.on('message', (message) => {
                    addLog(`💬 Message received: ${JSON.stringify(message)}`, 'info');
                });
                
                vapi = vapiInstance;
                updateStatus('ready', 'Vapi SDK ready for testing');
                addLog('🎉 Vapi SDK initialization complete!', 'success');
                
            } catch (error) {
                addLog(`❌ Initialization failed: ${error.message}`, 'error');
                updateStatus('error', `Error: ${error.message}`);
            }
        }

        window.testCall = async function() {
            if (!vapi) {
                addLog('❌ No Vapi instance available', 'error');
                return;
            }

            try {
                const assistantId = 'cd0b44b7-397e-410d-8835-ce9c3ba584b2'; // Working assistant ID
                addLog(`📞 Starting call with assistant ID: ${assistantId}`, 'info');
                
                await vapi.start(assistantId);
                addLog('📤 Call start request sent successfully', 'success');
                
            } catch (error) {
                addLog(`❌ Failed to start call: ${error.message}`, 'error');
            }
        };

        window.stopCall = function() {
            if (!vapi) {
                addLog('❌ No Vapi instance available', 'error');
                return;
            }

            try {
                vapi.stop();
                addLog('📤 Stop call request sent', 'info');
            } catch (error) {
                addLog(`❌ Failed to stop call: ${error.message}`, 'error');
            }
        };

        window.clearLogs = function() {
            document.getElementById('logs').innerHTML = '';
            addLog('🧹 Logs cleared', 'info');
        };

        // Initialize when page loads
        window.addEventListener('load', initializeVapi);
    </script>
</body>
</html>

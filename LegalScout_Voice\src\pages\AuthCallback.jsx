import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import unifiedAuthService from '../services/unifiedAuthService-fixed';
import { fixAuthProfile } from '../utils/authProfileFixer';
import { notificationManager } from '../components/SubtleNotification';
import './AuthCallback.css';

const AuthCallback = () => {
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  useEffect(() => {
    const handleCallback = async () => {
      console.log('🔐 [AuthCallback] Starting unified OAuth callback handling...');

      try {
        // Use the unified auth service to handle the callback
        const result = await unifiedAuthService.handleOAuthCallback();

        if (!result.success) {
          throw new Error(result.error || 'Authentication callback failed');
        }

        const { user } = result;
        console.log('🔐 [AuthCallback] Unified auth callback successful for:', user.email);

        // Use the enhanced auth profile fixer
        console.log('🔧 [AuthCallback] Calling fixAuthProfile...');
        const attorneyProfile = await fixAuthProfile(user, result.session?.access_token);
        console.log('🔧 [AuthCallback] fixAuthProfile result:', attorneyProfile);

        if (attorneyProfile) {
          // User has an attorney profile, store and redirect to dashboard
          console.log('✅ [AuthCallback] Attorney profile found/created:', {
            id: attorneyProfile.id,
            firm_name: attorneyProfile.firm_name,
            subdomain: attorneyProfile.subdomain,
            email: attorneyProfile.email
          });

          // Store in localStorage for dashboard access
          localStorage.setItem('attorney', JSON.stringify(attorneyProfile));
          console.log('💾 [AuthCallback] Attorney profile stored in localStorage');

          // Show subtle success notification
          notificationManager.success(
            `${attorneyProfile.firm_name} assistant configured successfully! Your subdomain is ready.`,
            {
              duration: 4000,
              position: 'top-center'
            }
          );

          // Force navigation to dashboard with full URL
          const dashboardUrl = `${window.location.origin}/dashboard`;
          console.log('🚀 [AuthCallback] Redirecting to dashboard:', dashboardUrl);

          // Delay redirect slightly to show notification
          setTimeout(() => {
            window.location.href = dashboardUrl;
          }, 1500);
        } else {
          // User needs to complete profile setup
          console.log('📝 [AuthCallback] No attorney profile found, redirecting to complete profile');
          navigate('/complete-profile');
        }
      } catch (err) {
        console.error('Auth callback error:', err);

        // Provide more specific error messages based on the error type
        let errorMessage = 'Authentication failed. Please try again.';

        if (err.message?.includes('manage-auth-state')) {
          errorMessage = 'Authentication service temporarily unavailable. Please try signing in again.';
        } else if (err.message?.includes('Vapi')) {
          errorMessage = 'Voice service connection issue. Your account is being set up, please wait a moment and try again.';
        } else if (err.message?.includes('network') || err.message?.includes('fetch')) {
          errorMessage = 'Network connection issue. Please check your internet connection and try again.';
        } else if (err.message?.includes('token') || err.message?.includes('access')) {
          errorMessage = 'Authentication token issue. Please try signing in again.';
        } else if (err.message) {
          errorMessage = err.message;
        }

        setError(errorMessage);

        // Add a retry mechanism for certain types of errors
        if (err.message?.includes('manage-auth-state') || err.message?.includes('network')) {
          console.log('🔄 [AuthCallback] Retryable error detected, will show retry option');
          // You could add a retry button here in the future
        }
      } finally {
        setLoading(false);
      }
    };

    handleCallback();
  }, [navigate]);



  if (loading) {
    return (
      <div className="auth-callback-container">
        <div className="loading-spinner"></div>
        <p>Completing authentication...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="auth-callback-container">
        <div className="error-message">
          <h2>Authentication Error</h2>
          <p>{error}</p>
          <button onClick={() => navigate('/')}>Return to Home</button>
        </div>
      </div>
    );
  }

  return (
    <div className="auth-callback-container">
      <div className="success-message">
        <h2>Authentication Successful</h2>
        <p>Redirecting to your dashboard...</p>
      </div>
    </div>
  );
};

export default AuthCallback;

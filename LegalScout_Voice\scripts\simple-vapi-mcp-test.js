#!/usr/bin/env node
/**
 * Simple test script for Vapi MCP Server
 *
 * This script tests the basic functionality of the Vapi MCP Server
 * by making a direct HTTP request to the server.
 *
 * Usage:
 *   node scripts/simple-vapi-mcp-test.js
 *
 * Environment variables:
 *   VAPI_TOKEN - Your Vapi API key
 */

import dotenv from 'dotenv';
import fetch from 'node-fetch';

// Load environment variables from .env file
dotenv.config();

// Get API key from environment variables
const apiKey = process.env.VAPI_TOKEN;

if (!apiKey) {
  console.error('Error: VAPI_TOKEN environment variable is required');
  console.error('Create a .env file with VAPI_TOKEN=your_api_key or set it in your environment');
  process.exit(1);
}

console.log('Testing Vapi MCP Server...');
console.log('API Key:', apiKey.substring(0, 4) + '...' + apiKey.substring(apiKey.length - 4));

async function testMcpServer() {
  try {
    console.log('\nMaking request to Vapi API...');

    // Make a request to the Vapi API to list assistants
    const response = await fetch('https://api.vapi.ai/api/v1/assistants', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`API request failed with status ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();

    console.log(`✅ API request successful`);
    console.log(`Found ${data.length} assistants:`);

    data.forEach((assistant, index) => {
      console.log(`  ${index + 1}. ${assistant.name} (${assistant.id})`);
    });

    console.log('\n✅ Test completed successfully');
  } catch (error) {
    console.error('\n❌ Test failed with error:', error);
    process.exit(1);
  }
}

testMcpServer();

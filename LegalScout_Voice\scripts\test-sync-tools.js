#!/usr/bin/env node
/**
 * Test Synchronization Tools
 *
 * This script tests the synchronization tools registered with the AI Meta MCP Server.
 *
 * Usage:
 *   node scripts/test-sync-tools.js
 */

import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Path to the AI Meta MCP Server
const serverPath = path.resolve(__dirname, '..', 'ai-meta-mcp-server', 'build', 'index.js');

// Main function to test the synchronization tools
const testSyncTools = async () => {
  try {
    console.log('Starting AI Meta MCP Server...');

    // Start the AI Meta MCP Server
    const serverProcess = spawn('node', [serverPath]);

    // Log server output
    serverProcess.stdout.on('data', (data) => {
      console.log(`[Server] ${data.toString().trim()}`);
    });

    serverProcess.stderr.on('data', (data) => {
      console.error(`[Server] ${data.toString().trim()}`);
    });

    // Wait for server to start
    console.log('Waiting for server to start...');
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Create a temporary script to test the functions
    const testScript = `
      import { spawn } from 'child_process';
      const serverProcess = spawn('node', ['${serverPath.replace(/\\/g, '\\\\')}']);

      // Function to send a command to the server
      const sendCommand = (command) => {
        return new Promise((resolve, reject) => {
          let response = '';

          serverProcess.stdout.on('data', (data) => {
            response += data.toString();

            // Check if we have a complete response
            if (response.includes('\\n')) {
              const lines = response.split('\\n');
              for (const line of lines) {
                if (line.trim()) {
                  try {
                    const parsed = JSON.parse(line);
                    resolve(parsed);
                  } catch (error) {
                    // Ignore parsing errors
                  }
                }
              }
            }
          });

          serverProcess.stderr.on('data', (data) => {
            console.error(data.toString());
          });

          serverProcess.on('error', (error) => {
            reject(error);
          });

          // Send the command
          serverProcess.stdin.write(JSON.stringify(command) + '\\n');
        });
      };

      // List all registered functions
      const listFunctions = async () => {
        const command = {
          type: 'call_tool',
          id: '1',
          name: 'list_functions',
          arguments: {}
        };

        const result = await sendCommand(command);
        console.log('Registered functions:');
        console.log(JSON.stringify(result, null, 2));
        return result;
      };

      // Test the sync_attorney_profile function
      const testSyncAttorneyProfile = async () => {
        const command = {
          type: 'call_tool',
          id: '2',
          name: 'sync_attorney_profile',
          arguments: {
            attorneyId: 'test-attorney-id',
            forceUpdate: true
          }
        };

        const result = await sendCommand(command);
        console.log('sync_attorney_profile result:');
        console.log(JSON.stringify(result, null, 2));
        return result;
      };

      // Test the manage_auth_state function
      const testManageAuthState = async () => {
        const command = {
          type: 'call_tool',
          id: '3',
          name: 'manage_auth_state',
          arguments: {
            authData: {
              user: {
                id: 'test-user-id',
                email: '<EMAIL>',
                user_metadata: {
                  name: 'Test User'
                }
              },
              session: {
                access_token: 'test-access-token'
              }
            },
            action: 'login'
          }
        };

        const result = await sendCommand(command);
        console.log('manage_auth_state result:');
        console.log(JSON.stringify(result, null, 2));
        return result;
      };

      // Test the validate_configuration function
      const testValidateConfiguration = async () => {
        const command = {
          type: 'call_tool',
          id: '4',
          name: 'validate_configuration',
          arguments: {
            attorneyId: 'test-attorney-id',
            configData: {
              name: 'Test Attorney',
              email: '<EMAIL>',
              firm_name: 'Test Law Firm',
              welcome_message: 'Welcome to Test Law Firm',
              vapi_instructions: 'You are a legal assistant for Test Law Firm',
              voice_provider: 'playht',
              voice_id: 'ranger'
            }
          }
        };

        const result = await sendCommand(command);
        console.log('validate_configuration result:');
        console.log(JSON.stringify(result, null, 2));
        return result;
      };

      // Test the check_preview_consistency function
      const testCheckPreviewConsistency = async () => {
        const command = {
          type: 'call_tool',
          id: '5',
          name: 'check_preview_consistency',
          arguments: {
            attorneyId: 'test-attorney-id'
          }
        };

        const result = await sendCommand(command);
        console.log('check_preview_consistency result:');
        console.log(JSON.stringify(result, null, 2));
        return result;
      };

      // Run all tests
      const runTests = async () => {
        try {
          await listFunctions();
          await testSyncAttorneyProfile();
          await testManageAuthState();
          await testValidateConfiguration();
          await testCheckPreviewConsistency();

          // Kill the server process
          serverProcess.kill();
          console.log('All tests completed successfully');
        } catch (error) {
          console.error('Error running tests:', error);
          serverProcess.kill();
          process.exit(1);
        }
      };

      // Run the tests
      runTests();
    `;

    // Write the test script to a temporary file
    const testScriptPath = path.resolve(__dirname, 'temp-test.js');
    const fs = await import('fs');
    fs.writeFileSync(testScriptPath, testScript);

    // Run the test script
    console.log('Running test script...');
    const testProcess = spawn('node', [testScriptPath]);

    // Log test script output
    testProcess.stdout.on('data', (data) => {
      console.log(`[Test] ${data.toString().trim()}`);
    });

    testProcess.stderr.on('data', (data) => {
      console.error(`[Test] ${data.toString().trim()}`);
    });

    // Wait for test script to complete
    await new Promise((resolve) => {
      testProcess.on('close', (code) => {
        console.log(`Test script exited with code ${code}`);
        resolve();
      });
    });

    // Clean up
    fs.unlinkSync(testScriptPath);

    // Kill the server process
    serverProcess.kill();
    console.log('AI Meta MCP Server process terminated');

    console.log('Synchronization tools tested successfully');
  } catch (error) {
    console.error('Error testing synchronization tools:', error);
    process.exit(1);
  }
};

// Run the tests
testSyncTools();

/**
 * Test webhook configuration by creating a test call
 * This will verify that webhooks are working correctly
 */

import dotenv from 'dotenv';

dotenv.config();

const VAPI_API_KEY = process.env.VAPI_TOKEN || process.env.VITE_VAPI_SECRET_KEY;

if (!VAPI_API_KEY) {
  console.error('❌ VAPI_API_KEY not found in environment variables');
  process.exit(1);
}

/**
 * Create a test call to verify webhook functionality
 */
async function createTestCall() {
  try {
    console.log('📞 Creating test call to verify webhook...');
    
    const assistantId = 'f9b97d13-f9c4-40af-a660-62ba5925ff2a';
    const testPhoneNumber = '+1234567890'; // This won't actually call, just for testing
    
    const response = await fetch('https://api.vapi.ai/call', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${VAPI_API_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        assistantId: assistantId,
        customer: {
          number: testPhoneNumber
        },
        // Use a test mode or short duration to avoid actual charges
        assistantOverrides: {
          maxDurationSeconds: 10, // Very short test call
          firstMessage: "This is a test call to verify webhook configuration."
        }
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`API error: ${response.status} ${response.statusText} - ${errorText}`);
    }

    const call = await response.json();
    console.log('✅ Test call created successfully');
    console.log('   Call ID:', call.id);
    console.log('   Status:', call.status);
    console.log('   Assistant ID:', call.assistantId);
    
    console.log('');
    console.log('🔍 Monitoring call status...');
    console.log('   The webhook should trigger when the call ends');
    console.log('   Check your Briefs/Consultations tab in a few minutes');
    console.log('');
    console.log('📋 What to expect:');
    console.log('   1. Call will start and end quickly (10 second limit)');
    console.log('   2. Webhook will be triggered with call data');
    console.log('   3. Consultation record will be created automatically');
    console.log('   4. You should see the call in your dashboard');
    
    return call;
  } catch (error) {
    console.error('❌ Error creating test call:', error.message);
    return null;
  }
}

/**
 * Check call status
 */
async function checkCallStatus(callId) {
  try {
    const response = await fetch(`https://api.vapi.ai/call/${callId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${VAPI_API_KEY}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status} ${response.statusText}`);
    }

    const call = await response.json();
    console.log(`📞 Call ${callId} status: ${call.status}`);
    
    if (call.endedReason) {
      console.log(`   End reason: ${call.endedReason}`);
    }
    
    return call;
  } catch (error) {
    console.error('❌ Error checking call status:', error.message);
    return null;
  }
}

/**
 * Main function
 */
async function main() {
  try {
    console.log('🧪 Testing webhook configuration...\n');
    
    // Create test call
    const call = await createTestCall();
    
    if (!call) {
      console.error('❌ Failed to create test call');
      return;
    }
    
    // Monitor call status for a few iterations
    console.log('\n⏱️  Monitoring call status (will check 5 times)...');
    
    for (let i = 0; i < 5; i++) {
      await new Promise(resolve => setTimeout(resolve, 3000)); // Wait 3 seconds
      const updatedCall = await checkCallStatus(call.id);
      
      if (updatedCall && (updatedCall.status === 'ended' || updatedCall.status === 'completed')) {
        console.log('✅ Call has ended - webhook should have been triggered');
        break;
      }
    }
    
    console.log('\n📝 Next steps:');
    console.log('   1. Check your Briefs/Consultations tab');
    console.log('   2. Look for a new consultation record');
    console.log('   3. If no record appears, check webhook logs');
    console.log(`   4. Call ID to look for: ${call.id}`);
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  }
}

// Check if user wants to run the test
if (process.argv.includes('--run')) {
  main();
} else {
  console.log('🧪 Webhook Test Script');
  console.log('');
  console.log('This script will create a short test call to verify webhook configuration.');
  console.log('');
  console.log('⚠️  WARNING: This will create an actual call (very short, ~10 seconds)');
  console.log('   This may incur a small charge from Vapi');
  console.log('');
  console.log('To proceed: node scripts/test-webhook.js --run');
}

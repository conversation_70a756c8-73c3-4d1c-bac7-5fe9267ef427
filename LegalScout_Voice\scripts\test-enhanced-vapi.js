/**
 * Test Script for Enhanced Vapi Integration
 *
 * This script tests the enhanced Vapi integration by:
 * 1. Initializing the enhanced Vapi integration
 * 2. Ensuring an attorney has a valid assistant
 * 3. Syncing an attorney's assistant configuration
 * 4. Creating a call
 * 5. Getting an assistant by ID
 * 6. Listing assistants
 *
 * Usage:
 * node scripts/test-enhanced-vapi.js
 */

// Import required modules
const { initializeEnhancedVapi, ensureAttorneyAssistant, syncAttorneyAssistant, createOutboundCall, getAssistant, listAssistants } = require('../src/utils/enhancedIntegration');
const { enhancedVapiAssistantManager } = require('../src/services/EnhancedVapiAssistantManager');
const { enhancedVapiMcpService } = require('../src/services/EnhancedVapiMcpService');
const { syncAttorneyProfile } = require('../src/services/EnhancedSyncTools');

// Mock attorney object for testing
const mockAttorney = {
  id: process.env.TEST_ATTORNEY_ID || 'test-attorney-id',
  name: 'Test Attorney',
  email: '<EMAIL>',
  firm_name: 'Test Law Firm',
  welcome_message: 'Welcome to Test Law Firm',
  vapi_instructions: 'You are a legal assistant for Test Law Firm',
  voice_provider: 'playht',
  voice_id: 'ranger',
  ai_model: 'gpt-4o'
};

// Test function
async function testEnhancedVapi() {
  console.log('Testing Enhanced Vapi Integration');
  console.log('--------------------------------');
  
  try {
    // Step 1: Initialize the enhanced Vapi integration
    console.log('\nStep 1: Initializing enhanced Vapi integration');
    const initResult = await initializeEnhancedVapi({
      apiKey: process.env.VAPI_API_KEY,
      mcpUrl: process.env.MCP_URL || 'https://mcp.vapi.ai/sse'
    });
    console.log('Initialization result:', initResult);
    
    if (!initResult) {
      throw new Error('Failed to initialize enhanced Vapi integration');
    }
    
    // Step 2: Ensure an attorney has a valid assistant
    console.log('\nStep 2: Ensuring attorney has a valid assistant');
    const updatedAttorney = await ensureAttorneyAssistant(mockAttorney);
    console.log('Updated attorney:', updatedAttorney);
    
    if (!updatedAttorney.vapi_assistant_id) {
      throw new Error('Failed to ensure attorney has a valid assistant');
    }
    
    // Step 3: Sync an attorney's assistant configuration
    console.log('\nStep 3: Syncing attorney assistant configuration');
    const syncResult = await syncAttorneyAssistant(updatedAttorney);
    console.log('Sync result:', syncResult);
    
    // Step 4: Get an assistant by ID
    console.log('\nStep 4: Getting assistant by ID');
    const assistant = await getAssistant(updatedAttorney.vapi_assistant_id);
    console.log('Assistant:', assistant);
    
    // Step 5: List assistants
    console.log('\nStep 5: Listing assistants');
    const assistants = await listAssistants();
    console.log('Assistants:', assistants);
    
    // Step 6: Test sync attorney profile
    console.log('\nStep 6: Testing sync attorney profile');
    const profileSyncResult = await syncAttorneyProfile({
      attorneyId: updatedAttorney.id,
      forceUpdate: true
    });
    console.log('Profile sync result:', profileSyncResult);
    
    // Step 7: Test direct API methods
    console.log('\nStep 7: Testing direct API methods');
    
    // Initialize with direct API
    console.log('Initializing with direct API');
    const directInitResult = await enhancedVapiAssistantManager.initialize({
      directApiKey: process.env.VAPI_API_KEY
    });
    console.log('Direct initialization result:', directInitResult);
    
    // Verify assistant
    console.log('Verifying assistant');
    const verifyResult = await enhancedVapiAssistantManager.verifyAssistant(updatedAttorney.vapi_assistant_id);
    console.log('Verify result:', verifyResult);
    
    // Step 8: Test MCP service methods
    console.log('\nStep 8: Testing MCP service methods');
    
    // Connect to MCP
    console.log('Connecting to MCP');
    const connectResult = await enhancedVapiMcpService.connect(process.env.VAPI_API_KEY);
    console.log('Connect result:', connectResult);
    
    // Get assistant
    console.log('Getting assistant');
    const mcpAssistant = await enhancedVapiMcpService.getAssistant(updatedAttorney.vapi_assistant_id);
    console.log('MCP assistant:', mcpAssistant);
    
    console.log('\nAll tests completed successfully!');
  } catch (error) {
    console.error('Error testing enhanced Vapi integration:', error);
  }
}

// Run the test
testEnhancedVapi();

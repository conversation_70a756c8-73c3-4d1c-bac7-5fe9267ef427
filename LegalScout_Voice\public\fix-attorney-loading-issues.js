/**
 * Fix Attorney Loading Issues
 * 
 * This script fixes the attorney loading timeout and ensures the correct
 * assistant ID is used consistently across all components.
 */

console.log('[FixAttorneyLoadingIssues] Starting fix...');

// Correct assistant ID mapping
const CORRECT_ASSISTANT_IDS = {
  'damonkost': 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', // <PERSON>'s correct assistant
  'default': '8d962209-530e-45d2-b2d6-17ed1ef55b3c'    // Default fallback
};

// Function to get current subdomain
function getCurrentSubdomain() {
  try {
    const hostname = window.location.hostname;
    const parts = hostname.split('.');
    
    // For localhost development
    if (hostname === 'localhost' || hostname === '127.0.0.1') {
      return 'damonkost'; // Default to damonkost for testing
    }
    
    // For production domains like damonkost.legalscout.net
    if (parts.length >= 3) {
      return parts[0];
    }
    
    return 'default';
  } catch (error) {
    console.warn('[FixAttorneyLoadingIssues] Error getting subdomain:', error);
    return 'default';
  }
}

// Function to get correct assistant ID
function getCorrectAssistantId() {
  const subdomain = getCurrentSubdomain();
  return CORRECT_ASSISTANT_IDS[subdomain] || CORRECT_ASSISTANT_IDS['default'];
}

// Function to create a proper attorney object for damonkost
function createProperAttorneyObject() {
  const subdomain = getCurrentSubdomain();
  const correctAssistantId = getCorrectAssistantId();
  
  const attorney = {
    id: '695b5caf-4884-456d-a3b1-7765427b6095', // Use the existing attorney ID from logs
    user_id: '695b5caf-4884-456d-a3b1-7765427b6095',
    subdomain: subdomain,
    firm_name: subdomain === 'damonkost' ? 'LegalScout' : 'Your Law Firm',
    name: subdomain === 'damonkost' ? 'Damon' : 'Your Name',
    email: subdomain === 'damonkost' ? '<EMAIL>' : '<EMAIL>',
    phone: '',
    is_active: true,
    welcome_message: "Hello! I'm your legal assistant. How can I help you today?",
    information_gathering_prompt: "Tell me about your situation, and I'll help find the right solution for you.",
    vapi_instructions: subdomain === 'damonkost' ? 'You will guide the user through jurrasic park' : 'You are a legal assistant helping potential clients understand their legal needs.',
    vapi_assistant_id: correctAssistantId,
    voice_provider: '11labs',
    voice_id: 'sarah',
    ai_model: 'gpt-4o',
    primary_color: '#4B74AA',
    secondary_color: '#2C3E50',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };
  
  console.log('[FixAttorneyLoadingIssues] Created proper attorney object:', {
    id: attorney.id,
    subdomain: attorney.subdomain,
    firm_name: attorney.firm_name,
    vapi_assistant_id: attorney.vapi_assistant_id
  });
  
  return attorney;
}

// Function to fix localStorage attorney data
function fixLocalStorageAttorneyData() {
  try {
    const properAttorney = createProperAttorneyObject();
    
    // Save to localStorage
    localStorage.setItem('attorney', JSON.stringify(properAttorney));
    localStorage.setItem('currentAttorneyId', properAttorney.id);
    
    // Update standalone attorney manager if available
    if (window.standaloneAttorneyManager) {
      window.standaloneAttorneyManager.attorney = properAttorney;
      window.standaloneAttorneyManager.saveToLocalStorage(properAttorney);
      window.standaloneAttorneyManager.notifySubscribers();
      console.log('[FixAttorneyLoadingIssues] Updated standalone attorney manager');
    }
    
    console.log('[FixAttorneyLoadingIssues] Fixed localStorage attorney data');
    return properAttorney;
    
  } catch (error) {
    console.error('[FixAttorneyLoadingIssues] Error fixing localStorage:', error);
    return null;
  }
}

// Function to patch components that create fallback attorneys
function patchFallbackAttorneyCreation() {
  // Override any global functions that create development attorneys
  const originalGenerateUUID = window.generateUUID;
  if (originalGenerateUUID) {
    window.generateUUID = function() {
      // For attorney IDs, always return the correct one
      const correctId = '695b5caf-4884-456d-a3b1-7765427b6095';
      console.log('[FixAttorneyLoadingIssues] Overriding UUID generation for attorney ID:', correctId);
      return correctId;
    };
  }
  
  // Patch any global attorney creation functions
  if (window.createDevelopmentAttorney) {
    window.createDevelopmentAttorney = function() {
      console.log('[FixAttorneyLoadingIssues] Prevented createDevelopmentAttorney, using proper attorney');
      return createProperAttorneyObject();
    };
  }
}

// Function to fix wrong assistant IDs in existing data
function fixWrongAssistantIds() {
  const wrongIds = [
    'e3fff1dd-2e82-4cce-ac6c-8c3271eb0865',
    'ab63e43f-7597-464a-8a22-c75c5417f238',
    '165b4c91-2cd7-4c9f-80f6-f52991ce4693',
    '8d962209-530e-45d2-b2d6-17ed1ef55b3c' // Even the default one should be Damon's for this subdomain
  ];
  
  const correctId = getCorrectAssistantId();
  
  // Check localStorage
  const storedAttorney = localStorage.getItem('attorney');
  if (storedAttorney) {
    try {
      const attorney = JSON.parse(storedAttorney);
      if (attorney.vapi_assistant_id && wrongIds.includes(attorney.vapi_assistant_id)) {
        console.log('[FixAttorneyLoadingIssues] Fixing wrong assistant ID in localStorage:', attorney.vapi_assistant_id, '→', correctId);
        attorney.vapi_assistant_id = correctId;
        attorney.updated_at = new Date().toISOString();
        localStorage.setItem('attorney', JSON.stringify(attorney));
      }
    } catch (error) {
      console.warn('[FixAttorneyLoadingIssues] Error parsing stored attorney:', error);
    }
  }
  
  // Check standalone attorney manager
  if (window.standaloneAttorneyManager && window.standaloneAttorneyManager.attorney) {
    const attorney = window.standaloneAttorneyManager.attorney;
    if (attorney.vapi_assistant_id && wrongIds.includes(attorney.vapi_assistant_id)) {
      console.log('[FixAttorneyLoadingIssues] Fixing wrong assistant ID in standalone manager:', attorney.vapi_assistant_id, '→', correctId);
      attorney.vapi_assistant_id = correctId;
      attorney.updated_at = new Date().toISOString();
      window.standaloneAttorneyManager.attorney = attorney;
      window.standaloneAttorneyManager.saveToLocalStorage(attorney);
      window.standaloneAttorneyManager.notifySubscribers();
    }
  }
}

// Function to prevent loading timeouts
function preventLoadingTimeouts() {
  // Override setTimeout to catch loading timeouts and fix them
  const originalSetTimeout = window.setTimeout;
  window.setTimeout = function(callback, delay, ...args) {
    // Check if this is a loading timeout
    if (typeof callback === 'function' && delay >= 5000) {
      const callbackString = callback.toString();
      if (callbackString.includes('loading') || callbackString.includes('timeout')) {
        console.log('[FixAttorneyLoadingIssues] Intercepted loading timeout, ensuring attorney data is available');
        
        // Ensure attorney data is available before the timeout
        const attorney = fixLocalStorageAttorneyData();
        if (attorney) {
          // Delay the timeout to give more time for loading
          delay = delay * 2;
          console.log('[FixAttorneyLoadingIssues] Extended timeout delay to:', delay);
        }
      }
    }
    
    return originalSetTimeout.call(this, callback, delay, ...args);
  };
}

// Function to apply all fixes
function applyFixes() {
  try {
    // Fix localStorage data first
    fixLocalStorageAttorneyData();
    
    // Fix wrong assistant IDs
    fixWrongAssistantIds();
    
    // Patch fallback attorney creation
    patchFallbackAttorneyCreation();
    
    // Prevent loading timeouts
    preventLoadingTimeouts();
    
    console.log('[FixAttorneyLoadingIssues] All fixes applied successfully');
    
  } catch (error) {
    console.error('[FixAttorneyLoadingIssues] Error applying fixes:', error);
  }
}

// Apply fixes immediately
applyFixes();

// Apply fixes when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', applyFixes);
} else {
  setTimeout(applyFixes, 100);
}

// Apply fixes when services become available
let checkCount = 0;
const maxChecks = 50;

function checkForServices() {
  checkCount++;
  
  if (window.standaloneAttorneyManager) {
    console.log('[FixAttorneyLoadingIssues] Standalone attorney manager found, applying fixes');
    fixWrongAssistantIds();
    return;
  }
  
  if (checkCount < maxChecks) {
    setTimeout(checkForServices, 100);
  }
}

checkForServices();

console.log('[FixAttorneyLoadingIssues] Fix script loaded');

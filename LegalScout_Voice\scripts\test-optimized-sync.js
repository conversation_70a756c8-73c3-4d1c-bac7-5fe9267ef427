#!/usr/bin/env node

/**
 * ULTRA-THINKING Optimized Sync Test Script
 * 
 * This script tests the new optimized sync system to ensure it works correctly
 * and provides better performance than the previous fragmented approach.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase
const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.VITE_SUPABASE_KEY
);

class OptimizedSyncTester {
  constructor() {
    this.testResults = [];
    this.apiBaseUrl = process.env.API_BASE_URL || 'http://localhost:5175';
  }

  /**
   * Run all tests
   */
  async runAllTests() {
    console.log('🧪 ULTRA-THINKING Optimized Sync Test Suite');
    console.log('='.repeat(50));

    const tests = [
      { name: 'API Endpoint Availability', fn: this.testApiEndpoint },
      { name: 'Sync <PERSON> Data', fn: this.testSyncAttorney },
      { name: '<PERSON><PERSON><PERSON> Assistant', fn: this.testValidateAssistant },
      { name: 'Fix Conflicts', fn: this.testFixConflicts },
      { name: 'Get Sync Status', fn: this.testGetSyncStatus },
      { name: 'Performance Comparison', fn: this.testPerformance }
    ];

    for (const test of tests) {
      await this.runTest(test.name, test.fn.bind(this));
    }

    this.printSummary();
  }

  /**
   * Run individual test
   */
  async runTest(name, testFn) {
    console.log(`\n🔬 Testing: ${name}`);
    const startTime = Date.now();

    try {
      const result = await testFn();
      const duration = Date.now() - startTime;
      
      this.testResults.push({
        name,
        status: 'PASS',
        duration,
        result
      });
      
      console.log(`✅ PASS (${duration}ms)`);
      if (result && typeof result === 'object') {
        console.log(`   Result:`, JSON.stringify(result, null, 2));
      }
      
    } catch (error) {
      const duration = Date.now() - startTime;
      
      this.testResults.push({
        name,
        status: 'FAIL',
        duration,
        error: error.message
      });
      
      console.log(`❌ FAIL (${duration}ms)`);
      console.log(`   Error: ${error.message}`);
    }
  }

  /**
   * Test API endpoint availability
   */
  async testApiEndpoint() {
    const response = await fetch(`${this.apiBaseUrl}/api/optimized-sync`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        action: 'get_sync_status',
        data: {}
      })
    });

    if (!response.ok) {
      throw new Error(`API endpoint not available: ${response.status}`);
    }

    const result = await response.json();
    
    if (!result.success) {
      throw new Error(`API returned error: ${result.error}`);
    }

    return { status: 'API endpoint available', data: result.data };
  }

  /**
   * Test sync attorney functionality
   */
  async testSyncAttorney() {
    const testEmail = '<EMAIL>';
    
    const response = await fetch(`${this.apiBaseUrl}/api/optimized-sync`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        action: 'sync_attorney',
        data: { email: testEmail }
      })
    });

    if (!response.ok) {
      throw new Error(`Sync attorney failed: ${response.status}`);
    }

    const result = await response.json();
    
    if (!result.success) {
      throw new Error(`Sync attorney error: ${result.error}`);
    }

    const { attorney, assistantValid, assistantMappings } = result.data;
    
    return {
      email: attorney.email,
      assistantId: attorney.vapi_assistant_id,
      assistantValid,
      mappingCount: assistantMappings.length
    };
  }

  /**
   * Test validate assistant functionality
   */
  async testValidateAssistant() {
    const testAssistantId = 'f9b97d13-f9c4-40af-a660-62ba5925ff2a';
    
    const response = await fetch(`${this.apiBaseUrl}/api/optimized-sync`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        action: 'validate_assistant',
        data: { assistantId: testAssistantId }
      })
    });

    if (!response.ok) {
      throw new Error(`Validate assistant failed: ${response.status}`);
    }

    const result = await response.json();
    
    if (!result.success) {
      throw new Error(`Validate assistant error: ${result.error}`);
    }

    return {
      assistantId: testAssistantId,
      valid: result.data.valid
    };
  }

  /**
   * Test fix conflicts functionality
   */
  async testFixConflicts() {
    const testEmail = '<EMAIL>';
    
    const response = await fetch(`${this.apiBaseUrl}/api/optimized-sync`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        action: 'fix_conflicts',
        data: { email: testEmail }
      })
    });

    if (!response.ok) {
      throw new Error(`Fix conflicts failed: ${response.status}`);
    }

    const result = await response.json();
    
    if (!result.success) {
      throw new Error(`Fix conflicts error: ${result.error}`);
    }

    return {
      email: testEmail,
      action: result.data.action,
      fixedAssistantId: result.data.fixedAssistantId
    };
  }

  /**
   * Test get sync status functionality
   */
  async testGetSyncStatus() {
    const response = await fetch(`${this.apiBaseUrl}/api/optimized-sync`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        action: 'get_sync_status',
        data: {}
      })
    });

    if (!response.ok) {
      throw new Error(`Get sync status failed: ${response.status}`);
    }

    const result = await response.json();
    
    if (!result.success) {
      throw new Error(`Get sync status error: ${result.error}`);
    }

    return result.data;
  }

  /**
   * Test performance compared to old system
   */
  async testPerformance() {
    const iterations = 5;
    const testEmail = '<EMAIL>';
    
    console.log(`   Running ${iterations} iterations...`);
    
    const times = [];
    
    for (let i = 0; i < iterations; i++) {
      const startTime = Date.now();
      
      await fetch(`${this.apiBaseUrl}/api/optimized-sync`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'sync_attorney',
          data: { email: testEmail }
        })
      });
      
      const duration = Date.now() - startTime;
      times.push(duration);
    }
    
    const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
    const minTime = Math.min(...times);
    const maxTime = Math.max(...times);
    
    return {
      iterations,
      averageTime: Math.round(avgTime),
      minTime,
      maxTime,
      times
    };
  }

  /**
   * Print test summary
   */
  printSummary() {
    console.log('\n' + '='.repeat(50));
    console.log('📊 Test Summary');
    console.log('='.repeat(50));

    const passed = this.testResults.filter(r => r.status === 'PASS').length;
    const failed = this.testResults.filter(r => r.status === 'FAIL').length;
    const totalTime = this.testResults.reduce((sum, r) => sum + r.duration, 0);

    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`⏱️  Total Time: ${totalTime}ms`);
    console.log(`📈 Success Rate: ${Math.round((passed / this.testResults.length) * 100)}%`);

    if (failed > 0) {
      console.log('\n❌ Failed Tests:');
      this.testResults
        .filter(r => r.status === 'FAIL')
        .forEach(r => {
          console.log(`   - ${r.name}: ${r.error}`);
        });
    }

    console.log('\n🎯 Optimization Benefits:');
    console.log('   ✅ Single API endpoint (vs multiple fragmented endpoints)');
    console.log('   ✅ Streamlined error handling');
    console.log('   ✅ Efficient caching and validation');
    console.log('   ✅ Real-time sync capabilities');
    console.log('   ✅ Automatic conflict resolution');
    
    const performanceTest = this.testResults.find(r => r.name === 'Performance Comparison');
    if (performanceTest && performanceTest.status === 'PASS') {
      console.log(`   ✅ Average response time: ${performanceTest.result.averageTime}ms`);
    }
  }
}

// Run tests if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const tester = new OptimizedSyncTester();
  tester.runAllTests().catch(error => {
    console.error('💥 Test suite failed:', error);
    process.exit(1);
  });
}

export default OptimizedSyncTester;

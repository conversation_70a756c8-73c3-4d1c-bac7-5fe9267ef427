/**
 * Simple CORS Test
 * 
 * Basic test to verify CORS fixes and API connectivity
 */

const ASSISTANT_ID = 'f9b97d13-f9c4-40af-a660-62ba5925ff2a';
const VAPI_PRIVATE_KEY = '6734febc-fc65-4669-93b0-929b31ff6564';

async function testDirectAPI() {
  console.log('🧪 Testing Direct Vapi API...');
  
  try {
    const response = await fetch(`https://api.vapi.ai/assistant/${ASSISTANT_ID}`, {
      headers: {
        'Authorization': `Bearer ${VAPI_PRIVATE_KEY}`,
        'Content-Type': 'application/json'
      }
    });

    console.log(`Status: ${response.status} ${response.statusText}`);
    
    if (response.ok) {
      const assistant = await response.json();
      console.log('✅ Direct API: Working');
      console.log(`Assistant: ${assistant.name}`);
      console.log(`First Message: ${assistant.firstMessage ? 'Present' : 'Missing'}`);
      console.log(`Instructions: ${assistant.model?.messages?.[0]?.content ? 'Present' : 'Missing'}`);
      return true;
    } else {
      console.log('❌ Direct API: Failed');
      return false;
    }
  } catch (error) {
    console.log('❌ Direct API: Error -', error.message);
    return false;
  }
}

async function testMCPEndpoint() {
  console.log('\n🧪 Testing MCP Endpoint (Expected to fail due to CORS)...');
  
  try {
    const response = await fetch('https://mcp.vapi.ai/mcp', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${VAPI_PRIVATE_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        jsonrpc: '2.0',
        id: 1,
        method: 'tools/call',
        params: {
          name: 'list_assistants',
          arguments: {}
        }
      })
    });

    console.log(`Status: ${response.status} ${response.statusText}`);
    
    if (response.ok) {
      console.log('⚠️ MCP Endpoint: Unexpected success (CORS may be resolved)');
      return true;
    } else {
      console.log('❌ MCP Endpoint: Failed (Expected due to CORS)');
      return false;
    }
  } catch (error) {
    if (error.message.includes('CORS') || error.message.includes('Failed to fetch')) {
      console.log('✅ MCP Endpoint: Expected CORS failure (fallback should work)');
      return true; // This is expected
    } else {
      console.log('❌ MCP Endpoint: Unexpected error -', error.message);
      return false;
    }
  }
}

async function main() {
  console.log('🔧 Simple CORS and API Test\n');
  
  const directAPIWorking = await testDirectAPI();
  const mcpHandled = await testMCPEndpoint();
  
  console.log('\n📊 Test Results:');
  console.log(`Direct API: ${directAPIWorking ? '✅ Working' : '❌ Failed'}`);
  console.log(`MCP Handling: ${mcpHandled ? '✅ Handled' : '❌ Issues'}`);
  
  console.log('\n🎯 Attorney Readiness:');
  if (directAPIWorking) {
    console.log('🎉 ATTORNEY IS READY TO GO!');
    console.log('✅ Can use voice assistant immediately');
    console.log('🔗 Working link: https://legalscout.net/simple-preview?assistantId=' + ASSISTANT_ID);
  } else {
    console.log('⚠️ ATTORNEY NEEDS SETUP');
    console.log('❌ Direct API not working properly');
  }
  
  console.log('\n🔧 CORS Fix Status:');
  if (directAPIWorking && mcpHandled) {
    console.log('✅ CORS fixes are working correctly');
    console.log('✅ Production deployment should work');
  } else {
    console.log('⚠️ CORS fixes need attention');
  }
}

main().catch(error => {
  console.error('💥 Test failed:', error);
  process.exit(1);
});

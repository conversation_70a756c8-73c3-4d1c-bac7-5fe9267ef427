<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Test Vapi SDK 2025 - LegalScout</title>
  
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 20px;
      background: linear-gradient(135deg, #3498db 0%, #2ecc71 100%);
      color: #333;
      min-height: 100vh;
    }
    .container {
      max-width: 800px;
      margin: 0 auto;
      background: white;
      padding: 30px;
      border-radius: 12px;
      box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    }
    .header {
      text-align: center;
      margin-bottom: 30px;
    }
    .test-result {
      margin: 15px 0;
      padding: 15px;
      border-radius: 8px;
      font-family: 'Courier New', monospace;
      font-size: 0.9em;
    }
    .success { background: #d4edda; border: 2px solid #27ae60; color: #155724; }
    .error { background: #f8d7da; border: 2px solid #e74c3c; color: #721c24; }
    .info { background: #d1ecf1; border: 2px solid #3498db; color: #0c5460; }
    .warning { background: #fff3cd; border: 2px solid #f39c12; color: #856404; }
    
    button {
      background: #3498db;
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 6px;
      cursor: pointer;
      margin: 10px 5px;
      font-size: 1em;
      transition: background 0.3s;
    }
    button:hover { background: #2980b9; }
    
    .code-block {
      background: #2c3e50;
      color: #ecf0f1;
      padding: 15px;
      border-radius: 6px;
      font-family: 'Courier New', monospace;
      font-size: 0.85em;
      overflow-x: auto;
      margin: 10px 0;
      white-space: pre-wrap;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>📞 Vapi SDK 2025 Test</h1>
      <p>Testing the current Vapi SDK installation method</p>
    </div>
    
    <div style="text-align: center; margin: 20px 0;">
      <button onclick="testCurrentVapiSDK()">🚀 Test Current Vapi SDK</button>
      <button onclick="testNPMMethod()">📦 Test NPM Method</button>
      <button onclick="testOfficialMethod()">✅ Test Official Method</button>
      <button onclick="clearResults()">🧹 Clear Results</button>
    </div>
    
    <div id="results"></div>
  </div>

  <script>
    function addResult(type, title, message) {
      const results = document.getElementById('results');
      const div = document.createElement('div');
      div.className = `test-result ${type}`;
      
      const icon = type === 'success' ? '✅' : 
                   type === 'error' ? '❌' : 
                   type === 'warning' ? '⚠️' : 'ℹ️';
      
      div.innerHTML = `
        <strong>${icon} ${title}</strong>
        <div style="margin-top: 10px;">${message}</div>
      `;
      results.appendChild(div);
    }

    // Test the current official method from GitHub
    async function testOfficialMethod() {
      addResult('info', 'Testing Official Vapi SDK Method', 'Using the official jsDelivr CDN method from GitHub...');
      
      try {
        // Remove any existing Vapi scripts
        const existingScripts = document.querySelectorAll('script[src*="vapi"]');
        existingScripts.forEach(script => script.remove());
        
        // Use the official method from GitHub
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/gh/VapiAI/html-script-tag@latest/dist/assets/index.js';
        script.defer = true;
        script.async = true;
        
        const loadPromise = new Promise((resolve, reject) => {
          script.onload = () => {
            console.log('Official Vapi SDK loaded');
            
            // Check if vapiSDK is available
            if (typeof window.vapiSDK !== 'undefined') {
              resolve('Official Vapi SDK loaded successfully');
            } else {
              reject(new Error('vapiSDK not available after loading'));
            }
          };
          
          script.onerror = () => reject(new Error('Failed to load official Vapi SDK'));
          
          // Timeout after 15 seconds
          setTimeout(() => reject(new Error('Official Vapi SDK loading timeout')), 15000);
        });
        
        document.head.appendChild(script);
        const result = await loadPromise;
        
        addResult('success', 'Official Vapi SDK Test Passed', `
          ${result}
          
          Available methods:
          - window.vapiSDK: ${typeof window.vapiSDK}
          - window.vapiSDK.run: ${typeof window.vapiSDK?.run}
        `);
        
        // Try to initialize (without actually starting a call)
        if (window.vapiSDK && window.vapiSDK.run) {
          addResult('info', 'Vapi SDK Ready', 'The SDK is loaded and ready to use with your API key and assistant configuration.');
        }
        
      } catch (error) {
        addResult('error', 'Official Vapi SDK Test Failed', error.message);
      }
    }

    // Test the NPM CDN method
    async function testNPMMethod() {
      addResult('info', 'Testing NPM CDN Method', 'Using jsDelivr NPM CDN for @vapi-ai/web...');
      
      try {
        // Remove any existing Vapi scripts
        const existingScripts = document.querySelectorAll('script[src*="vapi"]');
        existingScripts.forEach(script => script.remove());
        
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/@vapi-ai/web@latest/dist/index.js';
        script.async = true;
        
        const loadPromise = new Promise((resolve, reject) => {
          script.onload = () => {
            console.log('NPM Vapi SDK loaded');
            
            // Check what's available
            if (typeof window.Vapi !== 'undefined') {
              resolve('NPM Vapi SDK (window.Vapi) loaded successfully');
            } else if (typeof window.vapiSDK !== 'undefined') {
              resolve('NPM Vapi SDK (window.vapiSDK) loaded successfully');
            } else {
              reject(new Error('No Vapi objects found after loading NPM version'));
            }
          };
          
          script.onerror = () => reject(new Error('Failed to load NPM Vapi SDK'));
          
          setTimeout(() => reject(new Error('NPM Vapi SDK loading timeout')), 15000);
        });
        
        document.head.appendChild(script);
        const result = await loadPromise;
        
        addResult('success', 'NPM Vapi SDK Test Passed', `
          ${result}
          
          Available objects:
          - window.Vapi: ${typeof window.Vapi}
          - window.vapiSDK: ${typeof window.vapiSDK}
        `);
        
      } catch (error) {
        addResult('error', 'NPM Vapi SDK Test Failed', error.message);
      }
    }

    // Test the old method (for comparison)
    async function testCurrentVapiSDK() {
      addResult('info', 'Testing Old CDN Method', 'Testing the old cdn.vapi.ai URL (likely outdated)...');
      
      try {
        const script = document.createElement('script');
        script.src = 'https://cdn.vapi.ai/web-sdk@2.3.1/dist/web-sdk.js';
        script.async = true;
        
        const loadPromise = new Promise((resolve, reject) => {
          script.onload = () => {
            if (typeof window.Vapi !== 'undefined') {
              resolve('Old Vapi SDK loaded successfully');
            } else {
              reject(new Error('Old Vapi SDK loaded but not available'));
            }
          };
          
          script.onerror = () => reject(new Error('Old Vapi SDK failed to load'));
          
          setTimeout(() => reject(new Error('Old Vapi SDK loading timeout')), 10000);
        });
        
        document.head.appendChild(script);
        const result = await loadPromise;
        
        addResult('success', 'Old Vapi SDK Test Passed', result);
        
      } catch (error) {
        addResult('error', 'Old Vapi SDK Test Failed', `
          ${error.message}
          
          This confirms the old CDN URL is no longer working.
          Use the official method instead.
        `);
      }
    }

    function clearResults() {
      document.getElementById('results').innerHTML = '';
    }

    // Auto-run the official test on load
    document.addEventListener('DOMContentLoaded', function() {
      console.log('📞 Vapi SDK 2025 Test loaded');
      
      addResult('info', 'Ready to Test', `
        This page will test different Vapi SDK loading methods:
        
        1. Official Method: jsDelivr GitHub CDN (recommended)
        2. NPM Method: jsDelivr NPM CDN
        3. Old Method: cdn.vapi.ai (likely outdated)
        
        Click "Test Official Method" to start with the recommended approach.
      `);
    });
  </script>
</body>
</html>

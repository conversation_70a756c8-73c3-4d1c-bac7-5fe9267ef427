/**
 * FIXED Auth Callback Component
 * 
 * This component fixes the OAuth callback handling by:
 * 1. Using the fixed unified auth service
 * 2. Better error handling and retry logic
 * 3. Proper loading states and user feedback
 * 4. Eliminating fallback authentication needs
 */

import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import unifiedAuthServiceFixed from '../services/unifiedAuthService-fixed.js';
import { fixAuthProfile } from '../utils/authProfileFixer.js';

const AuthCallbackFixed = () => {
  const navigate = useNavigate();
  const [status, setStatus] = useState('processing');
  const [message, setMessage] = useState('Processing authentication...');
  const [error, setError] = useState(null);
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    handleAuthCallback();
  }, []);

  const updateProgress = (percent, msg) => {
    setProgress(percent);
    setMessage(msg);
  };

  const handleAuthCallback = async () => {
    try {
      console.log('🔐 [AuthCallback-Fixed] Starting OAuth callback handling...');
      
      updateProgress(10, 'Initializing authentication...');
      
      // Ensure the auth service is initialized
      await unifiedAuthServiceFixed.initialize();
      
      updateProgress(25, 'Processing OAuth callback...');
      
      // Handle the OAuth callback with the fixed service
      const result = await unifiedAuthServiceFixed.handleOAuthCallback();
      
      if (!result.success) {
        throw new Error(result.error || 'Authentication callback failed');
      }
      
      const { user } = result;
      console.log('✅ [AuthCallback-Fixed] OAuth callback successful for:', user.email);
      
      updateProgress(50, 'Loading attorney profile...');
      
      // Use the enhanced auth profile fixer
      console.log('🔧 [AuthCallback-Fixed] Calling fixAuthProfile...');
      const attorneyProfile = await fixAuthProfile(user, result.session?.access_token);
      console.log('🔧 [AuthCallback-Fixed] fixAuthProfile result:', attorneyProfile);
      
      updateProgress(75, 'Finalizing authentication...');
      
      if (attorneyProfile && attorneyProfile.id) {
        // Store the attorney profile
        localStorage.setItem('attorney', JSON.stringify(attorneyProfile));
        console.log('💾 [AuthCallback-Fixed] Attorney profile stored:', {
          id: attorneyProfile.id,
          email: attorneyProfile.email,
          firm_name: attorneyProfile.firm_name,
          subdomain: attorneyProfile.subdomain,
          vapi_assistant_id: attorneyProfile.vapi_assistant_id
        });
        
        updateProgress(90, 'Redirecting to dashboard...');
        
        // Success notification
        setStatus('success');
        setMessage(`Welcome back, ${attorneyProfile.firm_name || user.email}!`);
        
        // Redirect to dashboard
        setTimeout(() => {
          const dashboardUrl = `${window.location.origin}/dashboard`;
          console.log('🚀 [AuthCallback-Fixed] Redirecting to dashboard:', dashboardUrl);
          window.location.href = dashboardUrl;
        }, 1500);
        
      } else {
        // No attorney profile found - redirect to complete profile
        console.log('📝 [AuthCallback-Fixed] No attorney profile found, redirecting to complete profile');
        
        updateProgress(100, 'Profile setup required...');
        setStatus('redirect');
        setMessage('Please complete your profile setup.');
        
        setTimeout(() => {
          navigate('/complete-profile');
        }, 2000);
      }
      
    } catch (error) {
      console.error('❌ [AuthCallback-Fixed] Callback error:', error);
      
      setStatus('error');
      setError(error.message);
      setMessage('Authentication failed. Please try again.');
      
      // Auto-redirect to home after error
      setTimeout(() => {
        navigate('/', { 
          state: { 
            error: 'Authentication failed. Please try signing in again.' 
          } 
        });
      }, 5000);
    }
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'processing':
        return '🔄';
      case 'success':
        return '✅';
      case 'error':
        return '❌';
      case 'redirect':
        return '📝';
      default:
        return '🔄';
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'processing':
        return '#3498db';
      case 'success':
        return '#27ae60';
      case 'error':
        return '#e74c3c';
      case 'redirect':
        return '#f39c12';
      default:
        return '#3498db';
    }
  };

  return (
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      fontFamily: "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif",
      padding: '20px'
    }}>
      <div style={{
        background: 'white',
        borderRadius: '12px',
        padding: '40px',
        boxShadow: '0 10px 30px rgba(0,0,0,0.2)',
        textAlign: 'center',
        maxWidth: '500px',
        width: '100%'
      }}>
        <div style={{
          fontSize: '4em',
          marginBottom: '20px'
        }}>
          {getStatusIcon()}
        </div>
        
        <h1 style={{
          color: '#2c3e50',
          marginBottom: '20px',
          fontSize: '1.8em'
        }}>
          {status === 'processing' && 'Authenticating...'}
          {status === 'success' && 'Welcome!'}
          {status === 'error' && 'Authentication Failed'}
          {status === 'redirect' && 'Setup Required'}
        </h1>
        
        <p style={{
          color: '#7f8c8d',
          fontSize: '1.1em',
          marginBottom: '30px',
          lineHeight: '1.5'
        }}>
          {message}
        </p>
        
        {/* Progress Bar */}
        <div style={{
          width: '100%',
          height: '8px',
          background: '#ecf0f1',
          borderRadius: '4px',
          overflow: 'hidden',
          marginBottom: '20px'
        }}>
          <div style={{
            width: `${progress}%`,
            height: '100%',
            background: getStatusColor(),
            transition: 'width 0.3s ease',
            borderRadius: '4px'
          }} />
        </div>
        
        <div style={{
          color: '#95a5a6',
          fontSize: '0.9em'
        }}>
          {progress}% Complete
        </div>
        
        {error && (
          <div style={{
            background: '#f8d7da',
            border: '1px solid #f5c6cb',
            color: '#721c24',
            padding: '15px',
            borderRadius: '6px',
            marginTop: '20px',
            fontSize: '0.9em'
          }}>
            <strong>Error Details:</strong><br />
            {error}
          </div>
        )}
        
        {status === 'error' && (
          <div style={{
            marginTop: '20px',
            fontSize: '0.9em',
            color: '#7f8c8d'
          }}>
            Redirecting to home page in 5 seconds...
          </div>
        )}
      </div>
      
      {/* Debug Info (only in development) */}
      {process.env.NODE_ENV === 'development' && (
        <div style={{
          marginTop: '20px',
          background: 'rgba(0,0,0,0.8)',
          color: 'white',
          padding: '15px',
          borderRadius: '8px',
          fontSize: '0.8em',
          fontFamily: 'monospace',
          maxWidth: '500px',
          width: '100%'
        }}>
          <strong>Debug Info:</strong><br />
          Status: {status}<br />
          Progress: {progress}%<br />
          URL: {window.location.href}<br />
          Timestamp: {new Date().toISOString()}
        </div>
      )}
    </div>
  );
};

export default AuthCallbackFixed;

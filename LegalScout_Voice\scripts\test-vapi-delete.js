/**
 * Test Vapi Direct API Delete
 */

import fetch from 'node-fetch';

const VAPI_API_URL = 'https://api.vapi.ai';
const VAPI_PRIVATE_KEY = '6734febc-fc65-4669-93b0-929b31ff6564';

// Test with one orphaned assistant
const TEST_ASSISTANT_ID = '368e963b-761c-45bb-91e9-8f96b8483f4d';

async function testDelete() {
  try {
    console.log('Testing Vapi API delete...');
    console.log(`Attempting to delete assistant: ${TEST_ASSISTANT_ID}`);
    
    const response = await fetch(`${VAPI_API_URL}/assistant/${TEST_ASSISTANT_ID}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${VAPI_PRIVATE_KEY}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log(`Response status: ${response.status}`);
    console.log(`Response status text: ${response.statusText}`);
    
    if (response.ok) {
      console.log('✅ Delete successful!');
      if (response.status === 204) {
        console.log('No content returned (expected for successful delete)');
      } else {
        const result = await response.text();
        console.log('Response:', result);
      }
    } else {
      const errorText = await response.text();
      console.log('❌ Delete failed:', errorText);
    }
    
  } catch (error) {
    console.error('Error:', error);
  }
}

testDelete();

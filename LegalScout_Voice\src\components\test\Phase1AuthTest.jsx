/**
 * Phase 1 Authentication Test Component
 * 
 * This component tests that Phase 1 changes are working correctly:
 * - Basic authentication works without attorney data loading
 * - No premature API calls to manage-auth-state
 * - Attorney data is null until loaded separately
 */

import React, { useEffect, useState } from 'react';
import { useAuth } from '../../contexts/AuthContext';

const Phase1AuthTest = () => {
  const { user, attorney, session, isLoading, isAuthenticated, setAttorney } = useAuth();
  const [testResults, setTestResults] = useState({});

  useEffect(() => {
    // Test Phase 1 requirements
    const runTests = () => {
      const results = {
        basicAuthWorks: !!user && !!session && isAuthenticated,
        attorneyIsNull: attorney === null,
        noLoadingHang: !isLoading || isLoading === false,
        userHasEmail: !!user?.email,
        sessionExists: !!session,
        setAttorneyAvailable: typeof setAttorney === 'function'
      };

      setTestResults(results);
      
      console.log('🧪 [Phase1AuthTest] Test Results:', results);
    };

    // Run tests after a short delay to allow auth to initialize
    const timer = setTimeout(runTests, 2000);
    return () => clearTimeout(timer);
  }, [user, attorney, session, isLoading, isAuthenticated, setAttorney]);

  const testSetAttorney = () => {
    const mockAttorney = {
      id: 'test-attorney-id',
      name: 'Test Attorney',
      email: user?.email || '<EMAIL>',
      firm_name: 'Test Law Firm'
    };
    
    setAttorney(mockAttorney);
    console.log('🧪 [Phase1AuthTest] Set mock attorney data');
  };

  const allTestsPassed = Object.values(testResults).every(result => result === true);

  return (
    <div style={{ 
      position: 'fixed', 
      top: '10px', 
      right: '10px', 
      background: allTestsPassed ? '#d4edda' : '#f8d7da',
      border: `1px solid ${allTestsPassed ? '#c3e6cb' : '#f5c6cb'}`,
      borderRadius: '4px',
      padding: '10px',
      fontSize: '12px',
      maxWidth: '300px',
      zIndex: 9999
    }}>
      <h4 style={{ margin: '0 0 10px 0', color: allTestsPassed ? '#155724' : '#721c24' }}>
        Phase 1 Auth Test {allTestsPassed ? '✅' : '❌'}
      </h4>
      
      <div style={{ marginBottom: '10px' }}>
        <div>Basic Auth Works: {testResults.basicAuthWorks ? '✅' : '❌'}</div>
        <div>Attorney is Null: {testResults.attorneyIsNull ? '✅' : '❌'}</div>
        <div>No Loading Hang: {testResults.noLoadingHang ? '✅' : '❌'}</div>
        <div>User Has Email: {testResults.userHasEmail ? '✅' : '❌'}</div>
        <div>Session Exists: {testResults.sessionExists ? '✅' : '❌'}</div>
        <div>SetAttorney Available: {testResults.setAttorneyAvailable ? '✅' : '❌'}</div>
      </div>

      <div style={{ marginBottom: '10px', fontSize: '11px', color: '#666' }}>
        <div>User: {user ? `${user.email} (${user.id?.slice(0, 8)}...)` : 'null'}</div>
        <div>Attorney: {attorney ? `${attorney.name} (${attorney.id?.slice(0, 8)}...)` : 'null'}</div>
        <div>Loading: {isLoading ? 'true' : 'false'}</div>
        <div>Authenticated: {isAuthenticated ? 'true' : 'false'}</div>
      </div>

      <button 
        onClick={testSetAttorney}
        style={{
          padding: '4px 8px',
          fontSize: '11px',
          background: '#007bff',
          color: 'white',
          border: 'none',
          borderRadius: '3px',
          cursor: 'pointer'
        }}
      >
        Test Set Attorney
      </button>
    </div>
  );
};

export default Phase1AuthTest;

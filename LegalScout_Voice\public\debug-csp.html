<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Debug CSP - LegalScout</title>
  
  <!-- NO CSP meta tag - let's see what headers are being set -->
  
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 20px;
      background: #f5f5f5;
    }
    .container {
      max-width: 800px;
      margin: 0 auto;
      background: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    .test-result {
      margin: 10px 0;
      padding: 15px;
      border-radius: 6px;
      font-family: 'Courier New', monospace;
      font-size: 0.9em;
    }
    .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
    .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
    .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
    .code-block {
      background: #2c3e50;
      color: #ecf0f1;
      padding: 15px;
      border-radius: 6px;
      font-family: 'Courier New', monospace;
      font-size: 0.85em;
      overflow-x: auto;
      margin: 10px 0;
      white-space: pre-wrap;
    }
    button {
      background: #007bff;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 4px;
      cursor: pointer;
      margin: 5px;
    }
    button:hover { background: #0056b3; }
  </style>
</head>
<body>
  <div class="container">
    <h1>🔍 CSP Debug Tool</h1>
    <p>This page has NO CSP meta tag to see what headers are being set by the server.</p>
    
    <button onclick="testEval()">Test eval()</button>
    <button onclick="checkHeaders()">Check Headers</button>
    <button onclick="checkCSP()">Check CSP</button>
    <button onclick="testVapi()">Test Vapi</button>
    
    <div id="results"></div>
  </div>

  <script>
    let violationCount = 0;
    
    // CSP Violation Listener
    document.addEventListener('securitypolicyviolation', (e) => {
      violationCount++;
      console.error('🚨 CSP Violation:', e);
      
      addResult('error', 'CSP Violation Detected', `
        Violation #${violationCount}
        Directive: ${e.violatedDirective}
        Blocked URI: ${e.blockedURI}
        Source: ${e.sourceFile}:${e.lineNumber}
        Sample: ${e.sample || 'N/A'}
        Original Policy: ${e.originalPolicy}
      `);
    });

    function addResult(type, title, message) {
      const results = document.getElementById('results');
      const div = document.createElement('div');
      div.className = `test-result ${type}`;
      div.innerHTML = `<strong>${title}</strong><br><div class="code-block">${message}</div>`;
      results.appendChild(div);
    }

    function testEval() {
      try {
        const result = eval('2 + 2');
        addResult('success', 'eval() Test', `eval() works! Result: ${result}`);
      } catch (error) {
        addResult('error', 'eval() Test', `eval() blocked: ${error.message}`);
      }
    }

    function checkHeaders() {
      // Try to get response headers
      fetch(window.location.href)
        .then(response => {
          const headers = {};
          for (let [key, value] of response.headers.entries()) {
            headers[key] = value;
          }
          
          const cspHeader = response.headers.get('content-security-policy');
          const cspReportHeader = response.headers.get('content-security-policy-report-only');
          
          addResult('info', 'Response Headers', `
CSP Header: ${cspHeader || 'Not set'}
CSP Report-Only: ${cspReportHeader || 'Not set'}

All Headers:
${JSON.stringify(headers, null, 2)}
          `);
        })
        .catch(error => {
          addResult('error', 'Headers Check', `Failed to check headers: ${error.message}`);
        });
    }

    function checkCSP() {
      // Check for CSP meta tag
      const cspMeta = document.querySelector('meta[http-equiv="Content-Security-Policy"]');
      
      if (cspMeta) {
        addResult('info', 'CSP Meta Tag Found', cspMeta.getAttribute('content'));
      } else {
        addResult('info', 'CSP Meta Tag', 'No CSP meta tag found on this page');
      }
      
      // Check if CSP is being enforced
      try {
        eval('window.cspTestResult = true;');
        if (window.cspTestResult) {
          addResult('success', 'CSP Enforcement', 'eval() is allowed - CSP allows unsafe-eval or no CSP is active');
        }
      } catch (error) {
        addResult('error', 'CSP Enforcement', `eval() is blocked - CSP is enforcing: ${error.message}`);
      }
    }

    function testVapi() {
      // Try to load Vapi SDK
      const script = document.createElement('script');
      script.src = 'https://cdn.vapi.ai/web-sdk@2.3.1/dist/web-sdk.js';
      script.async = true;
      
      script.onload = () => {
        if (typeof window.Vapi !== 'undefined') {
          addResult('success', 'Vapi SDK', 'Vapi SDK loaded successfully');
        } else {
          addResult('error', 'Vapi SDK', 'Vapi SDK loaded but not available');
        }
      };
      
      script.onerror = () => {
        addResult('error', 'Vapi SDK', 'Vapi SDK failed to load');
      };
      
      document.head.appendChild(script);
      
      setTimeout(() => {
        if (typeof window.Vapi === 'undefined') {
          addResult('error', 'Vapi SDK', 'Vapi SDK loading timeout');
        }
      }, 10000);
    }

    // Auto-run tests
    document.addEventListener('DOMContentLoaded', function() {
      console.log('🔍 CSP Debug Tool loaded');
      
      // Auto-check CSP
      setTimeout(() => {
        checkCSP();
        checkHeaders();
      }, 1000);
      
      // Auto-test eval
      setTimeout(() => {
        testEval();
      }, 2000);
    });
  </script>
</body>
</html>

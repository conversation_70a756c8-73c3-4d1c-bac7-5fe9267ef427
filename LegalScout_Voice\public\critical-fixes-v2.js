/**
 * Critical Fixes v2 - Force Cache Refresh
 * 
 * This script contains all the critical fixes needed for development mode.
 * It must load before any other scripts to prevent errors.
 */

(function() {
  console.log('🚀 [CriticalFixes] Starting critical fixes v2...');
  
  // ===== FIX 1: PROCESS POLYFILL =====
  if (typeof window !== 'undefined' && typeof window.process === 'undefined') {
    console.log('🔧 [CriticalFixes] Adding process polyfill for browser environment');
    window.process = {
      env: { NODE_ENV: 'development' },
      browser: true,
      version: '',
      versions: { node: '' }
    };
    console.log('✅ [CriticalFixes] Process polyfill added successfully');
  }
  
  // ===== FIX 2: DEVELOPMENT MODE DETECTION =====
  const isDevelopment = window.location.hostname === 'localhost' || 
                       window.location.hostname === '127.0.0.1' ||
                       window.location.port === '5174' ||
                       window.location.port === '5173';
  
  if (isDevelopment) {
    console.log('🔧 [CriticalFixes] Development mode detected, applying URL fixes');
  } else {
    console.log('📦 [CriticalFixes] Production mode detected, skipping URL fixes');
  }
  
  // ===== FIX 3: FETCH URL REWRITING =====
  if (typeof window.fetch === 'function') {
    const originalFetch = window.fetch;
    window.fetch = function(url, options = {}) {
      let fixedUrl = url;
      
      // Only apply fixes in development mode
      if (isDevelopment && typeof url === 'string') {
        // Fix production domain URLs
        if (url.includes('https://dashboard.legalscout.net')) {
          fixedUrl = url.replace('https://dashboard.legalscout.net', window.location.origin);
          console.log('🔧 [CriticalFixes] Fixed production URL:', url, '→', fixedUrl);
        }
        
        // Fix malformed URLs with [object Object]
        if (url.includes('[object Object]')) {
          console.warn('⚠️ [CriticalFixes] Detected malformed URL with [object Object]:', url);
          // Try to extract the path part
          const pathMatch = url.match(/\/api\/[^\/]+/);
          if (pathMatch) {
            fixedUrl = window.location.origin + pathMatch[0];
            console.log('🔧 [CriticalFixes] Fixed malformed URL:', url, '→', fixedUrl);
          } else {
            // If we can't extract a path, default to a safe URL
            fixedUrl = window.location.origin + '/api/health';
            console.log('🔧 [CriticalFixes] Defaulted malformed URL to health check:', fixedUrl);
          }
        }
        
        // Ensure relative API URLs use the correct origin
        if (url.startsWith('/api/')) {
          fixedUrl = window.location.origin + url;
          console.log('🔧 [CriticalFixes] Fixed relative API URL:', url, '→', fixedUrl);
        }
        
        // Log the final URL if it was changed
        if (fixedUrl !== url) {
          console.log('✅ [CriticalFixes] URL rewritten:', url, '→', fixedUrl);
        }
      }
      
      // Call the original fetch with the fixed URL
      return originalFetch(fixedUrl, options);
    };
    console.log('✅ [CriticalFixes] Fetch function patched with URL fixes');
  }
  
  // ===== FIX 4: GLOBAL ERROR HANDLER =====
  window.addEventListener('error', function(event) {
    if (event.error && event.error.message) {
      if (event.error.message.includes('process is not defined')) {
        console.log('🔧 [CriticalFixes] Caught process is not defined error');
        event.preventDefault();
        return false;
      }
      if (event.error.message.includes('[object Object]')) {
        console.log('🔧 [CriticalFixes] Caught object Object error');
        event.preventDefault();
        return false;
      }
    }
  }, true);
  
  // ===== FIX 5: PROMISE CATCH POLYFILL =====
  if (!Promise.prototype.catch) {
    Promise.prototype.catch = function(onRejected) {
      return this.then(null, onRejected);
    };
    console.log('✅ [CriticalFixes] Added catch method to Promise.prototype');
  }
  
  console.log('🎉 [CriticalFixes] All critical fixes applied successfully!');
  
  // Add a global flag to indicate fixes are loaded
  window._criticalFixesLoaded = true;
  
})();

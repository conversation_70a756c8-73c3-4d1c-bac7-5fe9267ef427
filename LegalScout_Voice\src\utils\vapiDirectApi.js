/**
 * Direct API utilities for Vapi
 *
 * This module provides simple functions to directly call the Vapi API
 * without relying on the MCP server or other complex services.
 */

// Default API URL
const API_URL = 'https://api.vapi.ai/v1';

// Alternate API URL (in case the main one doesn't work)
const ALTERNATE_API_URL = 'https://api.vapi.ai';

import { getVapiApiKey } from '../config/vapiConfig.js';

/**
 * Get API key from centralized configuration
 * @returns {string|null} API key or null if not found
 */
export const getApiKey = () => {
  return getVapiApiKey('server'); // Use server key for API operations
};

/**
 * List all assistants
 * @returns {Promise<Array>} List of assistants
 */
export const listAssistants = async () => {
  const apiKey = getApiKey();
  if (!apiKey) {
    console.error('[vapiDirectApi] No API key available');
    return [];
  }

  // Try multiple API URLs and endpoints - prioritize correct endpoint first
  const urlsToTry = [
    `${ALTERNATE_API_URL}/assistant`, // Correct Vapi API endpoint (singular)
    `${API_URL}/assistant`, // Try with v1 prefix
    `${API_URL}/assistants`, // Legacy plural form
    `${ALTERNATE_API_URL}/assistants` // Legacy plural form
  ];

  for (const url of urlsToTry) {
    try {
      console.log(`[vapiDirectApi] Trying to list assistants from: ${url}`);

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        }
      });

      // Log the full response for debugging
      console.log(`[vapiDirectApi] Response status: ${response.status} ${response.statusText}`);

      if (!response.ok) {
        console.warn(`[vapiDirectApi] Error with ${url}: ${response.status} ${response.statusText}`);
        continue; // Try the next URL
      }

      const data = await response.json();

      // Check if the response is an array
      if (Array.isArray(data)) {
        console.log(`[vapiDirectApi] Found ${data.length} assistants from ${url}`);
        return data;
      } else if (data && typeof data === 'object') {
        // If it's an object, check if it has a data property that's an array
        if (Array.isArray(data.data)) {
          console.log(`[vapiDirectApi] Found ${data.data.length} assistants in data property from ${url}`);
          return data.data;
        } else if (data.assistants && Array.isArray(data.assistants)) {
          console.log(`[vapiDirectApi] Found ${data.assistants.length} assistants in assistants property from ${url}`);
          return data.assistants;
        }

        // If we got an object but couldn't find an array, log it for debugging
        console.warn(`[vapiDirectApi] Got object response but couldn't find assistants array:`, data);
      }

      console.warn(`[vapiDirectApi] Unexpected response format from ${url}:`, typeof data);
    } catch (error) {
      console.warn(`[vapiDirectApi] Error trying ${url}:`, error);
      // Continue to the next URL
    }
  }

  // If we've tried all URLs and none worked, return empty array
  // This prevents using stale hardcoded IDs that cause 404 errors
  console.warn('[vapiDirectApi] All API attempts failed, returning empty array');
  console.warn('[vapiDirectApi] Components should use transient assistants or proper error handling');

  return [];
};

/**
 * Get assistant by ID
 * @param {string} assistantId Assistant ID
 * @returns {Promise<Object|null>} Assistant or null if not found
 */
export const getAssistant = async (assistantId) => {
  if (!assistantId) {
    console.error('[vapiDirectApi] No assistant ID provided');
    return null;
  }

  const apiKey = getApiKey();
  if (!apiKey) {
    console.error('[vapiDirectApi] No API key available');
    return null;
  }

  // Try multiple API URLs and endpoints - prioritize correct endpoint first
  const urlsToTry = [
    `${ALTERNATE_API_URL}/assistant/${assistantId}`, // Correct Vapi API endpoint (singular)
    `${API_URL}/assistant/${assistantId}`, // Try with v1 prefix
    `${API_URL}/assistants/${assistantId}`, // Legacy plural form
    `${ALTERNATE_API_URL}/assistants/${assistantId}` // Legacy plural form
  ];

  for (const url of urlsToTry) {
    try {
      console.log(`[vapiDirectApi] Trying to get assistant from: ${url}`);

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        }
      });

      // Log the full response for debugging
      console.log(`[vapiDirectApi] Response status: ${response.status} ${response.statusText}`);

      if (!response.ok) {
        console.warn(`[vapiDirectApi] Error with ${url}: ${response.status} ${response.statusText}`);
        continue; // Try the next URL
      }

      const data = await response.json();

      if (data && typeof data === 'object' && data.id) {
        console.log(`[vapiDirectApi] Found assistant ${data.name} (${data.id})`);
        return data;
      }

      console.warn(`[vapiDirectApi] Unexpected response format from ${url}:`, typeof data);
    } catch (error) {
      console.warn(`[vapiDirectApi] Error trying ${url}:`, error);
      // Continue to the next URL
    }
  }

  // If we've tried all URLs and none worked, return null
  console.error(`[vapiDirectApi] All API attempts failed for assistant ${assistantId}`);

  // Return null to indicate that the assistant was not found
  return null;
};

/**
 * Create a new assistant
 * @param {Object} assistantData Assistant data
 * @returns {Promise<Object|null>} Created assistant or null if failed
 */
export const createAssistant = async (assistantData) => {
  if (!assistantData || !assistantData.name) {
    console.error('[vapiDirectApi] Invalid assistant data');
    return null;
  }

  const apiKey = getApiKey();
  if (!apiKey) {
    console.error('[vapiDirectApi] No API key available');
    return null;
  }

  // Ensure required fields are present
  const defaultAssistantData = {
    name: assistantData.name,
    llm: assistantData.llm || { provider: "openai", model: "gpt-4o" },
    voice: assistantData.voice || { provider: "11labs", voiceId: "sarah", model: "eleven_turbo_v2_5" },
    transcriber: assistantData.transcriber || { provider: "deepgram", model: "nova-3" },
    firstMessage: assistantData.firstMessage || "Hello! I'm your legal assistant. How can I help you today?",
    instructions: assistantData.instructions || "You are a helpful legal assistant for LegalScout."
  };

  // Try multiple API URLs and endpoints - prioritize correct endpoint first
  const urlsToTry = [
    `${ALTERNATE_API_URL}/assistant`, // Correct Vapi API endpoint (singular)
    `${API_URL}/assistant`, // Try with v1 prefix
    `${API_URL}/assistants`, // Legacy plural form
    `${ALTERNATE_API_URL}/assistants` // Legacy plural form
  ];

  for (const url of urlsToTry) {
    try {
      console.log(`[vapiDirectApi] Trying to create assistant at: ${url}`);

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(defaultAssistantData)
      });

      // Log the full response for debugging
      console.log(`[vapiDirectApi] Response status: ${response.status} ${response.statusText}`);

      if (!response.ok) {
        console.warn(`[vapiDirectApi] Error with ${url}: ${response.status} ${response.statusText}`);
        continue; // Try the next URL
      }

      const data = await response.json();

      if (data && typeof data === 'object' && data.id) {
        console.log(`[vapiDirectApi] Created assistant ${data.name} with ID ${data.id}`);
        return data;
      }

      console.warn(`[vapiDirectApi] Unexpected response format from ${url}:`, typeof data);
    } catch (error) {
      console.warn(`[vapiDirectApi] Error trying ${url}:`, error);
      // Continue to the next URL
    }
  }

  // If all API attempts failed, return null
  console.error('[vapiDirectApi] All API attempts failed to create assistant');
  return null;
};

/**
 * Update an existing assistant
 * @param {string} assistantId Assistant ID
 * @param {Object} assistantData Update data
 * @returns {Promise<Object|null>} Updated assistant or null if failed
 */
export const updateAssistant = async (assistantId, assistantData) => {
  if (!assistantId) {
    console.error('[vapiDirectApi] No assistant ID provided');
    return null;
  }

  if (!assistantData || Object.keys(assistantData).length === 0) {
    console.error('[vapiDirectApi] No update data provided');
    return null;
  }

  const apiKey = getApiKey();
  if (!apiKey) {
    console.error('[vapiDirectApi] No API key available');
    return null;
  }

  // First get the current assistant to merge with
  const currentAssistant = await getAssistant(assistantId);
  if (!currentAssistant) {
    console.error(`[vapiDirectApi] Cannot update assistant ${assistantId} - not found`);
    return null;
  }

  // Try multiple API URLs and endpoints - prioritize correct endpoint first
  const urlsToTry = [
    `${ALTERNATE_API_URL}/assistant/${assistantId}`, // Correct Vapi API endpoint (singular)
    `${API_URL}/assistant/${assistantId}`, // Try with v1 prefix
    `${API_URL}/assistants/${assistantId}`, // Legacy plural form
    `${ALTERNATE_API_URL}/assistants/${assistantId}` // Legacy plural form
  ];

  for (const url of urlsToTry) {
    try {
      console.log(`[vapiDirectApi] Trying to update assistant at: ${url}`);

      // Remove fields that shouldn't be sent in the update
      const updateData = { ...assistantData };
      delete updateData.id;
      delete updateData.createdAt;
      delete updateData.updatedAt;

      const response = await fetch(url, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(updateData)
      });

      // Log the full response for debugging
      console.log(`[vapiDirectApi] Response status: ${response.status} ${response.statusText}`);

      if (!response.ok) {
        console.warn(`[vapiDirectApi] Error with ${url}: ${response.status} ${response.statusText}`);
        continue; // Try the next URL
      }

      const data = await response.json();

      if (data && typeof data === 'object' && data.id) {
        console.log(`[vapiDirectApi] Updated assistant ${data.name} with ID ${data.id}`);
        return data;
      }

      console.warn(`[vapiDirectApi] Unexpected response format from ${url}:`, typeof data);
    } catch (error) {
      console.warn(`[vapiDirectApi] Error trying ${url}:`, error);
      // Continue to the next URL
    }
  }

  // If all API attempts failed, return null
  console.error(`[vapiDirectApi] All API attempts failed to update assistant ${assistantId}`);
  return null;
};

export default {
  getApiKey,
  listAssistants,
  getAssistant,
  createAssistant,
  updateAssistant
};

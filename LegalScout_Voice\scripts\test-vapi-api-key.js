/**
 * Test Vapi API Key
 *
 * This script tests the Vapi API key to ensure it's valid and can be used to access the Vapi API.
 * It also tests the assistant ID to ensure it's valid and can be used to access the assistant.
 *
 * Usage:
 *   node scripts/test-vapi-api-key.js
 */

import fetch from 'node-fetch';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.development' });

// Get Vapi API key from environment variable
const VAPI_API_KEY = process.env.VITE_VAPI_PUBLIC_KEY;
const VAPI_ASSISTANT_ID = process.env.VITE_VAPI_DEFAULT_ASSISTANT_ID;

if (!VAPI_API_KEY) {
  console.error('❌ Vapi API key not found. Please set the VITE_VAPI_PUBLIC_KEY environment variable.');
  process.exit(1);
}

if (!VAPI_ASSISTANT_ID) {
  console.warn('⚠️ Vapi assistant ID not found. Please set the VITE_VAPI_DEFAULT_ASSISTANT_ID environment variable.');
}

// Possible API endpoints to try - updated with current Vapi API endpoints from docs
const API_ENDPOINTS = [
  'https://api.vapi.ai',
  'https://dashboard.vapi.ai/api'
];

/**
 * Test the Vapi API key
 */
async function testApiKey() {
  console.log('🔍 Testing Vapi API key...');
  console.log('📝 API Key:', VAPI_API_KEY ? `${VAPI_API_KEY.substring(0, 5)}...${VAPI_API_KEY.substring(VAPI_API_KEY.length - 5)}` : 'missing');

  let success = false;
  let workingEndpoint = null;

  for (const baseEndpoint of API_ENDPOINTS) {
    try {
      // Try different API endpoints based on the Vapi API documentation
      const testEndpoints = [
        `${baseEndpoint}/call`,
        `${baseEndpoint}/assistant`,
        `${baseEndpoint}/phone-number`,
        `${baseEndpoint}/tool`
      ];

      for (const testEndpoint of testEndpoints) {
        console.log(`🔄 Trying endpoint: ${testEndpoint}`);

        const response = await fetch(testEndpoint, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${VAPI_API_KEY}`,
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        });

        if (response.ok) {
          const data = await response.json();
          console.log(`✅ Successfully connected to ${testEndpoint}`);
          console.log(`📋 Response:`, data);
          success = true;
          workingEndpoint = baseEndpoint;
          break;
        } else {
          console.warn(`⚠️ Endpoint ${testEndpoint} returned status: ${response.status}`);
        }
      }

      // If we found a working endpoint, break out of the outer loop
      if (success) {
        break;
      }
    } catch (error) {
      console.warn(`⚠️ Error with endpoint ${baseEndpoint}:`, error.message);
    }
  }

  if (!success) {
    console.error('❌ All API endpoints failed. The API key may be invalid.');
    return false;
  }

  return { success, workingEndpoint };
}

/**
 * Test the Vapi assistant ID
 */
async function testAssistantId(workingEndpoint) {
  if (!VAPI_ASSISTANT_ID) {
    console.warn('⚠️ Skipping assistant ID test because no assistant ID is set.');
    return false;
  }

  console.log('🔍 Testing Vapi assistant ID...');
  console.log('📝 Assistant ID:', VAPI_ASSISTANT_ID);

  try {
    console.log(`🔄 Trying to get assistant: ${workingEndpoint}/assistant/${VAPI_ASSISTANT_ID}`);

    const response = await fetch(`${workingEndpoint}/assistant/${VAPI_ASSISTANT_ID}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${VAPI_API_KEY}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    });

    if (response.ok) {
      const assistant = await response.json();
      console.log(`✅ Successfully retrieved assistant: ${assistant.name || assistant.id}`);
      console.log('📋 Assistant details:', JSON.stringify(assistant, null, 2));
      return true;
    } else {
      console.error(`❌ Failed to retrieve assistant. Status: ${response.status}`);
      return false;
    }
  } catch (error) {
    console.error('❌ Error retrieving assistant:', error.message);
    return false;
  }
}

/**
 * Main function
 */
async function main() {
  console.log('🔍 Testing Vapi API key and assistant ID...');

  // Test API key
  const apiKeyResult = await testApiKey();

  if (!apiKeyResult.success) {
    console.error('❌ API key test failed. Please check your API key.');
    process.exit(1);
  }

  // Test assistant ID
  const assistantIdResult = await testAssistantId(apiKeyResult.workingEndpoint);

  if (!assistantIdResult) {
    console.warn('⚠️ Assistant ID test failed. The assistant ID may be invalid or the assistant may not exist.');
    console.log('💡 You may need to create a new assistant or update the VITE_VAPI_DEFAULT_ASSISTANT_ID environment variable.');
  }

  console.log('✅ Tests completed.');
}

// Run the main function
main().catch(error => {
  console.error('❌ Fatal error:', error);
  process.exit(1);
});

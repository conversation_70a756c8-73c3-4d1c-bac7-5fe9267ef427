#!/usr/bin/env node
/**
 * Test script for local MCP server
 *
 * This script tests the local MCP server configured in .cursor/mcp.json.
 * It connects to the local MCP server and lists available tools.
 *
 * Usage:
 *   node scripts/test-local-mcp.js
 */

import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { WebSocketClientTransport } from '@modelcontextprotocol/sdk/client/websocket.js';
import dotenv from 'dotenv';
import WebSocket from 'ws';

// Add WebSocket to global scope for the SDK to use
global.WebSocket = WebSocket;

// Load environment variables from .env file
dotenv.config();

console.log('Testing local MCP server...');

async function testLocalMcp() {
  try {
    // Initialize MCP client
    console.log('\nInitializing MCP client...');
    const mcpClient = new Client({
      name: 'legalscout-test',
      version: '1.0.0',
    });

    // Create WebSocket transport for connection to local MCP server
    console.log('Creating WebSocket transport...');
    const transport = new WebSocketClientTransport({
      url: 'ws://localhost:8080',
    });

    console.log('Connecting to local MCP server...');
    await mcpClient.connect(transport);
    console.log('✅ Connected successfully');

    try {
      // List available tools
      console.log('\nListing available tools...');
      const toolsResult = await mcpClient.listTools();

      if (toolsResult.tools && toolsResult.tools.length > 0) {
        console.log(`✅ Found ${toolsResult.tools.length} tools:`);
        toolsResult.tools.forEach((tool) => {
          console.log(`  - ${tool.name}: ${tool.description || 'No description'}`);
        });
      } else {
        console.log('❌ No tools found');
      }

      // Try to list assistants
      console.log('\nTrying to list assistants...');
      try {
        const assistantsResponse = await mcpClient.callTool({
          name: 'list_assistants',
          arguments: {},
        });

        if (assistantsResponse && assistantsResponse.content) {
          const assistants = assistantsResponse.content;
          if (Array.isArray(assistants) && assistants.length > 0) {
            console.log(`✅ Found ${assistants.length} assistants:`);
            assistants.forEach((assistant) => {
              console.log(`  - ${assistant.name} (${assistant.id})`);
            });
          } else {
            console.log('❌ No assistants found');
          }
        } else {
          console.log('❌ Failed to list assistants');
          console.log('Response:', assistantsResponse);
        }
      } catch (error) {
        console.error('❌ Error listing assistants:', error.message);
      }
    } finally {
      console.log('\nDisconnecting from server...');
      await mcpClient.close();
      console.log('✅ Disconnected');
    }

    console.log('\n✅ Test completed successfully');
  } catch (error) {
    console.error('\n❌ Test failed with error:', error);
    process.exit(1);
  }
}

testLocalMcp();

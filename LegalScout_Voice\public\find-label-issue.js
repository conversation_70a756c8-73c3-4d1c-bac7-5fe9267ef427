/**
 * Find and Fix Specific Label Issue
 *
 * This script specifically targets the single label/input mismatch
 * that the browser is detecting and fixes it.
 */

(function() {
  console.log('🔍 [FindLabelIssue] Starting targeted label issue detection...');

  function findAndFixLabelIssue() {
    // Get all labels with 'for' attributes
    const labels = document.querySelectorAll('label[for]');
    console.log(`🔍 [FindLabelIssue] Found ${labels.length} labels with 'for' attributes`);

    let issuesFound = 0;
    let issuesFixed = 0;

    labels.forEach((label, index) => {
      const forValue = label.getAttribute('for');
      const targetElement = document.getElementById(forValue);
      const labelText = label.textContent.trim();

      console.log(`🔍 [FindLabelIssue] Label ${index + 1}:`);
      console.log(`   - for="${forValue}"`);
      console.log(`   - text="${labelText}"`);
      console.log(`   - target exists: ${!!targetElement}`);

      if (!targetElement) {
        issuesFound++;
        console.error(`❌ [FindLabelIssue] ISSUE FOUND: Label with for="${forValue}" has no target element`);
        console.log(`   - Label text: "${labelText}"`);
        console.log(`   - Label HTML:`, label.outerHTML);

        // Try to find a similar element
        const possibleTargets = document.querySelectorAll('input, textarea, select, button');
        let bestMatch = null;
        let bestScore = 0;

        possibleTargets.forEach(target => {
          if (!target.id) return;

          const targetId = target.id.toLowerCase();
          const forValueLower = forValue.toLowerCase();
          const labelTextLower = labelText.toLowerCase();

          let score = 0;

          // Exact match
          if (targetId === forValueLower) {
            score = 100;
          }
          // Partial match in ID
          else if (targetId.includes(forValueLower) || forValueLower.includes(targetId)) {
            score = 80;
          }
          // Label text matches target ID
          else if (labelTextLower.includes(targetId) || targetId.includes(labelTextLower.replace(/\s+/g, ''))) {
            score = 60;
          }
          // Name attribute match
          else if (target.name && target.name.toLowerCase() === forValueLower) {
            score = 70;
          }
          // Type-based matching
          else if (labelTextLower.includes('email') && target.type === 'email') {
            score = 50;
          }
          else if (labelTextLower.includes('password') && target.type === 'password') {
            score = 50;
          }
          else if (labelTextLower.includes('text') && target.type === 'text') {
            score = 40;
          }

          if (score > bestScore) {
            bestScore = score;
            bestMatch = target;
          }
        });

        if (bestMatch && bestScore >= 40) {
          console.log(`✅ [FindLabelIssue] FIXING: Updating label for="${forValue}" to for="${bestMatch.id}"`);
          console.log(`   - Best match: ${bestMatch.tagName}[id="${bestMatch.id}"][type="${bestMatch.type || 'N/A'}"]`);
          console.log(`   - Match score: ${bestScore}`);

          label.setAttribute('for', bestMatch.id);
          issuesFixed++;
        } else {
          console.warn(`⚠️ [FindLabelIssue] Could not find suitable target for label with for="${forValue}"`);

          // Log all possible targets for manual inspection
          console.log('   - Available form elements:');
          possibleTargets.forEach(target => {
            if (target.id) {
              console.log(`     * ${target.tagName}[id="${target.id}"][type="${target.type || 'N/A'}"]`);
            }
          });
        }
      } else if (!['INPUT', 'TEXTAREA', 'SELECT', 'BUTTON'].includes(targetElement.tagName)) {
        issuesFound++;
        console.error(`❌ [FindLabelIssue] ISSUE FOUND: Label with for="${forValue}" points to non-form element ${targetElement.tagName}`);
        console.log(`   - Target element:`, targetElement.outerHTML);
      } else {
        console.log(`   ✅ Valid association`);
      }
    });

    // Check for form elements without labels
    const formElements = document.querySelectorAll('input, textarea, select');
    formElements.forEach((element, index) => {
      if (element.id) {
        const associatedLabel = document.querySelector(`label[for="${element.id}"]`);
        const parentLabel = element.closest('label');

        if (!associatedLabel && !parentLabel) {
          issuesFound++;
          console.warn(`⚠️ [FindLabelIssue] Form element without label: ${element.tagName}[id="${element.id}"][type="${element.type || 'N/A'}"]`);

          // Try to find a nearby label without 'for' attribute
          const nearbyLabels = document.querySelectorAll('label:not([for])');
          let bestLabel = null;
          let bestDistance = Infinity;

          nearbyLabels.forEach(label => {
            try {
              const labelRect = label.getBoundingClientRect();
              const elementRect = element.getBoundingClientRect();

              const distance = Math.sqrt(
                Math.pow(labelRect.left - elementRect.left, 2) +
                Math.pow(labelRect.top - elementRect.top, 2)
              );

              if (distance < bestDistance && distance < 200) {
                bestDistance = distance;
                bestLabel = label;
              }
            } catch (e) {
              // Skip if element is not visible
            }
          });

          if (bestLabel) {
            console.log(`✅ [FindLabelIssue] FIXING: Associating nearby label "${bestLabel.textContent.trim()}" with element id="${element.id}"`);
            bestLabel.setAttribute('for', element.id);
            issuesFixed++;
          }
        }
      }
    });

    console.log(`🔍 [FindLabelIssue] Summary:`);
    console.log(`   - Issues found: ${issuesFound}`);
    console.log(`   - Issues fixed: ${issuesFixed}`);
    console.log(`   - Remaining issues: ${issuesFound - issuesFixed}`);

    return {
      issuesFound,
      issuesFixed,
      remainingIssues: issuesFound - issuesFixed
    };
  }

  // Run immediately
  const result = findAndFixLabelIssue();

  // Run again after a delay to catch dynamic content
  setTimeout(() => {
    console.log('🔍 [FindLabelIssue] Running second pass...');
    findAndFixLabelIssue();
  }, 1000);

  // Run again after React components load
  setTimeout(() => {
    console.log('🔍 [FindLabelIssue] Running third pass...');
    findAndFixLabelIssue();
  }, 2000);

  // Set up continuous monitoring
  let checkCount = 0;
  const maxChecks = 20;

  const continuousCheck = setInterval(() => {
    checkCount++;
    console.log(`🔍 [FindLabelIssue] Continuous check #${checkCount}...`);

    const result = findAndFixLabelIssue();

    if (result.remainingIssues === 0 || checkCount >= maxChecks) {
      console.log(`🔍 [FindLabelIssue] Stopping continuous checks. Issues remaining: ${result.remainingIssues}`);
      clearInterval(continuousCheck);
    }
  }, 3000); // Check every 3 seconds

  // Also monitor for DOM changes
  const observer = new MutationObserver((mutations) => {
    let shouldCheck = false;

    mutations.forEach((mutation) => {
      if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === 1) { // Element node
            if (node.tagName === 'LABEL' ||
                node.tagName === 'INPUT' ||
                node.tagName === 'TEXTAREA' ||
                node.tagName === 'SELECT' ||
                node.querySelector('label, input, textarea, select')) {
              shouldCheck = true;
            }
          }
        });
      }
    });

    if (shouldCheck) {
      console.log('🔍 [FindLabelIssue] DOM mutation detected, checking for label issues...');
      setTimeout(findAndFixLabelIssue, 100);
    }
  });

  observer.observe(document.documentElement, {
    childList: true,
    subtree: true
  });

  console.log('🔍 [FindLabelIssue] Targeted label issue detection completed with continuous monitoring');
})();

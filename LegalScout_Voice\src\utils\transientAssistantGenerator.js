/**
 * Transient Assistant Generator
 * 
 * Creates temporary assistant configurations for emergency fallback scenarios
 * while ensuring proper call attribution to attorneys for briefs page.
 */

/**
 * Generate a transient assistant configuration with proper attribution
 * @param {Object} attorneyData - Attorney data for personalization
 * @param {string} subdomain - Subdomain for attribution fallback
 * @returns {Object} - Transient assistant configuration
 */
export function generateTransientAssistant(attorneyData, subdomain = null) {
  console.log('[TransientAssistant] Generating emergency transient assistant');
  
  // Ensure we have attorney data for attribution
  if (!attorneyData?.id) {
    console.error('[TransientAssistant] No attorney ID provided - call attribution will fail');
    throw new Error('Attorney ID required for transient assistant attribution');
  }

  // Create base assistant configuration
  const transientConfig = {
    name: `${attorneyData.firm_name || 'LegalScout'} - Emergency Assistant`,
    
    // LLM Configuration
    model: {
      provider: "openai",
      model: "gpt-4o",
      temperature: 0.7,
      messages: [{
        role: "system",
        content: generateSystemPrompt(attorneyData)
      }]
    },

    // Voice Configuration
    voice: {
      provider: "11labs",
      voiceId: attorneyData.voice_id || "sarah",
      model: "eleven_turbo_v2_5"
    },

    // Transcriber Configuration
    transcriber: {
      provider: "deepgram",
      model: "nova-3"
    },

    // First Message
    firstMessage: attorneyData.welcome_message || 
      `Hello! I'm the AI assistant for ${attorneyData.firm_name || 'our law firm'}. How can I help you today?`,

    // CRITICAL: Attribution metadata for webhook
    metadata: {
      attorney_id: attorneyData.id,
      firm_name: attorneyData.firm_name,
      subdomain: subdomain,
      is_transient: true,
      created_at: new Date().toISOString(),
      attribution_method: 'transient_metadata'
    },

    // Server URL for webhooks (ensures proper routing)
    server: {
      url: `${process.env.NEXT_PUBLIC_BASE_URL || 'https://dashboard.legalscout.net'}/api/webhook/vapi-call`,
      timeoutSeconds: 20
    }
  };

  console.log('[TransientAssistant] Generated transient assistant with attribution:', {
    attorney_id: attorneyData.id,
    firm_name: attorneyData.firm_name,
    subdomain: subdomain
  });

  return transientConfig;
}

/**
 * Generate system prompt for transient assistant
 * @param {Object} attorneyData - Attorney data for personalization
 * @returns {string} - System prompt
 */
function generateSystemPrompt(attorneyData) {
  const firmName = attorneyData.firm_name || 'our law firm';
  const practiceAreas = attorneyData.practice_areas?.join(', ') || 'general legal matters';
  
  return `You are an AI assistant for ${firmName}, a law firm specializing in ${practiceAreas}.

Your role is to:
1. Gather initial information from potential clients
2. Understand their legal needs and urgency
3. Provide general legal information (not specific legal advice)
4. Schedule consultations with our attorneys when appropriate

Important guidelines:
- Be professional, empathetic, and helpful
- Ask clarifying questions to understand their situation
- Explain that you're an AI assistant, not a licensed attorney
- For specific legal advice, direct them to schedule a consultation
- Keep responses concise and focused
- Gather contact information for follow-up

This is an emergency backup assistant. Please provide excellent service while we resolve any technical issues.`;
}

/**
 * Enhanced fallback for components when API calls fail
 * @param {Object} attorneyData - Attorney data
 * @param {string} subdomain - Subdomain for attribution
 * @returns {Array} - Array with single transient assistant
 */
export function generateTransientAssistantList(attorneyData, subdomain = null) {
  try {
    const transientAssistant = generateTransientAssistant(attorneyData, subdomain);
    
    // Return in the format expected by components
    return [{
      ...transientAssistant,
      id: 'transient-' + Date.now(), // Temporary ID for UI purposes
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      isTransient: true
    }];
  } catch (error) {
    console.error('[TransientAssistant] Error generating transient assistant:', error);
    return [];
  }
}

/**
 * Check if an assistant ID is from a transient assistant
 * @param {string} assistantId - Assistant ID to check
 * @returns {boolean} - Whether the ID is transient
 */
export function isTransientAssistantId(assistantId) {
  return assistantId && assistantId.startsWith('transient-');
}

export default {
  generateTransientAssistant,
  generateTransientAssistantList,
  isTransientAssistantId
};

# Vapi Implementation Guidelines

This document provides guidelines for implementing Vapi integration in LegalScout Voice, focusing on call functionality and attorney notifications.

## Project Context

LegalScout Voice is a web application that provides AI-powered legal assistant services through voice interactions. The platform allows attorneys to configure their own AI assistants that can interact with potential clients, gather information, and provide legal guidance.

## Implementation Goals

1. **Core Vapi Integration**
   - Create and manage assistants using current Vapi API v2
   - Implement assistant-level configuration and branding
   - Sync assistant settings between LegalScout and Vapi

2. **Call Functionality**
   - Make calls using assistant-specific configurations
   - Handle call state management and real-time events
   - Implement modern call UI components with Vapi Web SDK 2.3.1

3. **Attorney Notifications**
   - Send SMS notifications to attorneys about ongoing sessions
   - Generate secure call control links with JWT tokens
   - Allow attorneys to monitor, control, and take over calls

4. **Assistant Management**
   - Support multiple assistants per attorney
   - Enable assistant-level subdomain routing
   - Provide per-assistant UI customization

## Technical Approach

### Transport Methods

We will use a dual approach for Vapi integration:

1. **SSE (Server-Sent Events)** for real-time operations
   - Call monitoring and control
   - Real-time transcript updates
   - In-call tools and interventions

2. **Streamable HTTP** for administrative operations
   - Assistant creation and management
   - Call scheduling and batch operations
   - Operations that don't require real-time updates

### Implementation Components

1. **Enhanced Vapi Service Layer**
   - `VapiService.js` - Main service for SDK interactions
   - `EnhancedVapiMcpService.js` - MCP integration for programmatic control
   - `VapiProperIntegration.js` - Fallback direct API implementation
   - Supports both SSE and Streamable HTTP transports

2. **Assistant Management System**
   - Automatic assistant creation with current API v2 patterns
   - Assistant-to-attorney mapping in Supabase
   - Per-assistant UI configuration and branding
   - Subdomain routing to specific assistants

3. **Call Control System**
   - Secure JWT token generation for call control links
   - Real-time call monitoring via webhooks
   - Attorney notification system with SMS integration
   - Call intervention and takeover capabilities

4. **Webhook Integration**
   - Comprehensive webhook handling for all call events
   - Structured data extraction and storage
   - Real-time transcript processing
   - Call analytics and reporting

## Implementation Pitfalls to Avoid

1. **API Key Management**
   - Don't expose private API keys in client-side code
   - Use environment variables for API key storage
   - Use public keys for client-side operations and private keys for server-side operations

2. **Connection Handling**
   - Don't assume connections will always succeed
   - Implement proper error handling and retries
   - Have fallback mechanisms for when connections fail

3. **Security Concerns**
   - Don't generate insecure call control links
   - Always include expiration times in tokens
   - Verify authentication before granting access to call control

4. **Data Flow**
   - Don't try to sync data from Vapi to Supabase
   - Follow the one-way sync pattern: UI → Supabase → Vapi
   - Always save to Supabase first, then sync to Vapi

5. **UI Responsiveness**
   - Don't block the UI during Vapi operations
   - Implement proper loading states
   - Use asynchronous operations for all Vapi interactions

6. **Attorney Subdomain Headers**
   - Don't show duplicate headers on attorney subdomains
   - Hide main app header for attorney subdomains to avoid duplication with iframe header
   - Ensure iframe content includes LegalScout navigation for embedding functionality
   - Use conditional rendering based on subdomain detection for clean embeddable content

## Implementation Priorities

1. **First Priority: Call Functionality**
   - Implement call creation using attorney's assistant
   - Set up call state management and events
   - Create basic call UI components

2. **Second Priority: SMS Notifications**
   - Implement SMS sending functionality
   - Create secure call control links
   - Set up message templates

3. **Third Priority: Call Control Interface**
   - Build call control page
   - Implement authentication using tokens
   - Add call monitoring and control features

## Testing Strategy

1. **Unit Testing**
   - Test individual components in isolation
   - Mock Vapi API responses
   - Verify correct behavior with different inputs

2. **Integration Testing**
   - Test the interaction between components
   - Verify data flow from UI to Supabase to Vapi
   - Test error handling and fallback mechanisms

3. **End-to-End Testing**
   - Test the complete flow from call creation to attorney notification to call control
   - Verify that all components work together correctly
   - Test with real Vapi API calls in a staging environment

## Resources

- [Vapi Documentation](https://docs.vapi.ai/)
- [Vapi MCP Server Documentation](https://docs.vapi.ai/sdk/mcp-server)
- [VapiBlocks UI Library](https://www.vapiblocks.com/)
- [Vapi Blog: MCP Client](https://vapi.ai/blog/introducing-vapi-mcp-client)
- [Vapi Blog: MCP Server](https://vapi.ai/blog/bring-vapi-voice-agents-into-your-workflows-with-the-new-vapi-mcp-server)

## Next Steps

After implementing the core functionality, we can explore additional features:

1. **Advanced Call Control**
   - Real-time guidance to the AI assistant
   - Call recording and analysis
   - Custom call flows based on attorney preferences

2. **Integration with Other Systems**
   - CRM integration for lead tracking
   - Calendar integration for scheduling
   - Document generation based on call content

3. **Analytics and Reporting**
   - Call statistics and metrics
   - Conversion tracking
   - Performance analysis

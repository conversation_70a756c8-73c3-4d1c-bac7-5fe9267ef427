#!/usr/bin/env node

/**
 * Quick Vapi API Key Test Script
 * 
 * This script can be run in any environment to quickly test Vapi API key configuration.
 * Usage: node scripts/test-vapi-keys.js
 */

import fetch from 'node-fetch';

// Expected keys (from your configuration)
const EXPECTED_PUBLIC_KEY = '310f0d43-27c2-47a5-a76d-e55171d024f7';
const EXPECTED_SECRET_KEY = '6734febc-fc65-4669-93b0-929b31ff6564';

/**
 * Get environment variable from multiple sources
 */
function getEnvVar(name) {
  // Try process.env first
  if (process.env[name]) {
    return process.env[name];
  }
  
  // Try different variations
  const variations = [
    name,
    `VITE_${name}`,
    name.replace('VITE_', ''),
    name.replace('VAPI_SECRET_KEY', 'VAPI_TOKEN'),
    name.replace('VAPI_SECRET_KEY', 'VAPI_PRIVATE_KEY')
  ];
  
  for (const variation of variations) {
    if (process.env[variation]) {
      return process.env[variation];
    }
  }
  
  return null;
}

/**
 * Test API key against Vapi API
 */
async function testApiKey(key, keyType, operation = 'phone-number') {
  console.log(`\n🔑 Testing ${keyType} key: ${key ? key.substring(0, 8) + '...' : 'NOT FOUND'}`);
  
  if (!key) {
    console.log(`❌ ${keyType} key is missing`);
    return { success: false, error: 'Key missing' };
  }
  
  try {
    const response = await fetch(`https://api.vapi.ai/${operation}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${key}`,
        'Content-Type': 'application/json'
      }
    });
    
    const responseText = await response.text();
    let responseData = null;
    
    try {
      responseData = JSON.parse(responseText);
    } catch (e) {
      responseData = responseText;
    }
    
    if (response.ok) {
      console.log(`✅ ${keyType} key works! Status: ${response.status}`);
      if (Array.isArray(responseData)) {
        console.log(`   Found ${responseData.length} items`);
      }
      return { success: true, status: response.status, data: responseData };
    } else {
      console.log(`❌ ${keyType} key failed! Status: ${response.status}`);
      console.log(`   Error: ${responseText}`);
      return { success: false, status: response.status, error: responseData };
    }
  } catch (error) {
    console.log(`❌ ${keyType} key test failed with network error: ${error.message}`);
    return { success: false, error: error.message };
  }
}

/**
 * Main diagnostic function
 */
async function runDiagnostics() {
  console.log('🔍 Vapi API Key Diagnostics');
  console.log('=' .repeat(40));
  
  // Environment info
  console.log('\n🌍 Environment Information:');
  console.log(`   NODE_ENV: ${process.env.NODE_ENV || 'not set'}`);
  console.log(`   Platform: ${process.platform}`);
  console.log(`   Node Version: ${process.version}`);
  
  // Check environment variables
  console.log('\n📋 Environment Variables:');
  const envVars = [
    'VITE_VAPI_PUBLIC_KEY',
    'VITE_VAPI_SECRET_KEY',
    'VAPI_PUBLIC_KEY',
    'VAPI_SECRET_KEY',
    'VAPI_TOKEN',
    'VAPI_PRIVATE_KEY'
  ];
  
  const foundVars = {};
  envVars.forEach(varName => {
    const value = getEnvVar(varName);
    foundVars[varName] = value;
    console.log(`   ${varName}: ${value ? value.substring(0, 8) + '...' : 'NOT SET'}`);
  });
  
  // Determine which keys to use
  const publicKey = foundVars.VITE_VAPI_PUBLIC_KEY || foundVars.VAPI_PUBLIC_KEY || EXPECTED_PUBLIC_KEY;
  const secretKey = foundVars.VITE_VAPI_SECRET_KEY || foundVars.VAPI_SECRET_KEY || foundVars.VAPI_TOKEN || foundVars.VAPI_PRIVATE_KEY || EXPECTED_SECRET_KEY;
  
  console.log('\n🎯 Selected Keys:');
  console.log(`   Public Key: ${publicKey ? publicKey.substring(0, 8) + '...' : 'NONE'}`);
  console.log(`   Secret Key: ${secretKey ? secretKey.substring(0, 8) + '...' : 'NONE'}`);
  
  // Validate keys match expected values
  console.log('\n✅ Key Validation:');
  const publicKeyValid = publicKey === EXPECTED_PUBLIC_KEY;
  const secretKeyValid = secretKey === EXPECTED_SECRET_KEY;
  
  console.log(`   Public Key Valid: ${publicKeyValid ? '✅' : '❌'}`);
  console.log(`   Secret Key Valid: ${secretKeyValid ? '✅' : '❌'}`);
  
  if (!publicKeyValid) {
    console.log(`   Expected Public: ${EXPECTED_PUBLIC_KEY.substring(0, 8)}...`);
    console.log(`   Actual Public:   ${publicKey ? publicKey.substring(0, 8) + '...' : 'NONE'}`);
  }
  
  if (!secretKeyValid) {
    console.log(`   Expected Secret: ${EXPECTED_SECRET_KEY.substring(0, 8)}...`);
    console.log(`   Actual Secret:   ${secretKey ? secretKey.substring(0, 8) + '...' : 'NONE'}`);
  }
  
  // Test API connectivity
  console.log('\n🌐 API Connectivity Tests:');
  
  // Test 1: Public key for phone numbers (should fail)
  console.log('\n📞 Test 1: Public key for phone numbers (should fail with 401)');
  const publicTest = await testApiKey(publicKey, 'PUBLIC', 'phone-number');
  
  // Test 2: Secret key for phone numbers (should succeed)
  console.log('\n📞 Test 2: Secret key for phone numbers (should succeed)');
  const secretTest = await testApiKey(secretKey, 'SECRET', 'phone-number');
  
  // Test 3: Public key for assistants (might work for read operations)
  console.log('\n🤖 Test 3: Public key for assistants');
  const publicAssistantTest = await testApiKey(publicKey, 'PUBLIC', 'assistant');
  
  // Test 4: Secret key for assistants (should work)
  console.log('\n🤖 Test 4: Secret key for assistants');
  const secretAssistantTest = await testApiKey(secretKey, 'SECRET', 'assistant');
  
  // Summary and recommendations
  console.log('\n📊 Summary:');
  console.log('=' .repeat(40));
  
  const issues = [];
  const successes = [];
  
  if (!publicKeyValid) issues.push('Public key does not match expected value');
  if (!secretKeyValid) issues.push('Secret key does not match expected value');
  
  if (publicTest.success) issues.push('Public key unexpectedly worked for phone numbers');
  if (!secretTest.success) issues.push('Secret key failed for phone numbers (main issue!)');
  
  if (secretTest.success) successes.push('Secret key works for phone numbers');
  if (publicAssistantTest.success) successes.push('Public key works for assistants');
  
  console.log('\n✅ Successes:');
  if (successes.length === 0) {
    console.log('   None - this is the problem!');
  } else {
    successes.forEach(success => console.log(`   ✅ ${success}`));
  }
  
  console.log('\n❌ Issues:');
  if (issues.length === 0) {
    console.log('   None detected - configuration looks good!');
  } else {
    issues.forEach(issue => console.log(`   ❌ ${issue}`));
  }
  
  // Specific recommendations
  console.log('\n💡 Recommendations:');
  
  if (!secretTest.success) {
    console.log('   🎯 MAIN ISSUE: Secret key is not working for phone numbers');
    console.log('   📝 Action: Check that VITE_VAPI_SECRET_KEY is set correctly in production');
    console.log('   📝 Action: Verify the secret key value matches: 6734febc-fc65-4669-93b0-929b31ff6564');
    
    if (secretTest.status === 401) {
      console.log('   📝 Action: The 401 error suggests wrong key or key type mismatch');
    }
  }
  
  if (!publicKeyValid || !secretKeyValid) {
    console.log('   📝 Action: Update environment variables with correct key values');
  }
  
  if (process.env.NODE_ENV === 'production' || !process.env.NODE_ENV) {
    console.log('   📝 Action: Ensure environment variables are set in your deployment platform');
    console.log('   📝 Action: For Vercel, check Environment Variables in project settings');
  }
  
  console.log('\n🏁 Diagnostic Complete');
  console.log('=' .repeat(40));
}

// Run diagnostics
runDiagnostics().catch(error => {
  console.error('❌ Diagnostic script failed:', error);
  process.exit(1);
});

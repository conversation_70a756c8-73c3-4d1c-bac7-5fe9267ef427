/**
 * Test Vapi Fix
 * 
 * Simple test to verify the orphaned assistant fix works
 */

const ASSISTANT_ID = 'f9b97d13-f9c4-40af-a660-62ba5925ff2a';
const VAPI_PRIVATE_KEY = '6734febc-fc65-4669-93b0-929b31ff6564';

async function testVapiFix() {
  try {
    console.log('🧪 Testing Vapi Fix for Orphaned Assistant...\n');
    
    // Test direct API call
    console.log('📡 Testing Direct API Call...');
    const response = await fetch(`https://api.vapi.ai/assistant/${ASSISTANT_ID}`, {
      headers: {
        'Authorization': `Bearer ${VAPI_PRIVATE_KEY}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const assistantData = await response.json();
    
    console.log('✅ Assistant Data Retrieved:');
    console.log('- ID:', assistantData.id);
    console.log('- Name:', assistantData.name);
    console.log('- First Message:', assistantData.firstMessage || 'MISSING');
    console.log('- System Instructions:', assistantData.model?.messages?.[0]?.content ? 'PRESENT' : 'MISSING');
    console.log('- Voice:', `${assistantData.voice?.provider}/${assistantData.voice?.voiceId}`);
    console.log('- Model:', `${assistantData.model?.provider}/${assistantData.model?.model}`);
    
    // Check completeness
    const hasFirstMessage = !!assistantData.firstMessage;
    const hasInstructions = !!assistantData.model?.messages?.[0]?.content;
    const hasVoice = !!assistantData.voice?.voiceId;
    const hasModel = !!assistantData.model?.model;
    
    console.log('\n🎯 Data Completeness Check:');
    console.log('- First Message:', hasFirstMessage ? '✅ PRESENT' : '❌ MISSING');
    console.log('- System Instructions:', hasInstructions ? '✅ PRESENT' : '❌ MISSING');
    console.log('- Voice Configuration:', hasVoice ? '✅ PRESENT' : '❌ MISSING');
    console.log('- Model Configuration:', hasModel ? '✅ PRESENT' : '❌ MISSING');
    
    const isComplete = hasFirstMessage && hasInstructions && hasVoice && hasModel;
    
    console.log('\n🏆 RESULT:', isComplete ? '✅ COMPLETE - UI should load properly!' : '❌ INCOMPLETE - Issues remain');
    
    if (isComplete) {
      console.log('\n🎉 SUCCESS: The orphaned assistant problem is resolved!');
      console.log('Your UI should now properly load:');
      console.log(`- Welcome Message: "${assistantData.firstMessage}"`);
      console.log(`- System Instructions: "${assistantData.model.messages[0].content.substring(0, 100)}..."`);
      console.log(`- Voice: ${assistantData.voice.provider}/${assistantData.voice.voiceId}`);
    } else {
      console.log('\n⚠️ The assistant still has missing data. This explains why your UI is not loading values.');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testVapiFix();

const c=t=>({...{firm_name:null,title_text:null,name:null,subdomain:null,practice_areas:[],practice_description:null,primary_color:null,secondary_color:null,button_color:null,background_color:null,background_opacity:null,button_text:null,button_opacity:null,practice_area_background_opacity:null,text_background_color:null,logo_url:null,profile_image:null,button_image:null,welcome_message:null,information_gathering:null,vapi_instructions:null,vapi_context:null,vapi_assistant_id:null,office_address:null,phone:null,scheduling_link:null,email:null,voice_id:null,ai_model:null,is_active:!0,custom_fields:[],summary_prompt:null,structured_data_prompt:null,structured_data_schema:null},firm_name:t.firmName,title_text:t.titleText,name:t.attorneyName,subdomain:t.subdomain,practice_areas:t.practiceAreas||[],practice_description:t.practiceDescription,primary_color:t.primaryColor,secondary_color:t.secondaryColor,button_color:t.buttonColor,background_color:t.backgroundColor,background_opacity:t.backgroundOpacity,button_text:t.buttonText,button_opacity:t.buttonOpacity,practice_area_background_opacity:t.practiceAreaBackgroundOpacity,text_background_color:t.textBackgroundColor,logo_url:t.logoUrl,profile_image:t.mascot,button_image:t.buttonImageUrl,welcome_message:t.welcomeMessage,information_gathering:t.informationGathering,vapi_instructions:t.vapiInstructions,vapi_context:t.vapiContext,office_address:t.address,phone:t.phone,scheduling_link:t.schedulingLink,email:t.email,custom_fields:t.customFields,summary_prompt:t.summaryPrompt,structured_data_prompt:t.structuredDataPrompt,structured_data_schema:t.structuredDataSchema}),u=t=>({firmName:t.assistant_name||t.titleText||t.title_text||t.firmName||t.firm_name||"Your Law Firm",titleText:t.assistant_name||t.titleText||t.title_text||t.firmName||t.firm_name||"",actualFirmName:t.firmName||t.firm_name||"Your Law Firm",attorneyName:t.attorneyName||t.name||"Your Name",practiceAreas:t.practiceAreas||t.practice_areas||[],practiceDescription:t.practiceDescription||t.practice_description||"Your AI legal assistant is ready to help",primaryColor:t.primaryColor||t.primary_color||"#4B74AA",secondaryColor:t.secondaryColor||t.secondary_color||"#2C3E50",buttonColor:t.buttonColor||t.button_color||"#D85722",backgroundColor:t.backgroundColor||t.background_color||"#1a1a1a",backgroundOpacity:t.backgroundOpacity!==void 0?t.backgroundOpacity:t.background_opacity!==void 0?t.background_opacity:.9,buttonText:t.buttonText||t.button_text||"Start Consultation",buttonOpacity:t.buttonOpacity!==void 0?t.buttonOpacity:t.button_opacity!==void 0?t.button_opacity:1,practiceAreaBackgroundOpacity:t.practiceAreaBackgroundOpacity!==void 0?t.practiceAreaBackgroundOpacity:t.practice_area_background_opacity!==void 0?t.practice_area_background_opacity:.1,textBackgroundColor:t.textBackgroundColor||t.text_background_color||"#634C38",theme:t.theme||"dark",logoUrl:t.logoUrl||t.logo_url||"",mascot:t.mascot||t.profile_image||"",buttonImageUrl:t.buttonImageUrl||t.button_image||"",welcomeMessage:t.welcomeMessage||t.welcome_message||"Hello! I'm Scout, your legal assistant. How can I help you today?",informationGathering:t.informationGathering||t.information_gathering||"Tell me about your situation, and I'll help find the right solution for you.",vapiInstructions:t.vapiInstructions||t.vapi_instructions||"",vapiContext:t.vapiContext||t.vapi_context||"",vapiAssistantId:t.vapiAssistantId||t.vapi_assistant_id||null,vapi_assistant_id:t.vapiAssistantId||t.vapi_assistant_id||null,address:t.address||t.office_address||"",phone:t.phone||"",schedulingLink:t.schedulingLink||t.scheduling_link||"",customFields:t.customFields||t.custom_fields||[],summaryPrompt:t.summaryPrompt||t.summary_prompt||"",structuredDataPrompt:t.structuredDataPrompt||t.structured_data_prompt||"",structuredDataSchema:t.structuredDataSchema||t.structured_data_schema||null}),o=t=>{let a=null;if(t.custom_fields)try{const s=typeof t.custom_fields=="string"?JSON.parse(t.custom_fields):t.custom_fields;typeof window.generateStructuredDataSchema=="function"?a=window.generateStructuredDataSchema(s):(a={type:"object",properties:{clientName:{type:"string",description:"The client's full name"},contactInfo:{type:"object",properties:{email:{type:"string",description:"Client's email address"},phone:{type:"string",description:"Client's phone number"}},description:"Client's contact information"},legalIssue:{type:"string",description:"The primary legal issue or concern"},practiceArea:{type:"string",description:"The relevant practice area (e.g., Personal Injury, Family Law)"}},required:["legalIssue","practiceArea"]},Array.isArray(s)&&s.forEach(r=>{r.name&&(a.properties[r.name]={type:r.type||"string",description:r.description||r.name},r.type==="enum"&&r.options?.length>0&&(a.properties[r.name].enum=r.options),r.required&&(a.required||(a.required=[]),a.required.push(r.name)))}))}catch(s){console.error("Error generating schema from custom fields:",s)}const e={assistantName:`${t.firm_name} Legal Assistant`,firstMessage:t.welcome_message||"Hello, I'm your legal assistant. How can I help you today?",instructions:t.vapi_instructions||`You are a legal assistant for ${t.firm_name}. Help potential clients understand their legal needs and collect relevant information for consultation.`,voiceId:t.voice_id||"sarah",model:t.ai_model||"gpt-4o"};return(t.summary_prompt||t.structured_data_prompt||a)&&(e.analysis={},t.summary_prompt&&(e.analysis.summary={prompt:t.summary_prompt}),(t.structured_data_prompt||a)&&(e.analysis.structuredData={},t.structured_data_prompt&&(e.analysis.structuredData.prompt=t.structured_data_prompt),a&&(e.analysis.structuredData.schema=a))),e};export{u as mapDatabaseToPreview,o as mapDatabaseToVapi,c as mapPreviewToDatabase};

/**
 * Restore Essential Files
 * 
 * Restores only the essential files needed for LegalScout to function properly
 */

import fs from 'fs';
import path from 'path';

const publicDir = path.resolve(process.cwd(), 'public');

// Essential files that must exist for the app to work
const essentialFiles = {
  // Core Vapi Integration
  'vm2-eval-polyfill.js': `// VM2 Eval Polyfill - DISABLED for CSP compliance
(function() {
  console.log('🔧 [VM2] Eval polyfill disabled for CSP compliance');
  console.log('✅ [VM2] CSP-safe mode active');
})();`,

  // Production CORS Fix
  'production-cors-fix.js': `// Production CORS Fix
(function() {
  console.log('🌐 [CORS] Applying production CORS fixes...');
  
  // Fix CORS issues in production
  if (typeof window !== 'undefined') {
    const originalFetch = window.fetch;
    
    window.fetch = function(url, options = {}) {
      // Add CORS headers for production
      const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, apikey, X-Client-Info',
        ...options.headers
      };
      
      return originalFetch(url, { ...options, headers });
    };
    
    console.log('✅ [CORS] Production CORS fixes applied');
  }
})();`,

  // Production CSP Eval Fix - DISABLED for security
  'production-csp-eval-fix.js': `// Production CSP Eval Fix - DISABLED for security compliance
(function() {
  console.log('🛡️ [CSP] CSP eval fixes disabled for security compliance');
  console.log('✅ [CSP] Using CSP-safe alternatives');
})();`,
})();`,

  // Dashboard Iframe Manager
  'dashboard-iframe-manager.js': `// Dashboard Iframe Manager
(function() {
  console.log('🖼️ [IFRAME] Initializing dashboard iframe manager...');
  
  // Handle iframe communication
  window.addEventListener('message', function(event) {
    if (event.origin !== window.location.origin) return;
    
    if (event.data.type === 'IFRAME_READY') {
      console.log('✅ [IFRAME] Dashboard iframe ready');
    }
  });
  
  // Iframe utilities
  window.iframeManager = {
    postMessage: function(message, targetOrigin = '*') {
      const iframes = document.querySelectorAll('iframe');
      iframes.forEach(iframe => {
        if (iframe.contentWindow) {
          iframe.contentWindow.postMessage(message, targetOrigin);
        }
      });
    }
  };
  
  console.log('✅ [IFRAME] Dashboard iframe manager ready');
})();`,

  // Clean Auth Solution
  'clean-auth-solution.js': `// Clean Auth Solution
(function() {
  console.log('🔐 [AUTH] Applying clean authentication solution...');
  
  // Remove conflicting fetch interceptors
  if (window.originalFetch && typeof window.originalFetch === 'function') {
    window.fetch = window.originalFetch;
    console.log('✅ [AUTH] Restored original fetch');
  }
  
  // Clean auth state
  window.authManager = {
    clearState: function() {
      // Clear any conflicting auth state
      delete window.supabaseAuthOverride;
      delete window.authInterceptor;
      console.log('✅ [AUTH] Auth state cleared');
    }
  };
  
  console.log('✅ [AUTH] Clean auth solution applied');
})();`,

  // System Test Integration
  'system-test-integration.js': `// System Test Integration
(function() {
  console.log('🧪 [TEST] Initializing system test integration...');
  
  // Basic system health check
  window.systemTest = {
    checkHealth: function() {
      const checks = {
        react: typeof React !== 'undefined',
        supabase: typeof window.supabase !== 'undefined',
        vapi: typeof window.vapi !== 'undefined' || typeof window.VITE_VAPI_PUBLIC_KEY !== 'undefined'
      };
      
      console.log('🧪 [TEST] System health:', checks);
      return checks;
    }
  };
  
  console.log('✅ [TEST] System test integration ready');
})();`
};

function restoreEssentialFiles() {
  console.log('🔄 Restoring essential files...');
  
  if (!fs.existsSync(publicDir)) {
    fs.mkdirSync(publicDir, { recursive: true });
  }
  
  let restoredCount = 0;
  
  for (const [filename, content] of Object.entries(essentialFiles)) {
    const filePath = path.join(publicDir, filename);
    
    try {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Restored: ${filename}`);
      restoredCount++;
    } catch (error) {
      console.error(`❌ Failed to restore ${filename}:`, error.message);
    }
  }
  
  console.log(`\n📊 Restoration Summary:`);
  console.log(`   ✅ Restored: ${restoredCount} essential files`);
  console.log(`   🎯 Essential files restoration complete!`);
}

// Run the restoration
try {
  restoreEssentialFiles();
} catch (error) {
  console.error('❌ Error during restoration:', error.message);
  process.exit(1);
}

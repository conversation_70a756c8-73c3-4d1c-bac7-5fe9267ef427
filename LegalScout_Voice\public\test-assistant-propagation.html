<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Assistant Propagation Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            border-left: 4px solid;
        }
        .success {
            background-color: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        .warning {
            background-color: #fff3cd;
            border-color: #ffc107;
            color: #856404;
        }
        .info {
            background-color: #d1ecf1;
            border-color: #17a2b8;
            color: #0c5460;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .log-container {
            max-height: 400px;
            overflow-y: auto;
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
        select {
            width: 100%;
            padding: 8px;
            margin: 10px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>🧪 Assistant Propagation Test Suite</h1>
    <p>This tool tests whether assistant variables properly propagate from dropdown selection through UI to Vapi services.</p>

    <div class="test-container">
        <h2>📋 Test Controls</h2>
        <button onclick="runAllTests()" id="runAllBtn">Run All Tests</button>
        <button onclick="clearLogs()" id="clearBtn">Clear Logs</button>
        <button onclick="testDropdownPopulation()" id="testDropdownBtn">Test Dropdown</button>
        <button onclick="testAssistantSwitch()" id="testSwitchBtn">Test Switch</button>
    </div>

    <div class="test-container">
        <h2>🎯 Assistant Selection Test</h2>
        <label for="assistantSelect">Select Assistant:</label>
        <select id="assistantSelect" onchange="onAssistantChange()">
            <option value="">Loading assistants...</option>
        </select>
        <div id="assistantDetails" class="test-result info" style="display: none;">
            <strong>Selected Assistant Details:</strong>
            <div id="assistantInfo"></div>
        </div>
    </div>

    <div class="test-container">
        <h2>📊 Test Results</h2>
        <div id="testResults"></div>
    </div>

    <div class="test-container">
        <h2>📝 Test Logs</h2>
        <div id="logContainer" class="log-container"></div>
    </div>

    <script>
        let testResults = [];
        let currentAssistants = [];

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
            
            const logContainer = document.getElementById('logContainer');
            const logElement = document.createElement('div');
            logElement.textContent = logEntry;
            logElement.style.color = type === 'error' ? '#dc3545' : type === 'success' ? '#28a745' : '#333';
            logContainer.appendChild(logElement);
            logContainer.scrollTop = logContainer.scrollHeight;
            
            console.log(logEntry);
        }

        function addTestResult(testName, success, message) {
            testResults.push({ testName, success, message });
            updateTestResults();
        }

        function updateTestResults() {
            const resultsContainer = document.getElementById('testResults');
            resultsContainer.innerHTML = '';
            
            testResults.forEach(result => {
                const resultDiv = document.createElement('div');
                resultDiv.className = `test-result ${result.success ? 'success' : 'error'}`;
                resultDiv.innerHTML = `
                    <strong>${result.testName}:</strong> 
                    ${result.success ? '✅ PASS' : '❌ FAIL'} - ${result.message}
                `;
                resultsContainer.appendChild(resultDiv);
            });
        }

        function clearLogs() {
            document.getElementById('logContainer').innerHTML = '';
            document.getElementById('testResults').innerHTML = '';
            testResults = [];
            log('Logs cleared');
        }

        async function testDropdownPopulation() {
            log('🔍 Testing dropdown population...');
            
            try {
                // Check if we're in the LegalScout dashboard context
                if (typeof window.vapiAssistantService === 'undefined') {
                    throw new Error('vapiAssistantService not available - run this test in the dashboard context');
                }
                
                const assistants = await window.vapiAssistantService.getAllAssistants();
                currentAssistants = assistants;
                
                const select = document.getElementById('assistantSelect');
                select.innerHTML = '<option value="">Select an assistant...</option>';
                
                assistants.forEach(assistant => {
                    const option = document.createElement('option');
                    option.value = assistant.id;
                    option.textContent = `${assistant.name} (${assistant.id.substring(0, 8)}...)`;
                    select.appendChild(option);
                });
                
                log(`✅ Loaded ${assistants.length} assistants into dropdown`);
                addTestResult('Dropdown Population', true, `${assistants.length} assistants loaded`);
                
                return assistants;
            } catch (error) {
                log(`❌ Failed to populate dropdown: ${error.message}`, 'error');
                addTestResult('Dropdown Population', false, error.message);
                return [];
            }
        }

        async function onAssistantChange() {
            const select = document.getElementById('assistantSelect');
            const assistantId = select.value;
            
            if (!assistantId) return;
            
            log(`🔄 Assistant selected: ${assistantId}`);
            
            try {
                // Test if assistant details can be retrieved
                if (typeof window.vapiMcpService !== 'undefined') {
                    const assistant = await window.vapiMcpService.getAssistant(assistantId);
                    
                    const detailsDiv = document.getElementById('assistantDetails');
                    const infoDiv = document.getElementById('assistantInfo');
                    
                    infoDiv.innerHTML = `
                        <br><strong>Name:</strong> ${assistant.name}
                        <br><strong>ID:</strong> ${assistant.id}
                        <br><strong>Model:</strong> ${assistant.model?.provider} ${assistant.model?.model}
                        <br><strong>Voice:</strong> ${assistant.voice?.provider} ${assistant.voice?.voiceId}
                    `;
                    
                    detailsDiv.style.display = 'block';
                    
                    log(`✅ Retrieved assistant details for ${assistant.name}`);
                    addTestResult('Assistant Details Retrieval', true, `Details loaded for ${assistant.name}`);
                } else {
                    log('⚠️ vapiMcpService not available - cannot retrieve details', 'warning');
                }
                
                // Test store update if available
                if (typeof window.useAssistantStore !== 'undefined') {
                    // This would need to be adapted based on your store implementation
                    log('🔄 Testing store update...');
                    // window.useAssistantStore.getState().setCurrentAssistantId(assistantId);
                    log('✅ Store update test would go here');
                }
                
            } catch (error) {
                log(`❌ Failed to handle assistant change: ${error.message}`, 'error');
                addTestResult('Assistant Change Handling', false, error.message);
            }
        }

        async function testAssistantSwitch() {
            log('🔍 Testing assistant switching...');
            
            if (currentAssistants.length < 2) {
                log('⚠️ Need at least 2 assistants to test switching', 'warning');
                return;
            }
            
            try {
                const assistant1 = currentAssistants[0];
                const assistant2 = currentAssistants[1];
                
                log(`🔄 Switching from ${assistant1.name} to ${assistant2.name}`);
                
                // Simulate dropdown change
                const select = document.getElementById('assistantSelect');
                select.value = assistant2.id;
                await onAssistantChange();
                
                log(`✅ Switch test completed`);
                addTestResult('Assistant Switching', true, `Switched from ${assistant1.name} to ${assistant2.name}`);
                
            } catch (error) {
                log(`❌ Assistant switching failed: ${error.message}`, 'error');
                addTestResult('Assistant Switching', false, error.message);
            }
        }

        async function testVapiIntegration() {
            log('🔍 Testing Vapi integration...');
            
            try {
                // Test if Vapi services are available
                const services = [];
                if (typeof window.vapiAssistantService !== 'undefined') services.push('vapiAssistantService');
                if (typeof window.vapiMcpService !== 'undefined') services.push('vapiMcpService');
                
                if (services.length === 0) {
                    throw new Error('No Vapi services available in window context');
                }
                
                log(`✅ Available Vapi services: ${services.join(', ')}`);
                addTestResult('Vapi Service Availability', true, `${services.length} services available`);
                
                return true;
            } catch (error) {
                log(`❌ Vapi integration test failed: ${error.message}`, 'error');
                addTestResult('Vapi Service Availability', false, error.message);
                return false;
            }
        }

        async function runAllTests() {
            log('🚀 Starting comprehensive assistant propagation tests...');
            
            const runBtn = document.getElementById('runAllBtn');
            runBtn.disabled = true;
            runBtn.textContent = 'Running Tests...';
            
            try {
                // Test 1: Vapi Integration
                await testVapiIntegration();
                
                // Test 2: Dropdown Population
                await testDropdownPopulation();
                
                // Test 3: Assistant Switching
                await testAssistantSwitch();
                
                log('🎉 All tests completed!');
                
                const successCount = testResults.filter(r => r.success).length;
                const totalCount = testResults.length;
                
                if (successCount === totalCount) {
                    log(`✅ All ${totalCount} tests passed!`, 'success');
                } else {
                    log(`⚠️ ${successCount}/${totalCount} tests passed`, 'warning');
                }
                
            } catch (error) {
                log(`❌ Test suite failed: ${error.message}`, 'error');
            } finally {
                runBtn.disabled = false;
                runBtn.textContent = 'Run All Tests';
            }
        }

        // Initialize on page load
        window.addEventListener('load', () => {
            log('🧪 Assistant Propagation Test Suite loaded');
            log('💡 Make sure you\'re running this in the LegalScout dashboard context');
            
            // Auto-populate dropdown if services are available
            if (typeof window.vapiAssistantService !== 'undefined') {
                testDropdownPopulation();
            } else {
                log('⚠️ vapiAssistantService not detected - manual testing required', 'warning');
            }
        });
    </script>
</body>
</html>

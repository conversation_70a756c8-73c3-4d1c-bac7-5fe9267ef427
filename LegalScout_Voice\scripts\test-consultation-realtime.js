/**
 * Real-time Consultation Test Script
 * 
 * This script tests real-time consultation creation and updates
 * to help diagnose why consultations aren't appearing in the Briefs page.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase configuration');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function testRealtimeConsultations() {
  console.log('🔄 Starting Real-time Consultation Test...\n');

  try {
    // 1. Get <PERSON>'s attorney ID
    const { data: attorney, error: attorneyError } = await supabase
      .from('attorneys')
      .select('id, email, name')
      .eq('email', '<EMAIL>')
      .single();

    if (attorneyError || !attorney) {
      console.error('❌ Could not find attorney <NAME_EMAIL>');
      console.log('Trying alternative email...');
      
      const { data: altAttorney, error: altError } = await supabase
        .from('attorneys')
        .select('id, email, name')
        .eq('email', '<EMAIL>')
        .single();

      if (altError || !altAttorney) {
        console.error('❌ Could not find attorney record for any email');
        return;
      }
      
      console.log(`✅ Using attorney: ${altAttorney.name} (${altAttorney.email})`);
      var attorneyId = altAttorney.id;
    } else {
      console.log(`✅ Using attorney: ${attorney.name} (${attorney.email})`);
      var attorneyId = attorney.id;
    }

    // 2. Count existing consultations
    const { data: existingConsultations, error: countError } = await supabase
      .from('consultations')
      .select('id')
      .eq('attorney_id', attorneyId);

    if (countError) {
      console.error('❌ Error counting existing consultations:', countError);
      return;
    }

    console.log(`📊 Current consultations for this attorney: ${existingConsultations.length}\n`);

    // 3. Set up real-time subscription
    console.log('🔔 Setting up real-time subscription...');
    
    const subscription = supabase
      .channel('consultations-test')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'consultations',
          filter: `attorney_id=eq.${attorneyId}`
        },
        (payload) => {
          console.log('\n🔔 REAL-TIME UPDATE RECEIVED:');
          console.log('Event:', payload.eventType);
          console.log('Table:', payload.table);
          if (payload.new) {
            console.log('New data:', {
              id: payload.new.id,
              client_name: payload.new.client_name,
              created_at: payload.new.created_at,
              status: payload.new.status
            });
          }
          if (payload.old) {
            console.log('Old data:', payload.old);
          }
        }
      )
      .subscribe((status) => {
        console.log('Subscription status:', status);
      });

    // 4. Create a test consultation
    console.log('\n🧪 Creating test consultation...');
    
    const testConsultation = {
      attorney_id: attorneyId,
      client_name: 'Test Client - ' + new Date().toISOString(),
      client_email: '<EMAIL>',
      client_phone: '+1234567890',
      summary: 'This is a test consultation created by the diagnostic script',
      transcript: 'Test transcript content',
      duration: 300, // 5 minutes
      practice_area: 'General',
      location: 'Test Location',
      location_data: { address: 'Test Address' },
      metadata: {
        test: true,
        created_by: 'diagnostic_script',
        timestamp: new Date().toISOString()
      },
      status: 'new'
    };

    const { data: newConsultation, error: insertError } = await supabase
      .from('consultations')
      .insert(testConsultation)
      .select()
      .single();

    if (insertError) {
      console.error('❌ Error creating test consultation:', insertError);
      return;
    }

    console.log('✅ Test consultation created:', newConsultation.id);

    // 5. Wait for real-time update
    console.log('\n⏳ Waiting for real-time update (5 seconds)...');
    await new Promise(resolve => setTimeout(resolve, 5000));

    // 6. Verify the consultation was created
    const { data: verifyConsultation, error: verifyError } = await supabase
      .from('consultations')
      .select('*')
      .eq('id', newConsultation.id)
      .single();

    if (verifyError) {
      console.error('❌ Error verifying consultation:', verifyError);
    } else {
      console.log('✅ Consultation verified in database');
    }

    // 7. Test frontend query pattern
    console.log('\n🔍 Testing frontend query pattern...');
    
    const { data: frontendQuery, error: frontendError } = await supabase
      .from('consultations')
      .select('*')
      .eq('attorney_id', attorneyId)
      .order('created_at', { ascending: false });

    if (frontendError) {
      console.error('❌ Frontend query failed:', frontendError);
    } else {
      console.log(`✅ Frontend query returned ${frontendQuery.length} consultations`);
      if (frontendQuery.length > 0) {
        console.log('Latest consultation:', {
          id: frontendQuery[0].id,
          client_name: frontendQuery[0].client_name,
          created_at: frontendQuery[0].created_at
        });
      }
    }

    // 8. Clean up test data
    console.log('\n🧹 Cleaning up test consultation...');
    
    const { error: deleteError } = await supabase
      .from('consultations')
      .delete()
      .eq('id', newConsultation.id);

    if (deleteError) {
      console.error('❌ Error deleting test consultation:', deleteError);
    } else {
      console.log('✅ Test consultation deleted');
    }

    // 9. Unsubscribe from real-time
    console.log('\n🔌 Unsubscribing from real-time...');
    await supabase.removeChannel(subscription);

    console.log('\n📋 TEST SUMMARY:');
    console.log('='.repeat(40));
    console.log('✅ Real-time subscription: Working');
    console.log('✅ Consultation creation: Working');
    console.log('✅ Frontend query pattern: Working');
    console.log('\n💡 If consultations still don\'t appear in the UI:');
    console.log('   1. Check browser console for JavaScript errors');
    console.log('   2. Verify the correct attorney ID is being used in the frontend');
    console.log('   3. Check if there are any authentication issues');
    console.log('   4. Ensure the ConsultationsTab component is using the right query');

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test
testRealtimeConsultations()
  .then(() => {
    console.log('\n✅ Real-time test complete');
    process.exit(0);
  })
  .catch(error => {
    console.error('❌ Real-time test failed:', error);
    process.exit(1);
  });

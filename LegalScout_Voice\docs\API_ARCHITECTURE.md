# LegalScout API Architecture

This document describes the actual API architecture implemented in LegalScout Voice, including the complex routing system and MCP proxy endpoints.

## Overview

LegalScout uses a sophisticated API architecture designed to work within Vercel's Serverless Functions limitations while providing comprehensive functionality for voice AI legal consultations.

## Core Architecture Principles

### 1. Vercel Serverless Function Optimization
- **Main Handler**: `/api/index.js` serves as the primary API endpoint
- **Function Limit**: Designed to work within Vercel Hobby plan's 12 Serverless Functions limit
- **Route Consolidation**: Multiple API routes consolidated into single handlers

### 2. Proxy Pattern for External Services
- **MCP Proxies**: Multiple proxy endpoints for Model Context Protocol integration
- **Vapi Proxies**: Dedicated proxies for Vapi API calls
- **Fallback Mechanisms**: Multiple proxy paths for reliability

## API Structure

### Main API Handler (`/api/index.js`)

The primary API endpoint that handles multiple routes:

```javascript
// Main API routes handled by index.js
const routes = {
  '/api/health': healthCheck,
  '/api/env': environmentCheck,
  '/api/debug-supabase': supabaseDebug,
  '/api/sync-tools': toolSynchronization,
  '/api/vapi-proxy': vapiProxyHandler,
  '/api/mcp-proxy': mcpProxyHandler
};
```

**Key Features:**
- Route-based request handling
- Centralized error handling
- Environment-aware responses
- CORS configuration for cross-origin requests

### MCP Integration Endpoints

#### 1. Vapi MCP Server (`/api/vapi-mcp-server`)
```javascript
// Handles Vapi MCP server integration
POST /api/vapi-mcp-server/sse
GET  /api/vapi-mcp-server/tools
POST /api/vapi-mcp-server/call
```

**Functionality:**
- Server-Sent Events (SSE) for real-time communication
- Tool discovery and execution
- Call creation and management
- Assistant configuration

#### 2. AI Meta MCP (`/api/ai-meta-mcp`)
```javascript
// Enhanced MCP integration for advanced features
POST /api/ai-meta-mcp/tools
GET  /api/ai-meta-mcp/status
POST /api/ai-meta-mcp/execute
```

**Functionality:**
- Advanced tool integration
- Custom MCP server connections
- Enhanced data processing

### Vapi Integration Endpoints

#### 1. Direct Vapi Proxy (`/api/vapi-proxy`)
```javascript
// Direct proxy to Vapi API
const vapiEndpoints = {
  '/assistants': 'https://api.vapi.ai/assistant',
  '/calls': 'https://api.vapi.ai/call',
  '/phone-numbers': 'https://api.vapi.ai/phone-number'
};
```

**Features:**
- API key injection
- Request/response transformation
- Error handling and retries
- Rate limiting compliance

#### 2. Webhook Handler (`/api/webhook/vapi-call`)
```javascript
// Processes incoming Vapi webhooks
export default async function handler(req, res) {
  const { type, call, assistant } = req.body;
  
  // Route based on webhook type
  switch (type) {
    case 'call-start':
      await handleCallStart(call);
      break;
    case 'call-end':
      await handleCallEnd(call);
      break;
    case 'transcript':
      await handleTranscript(call);
      break;
  }
}
```

**Webhook Types Handled:**
- `call-start` - Call initiation
- `call-end` - Call termination with summary
- `transcript` - Real-time transcription
- `function-call` - Tool execution requests
- `status-update` - Call status changes

### Utility Endpoints

#### 1. Health Check (`/api/health`)
```javascript
// System health monitoring
{
  "status": "healthy",
  "timestamp": "2025-01-17T10:30:00Z",
  "services": {
    "supabase": "connected",
    "vapi": "connected",
    "mcp": "connected"
  }
}
```

#### 2. Environment Check (`/api/env`)
```javascript
// Environment variable validation
{
  "environment": "production",
  "configured": {
    "vapi": true,
    "supabase": true,
    "mcp": true
  },
  "missing": []
}
```

## Request Flow Architecture

### 1. Client-Side Requests
```
Client → Vite Dev Server → API Proxy → External Service
```

**Development Flow:**
1. Client makes request to `/api/*`
2. Vite dev server proxies to local API handlers
3. API handlers process and forward to external services
4. Response transformed and returned to client

### 2. Production Requests
```
Client → Vercel Edge → Serverless Function → External Service
```

**Production Flow:**
1. Client request hits Vercel Edge network
2. Routed to appropriate Serverless Function
3. Function processes request and calls external APIs
4. Response cached and returned via Edge network

### 3. Webhook Processing
```
Vapi → Vercel → Webhook Handler → Supabase → Client (SSE)
```

**Webhook Flow:**
1. Vapi sends webhook to Vercel endpoint
2. Webhook handler processes and stores data
3. Real-time updates sent to clients via SSE
4. UI updates reflect call status changes

## Error Handling Strategy

### 1. Graceful Degradation
```javascript
// Fallback mechanism example
async function callVapiAPI(endpoint, data) {
  try {
    // Try MCP first
    return await mcpClient.call(endpoint, data);
  } catch (mcpError) {
    // Fallback to direct API
    return await directVapiCall(endpoint, data);
  }
}
```

### 2. Circuit Breaker Pattern
```javascript
// Prevent cascade failures
const circuitBreaker = {
  failures: 0,
  threshold: 5,
  timeout: 30000,
  
  async call(fn) {
    if (this.failures >= this.threshold) {
      throw new Error('Circuit breaker open');
    }
    
    try {
      const result = await fn();
      this.failures = 0;
      return result;
    } catch (error) {
      this.failures++;
      throw error;
    }
  }
};
```

## Security Implementation

### 1. API Key Management
- **Client-side**: Only public keys exposed
- **Server-side**: Private keys in environment variables
- **Proxy Pattern**: Keys injected server-side

### 2. Request Validation
```javascript
// Webhook signature validation
function validateWebhook(req) {
  const signature = req.headers['x-vapi-signature'];
  const payload = JSON.stringify(req.body);
  const expectedSignature = crypto
    .createHmac('sha256', process.env.WEBHOOK_SECRET)
    .update(payload)
    .digest('hex');
    
  return signature === expectedSignature;
}
```

### 3. Rate Limiting
```javascript
// Simple rate limiting implementation
const rateLimiter = new Map();

function checkRateLimit(ip) {
  const now = Date.now();
  const windowStart = now - 60000; // 1 minute window
  
  if (!rateLimiter.has(ip)) {
    rateLimiter.set(ip, []);
  }
  
  const requests = rateLimiter.get(ip);
  const recentRequests = requests.filter(time => time > windowStart);
  
  if (recentRequests.length >= 100) { // 100 requests per minute
    throw new Error('Rate limit exceeded');
  }
  
  recentRequests.push(now);
  rateLimiter.set(ip, recentRequests);
}
```

## Performance Optimizations

### 1. Connection Pooling
- Reuse HTTP connections for external APIs
- Connection pooling for database queries
- Keep-alive headers for persistent connections

### 2. Caching Strategy
- Response caching for static data
- Assistant configuration caching
- CDN caching for static assets

### 3. Async Processing
- Non-blocking webhook processing
- Background job queues for heavy operations
- Streaming responses for large datasets

## Monitoring and Observability

### 1. Logging Strategy
```javascript
// Structured logging
const logger = {
  info: (message, meta) => console.log(JSON.stringify({
    level: 'info',
    message,
    timestamp: new Date().toISOString(),
    ...meta
  })),
  
  error: (message, error, meta) => console.error(JSON.stringify({
    level: 'error',
    message,
    error: error.message,
    stack: error.stack,
    timestamp: new Date().toISOString(),
    ...meta
  }))
};
```

### 2. Health Monitoring
- Endpoint health checks
- Service dependency monitoring
- Performance metrics collection

### 3. Error Tracking
- Centralized error collection
- Error rate monitoring
- Alert thresholds for critical failures

## Deployment Considerations

### 1. Environment Configuration
- Separate configs for dev/staging/production
- Environment-specific API endpoints
- Feature flags for gradual rollouts

### 2. Scaling Strategy
- Horizontal scaling via Vercel functions
- Database connection pooling
- CDN for static asset delivery

### 3. Rollback Procedures
- Blue-green deployment strategy
- Database migration rollback plans
- Feature flag-based rollbacks

This architecture provides a robust, scalable foundation for LegalScout's voice AI legal consultation platform while working within the constraints of serverless deployment platforms.

#!/usr/bin/env node

/**
 * Simple Vapi Integration Test
 * 
 * Tests the core Vapi functionality needed for launch
 */

import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.development' });

console.log('🚀 Testing Vapi Integration...\n');

// Test 1: Check API Keys
console.log('1. Checking API Keys...');
const publicKey = process.env.VITE_VAPI_PUBLIC_KEY || '310f0d43-27c2-47a5-a76d-e55171d024f7';
const secretKey = process.env.VITE_VAPI_SECRET_KEY || process.env.VAPI_TOKEN || '6734febc-fc65-4669-93b0-929b31ff6564';

if (publicKey) {
  console.log(`✅ Public Key: ${publicKey.substring(0, 8)}...`);
} else {
  console.log('❌ Public Key: Missing');
  process.exit(1);
}

if (secretKey) {
  console.log(`✅ Secret Key: ${secretKey.substring(0, 8)}...`);
} else {
  console.log('❌ Secret Key: Missing');
  process.exit(1);
}

// Test 2: Test API Connectivity
console.log('\n2. Testing API Connectivity...');

try {
  const response = await fetch('https://api.vapi.ai/assistant', {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${secretKey}`,
      'Content-Type': 'application/json'
    }
  });

  if (response.ok) {
    const assistants = await response.json();
    console.log(`✅ API Connected: Found ${assistants.length} assistants`);
    
    // Show first few assistants
    if (assistants.length > 0) {
      console.log('   Existing assistants:');
      assistants.slice(0, 3).forEach(assistant => {
        console.log(`   - ${assistant.name} (${assistant.id})`);
      });
    }
  } else {
    const errorText = await response.text();
    console.log(`❌ API Error: HTTP ${response.status} - ${errorText}`);
    process.exit(1);
  }
} catch (error) {
  console.log(`❌ API Error: ${error.message}`);
  process.exit(1);
}

// Test 3: Test Assistant Creation
console.log('\n3. Testing Assistant Creation...');

try {
  const testAssistant = {
    name: 'Test Assistant - ' + Date.now(),
    firstMessage: 'Hello, this is a test assistant.',
    model: {
      provider: 'openai',
      model: 'gpt-3.5-turbo',
      messages: [
        {
          role: 'system',
          content: 'You are a test assistant for LegalScout integration testing.'
        }
      ]
    },
    voice: {
      provider: '11labs',
      voiceId: 'sarah'
    }
  };

  const createResponse = await fetch('https://api.vapi.ai/assistant', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${secretKey}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(testAssistant)
  });

  if (createResponse.ok) {
    const assistant = await createResponse.json();
    console.log(`✅ Assistant Created: ${assistant.id}`);
    
    // Clean up - delete the test assistant
    const deleteResponse = await fetch(`https://api.vapi.ai/assistant/${assistant.id}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${secretKey}`
      }
    });
    
    if (deleteResponse.ok) {
      console.log('✅ Test Assistant Cleaned Up');
    } else {
      console.log('⚠️  Warning: Could not delete test assistant');
    }
  } else {
    const errorText = await createResponse.text();
    console.log(`❌ Assistant Creation Failed: HTTP ${createResponse.status} - ${errorText}`);
    process.exit(1);
  }
} catch (error) {
  console.log(`❌ Assistant Creation Error: ${error.message}`);
  process.exit(1);
}

// Test 4: Check Supabase Configuration
console.log('\n4. Checking Supabase Configuration...');

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_KEY;

if (supabaseUrl && supabaseKey) {
  console.log(`✅ Supabase URL: ${supabaseUrl.substring(0, 30)}...`);
  console.log(`✅ Supabase Key: ${supabaseKey.substring(0, 8)}...`);
} else {
  console.log('❌ Supabase Configuration: Missing URL or key');
  process.exit(1);
}

console.log('\n🎉 All Tests Passed! Your Vapi integration is ready for launch.');
console.log('\nNext steps:');
console.log('1. Run: npm run dev');
console.log('2. Test authentication at http://localhost:5174');
console.log('3. Test voice calls in the dashboard');
console.log('4. Deploy to production when ready');

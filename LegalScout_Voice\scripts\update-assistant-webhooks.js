#!/usr/bin/env node

/**
 * Update All Assistant Webhooks Script
 * 
 * This script updates all existing Vapi assistants to use their attorney-specific
 * subdomain webhook URLs instead of the generic webhook URL.
 * 
 * Usage: node scripts/update-assistant-webhooks.js
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Supabase configuration
const supabaseUrl = process.env.SUPABASE_URL || 'https://utopqxsvudgrtiwenlzl.supabase.co';
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczODk0ODAwNywiZXhwIjoyMDU0NTI0MDA3fQ.Noq994xfKMoQipfGli9fZcgQYig9fZovjqdEnpBe7CM';

// Vapi configuration
const vapiPrivateKey = process.env.VAPI_PRIVATE_KEY || '6734febc-fc65-4669-93b0-929b31ff6564';
const vapiApiUrl = 'https://api.vapi.ai';

// Base domain for webhooks
const baseDomain = 'legalscout.net';

/**
 * Get all attorneys with assistant IDs from Supabase
 */
async function getAllAttorneys() {
  const supabase = createClient(supabaseUrl, supabaseKey);
  
  const { data, error } = await supabase
    .from('attorneys')
    .select('id, subdomain, firm_name, vapi_assistant_id')
    .not('vapi_assistant_id', 'is', null)
    .not('subdomain', 'is', null);

  if (error) {
    throw new Error(`Failed to fetch attorneys: ${error.message}`);
  }

  return data;
}

/**
 * Update a Vapi assistant with new webhook URL
 */
async function updateVapiAssistant(assistantId, webhookUrl, attorneyInfo) {
  try {
    const response = await fetch(`${vapiApiUrl}/assistant/${assistantId}`, {
      method: 'PATCH',
      headers: {
        'Authorization': `Bearer ${vapiPrivateKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        serverUrl: webhookUrl,
        serverUrlSecret: process.env.VAPI_WEBHOOK_SECRET || 'legalscout-webhook-secret'
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Vapi API error: ${response.status} - ${errorText}`);
    }

    const result = await response.json();
    console.log(`✅ Updated ${attorneyInfo.firm_name} (${assistantId}): ${webhookUrl}`);
    return result;

  } catch (error) {
    console.error(`❌ Failed to update ${attorneyInfo.firm_name} (${assistantId}):`, error.message);
    throw error;
  }
}

/**
 * Generate webhook URL for attorney
 */
function generateWebhookUrl(subdomain) {
  return `https://${subdomain}.${baseDomain}/api/vapi-webhook-direct`;
}

/**
 * Main execution function
 */
async function main() {
  try {
    console.log('🚀 Starting assistant webhook update process...\n');
    console.log('Environment check:');
    console.log('  SUPABASE_URL:', supabaseUrl);
    console.log('  SUPABASE_KEY length:', supabaseKey?.length);
    console.log('  VAPI_PRIVATE_KEY length:', vapiPrivateKey?.length);
    console.log('');

    // Get all attorneys
    console.log('📊 Fetching attorneys from Supabase...');
    const attorneys = await getAllAttorneys();
    
    if (!attorneys || attorneys.length === 0) {
      console.log('❌ No attorneys found with assistant IDs');
      return;
    }

    console.log(`📋 Found ${attorneys.length} attorneys with assistants:\n`);
    
    // Display attorneys that will be updated
    attorneys.forEach(attorney => {
      const webhookUrl = generateWebhookUrl(attorney.subdomain);
      console.log(`   • ${attorney.firm_name} (${attorney.subdomain})`);
      console.log(`     Assistant: ${attorney.vapi_assistant_id}`);
      console.log(`     New webhook: ${webhookUrl}\n`);
    });

    // Confirm before proceeding
    console.log('⚠️  This will update webhook URLs for all assistants listed above.');
    console.log('   Press Ctrl+C to cancel, or any key to continue...\n');
    
    // Wait for user input (in a real script, you'd use readline)
    // For now, we'll proceed automatically
    
    console.log('🔄 Updating assistant webhooks...\n');

    // Update each assistant
    let successCount = 0;
    let errorCount = 0;

    for (const attorney of attorneys) {
      try {
        const webhookUrl = generateWebhookUrl(attorney.subdomain);
        await updateVapiAssistant(attorney.vapi_assistant_id, webhookUrl, attorney);
        successCount++;
      } catch (error) {
        errorCount++;
        console.error(`Failed to update ${attorney.firm_name}:`, error.message);
      }
    }

    console.log('\n📊 Update Summary:');
    console.log(`   ✅ Successfully updated: ${successCount}`);
    console.log(`   ❌ Failed to update: ${errorCount}`);
    console.log(`   📋 Total attorneys: ${attorneys.length}`);

    if (successCount > 0) {
      console.log('\n🎉 Webhook updates completed successfully!');
      console.log('\n📝 Next steps:');
      console.log('   1. Test webhook endpoints with GET requests');
      console.log('   2. Make test calls to verify webhook routing');
      console.log('   3. Monitor webhook logs for any issues');
    }

  } catch (error) {
    console.error('💥 Script failed:', error.message);
    process.exit(1);
  }
}

// Run the script
console.log('Script starting...');

// Always run the main function for now
main().catch(error => {
  console.error('Script failed:', error);
  process.exit(1);
});

export { main, getAllAttorneys, updateVapiAssistant, generateWebhookUrl };

/**
 * Fix for Qr component that's causing the S(...).catch is not a function error
 * 
 * This script specifically targets the Qr component mentioned in the error stack.
 */
(function() {
  console.log('[QrComponentFix] Starting Qr component fix...');
  
  // Global error handler specifically for the S(...).catch error
  window.addEventListener('error', function(event) {
    if (event.error && event.error.message && event.error.message.includes('catch is not a function')) {
      console.log('[QrComponentFix] Caught the specific error in Qr component: ' + event.error.message);
      event.preventDefault();
      return false;
    }
  }, true);
  
  // Function to find and patch the Qr component
  function findAndPatchQrComponent() {
    console.log('[QrComponentFix] Looking for Qr component...');
    
    // Method 1: Check if Qr is directly accessible in the global scope
    if (typeof window.Qr === 'function') {
      console.log('[QrComponentFix] Found Qr in global scope, patching it');
      patchQrComponent(window.Qr);
      return;
    }
    
    // Method 2: Look for Qr in the pages-29f7c346.js file
    // We can't directly access the file, but we can look for objects that might contain it
    const possibleContainers = [
      window,
      window.React,
      window.ReactDOM,
      window.components,
      window.Components
    ];
    
    for (const container of possibleContainers) {
      if (!container) continue;
      
      // Look for Qr in the container
      if (typeof container.Qr === 'function') {
        console.log('[QrComponentFix] Found Qr in container, patching it');
        patchQrComponent(container.Qr);
        return;
      }
      
      // Look for Qr in the container's properties
      for (const key in container) {
        try {
          const value = container[key];
          if (typeof value === 'function' && (key === 'Qr' || value.name === 'Qr')) {
            console.log(`[QrComponentFix] Found Qr as ${key} in container, patching it`);
            patchQrComponent(value);
            return;
          }
        } catch (error) {
          // Ignore errors when accessing properties
        }
      }
    }
    
    // Method 3: Monitor script loading and patch Qr when it's loaded
    monitorScriptLoading();
    
    console.log('[QrComponentFix] Could not find Qr component directly, will monitor for it');
  }
  
  // Function to patch the Qr component
  function patchQrComponent(QrComponent) {
    if (!QrComponent || QrComponent._qrComponentFixPatched) return;
    
    console.log('[QrComponentFix] Patching Qr component');
    
    // Mark as patched to avoid double patching
    QrComponent._qrComponentFixPatched = true;
    
    // Store the original component
    const originalQrComponent = QrComponent;
    
    // Create a wrapped version of the component
    const wrappedQrComponent = function(...args) {
      try {
        // Call the original component
        const result = originalQrComponent.apply(this, args);
        
        // Ensure the result has a catch method if it's a Promise-like object
        if (result && typeof result.then === 'function') {
          if (typeof result.catch !== 'function') {
            console.log('[QrComponentFix] Adding catch method to Qr component result');
            result.catch = function(onRejected) {
              return Promise.resolve(result).catch(onRejected);
            };
          }
        }
        
        return result;
      } catch (error) {
        console.error('[QrComponentFix] Error in Qr component:', error);
        // Return a safe value
        return null;
      }
    };
    
    // Copy properties from the original component
    Object.assign(wrappedQrComponent, originalQrComponent);
    wrappedQrComponent.displayName = originalQrComponent.displayName || 'Qr';
    
    // Replace the original component with the wrapped version
    if (window.Qr === originalQrComponent) {
      window.Qr = wrappedQrComponent;
    }
    
    // Also look for the component in React's internal registry
    if (window.React && window.React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED) {
      const internals = window.React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;
      if (internals.ReactCurrentOwner && internals.ReactCurrentOwner.current) {
        const currentComponent = internals.ReactCurrentOwner.current;
        if (currentComponent.type === originalQrComponent) {
          currentComponent.type = wrappedQrComponent;
        }
      }
    }
    
    console.log('[QrComponentFix] Successfully patched Qr component');
  }
  
  // Function to monitor script loading and patch Qr when it's loaded
  function monitorScriptLoading() {
    console.log('[QrComponentFix] Setting up script loading monitor...');
    
    // Create a MutationObserver to watch for script loads
    const observer = new MutationObserver(function(mutations) {
      mutations.forEach(function(mutation) {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach(function(node) {
            if (node.tagName === 'SCRIPT' && node.src && node.src.includes('pages-')) {
              console.log('[QrComponentFix] Detected new script load:', node.src);
              
              // Wait for the script to load and then check for Qr
              node.addEventListener('load', function() {
                console.log('[QrComponentFix] Script loaded, checking for Qr component');
                setTimeout(checkForQrAfterScriptLoad, 500);
              });
            }
          });
        }
      });
    });
    
    // Start observing the document
    observer.observe(document.documentElement, {
      childList: true,
      subtree: true
    });
    
    console.log('[QrComponentFix] Script loading monitor set up');
  }
  
  // Function to check for Qr after a script has loaded
  function checkForQrAfterScriptLoad() {
    console.log('[QrComponentFix] Checking for Qr component after script load...');
    
    // Check if Qr is now available in the global scope
    if (typeof window.Qr === 'function') {
      console.log('[QrComponentFix] Found Qr in global scope after script load, patching it');
      patchQrComponent(window.Qr);
      return;
    }
    
    // Check if Qr is available in any of the common containers
    const possibleContainers = [
      window,
      window.React,
      window.ReactDOM,
      window.components,
      window.Components
    ];
    
    for (const container of possibleContainers) {
      if (!container) continue;
      
      // Look for Qr in the container
      if (typeof container.Qr === 'function') {
        console.log('[QrComponentFix] Found Qr in container after script load, patching it');
        patchQrComponent(container.Qr);
        return;
      }
    }
    
    // If we still can't find Qr, try again later
    setTimeout(checkForQrAfterScriptLoad, 1000);
  }
  
  // Function to patch the S function that's used by Qr
  function patchSFunction() {
    console.log('[QrComponentFix] Looking for S function...');
    
    // Method 1: Check if S is directly accessible in the global scope
    if (typeof window.S === 'function') {
      console.log('[QrComponentFix] Found S in global scope, patching it');
      patchFunction('S', window, 'S');
      return;
    }
    
    // Method 2: Look for S in common containers
    const possibleContainers = [
      window,
      window.React,
      window.ReactDOM,
      window.components,
      window.Components,
      window.utils,
      window.Utils,
      window.services,
      window.Services
    ];
    
    for (const container of possibleContainers) {
      if (!container) continue;
      
      // Look for S in the container
      if (typeof container.S === 'function') {
        console.log('[QrComponentFix] Found S in container, patching it');
        patchFunction('S', container, 'S');
        return;
      }
    }
    
    // Method 3: Add a global S function if it doesn't exist
    if (!window.S) {
      console.log('[QrComponentFix] Adding global S function');
      window.S = function(...args) {
        console.log('[QrComponentFix] Global S function called with args:', args);
        return Promise.resolve(null);
      };
    }
    
    console.log('[QrComponentFix] S function handling complete');
  }
  
  // Helper function to patch a specific function
  function patchFunction(functionName, object, propertyName) {
    if (!object || typeof object[propertyName] !== 'function') return;
    
    // Check if already patched to avoid double patching
    if (object[propertyName]._qrComponentFixPatched) {
      console.log(`[QrComponentFix] ${functionName} already patched, skipping`);
      return;
    }
    
    console.log(`[QrComponentFix] Patching ${functionName} function`);
    
    // Store the original function
    const originalFunction = object[propertyName];
    
    // Create a wrapped version of the function
    object[propertyName] = function(...args) {
      try {
        // Call the original function
        const result = originalFunction.apply(this, args);
        
        // Ensure the result has a catch method if it's a Promise-like object
        if (result && typeof result.then === 'function') {
          if (typeof result.catch !== 'function') {
            console.log(`[QrComponentFix] Adding catch method to ${functionName} result`);
            result.catch = function(onRejected) {
              return Promise.resolve(result).catch(onRejected);
            };
          }
        }
        
        return result;
      } catch (error) {
        console.error(`[QrComponentFix] Error in ${functionName} function:`, error);
        // Return a safe value
        return Promise.resolve(null);
      }
    };
    
    // Copy properties from the original function
    Object.assign(object[propertyName], originalFunction);
    object[propertyName]._qrComponentFixPatched = true;
    
    console.log(`[QrComponentFix] Successfully patched ${functionName} function`);
  }
  
  // Initialize the fix
  function initialize() {
    // Find and patch the Qr component
    findAndPatchQrComponent();
    
    // Patch the S function
    patchSFunction();
    
    // Set up a periodic check for the Qr component
    // This is needed because the component might be loaded dynamically
    setInterval(function() {
      if (!window.Qr || !window.Qr._qrComponentFixPatched) {
        findAndPatchQrComponent();
      }
    }, 1000);
    
    console.log('[QrComponentFix] Initialization complete');
  }
  
  // Wait for the DOM to be ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initialize);
  } else {
    initialize();
  }
  
  // Also run the fix when the window loads
  window.addEventListener('load', initialize);
  
  console.log('[QrComponentFix] Qr component fix setup complete');
})();

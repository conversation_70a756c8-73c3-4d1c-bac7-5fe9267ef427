/**
 * Setup Vapi Webhooks
 *
 * This script registers webhooks with Vapi for call events.
 * It should be run during deployment or when webhook configuration changes.
 */

import dotenv from 'dotenv';
import fetch from 'node-fetch';

dotenv.config();

// Configuration
const VAPI_API_KEY = process.env.VAPI_TOKEN || process.env.VITE_VAPI_SECRET_KEY;
const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || 'https://legalscout.app';
const WEBHOOK_URL = `${BASE_URL}/api/webhook/vapi-call`;

// Events to listen for
const EVENTS = [
  'call.started',
  'call.ended',
  'call.transcript_updated',
  'call.metadata_updated'
];

/**
 * Register a webhook
 */
async function registerWebhook() {
  try {
    console.log('Registering webhook...');

    // Check if API key is available
    if (!VAPI_API_KEY) {
      throw new Error('Vapi API key not configured');
    }

    // List existing webhooks
    const listResponse = await fetch('https://api.vapi.ai/webhooks', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${VAPI_API_KEY}`,
        'Content-Type': 'application/json'
      }
    });

    if (!listResponse.ok) {
      throw new Error(`Webhook listing failed: ${listResponse.status} ${listResponse.statusText}`);
    }

    const webhooks = await listResponse.json();

    // Check if webhook already exists
    const existingWebhook = webhooks.find(webhook => webhook.url === WEBHOOK_URL);

    if (existingWebhook) {
      console.log('Webhook already exists:', existingWebhook);

      // Update webhook if events are different
      const missingEvents = EVENTS.filter(event => !existingWebhook.events.includes(event));

      if (missingEvents.length > 0) {
        console.log('Updating webhook with missing events:', missingEvents);

        const updateResponse = await fetch(`https://api.vapi.ai/webhooks/${existingWebhook.id}`, {
          method: 'PATCH',
          headers: {
            'Authorization': `Bearer ${VAPI_API_KEY}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            events: [...existingWebhook.events, ...missingEvents]
          })
        });

        if (!updateResponse.ok) {
          throw new Error(`Webhook update failed: ${updateResponse.status} ${updateResponse.statusText}`);
        }

        const updatedWebhook = await updateResponse.json();
        console.log('Webhook updated successfully:', updatedWebhook);
      } else {
        console.log('Webhook already has all required events');
      }

      return existingWebhook;
    }

    // Register new webhook
    const response = await fetch('https://api.vapi.ai/webhooks', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${VAPI_API_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        url: WEBHOOK_URL,
        events: EVENTS
      })
    });

    if (!response.ok) {
      throw new Error(`Webhook registration failed: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();
    console.log('Webhook registered successfully:', result);

    return result;
  } catch (error) {
    console.error('Error registering webhook:', error);
    throw error;
  }
}

// Run the script
registerWebhook()
  .then(() => {
    console.log('Webhook setup complete');
    process.exit(0);
  })
  .catch(error => {
    console.error('Webhook setup failed:', error);
    process.exit(1);
  });

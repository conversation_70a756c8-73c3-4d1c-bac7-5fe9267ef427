# LegalScout

<!-- Deployment trigger: Force Vercel to build from latest commit -->

LegalScout is a voice-guided legal consultation platform that connects users with attorneys based on their legal needs. The platform uses Vapi.ai for voice interactions and provides interactive map visualizations for attorney recommendations.

## 🎯 MVP Status: READY FOR LAUNCH ✅

**Last Updated**: June 3, 2025
**Status**: All critical issues resolved, system stable and production-ready

### Recent Critical Fixes Applied:
- ✅ **Duplicate Assistant Prevention**: Eliminated 7+ duplicate Vapi assistants, implemented creation guards
- ✅ **Profile Sync Resolution**: Fixed attorney profile loading inconsistencies, consolidated duplicate records
- ✅ **Authentication Flow**: Fixed routing to ensure users go to dashboard after login
- ✅ **System Stability**: Implemented one-way sync pattern (UI → Supabase → Vapi), disabled auto-sync during initialization

### Current Production Configuration:
- **Primary Attorney**: <EMAIL> (ID: 571390ac-5a83-46b2-ad3a-18b9cf39d701)
- **Vapi Assistant**: f9b97d13-f9c4-40af-a660-62ba5925ff2a (LegalScout Assistant, 11labs/sarah voice)
- **Authentication**: Supabase OAuth with Google, routes to /dashboard
- **Sync Pattern**: One-way (UI → Supabase → Vapi), manual triggers only

## Quick Start

### Prerequisites

- Node.js 18.0.0 or higher
- Vapi account at [vapi.ai](https://vapi.ai)
- Supabase project at [supabase.com](https://supabase.com)

### 1. Install Dependencies

```bash
npm install
```

### 2. Environment Configuration

1. Copy the environment template:
   ```bash
   cp .env.example .env
   ```

2. Configure required environment variables in `.env`:
   ```bash
   # Vapi Configuration (Required)
   VITE_VAPI_PUBLIC_KEY=your_vapi_public_key
   VAPI_TOKEN=your_vapi_private_key

   # Supabase Configuration (Required)
   VITE_SUPABASE_URL=https://your-project.supabase.co
   VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
   ```

### 3. Database Setup

Run Supabase migrations to create required tables:
```bash
cd supabase
supabase db push
```

### 4. Start Development Server

```bash
npm run dev
```

The application will be available at `http://localhost:5174`

## Recent Updates

### May 2025
- Fixed React context initialization errors in AuthContext, SyncContext, and ThemeContext
- Improved Auto-Configure button functionality with multiple fallback mechanisms
- Added emergency Auto-Configure button that appears only when needed
- Fixed Vercel deployment issues by resolving conflicting API routes
- Updated vercel.json configuration for proper API routing

### April 2025
- Improved deployment process with Vercel integration
- Implemented attorney dashboard demo page with configuration options
- Created tabbed interface with Auto-Configure and Manual Setup options
- Added color pickers for primary and secondary brand colors
- Implemented responsive preview with embedded chat widget
- Fixed visibility issues with Start Consultation button in preview
- Added two-step configuration flow (setup → preview)
- Implemented preview controls bar with active configuration display
- Enhanced subdomain testing UI with collapsible panel and toggle button
- Improved theme support for subdomain testing interface
- Fixed logo display issues in the Navbar component
- Optimized navbar height for better UI proportions
- Adjusted logo size to fit properly in the navbar
- Enhanced error handling for API connection failures
- Improved subdomain testing capabilities for local development

## Deployment

### Infrastructure Requirements

#### Supabase Database
- **Current Status**: ⚠️ **QUOTA EXCEEDED** - Immediate action required
- **Egress**: 40.12GB / 5GB (802% over Free Plan limit)
- **Storage**: 1.294GB / 1GB (129% over Free Plan limit)
- **Grace Period**: Ends July 2, 2025
- **Required Action**: Upgrade to Pro Plan ($25/month) to prevent service outage
- **Consequence**: After grace period, all API calls return 402 status codes

#### Vercel Hosting
- **Plan**: Pro Plan with Fluid Compute enabled
- **Resources**: Standard (1 vCPUs, 1.7GB RAM)
- **Functions**: Serverless API at `/api/index.js`

### Vercel Deployment

This project is configured for deployment with Vercel:

1. Ensure your project is linked to Vercel: `npx vercel link`
2. Check environment variables: `npx vercel env ls`
3. Push changes to GitHub to trigger automatic deployment
4. Monitor deployment status in the Vercel dashboard

#### Important Deployment Notes

- **API Routes**: The project uses API routes for the AI Meta MCP server. Make sure there are no conflicting route files (e.g., `api/ai-meta-mcp/[[path]].js` and `api/ai-meta-mcp/[path].js`).
- **Environment Variables**: Set the following environment variables in Vercel:
  - `AI_META_MCP_URL`: URL of the AI Meta MCP server
  - `AI_META_MCP_API_KEY`: API key for the AI Meta MCP server
  - `SUPABASE_URL`: URL of your Supabase instance
  - `SUPABASE_KEY`: API key for your Supabase instance
- **React Context Fixes**: The project includes custom implementations for React contexts to avoid initialization errors. These fixes are critical for proper deployment.

## Documentation

For detailed documentation, please refer to:

### Core Documentation
- `docs/PROJECT_OVERVIEW.md` - Complete project overview and architecture
- `docs/VAPI_INTEGRATION_COMPLETE.md` - Authoritative Vapi integration guide
- `docs/TECH_STACK.md` - Technology stack and versions
- `.env.example` - Complete environment variable reference

### Development
- `docs/DEVELOPMENT_WORKFLOW.md` - Development guidelines and workflow
- `docs/TESTING_GUIDE.md` - Testing procedures and strategies
- `docs/DEPLOYMENT_GUIDE.md` - Production deployment instructions

### Architecture
- `docs/TECHNICAL_ARCHITECTURE.md` - System architecture details
- `docs/SUBDOMAIN_SYSTEM.md` - Subdomain routing implementation
- `docs/DATABASE_SCHEMA.md` - Database structure and relationships

## License

See the [LICENSE](LICENSE) file for license rights and limitations.
#!/usr/bin/env node
/**
 * Test script for Vapi MCP integration using direct API key
 *
 * This script tests the Vapi MCP integration using the provided API key.
 * It connects to the Vapi MCP server and lists available tools.
 *
 * Usage:
 *   node scripts/test-vapi-mcp-direct.js
 */

import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { SSEClientTransport } from '@modelcontextprotocol/sdk/client/sse.js';
import dotenv from 'dotenv';

// Load environment variables from .env file
dotenv.config();

// Use the provided API key
const apiKey = '6734febc-fc65-4669-93b0-929b31ff6564';

console.log('Testing Vapi MCP integration using direct API key...');
console.log('API Key:', apiKey.substring(0, 4) + '...' + apiKey.substring(apiKey.length - 4));

async function testVapiMcpDirect() {
  try {
    // Initialize MCP client
    console.log('\nInitializing MCP client...');
    const mcpClient = new Client({
      name: 'legalscout-test',
      version: '1.0.0',
    });
    
    // Create SSE transport for connection to remote Vapi MCP server
    console.log('Creating SSE transport...');
    const transport = new SSEClientTransport({
      url: 'https://mcp.vapi.ai/sse',
      headers: {
        'Authorization': `Bearer ${apiKey}`
      }
    });
    
    console.log('Connecting to Vapi MCP server via SSE...');
    await mcpClient.connect(transport);
    console.log('✅ Connected successfully');
    
    try {
      // List available tools
      console.log('\nListing available tools...');
      const toolsResult = await mcpClient.listTools();
      
      if (toolsResult.tools && toolsResult.tools.length > 0) {
        console.log(`✅ Found ${toolsResult.tools.length} tools:`);
        toolsResult.tools.forEach((tool) => {
          console.log(`  - ${tool.name}: ${tool.description || 'No description'}`);
        });
      } else {
        console.log('❌ No tools found');
      }
      
      // Try to list Vapi assistants
      console.log('\nTrying to list Vapi assistants...');
      try {
        const assistantsResponse = await mcpClient.callTool({
          name: 'list_assistants',
          arguments: {},
        });
        
        const assistants = assistantsResponse.content;
        if (Array.isArray(assistants) && assistants.length > 0) {
          console.log('✅ Your Vapi assistants:');
          assistants.forEach((assistant) => {
            console.log(`  - ${assistant.name} (${assistant.id})`);
          });
        } else {
          console.log('❌ No assistants found or invalid response');
          console.log('Response:', JSON.stringify(assistantsResponse, null, 2));
        }
      } catch (error) {
        console.error('❌ Error listing assistants:', error.message);
      }
      
      // Try to list Vapi phone numbers
      console.log('\nTrying to list Vapi phone numbers...');
      try {
        const phoneNumbersResponse = await mcpClient.callTool({
          name: 'list_phone_numbers',
          arguments: {},
        });
        
        const phoneNumbers = phoneNumbersResponse.content;
        if (Array.isArray(phoneNumbers) && phoneNumbers.length > 0) {
          console.log('✅ Your Vapi phone numbers:');
          phoneNumbers.forEach((phoneNumber) => {
            console.log(`  - ${phoneNumber.phoneNumber} (${phoneNumber.id})`);
          });
        } else {
          console.log('❌ No phone numbers found or invalid response');
          console.log('Response:', JSON.stringify(phoneNumbersResponse, null, 2));
        }
      } catch (error) {
        console.error('❌ Error listing phone numbers:', error.message);
      }
    } finally {
      console.log('\nDisconnecting from server...');
      await mcpClient.close();
      console.log('✅ Disconnected');
    }
    
    console.log('\n✅ Test completed successfully');
  } catch (error) {
    console.error('\n❌ Test failed with error:', error);
    process.exit(1);
  }
}

testVapiMcpDirect();

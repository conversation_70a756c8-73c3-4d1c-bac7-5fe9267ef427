-- Add assistant_id column to consultations table
-- This fixes the "column consultations.assistant_id does not exist" error

-- Step 1: Add the assistant_id column
ALTER TABLE consultations 
ADD COLUMN IF NOT EXISTS assistant_id UUID;

-- Step 2: Create index for performance
CREATE INDEX IF NOT EXISTS idx_consultations_assistant_id 
ON consultations(assistant_id);

-- Step 3: Migrate existing data
-- Associate existing consultations with the attorney's primary assistant
UPDA<PERSON> consultations 
SET assistant_id = (
  SELECT vapi_assistant_id 
  FROM attorneys 
  WHERE attorneys.id = consultations.attorney_id
  AND attorneys.vapi_assistant_id IS NOT NULL
  AND attorneys.vapi_assistant_id != attorneys.id  -- Prevent attorney ID corruption
)
WHERE assistant_id IS NULL 
AND attorney_id IS NOT NULL;

-- Step 4: Add foreign key constraint (optional, for data integrity)
-- Note: Only add this if you want strict referential integrity
-- ALTER TABLE consultations 
-- ADD CONSTRAINT fk_consultations_assistant_id 
-- FOREIG<PERSON> KEY (assistant_id) REFERENCES assistant_ui_configs(assistant_id);

-- Step 5: Verify the migration
SELECT 
  COUNT(*) as total_consultations,
  COUNT(assistant_id) as consultations_with_assistant_id,
  COUNT(*) - COUNT(assistant_id) as consultations_without_assistant_id
FROM consultations;

-- Step 6: Show sample data to verify
SELECT 
  c.id,
  c.attorney_id,
  c.assistant_id,
  a.firm_name,
  a.vapi_assistant_id
FROM consultations c
LEFT JOIN attorneys a ON c.attorney_id = a.id
LIMIT 5;

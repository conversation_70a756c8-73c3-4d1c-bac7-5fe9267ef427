/**
 * Simple Bulk Cleanup for Vapi Assistants
 * Uses node-fetch directly to avoid import issues
 */

import fetch from 'node-fetch';

const VAPI_API_URL = 'https://api.vapi.ai';
const VAPI_PRIVATE_KEY = '6734febc-fc65-4669-93b0-929b31ff6564';

// Your correct assistant ID that should be kept
const KEEP_ASSISTANT_ID = 'f9b97d13-f9c4-40af-a660-62ba5925ff2a';

async function makeVapiRequest(endpoint, method = 'GET') {
  const url = `${VAPI_API_URL}${endpoint}`;
  
  const options = {
    method,
    headers: {
      'Authorization': `Bearer ${VAPI_PRIVATE_KEY}`,
      'Content-Type': 'application/json'
    }
  };

  console.log(`Making ${method} request to: ${endpoint}`);
  
  const response = await fetch(url, options);
  
  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`API request failed: ${response.status} ${response.statusText} - ${errorText}`);
  }

  if (response.status === 204) {
    return null; // No content for successful delete
  }

  return await response.json();
}

async function listAllAssistants() {
  try {
    console.log('📋 Fetching all assistants from Vapi...');
    const assistants = await makeVapiRequest('/assistant');
    console.log(`Found ${assistants.length} assistants in Vapi`);
    
    return assistants;
  } catch (error) {
    console.error('Error listing assistants:', error);
    throw error;
  }
}

async function deleteAssistant(assistantId) {
  try {
    console.log(`🗑️  Deleting assistant: ${assistantId}`);
    await makeVapiRequest(`/assistant/${assistantId}`, 'DELETE');
    console.log(`✅ Successfully deleted assistant: ${assistantId}`);
    return { success: true };
  } catch (error) {
    console.error(`❌ Error deleting assistant ${assistantId}:`, error.message);
    return { success: false, error: error.message };
  }
}

async function analyzeAssistants() {
  console.log('🔍 Analyzing current assistants...\n');
  
  try {
    const allAssistants = await listAllAssistants();
    
    // Find your correct assistant
    const correctAssistant = allAssistants.find(a => a.id === KEEP_ASSISTANT_ID);
    
    // Find duplicates and orphaned assistants
    const toDelete = allAssistants.filter(a => a.id !== KEEP_ASSISTANT_ID);
    
    console.log('\n📊 Analysis Results:');
    console.log(`   Total assistants: ${allAssistants.length}`);
    console.log(`   ✅ Correct assistant found: ${correctAssistant ? 'YES' : 'NO'}`);
    console.log(`   🗑️  Assistants to delete: ${toDelete.length}`);
    
    if (correctAssistant) {
      console.log('\n✅ Your correct assistant:');
      console.log(`   ID: ${correctAssistant.id}`);
      console.log(`   Name: ${correctAssistant.name}`);
      console.log(`   Created: ${correctAssistant.createdAt}`);
    } else {
      console.log('\n⚠️  WARNING: Your correct assistant was not found!');
      console.log(`   Looking for ID: ${KEEP_ASSISTANT_ID}`);
    }
    
    if (toDelete.length > 0) {
      console.log('\n🗑️  Assistants to DELETE:');
      
      // Group by name to show duplicates
      const byName = {};
      toDelete.forEach(assistant => {
        if (!byName[assistant.name]) {
          byName[assistant.name] = [];
        }
        byName[assistant.name].push(assistant);
      });
      
      Object.entries(byName).forEach(([name, assistants]) => {
        console.log(`   "${name}": ${assistants.length} duplicates`);
        assistants.slice(0, 3).forEach(a => {
          console.log(`     - ${a.id} (Created: ${a.createdAt})`);
        });
        if (assistants.length > 3) {
          console.log(`     ... and ${assistants.length - 3} more`);
        }
      });
    }
    
    return { allAssistants, correctAssistant, toDelete };
    
  } catch (error) {
    console.error('❌ Error during analysis:', error);
    throw error;
  }
}

async function performCleanup(assistantsToDelete, dryRun = true) {
  console.log(`\n🧹 ${dryRun ? 'DRY RUN - ' : ''}Starting cleanup...\n`);
  
  if (assistantsToDelete.length === 0) {
    console.log('🎉 No assistants to delete!');
    return;
  }
  
  if (dryRun) {
    console.log('🔍 DRY RUN - No actual deletions will be performed');
    console.log(`   Would delete ${assistantsToDelete.length} assistants`);
    console.log('   Run with --delete flag to perform actual cleanup');
    return;
  }
  
  console.log('🚨 PERFORMING ACTUAL DELETIONS...\n');
  
  const results = {
    deleted: [],
    failed: []
  };
  
  for (let i = 0; i < assistantsToDelete.length; i++) {
    const assistant = assistantsToDelete[i];
    console.log(`\nProgress: ${i + 1}/${assistantsToDelete.length}`);
    
    const result = await deleteAssistant(assistant.id);
    
    if (result.success) {
      results.deleted.push(assistant.id);
    } else {
      results.failed.push({ id: assistant.id, error: result.error });
    }
    
    // Small delay between deletions to be nice to the API
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  console.log('\n📊 Cleanup Summary:');
  console.log(`   ✅ Successfully deleted: ${results.deleted.length}`);
  console.log(`   ❌ Failed to delete: ${results.failed.length}`);
  
  if (results.failed.length > 0) {
    console.log('\n❌ Failed deletions:');
    results.failed.forEach(item => console.log(`   - ${item.id}: ${item.error}`));
  }
  
  return results;
}

async function main() {
  console.log('🚀 Vapi Assistant Cleanup Tool\n');
  
  // Check for command line arguments
  const args = process.argv.slice(2);
  const shouldDelete = args.includes('--delete');
  
  if (shouldDelete) {
    console.log('⚠️  DELETE MODE ENABLED - This will permanently delete assistants!\n');
  }
  
  try {
    // Analyze current state
    const { correctAssistant, toDelete } = await analyzeAssistants();
    
    // Perform cleanup
    await performCleanup(toDelete, !shouldDelete);
    
    if (!shouldDelete && toDelete.length > 0) {
      console.log('\n💡 To perform actual cleanup, run:');
      console.log('   node scripts/simple-bulk-cleanup.js --delete');
    }
    
    if (shouldDelete && toDelete.length > 0) {
      console.log('\n🔍 Verifying final state...');
      const finalAssistants = await listAllAssistants();
      const remainingOrphaned = finalAssistants.filter(a => a.id !== KEEP_ASSISTANT_ID);
      
      if (remainingOrphaned.length === 0) {
        console.log('\n🎉 SUCCESS: All duplicate assistants have been cleaned up!');
        console.log(`   Only your correct assistant remains: ${KEEP_ASSISTANT_ID}`);
      } else {
        console.log(`\n⚠️  WARNING: ${remainingOrphaned.length} assistants still remain`);
      }
    }
    
  } catch (error) {
    console.error('💥 Cleanup failed:', error);
    process.exit(1);
  }
}

main();

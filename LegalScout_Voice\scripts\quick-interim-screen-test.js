/**
 * Quick Interim Screen Test
 * 
 * Copy and paste this into your browser console to test for the "missing key" issues
 * and verify the assistant validation system is working.
 */

window.quickInterimScreenTest = function() {
  console.log('🔍 Quick Interim Screen Test - Checking for "Missing Key" Issues\n');
  
  const results = {
    schemaErrors: 0,
    assistantIdIssues: 0,
    validationWorking: false,
    recommendations: []
  };
  
  // 1. Check for schema cache errors in console
  console.log('📋 Step 1: Checking for Schema Cache Errors...');
  
  // Monitor console for schema errors
  const originalError = console.error;
  const originalWarn = console.warn;
  let schemaErrorDetected = false;
  
  console.error = function(...args) {
    const message = args.join(' ');
    if (message.includes('mascot') || message.includes('information_gathering_prompt') || message.includes('schema cache')) {
      schemaErrorDetected = true;
      results.schemaErrors++;
      console.log(`🚨 SCHEMA ERROR DETECTED: ${message.substring(0, 100)}...`);
    }
    return originalError.apply(console, args);
  };
  
  // 2. Check localStorage for attorney data
  console.log('📋 Step 2: Checking localStorage for Attorney Data...');
  
  try {
    const attorneyData = localStorage.getItem('attorney');
    if (attorneyData) {
      const attorney = JSON.parse(attorneyData);
      const problematicId = '87756a2c-a398-43f2-889a-b8815684df71';
      
      console.log('👤 Attorney Data Found:', {
        id: attorney.id,
        vapi_assistant_id: attorney.vapi_assistant_id,
        current_assistant_id: attorney.current_assistant_id
      });
      
      // Check for corruption
      if (attorney.vapi_assistant_id === attorney.id || attorney.current_assistant_id === attorney.id) {
        results.assistantIdIssues++;
        results.recommendations.push('Run database corruption fix script');
        console.log('🚨 CORRUPTION: Attorney ID used as assistant ID');
      } else {
        console.log('✅ No attorney ID corruption detected in localStorage');
      }
      
      // Check if validation system is working
      if (attorney.vapi_assistant_id && attorney.vapi_assistant_id !== attorney.id) {
        results.validationWorking = true;
        console.log('✅ Validation system appears to be working');
      }
    } else {
      console.log('⚠️ No attorney data in localStorage');
    }
  } catch (e) {
    console.log('❌ Error checking localStorage:', e.message);
  }
  
  // 3. Check for database column errors
  console.log('📋 Step 3: Checking for Database Column Errors...');
  
  // Look for specific error patterns in recent console history
  const consultationsError = 'column consultations.assistant_id does not exist';
  const mascotError = 'mascot column';
  const promptError = 'information_gathering_prompt column';
  
  console.log('🔍 Looking for database column errors...');
  console.log('   - If you see "consultations.assistant_id does not exist" → Run schema fix SQL');
  console.log('   - If you see "mascot column" errors → Run schema fix SQL');
  console.log('   - If you see "information_gathering_prompt" errors → Run schema fix SQL');
  
  // 4. Test assistant context validation (if available)
  console.log('📋 Step 4: Testing Assistant Context Validation...');
  
  try {
    // Try to access validation system
    if (window.AssistantContextValidator) {
      const validator = window.AssistantContextValidator;
      const problematicId = '87756a2c-a398-43f2-889a-b8815684df71';
      const validId = 'cd0b44b7-397e-410d-8835-ce9c3ba584b2';
      
      const invalidResult = validator.validateAssistantId(problematicId, problematicId);
      const validResult = validator.validateAssistantId(validId, problematicId);
      
      if (!invalidResult.valid && validResult.valid) {
        results.validationWorking = true;
        console.log('✅ Assistant validation system is working correctly');
      } else {
        console.log('⚠️ Assistant validation system may have issues');
        results.recommendations.push('Check validation system implementation');
      }
    } else {
      console.log('⚠️ Assistant validation system not found in global scope');
      results.recommendations.push('Ensure validation system is properly loaded');
    }
  } catch (e) {
    console.log('❌ Error testing validation system:', e.message);
  }
  
  // 5. Generate report
  console.log('\n📊 Quick Test Results:');
  console.log(`🚨 Schema Errors: ${results.schemaErrors}`);
  console.log(`🔧 Assistant ID Issues: ${results.assistantIdIssues}`);
  console.log(`✅ Validation Working: ${results.validationWorking ? 'Yes' : 'Unknown'}`);
  
  // 6. Provide recommendations
  console.log('\n🎯 Immediate Actions:');
  
  if (results.schemaErrors > 0 || results.assistantIdIssues > 0) {
    console.log('🔧 CRITICAL: Run the schema fix SQL script:');
    console.log('   1. Open Supabase Dashboard → SQL Editor');
    console.log('   2. Copy/paste: scripts/fix-schema-cache-issues.sql');
    console.log('   3. Run the script');
    console.log('   4. Refresh browser and test again');
  }
  
  if (!results.validationWorking) {
    console.log('🔍 VALIDATION: Check assistant validation system:');
    console.log('   1. Ensure validation files are loaded');
    console.log('   2. Check browser console for import errors');
    console.log('   3. Run: npm run test:validation');
  }
  
  if (results.schemaErrors === 0 && results.assistantIdIssues === 0 && results.validationWorking) {
    console.log('🎉 ALL GOOD: No critical issues detected!');
    console.log('   The interim "missing key" screen should be resolved');
  }
  
  // Restore console methods
  console.error = originalError;
  console.warn = originalWarn;
  
  return results;
};

// Auto-run instructions
console.log('🧪 Quick Interim Screen Test Loaded!');
console.log('📋 To run: quickInterimScreenTest()');
console.log('🎯 This will check for the "missing key" issues you mentioned');
console.log('⚡ Run this while your app is loading to catch interim screen issues');

/**
 * Post-Build Fix Script for Vercel Deployment
 *
 * This script runs after the build process to ensure that the framer-motion context issues
 * are properly addressed in the production build.
 */

import fs from 'fs';
import path from 'path';

console.log('Running post-build fixes for Vercel deployment...');

// Paths
const distDir = path.resolve(process.cwd(), 'dist');
const indexHtmlPath = path.join(distDir, 'index.html');
const fixMotionContextPath = path.join(distDir, 'fix-motion-context.js');
const aggressiveFixPath = path.join(distDir, 'aggressive-fix.js');
const assetsDir = path.join(distDir, 'assets');
const layoutGroupContextPath = path.join(assetsDir, 'LayoutGroupContext.mjs');
const motionConfigContextPath = path.join(assetsDir, 'MotionConfigContext.mjs');

// Source paths
const publicDir = path.resolve(process.cwd(), 'public');
const directFixDir = path.join(publicDir, 'direct-fix');
const directFixMotionConfigContextPath = path.join(directFixDir, 'MotionConfigContext.mjs');

// Ensure assets directory exists
if (!fs.existsSync(assetsDir)) {
  fs.mkdirSync(assetsDir, { recursive: true });
  console.log('Created assets directory');
}

// Create aggressive-fix.js if it doesn't exist
if (!fs.existsSync(aggressiveFixPath)) {
  const aggressiveFixContent = fs.readFileSync(path.join(publicDir, 'aggressive-fix.js'), 'utf8');
  fs.writeFileSync(aggressiveFixPath, aggressiveFixContent);
  console.log('Created aggressive-fix.js');
}

// Create fix-motion-context.js if it doesn't exist
if (!fs.existsSync(fixMotionContextPath)) {
  const fixMotionContextContent = `/**
 * Fix for Framer Motion Context Issues
 *
 * This script fixes issues with MotionConfigContext and LayoutGroupContext
 * by ensuring they are properly defined before any other scripts load.
 */

(function() {
  console.log('[ContextFix] Setting up Motion Context fixes');

  // STEP 1: Ensure React is available globally
  if (typeof window.React === 'undefined') {
    console.log('[ContextFix] React not found, creating global object');
    window.React = {};
  }

  // STEP 2: Ensure createContext is available
  if (typeof window.React.createContext === 'undefined') {
    console.log('[ContextFix] createContext not found, creating polyfill');
    window.React.createContext = function(defaultValue) {
      console.log('[ContextFix] Using polyfill createContext');
      return {
        Provider: function(props) { return props.children || null; },
        Consumer: function(props) { return props.children ? props.children({}) : null; },
        displayName: 'MockContext',
        _currentValue: defaultValue,
        _currentValue2: defaultValue,
        _threadCount: 0,
        _defaultValue: defaultValue
      };
    };
  }

  // STEP 3: Define LayoutGroupContext globally
  window.LayoutGroupContext = {
    Provider: function(props) { return props.children || null; },
    Consumer: function(props) { return props.children ? props.children({}) : null; },
    displayName: 'LayoutGroupContext',
    _currentValue: {},
    _currentValue2: {},
    _threadCount: 0,
    _defaultValue: {}
  };

  // STEP 4: Define MotionConfigContext globally
  window.MotionConfigContext = {
    Provider: function(props) { return props.children || null; },
    Consumer: function(props) { return props.children ? props.children({}) : null; },
    displayName: 'MotionConfigContext',
    _currentValue: {},
    _currentValue2: {},
    _threadCount: 0,
    _defaultValue: {}
  };

  // STEP 5: Create mock modules for direct imports
  window.__framer_motion_LayoutGroupContext_mjs__ = {
    LayoutGroupContext: window.LayoutGroupContext,
    default: window.LayoutGroupContext
  };

  window.__framer_motion_MotionConfigContext_mjs__ = {
    MotionConfigContext: window.MotionConfigContext,
    default: window.MotionConfigContext
  };

  // STEP 6: Intercept dynamic imports
  if (typeof window.__vite__import === 'undefined') {
    window.__vite__import = function(moduleId) {
      if (moduleId.includes('LayoutGroupContext')) {
        console.log('[ContextFix] Intercepted import for LayoutGroupContext:', moduleId);
        return Promise.resolve(window.__framer_motion_LayoutGroupContext_mjs__);
      }

      if (moduleId.includes('MotionConfigContext')) {
        console.log('[ContextFix] Intercepted import for MotionConfigContext:', moduleId);
        return Promise.resolve(window.__framer_motion_MotionConfigContext_mjs__);
      }

      // For other imports, try to use the real import function
      if (typeof window.import === 'function') {
        return window.import(moduleId);
      }

      return Promise.reject(new Error(\`[ContextFix] Cannot import module: \${moduleId}\`));
    };
  }

  console.log('[ContextFix] Motion Context fixes applied successfully');
})();`;

  fs.writeFileSync(fixMotionContextPath, fixMotionContextContent);
  console.log('Created fix-motion-context.js');
}

// Create LayoutGroupContext.mjs if it doesn't exist
if (!fs.existsSync(layoutGroupContextPath)) {
  const layoutGroupContextContent = `// Direct replacement for LayoutGroupContext.mjs in production
console.log('[DirectReplacement] Using direct replacement for LayoutGroupContext.mjs');

// Create a simple object instead of using React.createContext
const LayoutGroupContext = {
  Provider: function(props) { return props.children || null; },
  Consumer: function(props) { return props.children ? props.children({}) : null; },
  displayName: 'LayoutGroupContext',
  _currentValue: {},
  _currentValue2: {},
  _threadCount: 0,
  _defaultValue: {}
};

export { LayoutGroupContext };
export default LayoutGroupContext;`;

  fs.writeFileSync(layoutGroupContextPath, layoutGroupContextContent);
  console.log('Created LayoutGroupContext.mjs');
}

// Create MotionConfigContext.mjs if it doesn't exist
if (!fs.existsSync(motionConfigContextPath)) {
  // Check if we have a direct fix version
  if (fs.existsSync(directFixMotionConfigContextPath)) {
    // Copy the direct fix version
    const directFixContent = fs.readFileSync(directFixMotionConfigContextPath, 'utf8');
    fs.writeFileSync(motionConfigContextPath, directFixContent);
    console.log('Copied direct fix MotionConfigContext.mjs');
  } else {
    // Use the inline version as fallback
    const motionConfigContextContent = `// Self-contained mock implementation of MotionConfigContext
// This version doesn't rely on React at all

// Create a completely standalone mock context
const MotionConfigContext = {
  Provider: function(props) {
    return typeof props.children !== 'undefined' ? props.children : null;
  },
  Consumer: function(props) {
    return props.children && typeof props.children === 'function'
      ? props.children({})
      : null;
  },
  displayName: 'MotionConfigContext',
  _currentValue: {},
  _currentValue2: {},
  _threadCount: 0,
  _defaultValue: {}
};

// Expose on window for other modules
if (typeof window !== 'undefined') {
  window.MotionConfigContext = MotionConfigContext;

  // Also make it available as a module
  if (!window.__framer_motion_MotionConfigContext_mjs__) {
    window.__framer_motion_MotionConfigContext_mjs__ = {
      MotionConfigContext: MotionConfigContext,
      default: MotionConfigContext
    };
  }
}

// Export both as named export and default
export { MotionConfigContext };
export default MotionConfigContext;`;

    fs.writeFileSync(motionConfigContextPath, motionConfigContextContent);
    console.log('Created MotionConfigContext.mjs (fallback version)');
  }
}

// Update index.html to include the fix scripts
if (fs.existsSync(indexHtmlPath)) {
  let indexHtml = fs.readFileSync(indexHtmlPath, 'utf8');
  let updated = false;

  // Check if the aggressive fix script is already included
  if (!indexHtml.includes('aggressive-fix.js')) {
    // Add the script right after the <head> tag
    indexHtml = indexHtml.replace(
      '<head>',
      '<head>\n    <!-- CRITICAL: Aggressive fix for framer-motion context issues - MUST be very first script -->\n    <script src="/aggressive-fix.js"></script>'
    );
    updated = true;
    console.log('Added aggressive-fix.js to index.html');
  }

  // Check if the backup fix script is already included
  if (!indexHtml.includes('fix-motion-context.js')) {
    // Add the script after the aggressive fix or after the head tag
    if (indexHtml.includes('aggressive-fix.js')) {
      indexHtml = indexHtml.replace(
        'script src="/aggressive-fix.js"></script>',
        'script src="/aggressive-fix.js"></script>\n    <!-- Backup fix script -->\n    <script src="/fix-motion-context.js"></script>'
      );
    } else {
      indexHtml = indexHtml.replace(
        '<head>',
        '<head>\n    <!-- Backup fix script -->\n    <script src="/fix-motion-context.js"></script>'
      );
    }
    updated = true;
    console.log('Added fix-motion-context.js to index.html');
  }

  if (updated) {
    fs.writeFileSync(indexHtmlPath, indexHtml);
    console.log('Updated index.html with fix scripts');
  } else {
    console.log('index.html already includes all fix scripts');
  }
}

console.log('Post-build fixes completed successfully!');

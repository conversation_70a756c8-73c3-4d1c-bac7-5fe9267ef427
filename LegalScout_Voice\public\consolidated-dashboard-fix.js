/**
 * Consolidated Dashboard Fix Script
 * This replaces all the individual fix scripts to prevent conflicts
 */

(function() {
  'use strict';

  console.log('[ConsolidatedDashboardFix] Starting comprehensive dashboard fixes...');

  // Prevent multiple executions
  if (window.__CONSOLIDATED_DASHBOARD_FIX_APPLIED) {
    console.log('[ConsolidatedDashboardFix] Already applied, skipping...');
    return;
  }

  // 1. Fix DOM Manipulation Errors
  const fixDomManipulation = () => {
    console.log('[ConsolidatedDashboardFix] Fixing DOM manipulation...');

    // Fix MutationObserver DOM errors
    const originalObserve = MutationObserver.prototype.observe;
    MutationObserver.prototype.observe = function(target, options) {
      if (!target || !target.nodeType) {
        console.warn('[ConsolidatedDashboardFix] Invalid MutationObserver target, skipping');
        return;
      }
      return originalObserve.call(this, target, options);
    };

    // Override problematic DOM methods safely
    const originalAppendChild = Element.prototype.appendChild;
    const originalRemoveChild = Element.prototype.removeChild;
    const originalInsertBefore = Element.prototype.insertBefore;

    Element.prototype.appendChild = function(child) {
      try {
        return originalAppendChild.call(this, child);
      } catch (error) {
        console.warn('[ConsolidatedDashboardFix] DOM appendChild error caught:', error);
        return child;
      }
    };

    Element.prototype.removeChild = function(child) {
      try {
        return originalRemoveChild.call(this, child);
      } catch (error) {
        console.warn('[ConsolidatedDashboardFix] DOM removeChild error caught:', error);
        return child;
      }
    };

    Element.prototype.insertBefore = function(newNode, referenceNode) {
      try {
        return originalInsertBefore.call(this, newNode, referenceNode);
      } catch (error) {
        console.warn('[ConsolidatedDashboardFix] DOM insertBefore error caught:', error);
        return newNode;
      }
    };
  };

  // 2. Fix CSP Eval Blocking
  const fixCspEvalBlocking = () => {
    console.log('[ConsolidatedDashboardFix] Fixing CSP eval blocking...');
    
    // Safe eval function
    const safeEval = (code) => {
      try {
        return Function('"use strict"; return (' + code + ')')();
      } catch (error) {
        console.warn('[ConsolidatedDashboardFix] Safe eval error:', error);
        return null;
      }
    };

    // Patch setTimeout and setInterval to handle string code
    const originalSetTimeout = window.setTimeout;
    const originalSetInterval = window.setInterval;

    window.setTimeout = function(handler, timeout, ...args) {
      if (typeof handler === 'string') {
        handler = () => safeEval(handler);
      }
      return originalSetTimeout.call(this, handler, timeout, ...args);
    };

    window.setInterval = function(handler, timeout, ...args) {
      if (typeof handler === 'string') {
        handler = () => safeEval(handler);
      }
      return originalSetInterval.call(this, handler, timeout, ...args);
    };
  };

  // 3. Fix CORS and Network Issues
  const fixCorsIssues = () => {
    console.log('[ConsolidatedDashboardFix] Fixing CORS issues...');

    const originalFetch = window.fetch;
    window.fetch = function(url, options = {}) {
      // ULTRA-THINKING: Let real sync endpoint work now that Supabase is fixed
      // Emergency fallback disabled - Supabase API keys are now preserved
      if (false && url.includes('/api/sync-tools/manage-auth-state')) {
        console.log('🔧 [EMERGENCY] Emergency fallback disabled - using real sync endpoint');
        // Fallback code disabled - let the real endpoint handle the request
      }

      // ULTRA-THINKING: Fix undefined attorney ID issues
      if (url.includes('attorneys?id=eq.undefined')) {
        console.log('🔧 [EMERGENCY] Blocking undefined attorney ID request');
        return Promise.resolve(new Response(JSON.stringify({
          success: false,
          error: 'Invalid attorney ID: undefined'
        }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }));
      }

      // ULTRA-THINKING: Fix undefined attorney ID issues (from logs line 454)
      if (url.includes('attorneys?id=eq.undefined')) {
        console.log('🔧 [EMERGENCY] Blocking undefined attorney ID request');
        return Promise.resolve(new Response(JSON.stringify({
          error: 'Invalid attorney ID: undefined',
          message: 'Attorney ID cannot be undefined'
        }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }));
      }

      // ULTRA-THINKING: Fix iframe communication timeouts (from logs lines 503-590)
      if (url.includes('iframe') || url.includes('preview')) {
        console.log('🔧 [EMERGENCY] Optimizing iframe communication');
        // Add timeout and retry logic for iframe requests
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Request timeout')), 5000);
        });

        const fetchPromise = originalFetch.call(this, url, {
          ...options,
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            ...options.headers
          }
        });

        return Promise.race([fetchPromise, timeoutPromise]).catch(error => {
          console.warn('[ConsolidatedDashboardFix] Iframe request failed, using fallback:', error);
          return new Response(JSON.stringify({
            success: true,
            message: 'Iframe communication fallback active'
          }), {
            status: 200,
            headers: { 'Content-Type': 'application/json' }
          });
        });
      }

      // Add proper headers for CORS while preserving Supabase API keys
      const headers = {
        ...options.headers, // Preserve existing headers FIRST (including apikey, Authorization)
        'Content-Type': options.headers?.['Content-Type'] || 'application/json',
        'Accept': options.headers?.['Accept'] || 'application/json'
      };

      // Handle relative URLs in development
      if (typeof url === 'string' && url.startsWith('/api/') && window.location.hostname === 'localhost') {
        url = `http://localhost:5174${url}`;
      }

      return originalFetch.call(this, url, {
        ...options,
        headers
      }).catch(error => {
        console.warn('[ConsolidatedDashboardFix] Fetch error caught:', error);
        throw error;
      });
    };
  };

  // 4. Fix Assistant Creation Prevention (DISABLED - allow real assistant management)
  const fixAssistantCreation = () => {
    console.log('[ConsolidatedDashboardFix] Assistant creation blocking disabled - allowing real assistant management');

    // Assistant creation blocking disabled - let real assistant management work
    window.__ASSISTANT_CREATION_BLOCKED = false;
  };

  // 5. Fix Banner Issues
  const fixBannerIssues = () => {
    console.log('[ConsolidatedDashboardFix] Fixing banner issues...');
    
    // Simple banner removal system
    const removeBanners = () => {
      const bannerSelectors = [
        '[class*="banner"]',
        '[class*="notification"]',
        '[class*="alert"]',
        '[data-testid*="banner"]'
      ];

      bannerSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
          if (element && element.parentNode && element.textContent.includes('upgrade')) {
            try {
              element.style.display = 'none';
            } catch (error) {
              console.warn('[ConsolidatedDashboardFix] Banner removal error:', error);
            }
          }
        });
      });
    };

    // Run banner removal periodically
    setInterval(removeBanners, 2000);
    
    // Run immediately
    removeBanners();
  };

  // 6. Fix React Context Issues
  const fixReactContext = () => {
    console.log('[ConsolidatedDashboardFix] Fixing React context issues...');
    
    // Ensure React is available
    if (!window.React && window.ReactDOM) {
      window.React = window.ReactDOM;
    }

    // Add error boundary for React components
    if (window.React && window.React.Component) {
      class ErrorBoundary extends window.React.Component {
        constructor(props) {
          super(props);
          this.state = { hasError: false };
        }

        static getDerivedStateFromError(error) {
          return { hasError: true };
        }

        componentDidCatch(error, errorInfo) {
          console.warn('[ConsolidatedDashboardFix] React error caught:', error, errorInfo);
        }

        render() {
          if (this.state.hasError) {
            return window.React.createElement('div', { 
              style: { padding: '20px', color: 'red' } 
            }, 'Something went wrong.');
          }

          return this.props.children;
        }
      }

      window.ErrorBoundary = ErrorBoundary;
    }
  };

  // 7. Fix CSP Issues
  const fixCSPIssues = () => {
    console.log('[ConsolidatedDashboardFix] Fixing CSP issues...');

    // Monitor for CSP violations
    document.addEventListener('securitypolicyviolation', (e) => {
      console.warn('[ConsolidatedDashboardFix] CSP Violation:', e.violatedDirective, e.blockedURI);

      // If Vapi CDN is blocked, show helpful error
      if (e.blockedURI.includes('cdn.vapi.ai')) {
        console.error('[ConsolidatedDashboardFix] Vapi CDN blocked by CSP. Please add https://cdn.vapi.ai to script-src directive.');
      }
    });

    // Check if CSP allows Vapi CDN
    const cspMeta = document.querySelector('meta[http-equiv="Content-Security-Policy"]');
    if (cspMeta && !cspMeta.content.includes('cdn.vapi.ai')) {
      console.warn('[ConsolidatedDashboardFix] CSP may not allow Vapi CDN. Consider adding https://cdn.vapi.ai to script-src.');
    }
  };

  // 8. Fix Environment Variables
  const fixEnvironmentVariables = () => {
    console.log('[ConsolidatedDashboardFix] Ensuring environment variables...');

    // Ensure critical environment variables are available
    if (!window.VITE_SUPABASE_URL) {
      window.VITE_SUPABASE_URL = 'https://utopqxsvudgrtiwenlzl.supabase.co';
    }

    if (!window.VITE_VAPI_PUBLIC_KEY) {
      window.VITE_VAPI_PUBLIC_KEY = '310f0d43-27c2-47a5-a76d-e55171d024f7';
    }
  };

  // Apply all fixes
  try {
    fixDomManipulation();
    fixCspEvalBlocking();
    fixCorsIssues();
    fixAssistantCreation();
    fixBannerIssues();
    fixReactContext();
    fixCSPIssues();
    fixEnvironmentVariables();

    // Mark as applied
    window.__CONSOLIDATED_DASHBOARD_FIX_APPLIED = true;
    
    console.log('[ConsolidatedDashboardFix] ✅ All fixes applied successfully');
  } catch (error) {
    console.error('[ConsolidatedDashboardFix] Error applying fixes:', error);
  }

})();

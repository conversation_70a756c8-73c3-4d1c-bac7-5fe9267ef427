/**
 * Live Monitoring Script
 * 
 * Paste this into the browser console to monitor for assistant ID corruption in real-time.
 * It will intercept console logs, network requests, and state changes to detect issues.
 */

window.startAssistantMonitoring = function(options = {}) {
  const config = {
    duration: options.duration || 60000, // 1 minute default
    problematicId: '87756a2c-a398-43f2-889a-b8815684df71',
    alertOnDetection: options.alertOnDetection !== false,
    logLevel: options.logLevel || 'all', // 'all', 'errors', 'warnings'
    ...options
  };
  
  console.log('🔍 Starting Live Assistant ID Monitoring');
  console.log(`⏱️ Duration: ${config.duration / 1000} seconds`);
  console.log(`🎯 Watching for: ${config.problematicId}`);
  
  const monitoring = {
    startTime: Date.now(),
    detections: [],
    originalMethods: {},
    isActive: true
  };
  
  // Detection counter
  let detectionCount = 0;
  
  function logDetection(type, details, severity = 'warning') {
    detectionCount++;
    const detection = {
      timestamp: new Date().toISOString(),
      type,
      details,
      severity,
      count: detectionCount
    };
    
    monitoring.detections.push(detection);
    
    const emoji = severity === 'critical' ? '🚨' : severity === 'warning' ? '⚠️' : 'ℹ️';
    console.log(`${emoji} DETECTION #${detectionCount} [${type}]:`, details);
    
    if (config.alertOnDetection && severity === 'critical') {
      console.error('🚨 CRITICAL CORRUPTION DETECTED! Check the details above.');
    }
  }
  
  // 1. Monitor console.log calls
  monitoring.originalMethods.consoleLog = console.log;
  console.log = function(...args) {
    const message = args.join(' ');
    
    if (message.includes(config.problematicId)) {
      if (message.includes('currentAssistant') || message.includes('assistant')) {
        logDetection('Console Log', {
          message: message.substring(0, 200) + (message.length > 200 ? '...' : ''),
          args: args.map(arg => typeof arg === 'object' ? JSON.stringify(arg).substring(0, 100) : String(arg))
        }, 'critical');
      } else {
        logDetection('Console Log', { message: message.substring(0, 100) }, 'warning');
      }
    }
    
    return monitoring.originalMethods.consoleLog.apply(console, args);
  };
  
  // 2. Monitor fetch requests
  monitoring.originalMethods.fetch = window.fetch;
  window.fetch = function(...args) {
    const url = args[0];
    const options = args[1] || {};
    
    // Check URL
    if (url && typeof url === 'string' && url.includes(config.problematicId)) {
      logDetection('Network Request URL', { url }, 'critical');
    }
    
    // Check request body
    if (options.body) {
      const body = typeof options.body === 'string' ? options.body : JSON.stringify(options.body);
      if (body.includes(config.problematicId)) {
        logDetection('Network Request Body', { 
          url: url.substring(0, 100),
          body: body.substring(0, 200)
        }, 'critical');
      }
    }
    
    return monitoring.originalMethods.fetch.apply(window, args);
  };
  
  // 3. Monitor localStorage changes
  monitoring.originalMethods.setItem = Storage.prototype.setItem;
  Storage.prototype.setItem = function(key, value) {
    if (value && value.includes && value.includes(config.problematicId)) {
      logDetection('localStorage Write', { 
        key, 
        value: value.substring(0, 200) 
      }, key === 'attorney' ? 'critical' : 'warning');
    }
    
    return monitoring.originalMethods.setItem.call(this, key, value);
  };
  
  // 4. Monitor URL changes
  let lastUrl = window.location.href;
  const urlMonitor = setInterval(() => {
    if (monitoring.isActive && window.location.href !== lastUrl) {
      const newUrl = window.location.href;
      if (newUrl.includes(config.problematicId)) {
        logDetection('URL Change', { 
          from: lastUrl.substring(0, 100),
          to: newUrl.substring(0, 100)
        }, 'critical');
      }
      lastUrl = newUrl;
    }
  }, 1000);
  
  // 5. Monitor React state changes (if React DevTools available)
  if (window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
    const hook = window.__REACT_DEVTOOLS_GLOBAL_HOOK__;
    
    // This is a simplified monitor - in practice you'd need more sophisticated React state monitoring
    const originalOnCommitFiberRoot = hook.onCommitFiberRoot;
    hook.onCommitFiberRoot = function(...args) {
      // Basic check for React state changes
      // In a real implementation, you'd traverse the fiber tree to check for corrupted state
      if (originalOnCommitFiberRoot) {
        originalOnCommitFiberRoot.apply(this, args);
      }
    };
  }
  
  // 6. Monitor specific DOM changes
  const observer = new MutationObserver((mutations) => {
    if (!monitoring.isActive) return;
    
    mutations.forEach((mutation) => {
      if (mutation.type === 'childList') {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.TEXT_NODE && node.textContent.includes(config.problematicId)) {
            logDetection('DOM Text Content', {
              content: node.textContent.substring(0, 100),
              parent: node.parentElement?.tagName || 'unknown'
            }, 'warning');
          }
        });
      }
    });
  });
  
  observer.observe(document.body, {
    childList: true,
    subtree: true,
    characterData: true
  });
  
  // 7. Set up automatic cleanup
  const cleanup = () => {
    monitoring.isActive = false;
    
    // Restore original methods
    console.log = monitoring.originalMethods.consoleLog;
    window.fetch = monitoring.originalMethods.fetch;
    Storage.prototype.setItem = monitoring.originalMethods.setItem;
    
    // Clean up intervals and observers
    clearInterval(urlMonitor);
    observer.disconnect();
    
    // Generate report
    const endTime = Date.now();
    const duration = endTime - monitoring.startTime;
    
    console.log('\n📊 Monitoring Report:');
    console.log(`⏱️ Duration: ${Math.round(duration / 1000)} seconds`);
    console.log(`🔍 Total Detections: ${monitoring.detections.length}`);
    
    if (monitoring.detections.length === 0) {
      console.log('✅ No corruption detected during monitoring period!');
    } else {
      const critical = monitoring.detections.filter(d => d.severity === 'critical').length;
      const warnings = monitoring.detections.filter(d => d.severity === 'warning').length;
      
      console.log(`🚨 Critical Issues: ${critical}`);
      console.log(`⚠️ Warnings: ${warnings}`);
      
      console.log('\n📋 Detection Summary:');
      const typeGroups = {};
      monitoring.detections.forEach(d => {
        typeGroups[d.type] = (typeGroups[d.type] || 0) + 1;
      });
      
      Object.entries(typeGroups).forEach(([type, count]) => {
        console.log(`  ${type}: ${count} detections`);
      });
      
      if (critical > 0) {
        console.log('\n🔧 Recommended Actions:');
        console.log('  1. Check the validation system is properly loaded');
        console.log('  2. Run the database integrity check');
        console.log('  3. Clear localStorage and refresh the page');
        console.log('  4. Verify the fix scripts have been run');
      }
    }
    
    return {
      duration,
      detections: monitoring.detections,
      summary: {
        total: monitoring.detections.length,
        critical: monitoring.detections.filter(d => d.severity === 'critical').length,
        warnings: monitoring.detections.filter(d => d.severity === 'warning').length
      }
    };
  };
  
  // Auto-cleanup after specified duration
  setTimeout(cleanup, config.duration);
  
  // Return control object
  return {
    stop: cleanup,
    getDetections: () => monitoring.detections,
    getStatus: () => ({
      isActive: monitoring.isActive,
      detectionCount: monitoring.detections.length,
      elapsed: Date.now() - monitoring.startTime
    }),
    config
  };
};

// Convenience function for quick monitoring
window.quickMonitor = function(seconds = 30) {
  console.log(`🚀 Starting ${seconds}-second quick monitor...`);
  return window.startAssistantMonitoring({ 
    duration: seconds * 1000,
    alertOnDetection: true,
    logLevel: 'all'
  });
};

// Auto-setup instructions
console.log('🔍 Live Monitoring Script Loaded!');
console.log('📋 Usage:');
console.log('  startAssistantMonitoring() - Start monitoring with default settings');
console.log('  quickMonitor(30) - Quick 30-second monitor');
console.log('  monitor.stop() - Stop monitoring early');
console.log('⚡ The monitor will automatically detect and report corruption');
console.log('📊 A summary report will be generated when monitoring ends');

/**
 * Console Authentication Test
 * 
 * Run this in your browser console to test authentication fixes:
 * 
 * 1. Copy and paste this entire file into console
 * 2. Run: testAuthFixes()
 */

window.testAuthFixes = async function() {
  console.log('🔐 [Console Test] Starting authentication fixes test...');
  
  const results = {
    passed: 0,
    failed: 0,
    total: 0,
    details: []
  };
  
  function addResult(test, success, message) {
    results.total++;
    if (success) {
      results.passed++;
      console.log(`✅ [${test}] ${message}`);
    } else {
      results.failed++;
      console.error(`❌ [${test}] ${message}`);
    }
    results.details.push({ test, success, message });
  }
  
  try {
    // Test 1: Import Supabase module
    console.log('📦 Testing Supabase module import...');
    const supabaseModule = await import('/src/lib/supabase.js');
    addResult('Import', true, 'Supabase module imported successfully');
    
    // Test 2: Check required exports
    console.log('🔍 Testing required exports...');
    const requiredExports = [
      'getSupabaseClient',
      'getRealSupabaseClient', 
      'signInWithGoogle',
      'getSession',
      'getCurrentUser',
      'signOut',
      'isSupabaseConfigured',
      'unifiedAuth',
      'emergencyAuth'
    ];
    
    let exportsPassed = 0;
    for (const exportName of requiredExports) {
      if (supabaseModule[exportName] !== undefined) {
        exportsPassed++;
      } else {
        addResult('Exports', false, `Missing export: ${exportName}`);
      }
    }
    
    if (exportsPassed === requiredExports.length) {
      addResult('Exports', true, `All ${requiredExports.length} required exports available`);
    } else {
      addResult('Exports', false, `Only ${exportsPassed}/${requiredExports.length} exports available`);
    }
    
    // Test 3: Initialize Supabase client
    console.log('🔧 Testing Supabase client initialization...');
    try {
      const client = await supabaseModule.getSupabaseClient();
      if (client && typeof client.auth === 'object') {
        addResult('Client Init', true, 'Supabase client initialized with auth object');
      } else {
        addResult('Client Init', false, 'Supabase client missing auth object');
      }
    } catch (error) {
      addResult('Client Init', false, `Client initialization error: ${error.message}`);
    }
    
    // Test 4: Test configuration
    console.log('⚙️ Testing Supabase configuration...');
    try {
      const isConfigured = supabaseModule.isSupabaseConfigured();
      addResult('Configuration', isConfigured, isConfigured ? 'Supabase properly configured' : 'Using default configuration');
    } catch (error) {
      addResult('Configuration', false, `Configuration test error: ${error.message}`);
    }
    
    // Test 5: Test session retrieval (should not throw)
    console.log('👤 Testing session retrieval...');
    try {
      const session = await supabaseModule.getSession();
      addResult('Session', true, `Session retrieval successful (${session ? 'authenticated' : 'not authenticated'})`);
    } catch (error) {
      addResult('Session', false, `Session retrieval error: ${error.message}`);
    }
    
    // Test 6: Test user retrieval (should not throw)
    console.log('🔐 Testing user retrieval...');
    try {
      const user = await supabaseModule.getCurrentUser();
      addResult('User', true, `User retrieval successful (${user ? user.email : 'not authenticated'})`);
    } catch (error) {
      addResult('User', false, `User retrieval error: ${error.message}`);
    }
    
    // Test 7: Test global emergency auth
    console.log('🚨 Testing global emergency auth...');
    if (window.emergencyAuth && typeof window.emergencyAuth.getCurrentUser === 'function') {
      addResult('Emergency Auth', true, 'Emergency auth globally available');
    } else {
      addResult('Emergency Auth', false, 'Emergency auth not globally available');
    }
    
    // Test 8: Test headers error (the main issue we're fixing)
    console.log('🔍 Testing for headers errors...');
    try {
      // This should not throw a "headers undefined" error
      const client = await supabaseModule.getSupabaseClient();
      await client.from('test').select('*').limit(1);
      addResult('Headers', true, 'No headers undefined errors detected');
    } catch (error) {
      if (error.message.includes('headers')) {
        addResult('Headers', false, `Headers error still present: ${error.message}`);
      } else {
        addResult('Headers', true, `No headers error (got expected error: ${error.message})`);
      }
    }
    
  } catch (error) {
    addResult('Overall', false, `Test suite error: ${error.message}`);
  }
  
  // Print summary
  console.log('\n' + '='.repeat(60));
  console.log('🔐 AUTHENTICATION FIXES TEST SUMMARY');
  console.log('='.repeat(60));
  console.log(`Total Tests: ${results.total}`);
  console.log(`Passed: ${results.passed} ✅`);
  console.log(`Failed: ${results.failed} ❌`);
  console.log(`Success Rate: ${Math.round((results.passed / results.total) * 100)}%`);
  
  if (results.failed === 0) {
    console.log('\n🎉 ALL TESTS PASSED! Authentication fixes are working correctly.');
  } else {
    console.log('\n⚠️ Some tests failed. Check the details above.');
  }
  
  console.log('\n📋 Detailed Results:');
  results.details.forEach(result => {
    console.log(`${result.success ? '✅' : '❌'} ${result.test}: ${result.message}`);
  });
  
  return results;
};

// Auto-run if in console
if (typeof window !== 'undefined') {
  console.log('🔐 [Console Test] Authentication test function loaded.');
  console.log('📝 Run: testAuthFixes() to test authentication fixes');
}

#!/bin/bash

# Quick and Safe Deployment for Assistant Data Synchronization
# This creates ONLY the core files needed - no modifications to existing files

echo "🚀 Quick Deploy: Assistant Data Synchronization Core Files"
echo "========================================================="
echo ""
echo "This will create 2 essential files to get started:"
echo "✅ src/services/assistantSyncManager.js (Core sync manager)"
echo "✅ src/hooks/useAssistantSync.js (React hook for components)"
echo ""
echo "⚠️  SAFE: No existing files will be modified"
echo "⚠️  You can review and test before committing"
echo ""

read -p "Create these files? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ Cancelled"
    exit 1
fi

# Create directories
mkdir -p src/services
mkdir -p src/hooks

echo "📝 Creating core synchronization files..."

# 1. AssistantSyncManager - Core synchronization coordinator
cat > "src/services/assistantSyncManager.js" << 'EOF'
/**
 * Assistant Sync Manager - Core synchronization coordinator
 * Manages data synchronization across all LegalScout dashboard components
 */

import { AssistantDataService } from './assistantDataService';

class AssistantSyncManager {
  constructor() {
    this.subscribers = new Set();
    this.currentAssistantId = null;
    this.currentAttorneyId = null;
    this.syncInProgress = false;
    this.lastSyncTime = null;
    this.realtimeSubscription = null;
    
    this.initializeRealtimeSubscriptions();
  }

  subscribe(callback) {
    this.subscribers.add(callback);
    return () => this.subscribers.delete(callback);
  }

  notifySubscribers(eventType, data) {
    console.log(`📡 [AssistantSyncManager] Notifying ${this.subscribers.size} subscribers:`, eventType);
    this.subscribers.forEach(callback => {
      try {
        callback(eventType, data);
      } catch (error) {
        console.error('❌ [AssistantSyncManager] Error in subscriber callback:', error);
      }
    });
  }

  async initializeRealtimeSubscriptions() {
    try {
      const { supabase } = await import('../lib/supabase');
      
      this.realtimeSubscription = supabase
        .channel('assistant_sync')
        .on('postgres_changes', 
          { event: '*', schema: 'public', table: 'assistant_ui_configs' },
          (payload) => this.handleRealtimeUpdate('ui_config', payload)
        )
        .on('postgres_changes',
          { event: '*', schema: 'public', table: 'assistant_subdomains' },
          (payload) => this.handleRealtimeUpdate('subdomain', payload)
        )
        .subscribe();
        
      console.log('✅ [AssistantSyncManager] Real-time subscriptions initialized');
    } catch (error) {
      console.error('❌ [AssistantSyncManager] Error initializing real-time subscriptions:', error);
    }
  }

  handleRealtimeUpdate(dataType, payload) {
    if (this.doesUpdateAffectCurrentAssistant(payload)) {
      this.notifySubscribers('realtime_update', {
        dataType,
        payload,
        assistantId: this.currentAssistantId,
        timestamp: new Date().toISOString()
      });
    }
  }

  doesUpdateAffectCurrentAssistant(payload) {
    if (!this.currentAssistantId || !payload.new) return false;
    return payload.new.assistant_id === this.currentAssistantId ||
           payload.new.current_assistant_id === this.currentAssistantId ||
           payload.new.vapi_assistant_id === this.currentAssistantId;
  }

  setCurrentAssistant(assistantId, attorneyId) {
    const previousAssistantId = this.currentAssistantId;
    this.currentAssistantId = assistantId;
    this.currentAttorneyId = attorneyId;
    
    if (previousAssistantId !== assistantId) {
      this.notifySubscribers('assistant_changed', {
        previousAssistantId,
        currentAssistantId: assistantId,
        attorneyId,
        timestamp: new Date().toISOString()
      });
    }
  }

  async synchronizeAssistantSelection(attorneyId, assistantId) {
    if (this.syncInProgress) return;

    try {
      this.syncInProgress = true;
      this.setCurrentAssistant(assistantId, attorneyId);

      // Use existing AssistantDataService if available
      if (AssistantDataService.synchronizeAssistantSelection) {
        const result = await AssistantDataService.synchronizeAssistantSelection(attorneyId, assistantId);
        this.lastSyncTime = new Date().toISOString();
        
        this.notifySubscribers('sync_completed', {
          assistantId, attorneyId, result,
          timestamp: this.lastSyncTime
        });
        
        return result;
      } else {
        // Fallback to basic notification
        this.notifySubscribers('assistant_selected', {
          assistantId, attorneyId,
          timestamp: new Date().toISOString()
        });
      }
    } catch (error) {
      this.notifySubscribers('sync_error', {
        error: error.message, assistantId, attorneyId,
        timestamp: new Date().toISOString()
      });
      throw error;
    } finally {
      this.syncInProgress = false;
    }
  }

  getSyncStatus() {
    return {
      currentAssistantId: this.currentAssistantId,
      currentAttorneyId: this.currentAttorneyId,
      syncInProgress: this.syncInProgress,
      lastSyncTime: this.lastSyncTime,
      subscriberCount: this.subscribers.size,
      hasRealtimeSubscription: !!this.realtimeSubscription
    };
  }
}

const assistantSyncManager = new AssistantSyncManager();
export { assistantSyncManager };
export default assistantSyncManager;
EOF

# 2. useAssistantSync Hook - React integration
cat > "src/hooks/useAssistantSync.js" << 'EOF'
/**
 * useAssistantSync Hook - React integration for assistant synchronization
 */

import { useState, useEffect, useCallback } from 'react';
import { assistantSyncManager } from '../services/assistantSyncManager';
import { useAssistantAware } from '../contexts/AssistantAwareContext';

export const useAssistantSync = (options = {}) => {
  const { autoSync = true, dataTypes = [] } = options;
  const [syncStatus, setSyncStatus] = useState(assistantSyncManager.getSyncStatus());
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [lastUpdate, setLastUpdate] = useState(null);

  const { currentAssistant, isAssistantSelected } = useAssistantAware();

  const handleSyncEvent = useCallback((eventType, data) => {
    console.log('🔄 [useAssistantSync] Received sync event:', eventType);
    
    switch (eventType) {
      case 'assistant_changed':
      case 'sync_completed':
        setIsLoading(false);
        setError(null);
        setSyncStatus(assistantSyncManager.getSyncStatus());
        setLastUpdate(data);
        break;
        
      case 'sync_error':
        setIsLoading(false);
        setError(data.error);
        break;
        
      case 'realtime_update':
        if (dataTypes.length === 0 || dataTypes.includes(data.dataType)) {
          setLastUpdate(data);
        }
        break;
    }
  }, [dataTypes]);

  useEffect(() => {
    const unsubscribe = assistantSyncManager.subscribe(handleSyncEvent);
    setSyncStatus(assistantSyncManager.getSyncStatus());
    return unsubscribe;
  }, [handleSyncEvent]);

  const syncAssistantSelection = useCallback(async (attorneyId, assistantId) => {
    try {
      setIsLoading(true);
      setError(null);
      await assistantSyncManager.synchronizeAssistantSelection(attorneyId, assistantId);
    } catch (error) {
      setError(error.message);
    }
  }, []);

  const forceRefresh = useCallback(async () => {
    if (currentAssistant?.attorneyId && currentAssistant?.id) {
      await syncAssistantSelection(currentAssistant.attorneyId, currentAssistant.id);
    }
  }, [currentAssistant, syncAssistantSelection]);

  return {
    syncStatus,
    isLoading,
    error,
    lastUpdate,
    syncAssistantSelection,
    forceRefresh,
    clearError: () => setError(null),
    currentAssistantId: syncStatus.currentAssistantId,
    currentAttorneyId: syncStatus.currentAttorneyId,
    isAssistantSelected: !!syncStatus.currentAssistantId
  };
};

export default useAssistantSync;
EOF

echo ""
echo "✅ Core files created successfully!"
echo ""
echo "📋 What was created:"
echo "   📄 src/services/assistantSyncManager.js - Core synchronization manager"
echo "   📄 src/hooks/useAssistantSync.js - React hook for components"
echo ""
echo "🔄 Next steps:"
echo "1. Test the files work: npm run build"
echo "2. If successful, commit: git add . && git commit -m 'Add core sync system'"
echo "3. Push: git push origin main"
echo ""
echo "📚 To complete the system:"
echo "   - Update components to use useAssistantSync hook"
echo "   - Enhance AssistantDataService with sync methods"
echo "   - Add comprehensive testing"
echo ""
echo "🎉 Core synchronization system is ready!"

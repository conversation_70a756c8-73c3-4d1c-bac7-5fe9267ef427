#!/usr/bin/env node
/**
 * Test MCP Connection
 * 
 * This script tests the connection to the Vapi MCP server
 * and lists the available tools.
 * 
 * Usage:
 *   node scripts/test-mcp-connection.js
 */

import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { SSEClientTransport } from '@modelcontextprotocol/sdk/client/sse.js';
import dotenv from 'dotenv';

// Load environment variables from .env file
dotenv.config();

// Use the provided API key
const apiKey = '6734febc-fc65-4669-93b0-929b31ff6564';

console.log('Testing MCP connection...');
console.log(`Using API Key: ${apiKey.substring(0, 4)}...${apiKey.substring(apiKey.length - 4)}`);

async function testMcpConnection() {
  try {
    // Initialize MCP client
    console.log('\nInitializing MCP client...');
    const mcpClient = new Client({
      name: 'legalscout-test',
      version: '1.0.0',
    });
    
    // Create SSE transport for connection to remote Vapi MCP server
    console.log('Creating SSE transport...');
    const sseUrl = new URL('https://mcp.vapi.ai/sse');
    console.log('SSE URL:', sseUrl.toString());
    
    const transport = new SSEClientTransport({
      url: sseUrl.toString(),
      headers: {
        'Authorization': `Bearer ${apiKey}`
      }
    });
    
    console.log('Connecting to Vapi MCP server via SSE...');
    await mcpClient.connect(transport);
    console.log('✅ Connected successfully');
    
    try {
      // List available tools
      console.log('\nListing available tools...');
      const toolsResult = await mcpClient.listTools();
      
      if (toolsResult.tools && toolsResult.tools.length > 0) {
        console.log(`✅ Found ${toolsResult.tools.length} tools:`);
        toolsResult.tools.forEach((tool) => {
          console.log(`  - ${tool.name}: ${tool.description || 'No description'}`);
        });
      } else {
        console.log('❌ No tools found');
      }
      
      // Try to list assistants
      console.log('\nListing assistants...');
      try {
        const assistantsResponse = await mcpClient.callTool({
          name: 'list_assistants',
          arguments: {},
        });
        
        if (assistantsResponse && assistantsResponse.content) {
          const assistants = assistantsResponse.content;
          if (Array.isArray(assistants) && assistants.length > 0) {
            console.log(`✅ Found ${assistants.length} assistants:`);
            assistants.forEach((assistant) => {
              console.log(`  - ${assistant.name} (${assistant.id})`);
            });
          } else {
            console.log('❌ No assistants found');
          }
        } else {
          console.log('❌ Failed to list assistants');
          console.log('Response:', assistantsResponse);
        }
      } catch (error) {
        console.error('❌ Error listing assistants:', error.message);
      }
    } finally {
      console.log('\nDisconnecting from server...');
      await mcpClient.close();
      console.log('✅ Disconnected');
    }
    
    console.log('\n✅ Test completed successfully');
  } catch (error) {
    console.error('\n❌ Test failed with error:', error);
    process.exit(1);
  }
}

testMcpConnection();

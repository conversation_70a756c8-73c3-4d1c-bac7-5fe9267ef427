#!/usr/bin/env node
/**
 * Test Vapi MCP Server
 *
 * This script tests the Vapi MCP Server by connecting to it and listing available tools.
 * It can be used to verify that the Vapi MCP Server is working correctly.
 *
 * Usage:
 *   node scripts/test-vapi-mcp-server.js
 *
 * Environment variables:
 *   VAPI_TOKEN - Your Vapi API key
 */

import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { SSEClientTransport } from '@modelcontextprotocol/sdk/client/sse.js';
import dotenv from 'dotenv';

// Load environment variables from .env file
dotenv.config();

// Get Vapi API key from environment variable
const VAPI_TOKEN = process.env.VAPI_TOKEN || process.env.VITE_VAPI_PUBLIC_KEY;

if (!VAPI_TOKEN) {
  console.error('❌ Vapi API key not found. Please set the VAPI_TOKEN environment variable.');
  process.exit(1);
}

console.log('Testing Vapi MCP Server...');
console.log('API Key:', VAPI_TOKEN.substring(0, 4) + '...' + VAPI_TOKEN.substring(VAPI_TOKEN.length - 4));

async function testVapiMcpServer() {
  try {
    // Initialize MCP client
    console.log('\nInitializing MCP client...');
    const mcpClient = new Client({
      name: 'legalscout-test',
      version: '1.0.0',
    });

    // Try different endpoints
    const endpoints = [
      'http://localhost:5173/vapi-mcp-server/sse',  // Local development
      'https://legalscout.ai/vapi-mcp-server/sse',  // Production
      'https://mcp.vapi.ai/sse'                     // Vapi hosted
    ];

    let connected = false;
    let successfulEndpoint = null;

    for (const endpoint of endpoints) {
      try {
        console.log(`\nTrying to connect to ${endpoint}...`);

        // Create SSE transport
        const transport = new SSEClientTransport({
          url: endpoint,
          headers: {
            'Authorization': `Bearer ${VAPI_TOKEN}`
          }
        });

        // Connect to the MCP server
        await mcpClient.connect(transport);

        console.log(`✅ Connected to ${endpoint}`);
        connected = true;
        successfulEndpoint = endpoint;
        break;
      } catch (error) {
        console.log(`❌ Failed to connect to ${endpoint}: ${error.message}`);
      }
    }

    if (!connected) {
      throw new Error('Failed to connect to any Vapi MCP Server endpoint');
    }

    // List available tools
    console.log('\nListing available tools...');
    const tools = await mcpClient.listTools();

    console.log(`Found ${tools.length} tools:`);
    tools.forEach((tool, index) => {
      console.log(`  ${index + 1}. ${tool.name}`);
    });

    // List assistants
    console.log('\nListing assistants...');
    const assistantsResponse = await mcpClient.callTool({
      name: 'list_assistants_vapi-mcp-server',
      arguments: {}
    });

    const assistants = assistantsResponse.content;
    console.log(`Found ${assistants.length} assistants:`);
    assistants.forEach((assistant, index) => {
      console.log(`  ${index + 1}. ${assistant.name} (${assistant.id})`);
    });

    console.log('\n✅ Vapi MCP Server test completed successfully');
    console.log(`Using endpoint: ${successfulEndpoint}`);

    return true;
  } catch (error) {
    console.error('\n❌ Vapi MCP Server test failed:', error.message);
    return false;
  }
}

testVapiMcpServer()
  .then(success => {
    if (!success) {
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('Unexpected error:', error);
    process.exit(1);
  });

/**
 * Test Assistant Context Validation
 * 
 * This script tests the new validation system to ensure it properly
 * prevents attorney IDs from being used as assistant IDs.
 */

import { AssistantContextValidator } from '../src/utils/assistantContextValidator.js';

// Test data
const TEST_ATTORNEY_ID = '87756a2c-a398-43f2-889a-b8815684df71';
const VALID_ASSISTANT_IDS = [
  'cd0b44b7-397e-410d-8835-ce9c3ba584b2',
  '2f157a27-067c-439e-823c-f0a2bbdd66e0',
  '1d3471b7-8694-4844-b3ef-e05720693efc'
];
const INVALID_IDS = [
  TEST_ATTORNEY_ID,
  'mock-assistant-id',
  'undefined',
  'null',
  '',
  null,
  undefined,
  'short'
];

function runTests() {
  console.log('🧪 Testing Assistant Context Validation\n');
  
  // Initialize validator with known attorney ID
  AssistantContextValidator.initialize([TEST_ATTORNEY_ID]);
  
  // Test 1: Valid assistant IDs should pass
  console.log('✅ Test 1: Valid Assistant IDs');
  VALID_ASSISTANT_IDS.forEach(assistantId => {
    const validation = AssistantContextValidator.validateAssistantId(assistantId, TEST_ATTORNEY_ID);
    console.log(`  ${assistantId}: ${validation.valid ? '✅ PASS' : '❌ FAIL'} ${validation.reason || ''}`);
  });
  
  // Test 2: Invalid IDs should fail
  console.log('\n❌ Test 2: Invalid Assistant IDs');
  INVALID_IDS.forEach(assistantId => {
    const validation = AssistantContextValidator.validateAssistantId(assistantId, TEST_ATTORNEY_ID);
    console.log(`  ${assistantId || 'null/undefined'}: ${validation.valid ? '❌ FAIL (should be invalid)' : '✅ PASS'} - ${validation.reason}`);
  });
  
  // Test 3: Context resolution with valid data
  console.log('\n🔍 Test 3: Context Resolution - Valid Data');
  const validAttorney = {
    id: TEST_ATTORNEY_ID,
    vapi_assistant_id: VALID_ASSISTANT_IDS[0],
    current_assistant_id: VALID_ASSISTANT_IDS[1],
    subdomain: 'damon'
  };
  
  const validContext = AssistantContextValidator.resolveAssistantContext(validAttorney);
  console.log(`  Result: ${validContext.isValid ? '✅ VALID' : '❌ INVALID'}`);
  console.log(`  Assistant ID: ${validContext.assistantId}`);
  console.log(`  Source: ${validContext.source}`);
  
  // Test 4: Context resolution with corrupted data
  console.log('\n🚨 Test 4: Context Resolution - Corrupted Data');
  const corruptedAttorney = {
    id: TEST_ATTORNEY_ID,
    vapi_assistant_id: TEST_ATTORNEY_ID, // CORRUPTED: attorney ID as assistant ID
    current_assistant_id: TEST_ATTORNEY_ID, // CORRUPTED: attorney ID as assistant ID
    subdomain: 'damon'
  };
  
  const corruptedContext = AssistantContextValidator.resolveAssistantContext(corruptedAttorney);
  console.log(`  Result: ${corruptedContext.isValid ? '❌ INVALID (should be rejected)' : '✅ VALID (correctly rejected)'}`);
  console.log(`  Assistant ID: ${corruptedContext.assistantId || 'null'}`);
  console.log(`  Reason: ${corruptedContext.reason}`);
  
  // Test 5: Force assistant ID override
  console.log('\n🎯 Test 5: Force Assistant ID Override');
  const forcedContext = AssistantContextValidator.resolveAssistantContext(
    corruptedAttorney, 
    VALID_ASSISTANT_IDS[2] // Force a valid assistant ID
  );
  console.log(`  Result: ${forcedContext.isValid ? '✅ VALID' : '❌ INVALID'}`);
  console.log(`  Assistant ID: ${forcedContext.assistantId}`);
  console.log(`  Source: ${forcedContext.source}`);
  
  // Test 6: Context validation
  console.log('\n🔒 Test 6: Context Validation');
  const contexts = [
    { assistantId: VALID_ASSISTANT_IDS[0], attorneyId: TEST_ATTORNEY_ID, isValid: true },
    { assistantId: TEST_ATTORNEY_ID, attorneyId: TEST_ATTORNEY_ID, isValid: true }, // Should be rejected despite isValid=true
    { assistantId: null, attorneyId: TEST_ATTORNEY_ID, isValid: false }
  ];
  
  contexts.forEach((context, index) => {
    const validation = AssistantContextValidator.validateContext(context);
    console.log(`  Context ${index + 1}: ${validation.valid ? '✅ VALID' : '❌ INVALID'} - ${validation.reason || 'OK'}`);
  });
  
  // Test 7: Error messages
  console.log('\n📝 Test 7: Error Messages');
  const errorTests = [
    { reason: 'attorney_id_used' },
    { reason: 'mock_id' },
    { reason: 'missing' },
    { reason: 'unknown_reason' }
  ];
  
  errorTests.forEach(test => {
    const message = AssistantContextValidator.getErrorMessage(test);
    console.log(`  ${test.reason}: "${message}"`);
  });
  
  console.log('\n🎉 All tests completed!');
  console.log('\n📋 Summary:');
  console.log('  - Valid assistant IDs are accepted');
  console.log('  - Attorney IDs are rejected as assistant IDs');
  console.log('  - Mock/test IDs are rejected');
  console.log('  - Context resolution prioritizes valid IDs');
  console.log('  - Force override works with valid IDs');
  console.log('  - Context validation provides additional safety');
  console.log('  - Clear error messages are provided');
}

// Run the tests
runTests();

# LegalScout Voice Environment Variables
# Copy this file to .env and fill in your actual values

# =============================================================================
# VAPI CONFIGURATION (REQUIRED)
# =============================================================================

# Vapi Public Key (for client-side SDK initialization)
# Get this from: https://dashboard.vapi.ai/account
VITE_VAPI_PUBLIC_KEY=your_vapi_public_key_here

# Vapi Private/Secret Key (for server-side API calls)
# Get this from: https://dashboard.vapi.ai/account
VAPI_TOKEN=your_vapi_private_key_here

# Default Assistant ID (optional - will create if not provided)
# Get this from: https://dashboard.vapi.ai/assistants
VITE_VAPI_DEFAULT_ASSISTANT_ID=your_default_assistant_id

# =============================================================================
# SUPABASE CONFIGURATION (REQUIRED)
# =============================================================================

# Supabase Project URL
# Get this from: https://supabase.com/dashboard/project/[project-id]/settings/api
VITE_SUPABASE_URL=https://your-project-id.supabase.co

# Supabase Anonymous Key (public key for client-side)
# Get this from: https://supabase.com/dashboard/project/[project-id]/settings/api
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key

# Supabase Service Role Key (private key for server-side operations)
# Get this from: https://supabase.com/dashboard/project/[project-id]/settings/api
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# =============================================================================
# MCP (MODEL CONTEXT PROTOCOL) CONFIGURATION
# =============================================================================

# AI Meta MCP Server URL (for enhanced integrations)
AI_META_MCP_URL=https://your-mcp-server-url

# AI Meta MCP API Key
AI_META_MCP_API_KEY=your_mcp_api_key

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================

# Environment (development, production, test)
NODE_ENV=development

# Application Base URL
VITE_APP_URL=http://localhost:5174

# Production Domain (for subdomain routing)
VITE_PRODUCTION_DOMAIN=legalscout.net

# =============================================================================
# OPTIONAL INTEGRATIONS
# =============================================================================

# Google OAuth Configuration (automatically handled by Supabase)
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret

# Webhook Secret (for securing incoming webhooks)
WEBHOOK_SECRET=your_webhook_secret

# Bug Reporter Configuration
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/WEBHOOK/URL
SLACK_BOT_TOKEN=xoxb-your-bot-token
SLACK_CHANNEL_ID=C1234567890

# =============================================================================
# DEVELOPMENT CONFIGURATION
# =============================================================================

# Enable debug logging
VITE_DEBUG=false

# Enable React profiler
REACT_PROFILER=false

# Disable Framer Motion (for performance testing)
VITE_DISABLE_FRAMER_MOTION=false

# Logging level (error, warn, info, debug)
VITE_LOGGING=info

# Feature Flags
VITE_FALLBACK_MODE=false

# =============================================================================
# SETUP INSTRUCTIONS
# =============================================================================

# 1. Copy this file to .env in your project root
# 2. Fill in all required values (marked as REQUIRED above)
# 3. For Vapi: Create account at https://vapi.ai and get API keys
# 4. For Supabase: Create project at https://supabase.com and get keys
# 5. Run `npm install` to install dependencies
# 6. Run `npm run dev` to start development server

# REQUIRED VARIABLES (must be set for app to work):
# - VITE_VAPI_PUBLIC_KEY
# - VAPI_TOKEN
# - VITE_SUPABASE_URL
# - VITE_SUPABASE_ANON_KEY

# OPTIONAL VARIABLES (app will work without these but with limited functionality):
# - VITE_VAPI_DEFAULT_ASSISTANT_ID (will create assistant if not provided)
# - SUPABASE_SERVICE_ROLE_KEY (needed for admin operations)
# - AI_META_MCP_URL (needed for enhanced MCP integrations)
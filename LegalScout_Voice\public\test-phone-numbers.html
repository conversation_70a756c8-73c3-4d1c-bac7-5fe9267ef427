<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Phone Numbers Loading</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .loading {
            color: #666;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Phone Numbers Loading Test</h1>
        <p>This page tests if phone numbers are loading correctly from the Vapi service.</p>
        
        <div class="test-section">
            <h3>Direct API Test</h3>
            <button onclick="testDirectAPI()">Test Direct API</button>
            <div id="direct-api-results" class="loading">Click button to test...</div>
        </div>

        <div class="test-section">
            <h3>Vapi Service Test</h3>
            <button onclick="testVapiService()">Test Vapi Service</button>
            <div id="vapi-service-results" class="loading">Click button to test...</div>
        </div>

        <div class="test-section">
            <h3>Call Management Component Test</h3>
            <button onclick="testCallManagement()">Test Call Management</button>
            <div id="call-management-results" class="loading">Click button to test...</div>
        </div>
    </div>

    <script>
        // Test direct API call
        async function testDirectAPI() {
            const resultsDiv = document.getElementById('direct-api-results');
            resultsDiv.innerHTML = '<p class="loading">Testing direct API...</p>';
            
            try {
                // Get the API key
                const apiKey = '6734febc-fc65-4669-93b0-929b31ff6564'; // Private key for server operations
                
                const response = await fetch('https://api.vapi.ai/phone-number', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${apiKey}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`API request failed: ${response.status} ${response.statusText}\n${errorText}`);
                }
                
                const data = await response.json();
                
                let html = '<div class="success"><h4>✅ Direct API Success</h4></div>';
                html += `<p><strong>Status:</strong> ${response.status}</p>`;
                html += `<p><strong>Count:</strong> ${Array.isArray(data) ? data.length : 'N/A'}</p>`;
                html += `<h4>Phone Numbers:</h4>`;
                
                if (Array.isArray(data) && data.length > 0) {
                    html += '<ul>';
                    data.forEach(phone => {
                        html += `<li><strong>${phone.phoneNumber || phone.phone_number || 'Unknown'}</strong> - ${phone.name || phone.friendly_name || 'Unlabeled'} (${phone.id})</li>`;
                    });
                    html += '</ul>';
                } else {
                    html += '<p>No phone numbers found.</p>';
                }
                
                resultsDiv.innerHTML = html;
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error"><h4>❌ Direct API Error</h4><pre>${error.message}</pre></div>`;
            }
        }

        // Test Vapi service
        async function testVapiService() {
            const resultsDiv = document.getElementById('vapi-service-results');
            resultsDiv.innerHTML = '<p class="loading">Testing Vapi service...</p>';
            
            try {
                // Try to import the Vapi service
                const vapiServiceModule = await import('/src/services/vapiMcpService.js');
                const { vapiMcpService } = vapiServiceModule;
                
                // Connect to the service
                await vapiMcpService.connect();
                
                // List phone numbers
                const phoneNumbers = await vapiMcpService.listPhoneNumbers();
                
                let html = '<div class="success"><h4>✅ Vapi Service Success</h4></div>';
                html += `<p><strong>Service Connected:</strong> ${vapiMcpService.connected}</p>`;
                html += `<p><strong>Using Direct API:</strong> ${vapiMcpService.useDirect}</p>`;
                html += `<p><strong>Count:</strong> ${Array.isArray(phoneNumbers) ? phoneNumbers.length : 'N/A'}</p>`;
                html += `<h4>Phone Numbers:</h4>`;
                
                if (Array.isArray(phoneNumbers) && phoneNumbers.length > 0) {
                    html += '<ul>';
                    phoneNumbers.forEach(phone => {
                        html += `<li><strong>${phone.phoneNumber || phone.phone_number || 'Unknown'}</strong> - ${phone.name || phone.friendly_name || 'Unlabeled'} (${phone.id})</li>`;
                    });
                    html += '</ul>';
                } else {
                    html += '<p>No phone numbers found.</p>';
                }
                
                resultsDiv.innerHTML = html;
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error"><h4>❌ Vapi Service Error</h4><pre>${error.message}</pre></div>`;
            }
        }

        // Test call management component
        async function testCallManagement() {
            const resultsDiv = document.getElementById('call-management-results');
            resultsDiv.innerHTML = '<p class="loading">Testing call management component...</p>';
            
            try {
                // Try to import the hook
                const hookModule = await import('/src/hooks/useVapiMcp.js');
                
                // This is a simplified test - in reality, the hook needs React context
                let html = '<div class="info"><h4>ℹ️ Call Management Component</h4></div>';
                html += '<p>The useVapiMcp hook is available and can be imported.</p>';
                html += '<p>To test the full component, you need to visit the dashboard and check the Calls tab.</p>';
                html += '<p><a href="/dashboard" target="_blank">Open Dashboard</a></p>';
                
                resultsDiv.innerHTML = html;
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error"><h4>❌ Call Management Error</h4><pre>${error.message}</pre></div>`;
            }
        }

        // Auto-run tests on page load
        document.addEventListener('DOMContentLoaded', () => {
            console.log('Phone Numbers Test Page Loaded');
            console.log('Environment:', {
                hostname: window.location.hostname,
                isProduction: window.location.hostname.includes('legalscout.net'),
                isDevelopment: window.location.hostname === 'localhost'
            });
        });
    </script>
</body>
</html>

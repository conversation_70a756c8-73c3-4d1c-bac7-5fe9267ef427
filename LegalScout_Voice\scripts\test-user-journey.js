#!/usr/bin/env node

/**
 * User Journey Test Suite
 * 
 * Tests the actual user flows that matter for MVP launch:
 * 1. Attorney authentication and dashboard access
 * 2. Voice call functionality
 * 3. Data persistence and sync
 */

import { config } from 'dotenv';
import fetch from 'node-fetch';

// Load environment variables
config({ path: '.env.development' });

const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

const log = (message, color = 'reset') => {
  console.log(`${colors[color]}${message}${colors.reset}`);
};

const issues = {
  blocking: [],
  important: [],
  minor: [],
  working: []
};

/**
 * Test Attorney Dashboard Data Flow
 */
async function testAttorneyDashboardFlow() {
  log('\n📊 Testing Attorney Dashboard Data Flow...', 'blue');
  
  try {
    const supabaseUrl = process.env.VITE_SUPABASE_URL;
    const supabaseKey = process.env.VITE_SUPABASE_KEY;
    
    // 1. Test attorney profile loading
    const profileResponse = await fetch(`${supabaseUrl}/rest/v1/attorneys?email=<EMAIL>&select=*`, {
      headers: {
        'apikey': supabaseKey,
        'Authorization': `Bearer ${supabaseKey}`
      }
    });

    if (!profileResponse.ok) {
      issues.blocking.push('Cannot load attorney profile from Supabase');
      return false;
    }

    const profiles = await profileResponse.json();
    if (profiles.length === 0) {
      issues.blocking.push('No attorney profile found in database');
      return false;
    }

    const attorney = profiles[0];
    log(`✅ Attorney profile loaded: ${attorney.firm_name}`, 'green');

    // 2. Test Vapi assistant verification
    if (!attorney.vapi_assistant_id) {
      issues.important.push('Attorney profile missing Vapi assistant ID');
      return false;
    }

    const vapiResponse = await fetch(`https://api.vapi.ai/assistant/${attorney.vapi_assistant_id}`, {
      headers: {
        'Authorization': `Bearer ${process.env.VITE_VAPI_SECRET_KEY}`,
        'Content-Type': 'application/json'
      }
    });

    if (vapiResponse.ok) {
      const assistant = await vapiResponse.json();
      log(`✅ Vapi assistant verified: ${assistant.name || 'Unnamed'}`, 'green');
      issues.working.push('Attorney dashboard data flow complete');
    } else {
      issues.important.push(`Vapi assistant not found or inaccessible (${vapiResponse.status})`);
    }

    return true;
  } catch (error) {
    issues.blocking.push(`Dashboard data flow error: ${error.message}`);
    return false;
  }
}

/**
 * Test Voice Call Prerequisites
 */
async function testVoiceCallPrerequisites() {
  log('\n🎤 Testing Voice Call Prerequisites...', 'blue');
  
  try {
    // 1. Test Vapi public key for client-side calls
    const publicKey = process.env.VITE_VAPI_PUBLIC_KEY;
    if (!publicKey || publicKey.length < 20) {
      issues.blocking.push('Vapi public key missing or invalid');
      return false;
    }
    log('✅ Vapi public key configured for client calls', 'green');

    // 2. Test assistant configuration
    const assistantId = 'f9b97d13-f9c4-40af-a660-62ba5925ff2a'; // Known working assistant
    const assistantResponse = await fetch(`https://api.vapi.ai/assistant/${assistantId}`, {
      headers: {
        'Authorization': `Bearer ${process.env.VITE_VAPI_SECRET_KEY}`,
        'Content-Type': 'application/json'
      }
    });

    if (assistantResponse.ok) {
      const assistant = await assistantResponse.json();
      log(`✅ Assistant ready for calls: ${assistant.model || 'Unknown model'}`, 'green');
      
      // Check voice configuration
      if (assistant.voice) {
        log(`✅ Voice configured: ${assistant.voice.provider}/${assistant.voice.voiceId}`, 'green');
      } else {
        issues.minor.push('Assistant voice configuration may be incomplete');
      }
      
      issues.working.push('Voice call prerequisites met');
    } else {
      issues.blocking.push(`Assistant not accessible for calls (${assistantResponse.status})`);
      return false;
    }

    return true;
  } catch (error) {
    issues.blocking.push(`Voice call prerequisites error: ${error.message}`);
    return false;
  }
}

/**
 * Test Authentication Flow Components
 */
async function testAuthenticationFlow() {
  log('\n🔐 Testing Authentication Flow Components...', 'blue');
  
  try {
    // 1. Test Supabase auth configuration
    const supabaseUrl = process.env.VITE_SUPABASE_URL;
    if (!supabaseUrl.includes('supabase.co')) {
      issues.blocking.push('Supabase URL appears invalid');
      return false;
    }
    log('✅ Supabase authentication endpoint configured', 'green');

    // 2. Test OAuth redirect configuration
    // This is more of a configuration check since we can't test OAuth without user interaction
    const expectedRedirect = 'http://localhost:5174/auth/callback';
    log(`✅ OAuth redirect configured: ${expectedRedirect}`, 'green');

    // 3. Test attorney table structure
    const tableResponse = await fetch(`${supabaseUrl}/rest/v1/attorneys?select=id&limit=1`, {
      headers: {
        'apikey': process.env.VITE_SUPABASE_KEY,
        'Authorization': `Bearer ${process.env.VITE_SUPABASE_KEY}`
      }
    });

    if (tableResponse.ok) {
      log('✅ Attorney table accessible for authentication', 'green');
      issues.working.push('Authentication flow components ready');
    } else {
      issues.important.push(`Attorney table not accessible (${tableResponse.status})`);
    }

    return true;
  } catch (error) {
    issues.blocking.push(`Authentication flow error: ${error.message}`);
    return false;
  }
}

/**
 * Test Data Persistence
 */
async function testDataPersistence() {
  log('\n💾 Testing Data Persistence...', 'blue');
  
  try {
    const supabaseUrl = process.env.VITE_SUPABASE_URL;
    const supabaseKey = process.env.VITE_SUPABASE_KEY;

    // Test if we can write to the database (using a test table or safe operation)
    // For safety, we'll just test read operations on existing data
    
    // 1. Test attorney profile updates (read-only test)
    const profileResponse = await fetch(`${supabaseUrl}/rest/v1/attorneys?select=id,updated_at&limit=1`, {
      headers: {
        'apikey': supabaseKey,
        'Authorization': `Bearer ${supabaseKey}`
      }
    });

    if (profileResponse.ok) {
      log('✅ Attorney profile persistence accessible', 'green');
    } else {
      issues.important.push('Cannot access attorney profile persistence');
    }

    // 2. Test call logs table (if it exists)
    const callLogsResponse = await fetch(`${supabaseUrl}/rest/v1/call_logs?select=id&limit=1`, {
      headers: {
        'apikey': supabaseKey,
        'Authorization': `Bearer ${supabaseKey}`
      }
    });

    if (callLogsResponse.ok) {
      log('✅ Call logs persistence accessible', 'green');
      issues.working.push('Data persistence systems operational');
    } else {
      issues.minor.push('Call logs table may not exist yet');
    }

    return true;
  } catch (error) {
    issues.important.push(`Data persistence error: ${error.message}`);
    return false;
  }
}

/**
 * Test Production Readiness
 */
async function testProductionReadiness() {
  log('\n🚀 Testing Production Readiness...', 'blue');
  
  try {
    // 1. Check for development-only configurations
    const devMode = process.env.VITE_DEV_MODE;
    if (devMode === 'true') {
      issues.minor.push('Development mode is enabled');
    } else {
      log('✅ Development mode disabled for production', 'green');
    }

    // 2. Check for placeholder values
    const hasPlaceholders = Object.entries(process.env)
      .some(([key, value]) => value && (value.includes('placeholder') || value.includes('your_')));
    
    if (hasPlaceholders) {
      issues.important.push('Some environment variables still contain placeholder values');
    } else {
      log('✅ No placeholder values in environment configuration', 'green');
    }

    // 3. Check API key security
    const publicKey = process.env.VITE_VAPI_PUBLIC_KEY;
    const secretKey = process.env.VITE_VAPI_SECRET_KEY;
    
    if (publicKey && secretKey && publicKey !== secretKey) {
      log('✅ Vapi public and secret keys are different (good security)', 'green');
      issues.working.push('Production security configuration ready');
    } else {
      issues.important.push('Vapi key configuration may have security issues');
    }

    return true;
  } catch (error) {
    issues.minor.push(`Production readiness check error: ${error.message}`);
    return false;
  }
}

/**
 * Generate User Journey Report
 */
function generateUserJourneyReport() {
  log('\n📋 USER JOURNEY TEST REPORT', 'bold');
  log('=' * 50, 'blue');
  
  if (issues.blocking.length > 0) {
    log('\n🚨 BLOCKING ISSUES (Must fix before launch):', 'red');
    issues.blocking.forEach((issue, i) => {
      log(`${i + 1}. ${issue}`, 'red');
    });
  }
  
  if (issues.important.length > 0) {
    log('\n⚠️ IMPORTANT ISSUES (Should fix before launch):', 'yellow');
    issues.important.forEach((issue, i) => {
      log(`${i + 1}. ${issue}`, 'yellow');
    });
  }
  
  if (issues.minor.length > 0) {
    log('\n📝 MINOR ISSUES (Can fix after launch):', 'blue');
    issues.minor.forEach((issue, i) => {
      log(`${i + 1}. ${issue}`, 'blue');
    });
  }
  
  if (issues.working.length > 0) {
    log('\n✅ WORKING USER JOURNEYS:', 'green');
    issues.working.forEach((item, i) => {
      log(`${i + 1}. ${item}`, 'green');
    });
  }
  
  const canLaunch = issues.blocking.length === 0;
  const shouldLaunch = issues.blocking.length === 0 && issues.important.length <= 2;
  
  log('\n🎯 LAUNCH RECOMMENDATION:', 'bold');
  if (canLaunch && shouldLaunch) {
    log('🚀 READY TO LAUNCH - All critical user journeys working', 'green');
  } else if (canLaunch) {
    log('⚠️ CAN LAUNCH - But fix important issues soon', 'yellow');
  } else {
    log('🛑 NOT READY - Fix blocking issues first', 'red');
  }
  
  return {
    canLaunch,
    shouldLaunch,
    blockingIssues: issues.blocking.length,
    importantIssues: issues.important.length,
    workingJourneys: issues.working.length
  };
}

/**
 * Main Test Runner
 */
async function runUserJourneyTests() {
  log('🧪 LegalScout Voice - User Journey Test Suite', 'bold');
  log('Testing real user flows for MVP launch...', 'blue');
  
  const tests = [
    testAttorneyDashboardFlow,
    testVoiceCallPrerequisites,
    testAuthenticationFlow,
    testDataPersistence,
    testProductionReadiness
  ];
  
  for (const test of tests) {
    await test();
  }
  
  const report = generateUserJourneyReport();
  
  // Exit with appropriate code
  process.exit(report.canLaunch ? 0 : 1);
}

// Run the tests
runUserJourneyTests().catch(error => {
  log(`💥 User journey tests failed: ${error.message}`, 'red');
  process.exit(1);
});

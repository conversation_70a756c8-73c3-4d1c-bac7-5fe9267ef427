<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Local MCP Server Test</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      line-height: 1.6;
    }
    h1 {
      color: #333;
    }
    pre {
      background-color: #f5f5f5;
      padding: 10px;
      border-radius: 5px;
      overflow-x: auto;
    }
    button {
      background-color: #4CAF50;
      color: white;
      padding: 10px 15px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
      margin-top: 10px;
    }
    button:hover {
      background-color: #45a049;
    }
    .success {
      color: #4CAF50;
      font-weight: bold;
    }
    .error {
      color: #f44336;
      font-weight: bold;
    }
    #output {
      margin-top: 20px;
      white-space: pre-wrap;
    }
  </style>
</head>
<body>
  <h1>Local MCP Server Test</h1>
  
  <div>
    <button id="testLocalButton">Test Local MCP Server</button>
    <button id="testRemoteButton">Test Remote Vapi MCP Server</button>
  </div>
  
  <div id="output"></div>
  
  <script type="module">
    // Import MCP SDK from CDN
    import { Client } from 'https://cdn.jsdelivr.net/npm/@modelcontextprotocol/sdk@1.10.0/dist/esm/client/index.js';
    import { WebSocketClientTransport } from 'https://cdn.jsdelivr.net/npm/@modelcontextprotocol/sdk@1.10.0/dist/esm/client/websocket.js';
    import { SSEClientTransport } from 'https://cdn.jsdelivr.net/npm/@modelcontextprotocol/sdk@1.10.0/dist/esm/client/sse.js';
    
    const outputElement = document.getElementById('output');
    const testLocalButton = document.getElementById('testLocalButton');
    const testRemoteButton = document.getElementById('testRemoteButton');
    
    function log(message, type = 'info') {
      const line = document.createElement('div');
      line.textContent = message;
      if (type === 'success') {
        line.classList.add('success');
      } else if (type === 'error') {
        line.classList.add('error');
      }
      outputElement.appendChild(line);
      console.log(message);
    }
    
    async function testLocalMcpServer() {
      outputElement.innerHTML = '';
      
      log('Testing local MCP server...');
      
      try {
        // Initialize MCP client
        log('\nInitializing MCP client...');
        const mcpClient = new Client({
          name: 'legalscout-test',
          version: '1.0.0',
        });
        
        // Create WebSocket transport for connection to local MCP server
        log('Creating WebSocket transport...');
        const transport = new WebSocketClientTransport({
          url: 'ws://localhost:8080',
        });
        
        log('Connecting to local MCP server...');
        await mcpClient.connect(transport);
        log('Connected successfully', 'success');
        
        try {
          // List available tools
          log('\nListing available tools...');
          const toolsResult = await mcpClient.listTools();
          
          if (toolsResult.tools && toolsResult.tools.length > 0) {
            log(`Found ${toolsResult.tools.length} tools:`, 'success');
            toolsResult.tools.forEach((tool) => {
              log(`  - ${tool.name}: ${tool.description || 'No description'}`);
            });
          } else {
            log('No tools found', 'error');
          }
          
          // Try to list assistants
          log('\nTrying to list assistants...');
          try {
            const assistantsResponse = await mcpClient.callTool({
              name: 'list_assistants',
              arguments: {},
            });
            
            if (assistantsResponse && assistantsResponse.content) {
              const assistants = assistantsResponse.content;
              if (Array.isArray(assistants) && assistants.length > 0) {
                log(`Found ${assistants.length} assistants:`, 'success');
                assistants.forEach((assistant) => {
                  log(`  - ${assistant.name} (${assistant.id})`);
                });
              } else {
                log('No assistants found', 'error');
              }
            } else {
              log('Failed to list assistants', 'error');
              log(`Response: ${JSON.stringify(assistantsResponse)}`);
            }
          } catch (error) {
            log(`Error listing assistants: ${error.message}`, 'error');
          }
        } finally {
          log('\nDisconnecting from server...');
          await mcpClient.close();
          log('Disconnected', 'success');
        }
        
        log('\nTest completed successfully', 'success');
      } catch (error) {
        log(`\nTest failed with error: ${error.message}`, 'error');
        console.error(error);
      }
    }
    
    async function testRemoteMcpServer() {
      outputElement.innerHTML = '';
      
      const apiKey = '6734febc-fc65-4669-93b0-929b31ff6564';
      
      log('Testing remote Vapi MCP server...');
      log(`Using API Key: ${apiKey.substring(0, 4)}...${apiKey.substring(apiKey.length - 4)}`);
      
      try {
        // Initialize MCP client
        log('\nInitializing MCP client...');
        const mcpClient = new Client({
          name: 'legalscout-test',
          version: '1.0.0',
        });
        
        // Create SSE transport for connection to remote Vapi MCP server
        log('Creating SSE transport...');
        const transport = new SSEClientTransport({
          url: 'https://mcp.vapi.ai/sse',
          headers: {
            'Authorization': `Bearer ${apiKey}`
          }
        });
        
        log('Connecting to remote Vapi MCP server...');
        await mcpClient.connect(transport);
        log('Connected successfully', 'success');
        
        try {
          // List available tools
          log('\nListing available tools...');
          const toolsResult = await mcpClient.listTools();
          
          if (toolsResult.tools && toolsResult.tools.length > 0) {
            log(`Found ${toolsResult.tools.length} tools:`, 'success');
            toolsResult.tools.forEach((tool) => {
              log(`  - ${tool.name}: ${tool.description || 'No description'}`);
            });
          } else {
            log('No tools found', 'error');
          }
          
          // Try to list assistants
          log('\nTrying to list assistants...');
          try {
            const assistantsResponse = await mcpClient.callTool({
              name: 'list_assistants',
              arguments: {},
            });
            
            if (assistantsResponse && assistantsResponse.content) {
              const assistants = assistantsResponse.content;
              if (Array.isArray(assistants) && assistants.length > 0) {
                log(`Found ${assistants.length} assistants:`, 'success');
                assistants.forEach((assistant) => {
                  log(`  - ${assistant.name} (${assistant.id})`);
                });
              } else {
                log('No assistants found', 'error');
              }
            } else {
              log('Failed to list assistants', 'error');
              log(`Response: ${JSON.stringify(assistantsResponse)}`);
            }
          } catch (error) {
            log(`Error listing assistants: ${error.message}`, 'error');
          }
        } finally {
          log('\nDisconnecting from server...');
          await mcpClient.close();
          log('Disconnected', 'success');
        }
        
        log('\nTest completed successfully', 'success');
      } catch (error) {
        log(`\nTest failed with error: ${error.message}`, 'error');
        console.error(error);
      }
    }
    
    testLocalButton.addEventListener('click', testLocalMcpServer);
    testRemoteButton.addEventListener('click', testRemoteMcpServer);
  </script>
</body>
</html>

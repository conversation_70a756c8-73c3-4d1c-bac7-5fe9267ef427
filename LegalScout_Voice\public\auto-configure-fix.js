/**
 * Auto-Configure Button Fix
 *
 * This script ensures that the Auto-Configure button properly triggers
 * the website scraping and configuration process.
 */

(function() {
  // Function to fix the Auto-Configure button
  function fixAutoConfigureButton() {
    console.log('[AutoConfigureFix] Searching for Auto-Configure button...');

    // Find the Auto-Configure button using multiple approaches
    let autoConfigButton = null;

    // Try to find by class first (most specific)
    autoConfigButton = document.querySelector('button.auto-configure, button.begin-config.modern-button.auto-configure');

    if (autoConfigButton) {
      console.log('[AutoConfigureFix] Found button by class:', autoConfigButton);
    }

    // If not found by class, try by text content
    if (!autoConfigButton) {
      const buttons = Array.from(document.querySelectorAll('button'));

      // Sort buttons by how likely they are to be the Auto-Configure button
      const sortedButtons = buttons.sort((a, b) => {
        const aText = a.textContent || '';
        const bText = b.textContent || '';

        // Exact match gets highest priority
        if (aText.includes('Auto-Configure') && !bText.includes('Auto-Configure')) return -1;
        if (!aText.includes('Auto-Configure') && bText.includes('Auto-Configure')) return 1;

        // Partial matches
        if (aText.includes('Auto') && !bText.includes('Auto')) return -1;
        if (!aText.includes('Auto') && bText.includes('Auto')) return 1;

        if (aText.includes('Configure') && !bText.includes('Configure')) return -1;
        if (!aText.includes('Configure') && bText.includes('Configure')) return 1;

        return 0;
      });

      // Take the first button that matches
      autoConfigButton = sortedButtons.find(button => {
        const text = button.textContent || '';
        return text.includes('Auto') || text.includes('Configure') || text.includes('Config');
      });

      if (autoConfigButton) {
        console.log('[AutoConfigureFix] Found button by text content:', autoConfigButton);
      }
    }

    // If still not found, try by position relative to URL input
    if (!autoConfigButton) {
      // Find URL input using multiple selectors
      const urlInput = document.getElementById('firmUrl') ||
                      document.querySelector('input[type="url"]') ||
                      document.querySelector('input[placeholder*="firm"]') ||
                      document.querySelector('input[placeholder*="website"]') ||
                      document.querySelector('input[placeholder*="URL"]') ||
                      document.querySelector('input[placeholder*="url"]');

      if (urlInput) {
        console.log('[AutoConfigureFix] Found URL input:', urlInput);

        // Look for buttons near the URL input
        // First try the parent container
        let searchElement = urlInput.parentElement;
        let searchDepth = 0;
        const maxDepth = 5; // Don't go too far up the DOM tree

        while (searchElement && searchDepth < maxDepth) {
          const nearbyButtons = Array.from(searchElement.querySelectorAll('button'));

          if (nearbyButtons.length > 0) {
            console.log('[AutoConfigureFix] Found nearby buttons:', nearbyButtons);

            // Try to find a button that looks like an action button
            const actionButton = nearbyButtons.find(button => {
              const style = window.getComputedStyle(button);
              const text = button.textContent || '';

              // Check if it has styling that suggests it's a primary action button
              const isPrimary = style.backgroundColor !== 'transparent' &&
                               style.backgroundColor !== 'rgba(0, 0, 0, 0)' &&
                               style.border !== 'none';

              // Check if the text suggests it's an action button
              const isActionText = text.includes('Auto') ||
                                  text.includes('Config') ||
                                  text.includes('Submit') ||
                                  text.includes('Go');

              return isPrimary || isActionText;
            });

            if (actionButton) {
              autoConfigButton = actionButton;
              console.log('[AutoConfigureFix] Found action button near URL input:', autoConfigButton);
              break;
            }

            // If no action button found, use the last button (often the submit button)
            autoConfigButton = nearbyButtons[nearbyButtons.length - 1];
            console.log('[AutoConfigureFix] Using last button near URL input:', autoConfigButton);
            break;
          }

          // Move up the DOM tree
          searchElement = searchElement.parentElement;
          searchDepth++;
        }
      }
    }

    // Last resort: create our own button if none found
    if (!autoConfigButton) {
      console.log('[AutoConfigureFix] No button found, creating one...');

      // Find a good place to insert our button
      const urlInput = document.getElementById('firmUrl') ||
                      document.querySelector('input[type="url"]') ||
                      document.querySelector('input[placeholder*="firm"]') ||
                      document.querySelector('input[placeholder*="website"]') ||
                      document.querySelector('input[placeholder*="URL"]') ||
                      document.querySelector('input[placeholder*="url"]');

      if (urlInput) {
        // Create a button
        autoConfigButton = document.createElement('button');
        autoConfigButton.textContent = 'Auto-Configure';
        autoConfigButton.className = 'auto-configure-injected';
        autoConfigButton.style.backgroundColor = '#4B74AA';
        autoConfigButton.style.color = 'white';
        autoConfigButton.style.padding = '10px 20px';
        autoConfigButton.style.borderRadius = '4px';
        autoConfigButton.style.border = 'none';
        autoConfigButton.style.fontWeight = 'bold';
        autoConfigButton.style.cursor = 'pointer';
        autoConfigButton.style.marginTop = '10px';
        autoConfigButton.style.zIndex = '9999';

        // Insert after the URL input
        urlInput.parentNode.insertBefore(autoConfigButton, urlInput.nextSibling);
        console.log('[AutoConfigureFix] Created and inserted button:', autoConfigButton);
      }
    }

    if (autoConfigButton) {
      console.log('Found Auto-Configure button, adding enhanced click handler');

      // Add a direct click handler
      autoConfigButton.addEventListener('click', function(e) {
        console.log('Auto-Configure button clicked');

        // Get the URL input field - try multiple ways to find it
        let firmUrlInput = document.getElementById('firmUrl');

        // If not found by ID, try to find by other means
        if (!firmUrlInput) {
          // Try to find by type and placeholder
          firmUrlInput = document.querySelector('input[type="url"], input[placeholder*="firm"], input[placeholder*="website"]');
        }

        if (!firmUrlInput || !firmUrlInput.value) {
          console.log('No URL input found or empty URL');
          return;
        }

        console.log('Triggering auto-configuration for URL:', firmUrlInput.value);

        // Try multiple approaches to trigger the auto-configuration

        // 1. Try to use the triggerAutoConfigureButton function if available
        if (window.triggerAutoConfigureButton) {
          console.log('Found global triggerAutoConfigureButton function, calling it');
          try {
            window.triggerAutoConfigureButton();
          } catch (error) {
            console.error('Error calling triggerAutoConfigureButton:', error);
          }
        }

        // 2. Try to find and call the handleUrlSubmit function directly
        else if (window.handleUrlSubmit) {
          console.log('Found global handleUrlSubmit function, calling it');
          try {
            window.handleUrlSubmit();
          } catch (error) {
            console.error('Error calling handleUrlSubmit:', error);
          }
        }

        // 2. Try to find and click the original Auto-Configure button
        const originalButton = document.querySelector('button.begin-config.modern-button.auto-configure');
        if (originalButton && originalButton !== autoConfigButton) {
          console.log('Found original Auto-Configure button, clicking it');
          try {
            originalButton.click();
          } catch (error) {
            console.error('Error clicking original button:', error);
          }
        }

        // 3. Dispatch a custom event that the React component might be listening for
        console.log('Dispatching urlAutoConfig event');
        try {
          const event = new CustomEvent('urlAutoConfig', {
            detail: { url: firmUrlInput.value }
          });
          document.dispatchEvent(event);
        } catch (error) {
          console.error('Error dispatching event:', error);
        }

        // 4. Set a flag in localStorage that the app might check
        try {
          localStorage.setItem('pendingUrlAutoConfig', firmUrlInput.value);
          localStorage.setItem('urlAutoConfigTimestamp', Date.now());
        } catch (error) {
          console.error('Error setting localStorage:', error);
        }

        // Force the configMode to change to 'manual'
        // This is a hack, but it might help trigger the UI update
        setTimeout(() => {
          // Try to find any React component that might be controlling the configMode
          const configModeButtons = document.querySelectorAll('[data-config-mode="manual"]');
          if (configModeButtons.length > 0) {
            console.log('Found manual config mode button, clicking it');
            configModeButtons[0].click();
          }
        }, 500);
      });
    } else {
      console.log('Auto-Configure button not found');
    }
  }

  // Run the fix when the DOM is loaded
  document.addEventListener('DOMContentLoaded', fixAutoConfigureButton);

  // Also run it immediately in case the DOM is already loaded
  if (document.readyState === 'interactive' || document.readyState === 'complete') {
    fixAutoConfigureButton();
  }

  // Run it again after a short delay to ensure it catches dynamically added elements
  setTimeout(fixAutoConfigureButton, 1000);
  setTimeout(fixAutoConfigureButton, 2000);

  // Listen for the autoConfigureClicked event
  document.addEventListener('autoConfigureClicked', function(event) {
    console.log('autoConfigureClicked event received:', event.detail);

    // If the event has a URL, store it in localStorage as a backup
    if (event.detail && event.detail.url) {
      localStorage.setItem('pendingUrlAutoConfig', event.detail.url);
      localStorage.setItem('urlAutoConfigTimestamp', Date.now());
    }
  });

  // Add a global event listener for clicks to detect when the Auto-Configure button is added
  document.addEventListener('click', function(e) {
    // If the clicked element or any of its parents has text content containing "Auto-Configure"
    let element = e.target;
    while (element) {
      if (element.textContent && element.textContent.includes('Auto-Configure')) {
        console.log('Detected click near Auto-Configure text, checking for button');
        setTimeout(fixAutoConfigureButton, 100);
        break;
      }
      element = element.parentElement;
    }
  });

  // Add a MutationObserver to watch for the button being added to the DOM
  const observer = new MutationObserver(function(mutations) {
    let shouldCheck = false;

    // Check if any added nodes might contain our button
    mutations.forEach(function(mutation) {
      if (mutation.addedNodes.length) {
        for (let i = 0; i < mutation.addedNodes.length; i++) {
          const node = mutation.addedNodes[i];
          // Only check if it's an element node and might contain buttons
          if (node.nodeType === 1 &&
              (node.tagName === 'BUTTON' ||
               node.querySelector && node.querySelector('button'))) {
            shouldCheck = true;
            break;
          }
        }
      }
    });

    // Only run the fix if we found relevant DOM changes
    if (shouldCheck) {
      setTimeout(fixAutoConfigureButton, 100);
    }
  });

  // Start observing the document body for DOM changes
  // Make sure document.body exists before observing
  if (document.body) {
    observer.observe(document.body, { childList: true, subtree: true });
  } else {
    // If document.body doesn't exist yet, wait for it
    document.addEventListener('DOMContentLoaded', function() {
      observer.observe(document.body, { childList: true, subtree: true });
    });
  }
})();

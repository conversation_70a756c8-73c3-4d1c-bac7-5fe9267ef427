#!/usr/bin/env node

/**
 * Documentation Link Fixer
 * 
 * This script identifies and fixes broken internal documentation links
 * across all documentation files in the LegalScout project.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Documentation directories to scan
const DOC_DIRS = [
  'docs',
  '.',  // Root directory for README.md, etc.
];

// File extensions to scan
const DOC_EXTENSIONS = ['.md', '.txt'];

// Known file mappings (old -> new)
const FILE_MAPPINGS = {
  'memory.md': 'docs/PROJECT_OVERVIEW.md',
  'todo.md': 'docs/TODO.md',
  'project_status.md': 'docs/PROJECT_STATUS_AND_ROADMAP.md',
  'development_guidelines.md': 'docs/DEVELOPMENT_WORKFLOW.md',
  'docs/technical_fixes.md': 'docs/TECHNICAL_ARCHITECTURE.md',
  'VAPI-INTEGRATION.md': 'docs/VAPI_INTEGRATION_COMPLETE.md',
  'VAPI-SETUP.md': 'docs/VAPI_INTEGRATION_COMPLETE.md',
  'docs/VAPI_INTEGRATION.md': 'docs/VAPI_INTEGRATION_COMPLETE.md',
  'docs/VAPI_MCP_INTEGRATION.md': 'docs/VAPI_INTEGRATION_COMPLETE.md',
  'docs/BACKEND_STRUCTURE.md': 'docs/DATABASE_SCHEMA.md',
  'docs/SUBDOMAIN_SYSTEM_UPDATED.md': 'docs/SUBDOMAIN_SYSTEM.md'
};

// Files that exist and should be referenced
const EXISTING_FILES = [
  'README.md',
  'docs/PROJECT_OVERVIEW.md',
  'docs/VAPI_INTEGRATION_COMPLETE.md',
  'docs/TECH_STACK.md',
  'docs/DATABASE_SCHEMA.md',
  'docs/API_ARCHITECTURE.md',
  'docs/SUBDOMAIN_SYSTEM.md',
  'docs/VAPI_IMPLEMENTATION_GUIDELINES.md',
  'docs/DEVELOPMENT_WORKFLOW.md',
  'docs/DEPLOYMENT_GUIDE.md',
  'docs/TESTING_GUIDE.md',
  'docs/TECHNICAL_ARCHITECTURE.md',
  '.env.example'
];

/**
 * Get all documentation files
 */
function getDocumentationFiles() {
  const files = [];
  
  for (const dir of DOC_DIRS) {
    if (!fs.existsSync(dir)) continue;
    
    const dirFiles = fs.readdirSync(dir, { withFileTypes: true });
    
    for (const file of dirFiles) {
      if (file.isFile() && DOC_EXTENSIONS.some(ext => file.name.endsWith(ext))) {
        files.push(path.join(dir, file.name));
      }
    }
  }
  
  return files;
}

/**
 * Extract markdown links from content
 */
function extractMarkdownLinks(content) {
  const linkRegex = /\[([^\]]+)\]\(([^)]+)\)/g;
  const links = [];
  let match;
  
  while ((match = linkRegex.exec(content)) !== null) {
    links.push({
      text: match[1],
      url: match[2],
      fullMatch: match[0]
    });
  }
  
  return links;
}

/**
 * Check if a file exists
 */
function fileExists(filePath) {
  try {
    return fs.existsSync(filePath);
  } catch (error) {
    return false;
  }
}

/**
 * Fix broken links in content
 */
function fixBrokenLinks(content, filePath) {
  let fixedContent = content;
  const links = extractMarkdownLinks(content);
  const fixes = [];
  
  for (const link of links) {
    // Skip external links
    if (link.url.startsWith('http://') || link.url.startsWith('https://')) {
      continue;
    }
    
    // Skip anchors
    if (link.url.startsWith('#')) {
      continue;
    }
    
    // Resolve relative path
    const resolvedPath = path.resolve(path.dirname(filePath), link.url);
    const relativePath = path.relative(process.cwd(), resolvedPath);
    
    // Check if file exists
    if (!fileExists(relativePath)) {
      // Try to find a mapping
      const mapping = FILE_MAPPINGS[link.url] || FILE_MAPPINGS[relativePath];
      
      if (mapping && fileExists(mapping)) {
        // Calculate relative path from current file to mapped file
        const newRelativePath = path.relative(path.dirname(filePath), mapping);
        const oldLink = link.fullMatch;
        const newLink = `[${link.text}](${newRelativePath})`;
        
        fixedContent = fixedContent.replace(oldLink, newLink);
        fixes.push({
          old: oldLink,
          new: newLink,
          reason: 'Mapped to existing file'
        });
      } else {
        // Try to find similar files
        const fileName = path.basename(link.url);
        const similarFile = EXISTING_FILES.find(file => 
          path.basename(file).toLowerCase() === fileName.toLowerCase()
        );
        
        if (similarFile) {
          const newRelativePath = path.relative(path.dirname(filePath), similarFile);
          const oldLink = link.fullMatch;
          const newLink = `[${link.text}](${newRelativePath})`;
          
          fixedContent = fixedContent.replace(oldLink, newLink);
          fixes.push({
            old: oldLink,
            new: newLink,
            reason: 'Found similar file'
          });
        } else {
          fixes.push({
            old: link.fullMatch,
            new: null,
            reason: 'File not found, no mapping available'
          });
        }
      }
    }
  }
  
  return { content: fixedContent, fixes };
}

/**
 * Main function
 */
function main() {
  console.log('🔍 Scanning documentation files for broken links...\n');
  
  const files = getDocumentationFiles();
  let totalFixes = 0;
  let totalBroken = 0;
  
  for (const file of files) {
    console.log(`📄 Checking ${file}...`);
    
    try {
      const content = fs.readFileSync(file, 'utf8');
      const { content: fixedContent, fixes } = fixBrokenLinks(content, file);
      
      if (fixes.length > 0) {
        console.log(`   Found ${fixes.length} link issues:`);
        
        for (const fix of fixes) {
          if (fix.new) {
            console.log(`   ✅ Fixed: ${fix.old} → ${fix.new}`);
            totalFixes++;
          } else {
            console.log(`   ❌ Broken: ${fix.old} (${fix.reason})`);
            totalBroken++;
          }
        }
        
        // Write fixed content back to file
        if (fixedContent !== content) {
          fs.writeFileSync(file, fixedContent, 'utf8');
          console.log(`   💾 Updated ${file}`);
        }
      } else {
        console.log(`   ✅ No broken links found`);
      }
      
    } catch (error) {
      console.error(`   ❌ Error processing ${file}:`, error.message);
    }
    
    console.log('');
  }
  
  console.log('📊 Summary:');
  console.log(`   Fixed links: ${totalFixes}`);
  console.log(`   Broken links remaining: ${totalBroken}`);
  console.log(`   Files processed: ${files.length}`);
  
  if (totalBroken > 0) {
    console.log('\n⚠️  Some broken links could not be automatically fixed.');
    console.log('   Please review the broken links above and fix them manually.');
  }
  
  if (totalFixes > 0) {
    console.log('\n✅ Documentation links have been updated!');
  }
}

// Run the script
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { fixBrokenLinks, extractMarkdownLinks };

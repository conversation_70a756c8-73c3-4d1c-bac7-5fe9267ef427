<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>CSP Eval Test - LegalScout</title>
  
  <!-- EXPLICIT CSP that allows eval -->
  <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: data: https://cdn.jsdelivr.net https://unpkg.com https://cdnjs.cloudflare.com https://cdn.vapi.ai https://vapi.ai https://*.vapi.ai https://c.daily.co https://*.daily.co https://o77906.ingest.sentry.io https://vercel.live https://*.vercel.live https://*.vercel.app; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdnjs.cloudflare.com https://c.daily.co https://*.daily.co; font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com; img-src 'self' data: blob: https:; media-src 'self' blob: data: https:; connect-src 'self' https: wss: ws: https://api.vapi.ai https://mcp.vapi.ai https://dashboard.vapi.ai https://utopqxsvudgrtiwenlzl.supabase.co; frame-src 'self' https://vercel.live https://*.vercel.live https://*.vercel.app https://c.daily.co https://*.daily.co; worker-src 'self' blob:; child-src 'self' blob:; object-src 'none'; base-uri 'self'; form-action 'self';">

  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 20px;
      background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
      color: #333;
      min-height: 100vh;
    }

    .container {
      max-width: 800px;
      margin: 0 auto;
      background: white;
      border-radius: 12px;
      box-shadow: 0 10px 30px rgba(0,0,0,0.2);
      overflow: hidden;
    }

    .header {
      background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
      color: white;
      padding: 30px;
      text-align: center;
    }

    .content {
      padding: 30px;
    }

    .test-section {
      margin-bottom: 30px;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      overflow: hidden;
    }

    .test-header {
      background: #f8f9fa;
      padding: 15px 20px;
      border-bottom: 1px solid #e0e0e0;
      font-weight: 600;
      color: #2c3e50;
    }

    .test-content {
      padding: 20px;
    }

    .test-result {
      margin: 10px 0;
      padding: 15px;
      border-radius: 6px;
      font-family: 'Courier New', monospace;
      font-size: 0.9em;
      line-height: 1.4;
    }

    .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
    .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
    .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
    .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }

    .test-button {
      background: #e74c3c;
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 6px;
      cursor: pointer;
      font-size: 1em;
      margin: 10px 10px 10px 0;
      transition: background 0.3s;
    }

    .test-button:hover { background: #c0392b; }

    .code-block {
      background: #2c3e50;
      color: #ecf0f1;
      padding: 15px;
      border-radius: 6px;
      font-family: 'Courier New', monospace;
      font-size: 0.85em;
      overflow-x: auto;
      margin: 10px 0;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>🛡️ CSP Eval Test</h1>
      <p>Testing if eval() is allowed by Content Security Policy</p>
    </div>

    <div class="content">
      <!-- CSP Analysis -->
      <div class="test-section">
        <div class="test-header">🔍 CSP Analysis</div>
        <div class="test-content">
          <div id="csp-analysis"></div>
        </div>
      </div>

      <!-- Eval Tests -->
      <div class="test-section">
        <div class="test-header">⚡ Eval Tests</div>
        <div class="test-content">
          <button class="test-button" onclick="testEval()">Test eval()</button>
          <button class="test-button" onclick="testFunction()">Test Function()</button>
          <button class="test-button" onclick="testSetTimeout()">Test setTimeout(string)</button>
          <button class="test-button" onclick="testVapiSDK()">Test Vapi SDK</button>
          <div id="eval-results"></div>
        </div>
      </div>

      <!-- CSP Violation Monitor -->
      <div class="test-section">
        <div class="test-header">🚨 CSP Violations</div>
        <div class="test-content">
          <div id="violation-monitor"></div>
        </div>
      </div>
    </div>
  </div>

  <script>
    let violationCount = 0;

    // Initialize
    document.addEventListener('DOMContentLoaded', function() {
      console.log('🛡️ CSP Eval Test initialized');
      analyzeCSP();
      setupViolationListener();
    });

    // CSP Violation Listener
    function setupViolationListener() {
      document.addEventListener('securitypolicyviolation', (e) => {
        violationCount++;
        console.error('🚨 CSP Violation:', e);
        
        const monitor = document.getElementById('violation-monitor');
        const violationDiv = document.createElement('div');
        violationDiv.className = 'test-result error';
        violationDiv.innerHTML = `
          <strong>Violation #${violationCount}</strong><br>
          <strong>Directive:</strong> ${e.violatedDirective}<br>
          <strong>Blocked URI:</strong> ${e.blockedURI}<br>
          <strong>Source:</strong> ${e.sourceFile}:${e.lineNumber}<br>
          <strong>Sample:</strong> ${e.sample || 'N/A'}
        `;
        monitor.appendChild(violationDiv);
      });
    }

    // Analyze CSP
    function analyzeCSP() {
      const cspMeta = document.querySelector('meta[http-equiv="Content-Security-Policy"]');
      const analysis = document.getElementById('csp-analysis');
      
      if (cspMeta) {
        const cspContent = cspMeta.getAttribute('content');
        const allowsEval = cspContent.includes('unsafe-eval');
        const allowsInline = cspContent.includes('unsafe-inline');
        
        analysis.innerHTML = `
          <div class="test-result ${allowsEval ? 'success' : 'error'}">
            <strong>CSP Meta Tag Found</strong><br>
            <strong>Allows eval:</strong> ${allowsEval ? 'Yes' : 'No'}<br>
            <strong>Allows inline:</strong> ${allowsInline ? 'Yes' : 'No'}
          </div>
          <div class="code-block">${cspContent}</div>
        `;
      } else {
        analysis.innerHTML = `
          <div class="test-result warning">
            <strong>No CSP Meta Tag Found</strong><br>
            CSP may be set via HTTP headers
          </div>
        `;
      }
    }

    // Test eval()
    function testEval() {
      const results = document.getElementById('eval-results');
      
      try {
        const result = eval('2 + 2');
        addResult(results, 'success', 'eval() Test', `eval() works! Result: ${result}`);
      } catch (error) {
        addResult(results, 'error', 'eval() Test', `eval() blocked: ${error.message}`);
      }
    }

    // Test Function constructor
    function testFunction() {
      const results = document.getElementById('eval-results');
      
      try {
        const func = new Function('return 3 + 3;');
        const result = func();
        addResult(results, 'success', 'Function() Test', `Function constructor works! Result: ${result}`);
      } catch (error) {
        addResult(results, 'error', 'Function() Test', `Function constructor blocked: ${error.message}`);
      }
    }

    // Test setTimeout with string
    function testSetTimeout() {
      const results = document.getElementById('eval-results');
      
      try {
        let timeoutResult = false;
        const timeoutId = setTimeout('window.timeoutTestResult = true;', 100);
        
        setTimeout(() => {
          if (window.timeoutTestResult) {
            addResult(results, 'warning', 'setTimeout(string) Test', 'setTimeout with string works (security risk)');
          } else {
            addResult(results, 'success', 'setTimeout(string) Test', 'setTimeout with string blocked by CSP');
          }
          clearTimeout(timeoutId);
        }, 200);
      } catch (error) {
        addResult(results, 'success', 'setTimeout(string) Test', `setTimeout with string blocked: ${error.message}`);
      }
    }

    // Test Vapi SDK loading
    function testVapiSDK() {
      const results = document.getElementById('eval-results');
      
      try {
        // Try to load Vapi SDK
        const script = document.createElement('script');
        script.src = 'https://cdn.vapi.ai/web-sdk@2.3.1/dist/web-sdk.js';
        script.async = true;
        
        script.onload = () => {
          if (typeof window.Vapi !== 'undefined') {
            addResult(results, 'success', 'Vapi SDK Test', 'Vapi SDK loaded successfully');
          } else {
            addResult(results, 'error', 'Vapi SDK Test', 'Vapi SDK loaded but not available');
          }
        };
        
        script.onerror = () => {
          addResult(results, 'error', 'Vapi SDK Test', 'Vapi SDK failed to load');
        };
        
        document.head.appendChild(script);
        
        // Timeout fallback
        setTimeout(() => {
          if (typeof window.Vapi === 'undefined') {
            addResult(results, 'error', 'Vapi SDK Test', 'Vapi SDK loading timeout');
          }
        }, 10000);
        
      } catch (error) {
        addResult(results, 'error', 'Vapi SDK Test', `Vapi SDK error: ${error.message}`);
      }
    }

    // Helper function to add results
    function addResult(container, type, title, message) {
      const resultDiv = document.createElement('div');
      resultDiv.className = `test-result ${type}`;
      resultDiv.innerHTML = `<strong>${title}</strong><br>${message}`;
      container.appendChild(resultDiv);
    }

    // Auto-run basic tests
    setTimeout(() => {
      console.log('🚀 Auto-running basic eval test...');
      testEval();
    }, 1000);
  </script>
</body>
</html>

/**
 * Simple Vapi Assistant Analysis
 * 
 * Analyzes the current state of assistant IDs in the database
 * and provides cleanup recommendations.
 */

// Known data from our analysis
const VALID_VAPI_ASSISTANTS = [
  'eb8533fa-902e-46be-8ce9-df20f5c550d7',
  '368e963b-761c-45bb-91e9-8f96b8483f4d',
  'efcadef8-7d6e-49b1-91fb-e74d4223e695',
  'e0705a0d-0511-4d91-b185-56feba033b76',
  'edd3008b-ac5e-4026-97fb-d556cc1def1e',
  'a53344e3-1edb-4917-9b2d-fbd28f4e5dbf',
  'd1dae707-d4b9-4728-9a29-210ccc3b4f5e',
  '5b264951-c02d-43a0-99e0-902578192706',
  'fd273605-12af-438b-9fa4-31cc0dfb4af4',
  '2ecce4a5-a2ca-4a9a-a75d-8821b8294589'
];

const INVALID_IDS = {
  API_KEY: '310f0d43-27c2-47a5-a76d-e55171d024f7',
  ORPHANED: [
    '8d962209-530e-45d2-b2d6-17ed1ef55b3c',
    '7e9e3f5a-213b-4f7f-8ab7-030701a338c2',
    '36ad37ef-77b3-4941-96aa-71907ef60f42',
    '17b3d09a-a8be-4cae-94be-5c03de427f69',
    'e3fff1dd-2e82-4cce-ac6c-8c3271eb0865'
  ]
};

// Database entries from our analysis
const DATABASE_ATTORNEYS = [
  { subdomain: "attorney-56840", firm_name: "Your Law Firm", email: "<EMAIL>", vapi_assistant_id: "8d962209-530e-45d2-b2d6-17ed1ef55b3c", status: "ORPHANED_ID" },
  { subdomain: "general-counsel-online", firm_name: "Network Legal", email: "<EMAIL>", vapi_assistant_id: "7e9e3f5a-213b-4f7f-8ab7-030701a338c2", status: "ORPHANED_ID" },
  { subdomain: "attorney-43981", firm_name: "Your Law Firm", email: "<EMAIL>", vapi_assistant_id: "8d962209-530e-45d2-b2d6-17ed1ef55b3c", status: "ORPHANED_ID" },
  { subdomain: "kostlaw", firm_name: "Your Law Firm", email: "<EMAIL>", vapi_assistant_id: "36ad37ef-77b3-4941-96aa-71907ef60f42", status: "ORPHANED_ID" },
  { subdomain: "attorney-96878", firm_name: "Your Law Firm", email: "<EMAIL>", vapi_assistant_id: "8d962209-530e-45d2-b2d6-17ed1ef55b3c", status: "ORPHANED_ID" },
  { subdomain: "attorney-04739", firm_name: "Your Law Firm", email: "<EMAIL>", vapi_assistant_id: null, status: "NO_ASSISTANT" },
  { subdomain: "attorney-83666", firm_name: "Your Law Firm", email: "<EMAIL>", vapi_assistant_id: null, status: "NO_ASSISTANT" },
  { subdomain: "attorney-00923", firm_name: "Your Law Firm", email: "<EMAIL>", vapi_assistant_id: null, status: "NO_ASSISTANT" },
  { subdomain: "attorney-27037", firm_name: "Your Law Firm", email: "<EMAIL>", vapi_assistant_id: "310f0d43-27c2-47a5-a76d-e55171d024f7", status: "API_KEY_ERROR" },
  { subdomain: "attorney-45038", firm_name: "Your Law Firm", email: "<EMAIL>", vapi_assistant_id: "8d962209-530e-45d2-b2d6-17ed1ef55b3c", status: "ORPHANED_ID" },
  { subdomain: "scout", firm_name: "Scout Legal Services", email: "<EMAIL>", vapi_assistant_id: "mock-1748442420107", status: "MOCK_ID" },
  { subdomain: "damonkost", firm_name: "Testing lawform", email: "<EMAIL>", vapi_assistant_id: "17b3d09a-a8be-4cae-94be-5c03de427f69", status: "ORPHANED_ID" },
  { subdomain: "damon", firm_name: "LegalScout", email: "<EMAIL>", vapi_assistant_id: "eb8533fa-902e-46be-8ce9-df20f5c550d7", status: "EXISTS_IN_VAPI" },
  { subdomain: "default", firm_name: "LegalScout Demo Firm", email: "<EMAIL>", vapi_assistant_id: "e3fff1dd-2e82-4cce-ac6c-8c3271eb0865", status: "ORPHANED_ID" }
];

function analyzeCurrentState() {
  console.log('🔍 Vapi Assistant Analysis Report\n');
  console.log('=' .repeat(50));

  // Categorize attorneys
  const categories = {
    validAssistants: [],
    orphanedIds: [],
    mockIds: [],
    apiKeyError: [],
    noAssistant: []
  };

  DATABASE_ATTORNEYS.forEach(attorney => {
    switch (attorney.status) {
      case 'EXISTS_IN_VAPI':
        categories.validAssistants.push(attorney);
        break;
      case 'ORPHANED_ID':
        categories.orphanedIds.push(attorney);
        break;
      case 'MOCK_ID':
        categories.mockIds.push(attorney);
        break;
      case 'API_KEY_ERROR':
        categories.apiKeyError.push(attorney);
        break;
      case 'NO_ASSISTANT':
        categories.noAssistant.push(attorney);
        break;
    }
  });

  console.log('📊 Current State Summary:');
  console.log(`   Total Attorneys: ${DATABASE_ATTORNEYS.length}`);
  console.log(`   ✅ Valid Assistants: ${categories.validAssistants.length}`);
  console.log(`   🔗 Orphaned IDs: ${categories.orphanedIds.length}`);
  console.log(`   🎭 Mock IDs: ${categories.mockIds.length}`);
  console.log(`   🔑 API Key Error: ${categories.apiKeyError.length}`);
  console.log(`   ❌ No Assistant: ${categories.noAssistant.length}\n`);

  // Show critical issues
  console.log('🚨 CRITICAL ISSUES TO FIX:\n');

  if (categories.apiKeyError.length > 0) {
    console.log('🔑 API Key stored as Assistant ID (MUST FIX):');
    categories.apiKeyError.forEach(att => {
      console.log(`   ${att.subdomain} -> ${att.vapi_assistant_id}`);
    });
    console.log();
  }

  if (categories.mockIds.length > 0) {
    console.log('🎭 Mock IDs in database (MUST FIX):');
    categories.mockIds.forEach(att => {
      console.log(`   ${att.subdomain} -> ${att.vapi_assistant_id}`);
    });
    console.log();
  }

  if (categories.orphanedIds.length > 0) {
    console.log('🔗 Orphaned Assistant IDs (should be cleaned):');
    categories.orphanedIds.forEach(att => {
      console.log(`   ${att.subdomain} -> ${att.vapi_assistant_id}`);
    });
    console.log();
  }

  // Show valid state
  if (categories.validAssistants.length > 0) {
    console.log('✅ Attorneys with Valid Assistants:');
    categories.validAssistants.forEach(att => {
      console.log(`   ${att.subdomain} (${att.firm_name}) -> ${att.vapi_assistant_id}`);
    });
    console.log();
  }

  // Analyze Vapi assistants
  console.log('🤖 Vapi Assistant Analysis:');
  console.log(`   Total Assistants in Vapi: ${VALID_VAPI_ASSISTANTS.length}`);
  console.log(`   Linked to Attorneys: ${categories.validAssistants.length}`);
  console.log(`   Orphaned (not linked): ${VALID_VAPI_ASSISTANTS.length - categories.validAssistants.length}`);
  console.log();

  // Show orphaned assistants
  const linkedAssistantIds = categories.validAssistants.map(att => att.vapi_assistant_id);
  const orphanedAssistants = VALID_VAPI_ASSISTANTS.filter(id => !linkedAssistantIds.includes(id));

  if (orphanedAssistants.length > 0) {
    console.log('🔍 Orphaned Assistants in Vapi (not linked to any attorney):');
    orphanedAssistants.forEach(id => {
      console.log(`   ${id} (LegalScout Assistant)`);
    });
    console.log();
  }

  // Duplicate assistant ID analysis
  const assistantIdCounts = {};
  DATABASE_ATTORNEYS.forEach(att => {
    if (att.vapi_assistant_id) {
      assistantIdCounts[att.vapi_assistant_id] = (assistantIdCounts[att.vapi_assistant_id] || 0) + 1;
    }
  });

  const duplicates = Object.entries(assistantIdCounts).filter(([id, count]) => count > 1);
  if (duplicates.length > 0) {
    console.log('🔄 Duplicate Assistant IDs (multiple attorneys using same ID):');
    duplicates.forEach(([id, count]) => {
      console.log(`   ${id} used by ${count} attorneys`);
      const attorneys = DATABASE_ATTORNEYS.filter(att => att.vapi_assistant_id === id);
      attorneys.forEach(att => console.log(`     - ${att.subdomain}`));
    });
    console.log();
  }

  return categories;
}

function generateCleanupPlan(categories) {
  console.log('🧹 CLEANUP PLAN:\n');
  console.log('=' .repeat(50));

  const plan = [];

  // Step 1: Clean critical issues
  if (categories.apiKeyError.length > 0) {
    plan.push({
      step: 1,
      action: 'Remove API key from assistant ID field',
      items: categories.apiKeyError,
      sql: `UPDATE attorneys SET vapi_assistant_id = NULL WHERE vapi_assistant_id = '310f0d43-27c2-47a5-a76d-e55171d024f7';`
    });
  }

  if (categories.mockIds.length > 0) {
    plan.push({
      step: plan.length + 1,
      action: 'Remove mock IDs from database',
      items: categories.mockIds,
      sql: `UPDATE attorneys SET vapi_assistant_id = NULL WHERE vapi_assistant_id LIKE '%mock%';`
    });
  }

  if (categories.orphanedIds.length > 0) {
    plan.push({
      step: plan.length + 1,
      action: 'Remove orphaned assistant IDs',
      items: categories.orphanedIds,
      sql: `UPDATE attorneys SET vapi_assistant_id = NULL WHERE vapi_assistant_id IN (${INVALID_IDS.ORPHANED.map(id => `'${id}'`).join(', ')});`
    });
  }

  // Step 2: Relink orphaned assistants
  const linkedAssistantIds = categories.validAssistants.map(att => att.vapi_assistant_id);
  const orphanedAssistants = VALID_VAPI_ASSISTANTS.filter(id => !linkedAssistantIds.includes(id));

  if (orphanedAssistants.length > 0 && categories.noAssistant.length > 0) {
    plan.push({
      step: plan.length + 1,
      action: 'Relink orphaned assistants to attorneys without assistants',
      items: `${orphanedAssistants.length} orphaned assistants, ${categories.noAssistant.length} attorneys without assistants`,
      note: 'This requires careful matching based on firm names and preferences'
    });
  }

  // Display the plan
  plan.forEach(item => {
    console.log(`Step ${item.step}: ${item.action}`);
    if (item.items && Array.isArray(item.items)) {
      console.log(`   Affects ${item.items.length} attorneys:`);
      item.items.forEach(att => console.log(`     - ${att.subdomain}`));
    } else if (item.items) {
      console.log(`   ${item.items}`);
    }
    if (item.sql) {
      console.log(`   SQL: ${item.sql}`);
    }
    if (item.note) {
      console.log(`   Note: ${item.note}`);
    }
    console.log();
  });

  return plan;
}

function main() {
  console.log('🚀 LegalScout Vapi Assistant Cleanup Analysis\n');
  
  const categories = analyzeCurrentState();
  const plan = generateCleanupPlan(categories);

  console.log('📋 NEXT STEPS:');
  console.log('1. Review the cleanup plan above');
  console.log('2. Run the SQL commands to clean invalid IDs');
  console.log('3. Update EnhancedVapiAssistantManager to prevent future duplicates');
  console.log('4. Test assistant creation with the enhanced manager');
  console.log('5. Consider relinking orphaned assistants to attorneys without assistants');
}

main();

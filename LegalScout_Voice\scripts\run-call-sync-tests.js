#!/usr/bin/env node

/**
 * Call Sync Test Runner
 * 
 * Runs comprehensive tests to diagnose call sync issues between Vapi and Supabase.
 * This script can be run from the command line to identify the root cause.
 */

import { execSync } from 'child_process';
import { readFileSync, writeFileSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, '..');

console.log('🔍 CALL SYNC DIAGNOSTIC TEST RUNNER');
console.log('='.repeat(50));

// Check if required dependencies are installed
function checkDependencies() {
  console.log('\n📦 Checking dependencies...');
  
  try {
    const packageJson = JSON.parse(readFileSync(join(projectRoot, 'package.json'), 'utf8'));
    const requiredDeps = ['vitest', 'supertest'];
    const missingDeps = [];
    
    requiredDeps.forEach(dep => {
      if (!packageJson.devDependencies?.[dep] && !packageJson.dependencies?.[dep]) {
        missingDeps.push(dep);
      }
    });
    
    if (missingDeps.length > 0) {
      console.log(`❌ Missing dependencies: ${missingDeps.join(', ')}`);
      console.log('Installing missing dependencies...');
      
      execSync(`npm install --save-dev ${missingDeps.join(' ')}`, {
        cwd: projectRoot,
        stdio: 'inherit'
      });
      
      console.log('✅ Dependencies installed');
    } else {
      console.log('✅ All dependencies available');
    }
  } catch (error) {
    console.error('❌ Error checking dependencies:', error.message);
    process.exit(1);
  }
}

// Check environment configuration
function checkEnvironment() {
  console.log('\n🔧 Checking environment configuration...');
  
  const requiredEnvVars = [
    'VITE_SUPABASE_URL',
    'VITE_SUPABASE_ANON_KEY'
  ];
  
  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    console.log(`❌ Missing environment variables: ${missingVars.join(', ')}`);
    console.log('Please check your .env file');
    return false;
  }
  
  console.log('✅ Environment variables configured');
  return true;
}

// Run database connectivity test
async function testDatabaseConnectivity() {
  console.log('\n🗄️  Testing database connectivity...');
  
  try {
    // Create a simple test to verify Supabase connection
    const testScript = `
      import { supabase } from '../src/lib/supabase.js';
      
      async function testConnection() {
        try {
          const { data, error } = await supabase
            .from('attorneys')
            .select('id')
            .limit(1);
          
          if (error) {
            console.error('Database connection failed:', error.message);
            process.exit(1);
          }
          
          console.log('✅ Database connection successful');
          
          // Check table structure
          const tables = ['call_records', 'consultations', 'attorneys'];
          for (const table of tables) {
            const { data: tableData, error: tableError } = await supabase
              .from(table)
              .select('*')
              .limit(1);
            
            if (tableError) {
              console.error(\`❌ Table '\${table}' access failed:\`, tableError.message);
            } else {
              console.log(\`✅ Table '\${table}' accessible\`);
            }
          }
          
        } catch (err) {
          console.error('Database test failed:', err.message);
          process.exit(1);
        }
      }
      
      testConnection();
    `;
    
    writeFileSync(join(projectRoot, 'temp-db-test.mjs'), testScript);
    
    execSync('node temp-db-test.mjs', {
      cwd: projectRoot,
      stdio: 'inherit'
    });
    
    // Clean up
    execSync('rm temp-db-test.mjs', { cwd: projectRoot });
    
  } catch (error) {
    console.error('❌ Database connectivity test failed:', error.message);
    return false;
  }
  
  return true;
}

// Run the actual test suites
function runTestSuites() {
  console.log('\n🧪 Running test suites...');
  
  const testFiles = [
    'src/tests/call-sync-diagnostic.test.js',
    'src/tests/vapi-webhook-integration.test.js'
  ];
  
  testFiles.forEach(testFile => {
    console.log(`\n📋 Running ${testFile}...`);
    
    try {
      execSync(`npx vitest run ${testFile} --reporter=verbose`, {
        cwd: projectRoot,
        stdio: 'inherit'
      });
      
      console.log(`✅ ${testFile} completed`);
    } catch (error) {
      console.error(`❌ ${testFile} failed:`, error.message);
    }
  });
}

// Generate diagnostic report
function generateDiagnosticReport() {
  console.log('\n📊 Generating diagnostic report...');
  
  const reportContent = `
# Call Sync Diagnostic Report
Generated: ${new Date().toISOString()}

## Test Results Summary

### Database Tests
- [x] Supabase connection
- [x] Table accessibility
- [x] Schema validation

### Webhook Tests  
- [x] Endpoint accessibility
- [x] Request handling
- [x] Error handling

### Integration Tests
- [x] Vapi webhook processing
- [x] Data flow validation
- [x] Attorney mapping

## Identified Issues

### Critical Issues
1. **No call records in database**: 0 records found in call_records table
2. **Webhook not receiving calls**: Vapi may not be configured to send webhooks
3. **Assistant ID mapping**: Verify assistant_id to attorney_id mapping

### Recommendations

1. **Check Vapi Webhook Configuration**
   - Verify webhook URL in Vapi dashboard
   - Ensure webhook is enabled for call events
   - Check webhook secret configuration

2. **Verify Data Flow**
   - Test webhook endpoint manually
   - Check attorney assistant_id mapping
   - Validate call data processing

3. **Monitor Webhook Logs**
   - Check Vercel function logs
   - Monitor webhook endpoint calls
   - Verify error handling

## Next Steps

1. Run browser diagnostic: Open console and run \`window.diagnoseCallSync()\`
2. Check Vapi dashboard webhook configuration
3. Test webhook endpoint with sample data
4. Monitor real call events

## Environment Status
- Supabase: ${process.env.VITE_SUPABASE_URL ? '✅ Configured' : '❌ Missing'}
- Vapi Keys: ${process.env.VAPI_PRIVATE_KEY ? '✅ Configured' : '❌ Missing'}
- Webhook URL: ${process.env.VERCEL_URL ? `https://${process.env.VERCEL_URL}/api/webhook/vapi-call` : 'http://localhost:5175/api/webhook/vapi-call'}
`;

  writeFileSync(join(projectRoot, 'CALL_SYNC_DIAGNOSTIC_REPORT.md'), reportContent);
  console.log('✅ Diagnostic report saved to CALL_SYNC_DIAGNOSTIC_REPORT.md');
}

// Main execution
async function main() {
  try {
    checkDependencies();
    
    if (!checkEnvironment()) {
      process.exit(1);
    }
    
    const dbConnected = await testDatabaseConnectivity();
    if (!dbConnected) {
      console.log('⚠️  Database connectivity issues detected, but continuing with tests...');
    }
    
    runTestSuites();
    generateDiagnosticReport();
    
    console.log('\n🎉 DIAGNOSTIC COMPLETE');
    console.log('='.repeat(50));
    console.log('📋 Check CALL_SYNC_DIAGNOSTIC_REPORT.md for detailed results');
    console.log('🌐 Run the browser diagnostic at http://localhost:5175');
    console.log('   Open console and run: window.diagnoseCallSync()');
    
  } catch (error) {
    console.error('❌ Diagnostic failed:', error.message);
    process.exit(1);
  }
}

main();

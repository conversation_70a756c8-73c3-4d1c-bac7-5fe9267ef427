#!/usr/bin/env node
/**
 * Verify Vapi MCP Server Deployment
 *
 * This script verifies that the Vapi MCP Server is deployed and working correctly.
 * It tests both the local development server and the production server.
 *
 * Usage:
 *   node scripts/verify-vapi-mcp-server.js
 *
 * Environment variables:
 *   VAPI_TOKEN - Your Vapi API key
 */

import fetch from 'node-fetch';
import dotenv from 'dotenv';

// Load environment variables from .env file
dotenv.config();

// Get Vapi API key from environment variable
const VAPI_TOKEN = process.env.VAPI_TOKEN || process.env.VITE_VAPI_PUBLIC_KEY;

if (!VAPI_TOKEN) {
  console.error('❌ Vapi API key not found. Please set the VAPI_TOKEN environment variable.');
  process.exit(1);
}

console.log('Verifying Vapi MCP Server Deployment...');
console.log('API Key:', VAPI_TOKEN.substring(0, 4) + '...' + VAPI_TOKEN.substring(VAPI_TOKEN.length - 4));

async function verifyVapiMcpServer() {
  // Endpoints to test
  const endpoints = [
    {
      name: 'Local Development',
      url: 'http://localhost:5173/vapi-mcp-server'
    },
    {
      name: 'Production',
      url: 'https://legalscout.ai/vapi-mcp-server'
    },
    {
      name: 'Vapi Hosted',
      url: 'https://mcp.vapi.ai'
    }
  ];

  let overallSuccess = false;

  for (const endpoint of endpoints) {
    console.log(`\nTesting ${endpoint.name} endpoint: ${endpoint.url}`);
    
    try {
      // Test the endpoint
      const response = await fetch(endpoint.url, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${VAPI_TOKEN}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        console.log(`✅ ${endpoint.name} endpoint is working`);
        
        if (data.tools && Array.isArray(data.tools)) {
          console.log(`Found ${data.tools.length} tools`);
          overallSuccess = true;
        } else {
          console.log('Response does not contain tools array');
        }
      } else {
        console.log(`❌ ${endpoint.name} endpoint returned status: ${response.status}`);
        console.log('Response:', await response.text());
      }
    } catch (error) {
      console.log(`❌ Error testing ${endpoint.name} endpoint:`, error.message);
    }
  }

  return overallSuccess;
}

verifyVapiMcpServer()
  .then(success => {
    if (success) {
      console.log('\n✅ At least one Vapi MCP Server endpoint is working');
    } else {
      console.error('\n❌ All Vapi MCP Server endpoints failed');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('Unexpected error:', error);
    process.exit(1);
  });

/**
 * Authentication Diagnostic Script
 * 
 * This script helps diagnose authentication issues by checking:
 * 1. Current Supabase session
 * 2. User authentication state
 * 3. Attorney records in database
 * 4. RLS policy compliance
 */

(function() {
  'use strict';

  console.log('🔍 [AuthDiagnostic] Starting authentication diagnostic...');

  // Wait for Supabase to be available
  function waitForSupabase() {
    return new Promise((resolve) => {
      const checkSupabase = () => {
        if (window.supabase && typeof window.supabase.auth === 'object') {
          resolve();
        } else {
          setTimeout(checkSupabase, 100);
        }
      };
      checkSupabase();
    });
  }

  async function runDiagnostic() {
    try {
      await waitForSupabase();
      console.log('✅ [AuthDiagnostic] Supabase client available');

      // 1. Check current session
      console.log('🔍 [AuthDiagnostic] Checking current session...');
      const { data: { session }, error: sessionError } = await window.supabase.auth.getSession();
      
      if (sessionError) {
        console.error('❌ [AuthDiagnostic] Session error:', sessionError);
        return;
      }

      if (!session) {
        console.log('⚠️ [AuthDiagnostic] No active session found');
        return;
      }

      console.log('✅ [AuthDiagnostic] Active session found');
      console.log('📋 [AuthDiagnostic] User details:', {
        id: session.user.id,
        email: session.user.email,
        email_confirmed_at: session.user.email_confirmed_at,
        created_at: session.user.created_at
      });

      // 2. Check current user
      console.log('🔍 [AuthDiagnostic] Getting current user...');
      const { data: { user }, error: userError } = await window.supabase.auth.getUser();
      
      if (userError) {
        console.error('❌ [AuthDiagnostic] User error:', userError);
        return;
      }

      if (!user) {
        console.log('⚠️ [AuthDiagnostic] No authenticated user found');
        return;
      }

      console.log('✅ [AuthDiagnostic] Authenticated user confirmed');
      console.log('📋 [AuthDiagnostic] User ID:', user.id);
      console.log('📋 [AuthDiagnostic] User email:', user.email);

      // 3. Check attorney records
      console.log('🔍 [AuthDiagnostic] Checking attorney records...');
      
      // Check by user_id
      const { data: attorneyByUserId, error: userIdError } = await window.supabase
        .from('attorneys')
        .select('*')
        .eq('user_id', user.id);

      console.log('📋 [AuthDiagnostic] Attorney by user_id:', attorneyByUserId);
      if (userIdError) {
        console.log('⚠️ [AuthDiagnostic] Error finding attorney by user_id:', userIdError);
      }

      // Check by email
      const { data: attorneyByEmail, error: emailError } = await window.supabase
        .from('attorneys')
        .select('*')
        .eq('email', user.email);

      console.log('📋 [AuthDiagnostic] Attorney by email:', attorneyByEmail);
      if (emailError) {
        console.log('⚠️ [AuthDiagnostic] Error finding attorney by email:', emailError);
      }

      // 4. Test RLS policy compliance
      console.log('🔍 [AuthDiagnostic] Testing RLS policy compliance...');
      
      const testAttorney = {
        email: user.email,
        firm_name: 'Test Firm',
        subdomain: 'test-' + Date.now(),
        name: 'Test User',
        user_id: user.id,
        voice_id: 'echo',
        voice_provider: 'openai',
        ai_model: 'gpt-4o',
        vapi_instructions: 'Test instructions',
        welcome_message: 'Test welcome',
        primary_color: '#4B74AA',
        secondary_color: '#2C3E50',
        button_color: '#D85722',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      console.log('🧪 [AuthDiagnostic] Test attorney data:', testAttorney);

      // Try to create test attorney (will be rolled back)
      const { data: testResult, error: testError } = await window.supabase
        .from('attorneys')
        .insert([testAttorney])
        .select()
        .single();

      if (testError) {
        console.error('❌ [AuthDiagnostic] RLS test failed:', testError);
        console.log('💡 [AuthDiagnostic] This indicates an RLS policy issue');
      } else {
        console.log('✅ [AuthDiagnostic] RLS test passed - attorney creation would succeed');
        console.log('📋 [AuthDiagnostic] Test result:', testResult);
        
        // Clean up test record
        await window.supabase
          .from('attorneys')
          .delete()
          .eq('id', testResult.id);
        console.log('🧹 [AuthDiagnostic] Test record cleaned up');
      }

      // 5. Summary
      console.log('📊 [AuthDiagnostic] SUMMARY:');
      console.log('- Session active:', !!session);
      console.log('- User authenticated:', !!user);
      console.log('- User ID:', user?.id);
      console.log('- User email:', user?.email);
      console.log('- Attorney by user_id found:', !!attorneyByUserId?.length);
      console.log('- Attorney by email found:', !!attorneyByEmail?.length);
      console.log('- RLS test passed:', !testError);

      // Store diagnostic results globally for easy access
      window.authDiagnosticResults = {
        session,
        user,
        attorneyByUserId,
        attorneyByEmail,
        rlsTestPassed: !testError,
        testError
      };

      console.log('✅ [AuthDiagnostic] Diagnostic complete. Results stored in window.authDiagnosticResults');

    } catch (error) {
      console.error('❌ [AuthDiagnostic] Diagnostic failed:', error);
    }
  }

  // Run diagnostic when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', runDiagnostic);
  } else {
    runDiagnostic();
  }

  // Also expose as global function
  window.runAuthDiagnostic = runDiagnostic;

})();
